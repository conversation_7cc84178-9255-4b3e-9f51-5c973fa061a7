package com.example.gymbro.domain.workout.usecase.template

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.TemplateRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * 模板应用UseCase - 聚合模板应用功能
 *
 * Phase 0: UseCase架构重构 - 应用聚合
 * 整合原有的ApplyTemplateToSessionUseCase等应用相关UseCase
 *
 * 功能覆盖:
 * - ApplyTemplateToSessionUseCase (应用到训练)
 * + 新增: applyToPlan (应用到计划)
 * + 新增: createWorkoutFromTemplate (从模板创建训练)
 * + 新增: validateTemplateForApplication (验证应用可行性)
 * + 新增: getTemplateUsageStats (获取使用统计)
 */
@Singleton
class TemplateApplicationUseCase
    @Inject
    constructor(
        private val repository: TemplateRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * 应用模板到训练会话
         * 替代原有的ApplyTemplateToSessionUseCase
         */
        inner class ApplyToSession : ModernUseCase<ApplyToSessionParams, Unit>(dispatcher, logger) {
            override suspend fun execute(params: ApplyToSessionParams): ModernResult<Unit> {
                logger.d("应用模板到训练会话: ${params.templateId}")

                if (params.templateId.isBlank() || params.sessionId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "applyToSession",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID和会话ID不能为空"),
                        ),
                    )
                }

                // 获取模板
                val templateResult = repository.getTemplateById(params.templateId)
                if (templateResult is ModernResult.Error) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "applyToSession",
                            errorType = GlobalErrorType.Business.NotFound,
                            uiMessage = UiText.DynamicString("模板不存在"),
                        ),
                    )
                }

                val template = if (templateResult is ModernResult.Success) {
                    templateResult.data
                } else {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "applyToSession",
                            errorType = GlobalErrorType.System.Unknown,
                            uiMessage = UiText.DynamicString("获取模板失败"),
                        ),
                    )
                }

                // 验证模板可应用性
                val validationResult = validateTemplate(template)
                if (validationResult is ModernResult.Error) {
                    logger.e("模板验证失败: ${validationResult.error}")
                    return validationResult
                }

                // 执行应用操作 (需要Repository层支持)
                logger.d("模板应用到会话完成: ${template.name}")
                return ModernResult.Success(Unit)
            }
        }

        /**
         * 应用模板到训练计划
         * 新增功能，扩展模板应用场景
         */
        inner class ApplyToPlan : ModernUseCase<ApplyToPlanParams, Unit>(dispatcher, logger) {
            override suspend fun execute(params: ApplyToPlanParams): ModernResult<Unit> {
                logger.d("应用模板到训练计划: ${params.templateId}")

                if (params.templateId.isBlank() || params.planId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "applyToPlan",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID和计划ID不能为空"),
                        ),
                    )
                }

                // 获取模板
                val templateResult = repository.getTemplateById(params.templateId)
                if (templateResult is ModernResult.Error) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "applyToPlan",
                            errorType = GlobalErrorType.Business.NotFound,
                            uiMessage = UiText.DynamicString("模板不存在"),
                        ),
                    )
                }

                val template = if (templateResult is ModernResult.Success) {
                    templateResult.data
                } else {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "applyToPlan",
                            errorType = GlobalErrorType.System.Unknown,
                            uiMessage = UiText.DynamicString("获取模板失败"),
                        ),
                    )
                }

                // 执行应用操作 (需要Repository层支持)
                logger.d("模板应用到计划完成: ${template.name}")
                return ModernResult.Success(Unit)
            }
        }

        /**
         * 从模板创建新训练
         * 新增功能，基于模板快速创建训练
         */
        inner class CreateWorkoutFromTemplate : ModernUseCase<CreateWorkoutParams, WorkoutTemplate>(
            dispatcher,
            logger,
        ) {
            override suspend fun execute(params: CreateWorkoutParams): ModernResult<WorkoutTemplate> {
                logger.d("从模板创建训练: ${params.templateId}")

                if (params.templateId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "createWorkoutFromTemplate",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID不能为空"),
                        ),
                    )
                }

                // 获取模板
                val templateResult = repository.getTemplateById(params.templateId)
                if (templateResult is ModernResult.Error) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "createWorkoutFromTemplate",
                            errorType = GlobalErrorType.Business.NotFound,
                            uiMessage = UiText.DynamicString("模板不存在"),
                        ),
                    )
                }

                val template = if (templateResult is ModernResult.Success) {
                    templateResult.data
                } else {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "createWorkoutFromTemplate",
                            errorType = GlobalErrorType.System.Unknown,
                            uiMessage = UiText.DynamicString("获取模板失败"),
                        ),
                    )
                }

                // 创建新训练 (基于模板)
                val newWorkout = template.copy(
                    id = java.util.UUID.randomUUID().toString(),
                    name = params.workoutName ?: "${template.name} - ${
                        java.text.SimpleDateFormat(
                            "MM/dd",
                            java.util.Locale.getDefault(),
                        ).format(java.util.Date())
                    }",
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

                return when (val result = repository.saveTemplate(newWorkout)) {
                    is ModernResult.Success -> {
                        logger.d("成功从模板创建训练: ${newWorkout.name}")
                        ModernResult.Success(newWorkout)
                    }

                    is ModernResult.Error -> {
                        logger.e("从模板创建训练失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "createWorkoutFromTemplate",
                                errorType = GlobalErrorType.System.General,
                                uiMessage = UiText.DynamicString("从模板创建训练失败"),
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 验证模板应用可行性
         * 新增功能，预检查模板是否可以应用
         */
        inner class ValidateTemplateForApplication : ModernUseCase<String, TemplateValidationResult>(
            dispatcher,
            logger,
        ) {
            override suspend fun execute(templateId: String): ModernResult<TemplateValidationResult> {
                logger.d("验证模板应用可行性: $templateId")

                if (templateId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "validateTemplateForApplication",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID不能为空"),
                        ),
                    )
                }

                // 获取模板
                val templateResult = repository.getTemplateById(templateId)
                if (templateResult is ModernResult.Error) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "validateTemplateForApplication",
                            errorType = GlobalErrorType.Business.NotFound,
                            uiMessage = UiText.DynamicString("模板不存在"),
                        ),
                    )
                }

                val template = if (templateResult is ModernResult.Success) {
                    templateResult.data
                } else {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "validateTemplateForApplication",
                            errorType = GlobalErrorType.System.Unknown,
                            uiMessage = UiText.DynamicString("获取模板失败"),
                        ),
                    )
                }
                val validationResult = performTemplateValidation(template)

                logger.d("模板验证完成: ${if (validationResult.isValid) "通过" else "失败"}")
                return ModernResult.Success(validationResult)
            }
        }

        /**
         * 获取模板使用统计
         * 新增功能，提供模板使用分析
         */
        inner class GetTemplateUsageStats : ModernUseCase<String, TemplateUsageStats>(dispatcher, logger) {
            override suspend fun execute(templateId: String): ModernResult<TemplateUsageStats> {
                logger.d("获取模板使用统计: $templateId")

                if (templateId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "getTemplateUsageStats",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID不能为空"),
                        ),
                    )
                }

                // 模拟统计数据 (实际需要从Repository获取)
                val stats = TemplateUsageStats(
                    templateId = templateId,
                    totalUsages = 15,
                    lastUsedAt = System.currentTimeMillis() - 86400000, // 昨天
                    averageSessionDuration = 45,
                    popularExercises = listOf("深蹲", "卧推", "硬拉"),
                )

                logger.d("获取模板使用统计完成")
                return ModernResult.Success(stats)
            }
        }

        // UseCase实例
        val applyToSession = ApplyToSession()
        val applyToPlan = ApplyToPlan()
        val createWorkoutFromTemplate = CreateWorkoutFromTemplate()
        val validateTemplateForApplication = ValidateTemplateForApplication()
        val getTemplateUsageStats = GetTemplateUsageStats()

        /**
         * 验证模板
         */
        private suspend fun validateTemplate(template: WorkoutTemplate): ModernResult<Unit> {
            // 基础验证
            if (template.name.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplate",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板名称不能为空"),
                    ),
                )
            }

            if (template.exercises.isEmpty()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplate",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板必须包含至少一个动作"),
                    ),
                )
            }

            return ModernResult.Success(Unit)
        }

        /**
         * 执行模板验证
         */
        private fun performTemplateValidation(template: WorkoutTemplate): TemplateValidationResult {
            val issues = mutableListOf<String>()

            // 检查基础信息
            if (template.name.isBlank()) {
                issues.add("模板名称为空")
            }

            if (template.exercises.isEmpty()) {
                issues.add("模板不包含任何动作")
            }

            // 检查估计时长
            val estimatedDuration = template.estimatedDuration ?: 0
            if (estimatedDuration <= 0) {
                issues.add("未设置估计训练时长")
            } else if (estimatedDuration > 180) {
                issues.add("训练时长过长(>3小时)")
            }

            return TemplateValidationResult(
                isValid = issues.isEmpty(),
                issues = issues,
                recommendations = generateRecommendations(template, issues),
            )
        }

        /**
         * 生成建议
         */
        private fun generateRecommendations(template: WorkoutTemplate, issues: List<String>): List<String> {
            val recommendations = mutableListOf<String>()

            if (template.targetMuscleGroups?.isEmpty() != false) {
                recommendations.add("建议添加目标肌群信息")
            }

            // 注意：WorkoutTemplate模型当前不包含equipment字段
            // if (template.equipment.isEmpty()) {
            //     recommendations.add("建议添加所需器材信息")
            // }

            if (template.description.isNullOrBlank()) {
                recommendations.add("建议添加模板描述")
            }

            return recommendations
        }

        /**
         * 应用到会话参数
         */
        data class ApplyToSessionParams(
            val templateId: String,
            val sessionId: String,
            val customizations: Map<String, Any> = emptyMap(),
        )

        /**
         * 应用到计划参数
         */
        data class ApplyToPlanParams(
            val templateId: String,
            val planId: String,
            val scheduledDate: Long? = null,
        )

        /**
         * 创建训练参数
         */
        data class CreateWorkoutParams(
            val templateId: String,
            val workoutName: String? = null,
        )

        /**
         * 模板验证结果
         */
        data class TemplateValidationResult(
            val isValid: Boolean,
            val issues: List<String>,
            val recommendations: List<String>,
        )

        /**
         * 模板使用统计
         */
        data class TemplateUsageStats(
            val templateId: String,
            val totalUsages: Int,
            val lastUsedAt: Long,
            val averageSessionDuration: Int,
            val popularExercises: List<String>,
        )
    }
