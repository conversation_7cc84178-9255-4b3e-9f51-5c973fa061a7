package com.example.gymbro.buildlogic.detekt.logging

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.KtCallExpression
import org.jetbrains.kotlin.psi.KtDotQualifiedExpression
import org.jetbrains.kotlin.psi.KtNamedFunction
import org.jetbrains.kotlin.psi.KtTreeVisitorVoid

/**
 * 限制每个函数中的 Timber 日志调用数量。
 *
 * 规则：每个函数最多只能有 1 个 Timber 日志调用。
 * 这有助于保持日志的简洁性和可读性。
 */
class MaxTimberLogsPerFunction(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "每个函数最多只能有 1 个 Timber 日志调用。",
        Debt.FIVE_MINS
    )

    private val maxLogsPerFunction: Int by config(1)

    override fun visitNamedFunction(function: KtNamedFunction) {
        super.visitNamedFunction(function)

        val timberCalls = countTimberCalls(function)

        if (timberCalls > maxLogsPerFunction) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(function),
                    "函数 '${function.name}' 包含 $timberCalls 个 Timber 日志调用，" +
                        "超过了限制的 $maxLogsPerFunction 个。请合并或移除多余的日志调用。"
                )
            )
        }
    }

    private fun countTimberCalls(function: KtNamedFunction): Int {
        var count = 0

        function.accept(object : KtTreeVisitorVoid() {
            override fun visitCallExpression(expression: KtCallExpression) {
                super.visitCallExpression(expression)

                val callText = expression.text

                // 检查 Timber 调用
                if (isTimberCall(callText)) {
                    count++
                }
            }

            override fun visitDotQualifiedExpression(expression: KtDotQualifiedExpression) {
                super.visitDotQualifiedExpression(expression)

                val text = expression.text

                // 检查 Timber.* 调用
                if (text.startsWith("Timber.") && isLogCall(text)) {
                    count++
                }
            }
        })

        return count
    }

    private fun isTimberCall(text: String): Boolean {
        return text.contains("Timber.d(") ||
            text.contains("Timber.i(") ||
            text.contains("Timber.w(") ||
            text.contains("Timber.e(") ||
            text.contains("Timber.v(") ||
            text.contains("timber.d(") ||
            text.contains("timber.i(") ||
            text.contains("timber.w(") ||
            text.contains("timber.e(") ||
            text.contains("timber.v(")
    }

    private fun isLogCall(text: String): Boolean {
        val logMethods = setOf("d(", "i(", "w(", "e(", "v(", "wtf(")
        return logMethods.any { method ->
            text.contains("Timber.$method")
        }
    }
}
