package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus

/**
 * Plan 导入日历功能 UseCase
 *
 * 🎯 核心功能：将 Plan 数据转换为日历可用的格式
 * - 状态数据：Plan 的进度状态传递给日历
 * - 模板信息：Template 名称和详情映射
 * - Plan 信息：计划基本信息和总体进度
 * - 日期映射：Plan 每日安排对应到具体日期
 *
 * 基于 MVI 2.0 架构和 Clean Architecture 设计
 */
@Singleton
class PlanToCalendarUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val templateRepository: TemplateRepository,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * 将 Plan 转换为日历条目列表
         *
         * @param planId Plan ID
         * @param startDate 开始日期
         * @return 日历条目列表
         */
        suspend fun convertPlanToCalendarEntries(
            planId: String,
            startDate: LocalDate,
        ): ModernResult<List<PlanCalendarEntry>> = withContext(ioDispatcher) {
            try {
                logger.d("开始转换 Plan 到日历条目: planId=$planId, startDate=$startDate")

                // 1. 获取 Plan 数据
                val plan = planRepository.getPlanById(planId)
                    ?: return@withContext ModernResult.Error(
                        ModernDataError(
                            operationName = "convertPlanToCalendarEntries",
                            errorType = GlobalErrorType.Data.NotFound,
                            uiMessage = UiText.DynamicString("Plan not found: $planId"),
                        ),
                    )

                // 2. 获取所有相关的 Template 信息
                val templateIds = plan.dailySchedule.values
                    .flatMap { it.getActiveTemplateReferences() }
                    .distinct()

                val templates = mutableMapOf<String, String>() // templateId -> templateName
                templateIds.forEach { templateId ->
                    when (val result = templateRepository.getTemplateById(templateId)) {
                        is ModernResult.Success -> {
                            result.data?.let { template ->
                                templates[templateId] = template.name
                            }
                        }

                        is ModernResult.Error -> {
                            logger.w("获取模板失败: templateId=$templateId, error=${result.error.message}")
                            // 继续处理其他模板，不中断整个流程
                        }

                        is ModernResult.Loading -> {
                            // 不应该发生，因为这是 suspend 函数
                        }
                    }
                }

                // 3. 转换为日历条目
                val calendarEntries = plan.dailySchedule.map { (dayNumber, dayPlan) ->
                    val entryDate = startDate.plus(DateTimeUnit.DAY * (dayNumber - 1)) // dayNumber 从 1 开始

                    // 构建模板列表
                    val calendarTemplates = dayPlan.getActiveTemplateReferences().map { templateId ->
                        PlanCalendarTemplate(
                            id = templateId,
                            name = templates[templateId] ?: "未知模板",
                            estimatedDuration = dayPlan.estimatedDuration,
                        )
                    }

                    // 转换进度状态
                    val entryStatus = when (dayPlan.progress) {
                        PlanProgressStatus.COMPLETED -> PlanCalendarEntryStatus.COMPLETED
                        PlanProgressStatus.IN_PROGRESS -> PlanCalendarEntryStatus.IN_PROGRESS
                        PlanProgressStatus.NOT_STARTED -> PlanCalendarEntryStatus.SCHEDULED
                    }

                    PlanCalendarEntry(
                        date = entryDate,
                        status = entryStatus,
                        // Plan 关联信息
                        planId = plan.id,
                        planName = plan.name.toString(),
                        planWeek = calculateWeekNumber(dayNumber, 7), // 假设每周7天
                        planDay = dayNumber,
                        // 模板信息
                        templates = calendarTemplates,
                        // 统计信息
                        estimatedDuration = dayPlan.estimatedDuration,
                        targetGoal = plan.targetGoal,
                        // 其他信息
                        notes = dayPlan.dayNotes?.toString(),
                        isRestDay = dayPlan.isRestDay,
                    )
                }.sortedBy { it.date }

                logger.d("成功转换 ${calendarEntries.size} 个日历条目")
                ModernResult.Success(calendarEntries)
            } catch (e: Exception) {
                logger.e("转换 Plan 到日历条目失败", e)
                ModernResult.Error(
                    ModernDataError(
                        operationName = "convertPlanToCalendarEntries",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("转换 Plan 到日历条目失败"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 批量导入多个 Plan 到日历
         *
         * @param planIds Plan ID 列表
         * @param startDates 对应的开始日期列表
         * @return 所有日历条目的合并列表
         */
        suspend fun importMultiplePlansToCalendar(
            planIds: List<String>,
            startDates: List<LocalDate>,
        ): ModernResult<List<PlanCalendarEntry>> = withContext(ioDispatcher) {
            try {
                require(planIds.size == startDates.size) {
                    "Plan IDs 和开始日期数量必须一致"
                }

                val allEntries = mutableListOf<PlanCalendarEntry>()

                planIds.zip(startDates).forEach { (planId, startDate) ->
                    when (val result = convertPlanToCalendarEntries(planId, startDate)) {
                        is ModernResult.Success -> {
                            allEntries.addAll(result.data)
                        }

                        is ModernResult.Error -> {
                            logger.w("导入 Plan 失败: planId=$planId, error=${result.error}")
                            // 继续处理其他 Plan，不中断整个流程
                        }

                        is ModernResult.Loading -> {
                            // 不应该发生，因为这是 suspend 函数
                        }
                    }
                }

                // 按日期排序并去重
                val sortedEntries = allEntries
                    .distinctBy { "${it.date}_${it.planId}_${it.planDay}" }
                    .sortedBy { it.date }

                logger.d("成功导入 ${sortedEntries.size} 个日历条目")
                ModernResult.Success(sortedEntries)
            } catch (e: Exception) {
                logger.e("批量导入 Plan 到日历失败", e)
                ModernResult.Error(
                    ModernDataError(
                        operationName = "importMultiplePlansToCalendar",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("批量导入 Plan 到日历失败"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 获取 Plan 的日历摘要信息
         *
         * @param planId Plan ID
         * @return Plan 的日历摘要
         */
        suspend fun getPlanCalendarSummary(
            planId: String,
        ): ModernResult<PlanCalendarSummary> = withContext(ioDispatcher) {
            try {
                val plan = planRepository.getPlanById(planId)
                    ?: return@withContext ModernResult.Error(
                        ModernDataError(
                            operationName = "getPlanCalendarSummary",
                            errorType = GlobalErrorType.Data.NotFound,
                            uiMessage = UiText.DynamicString("Plan not found: $planId"),
                        ),
                    )

                val totalDays = plan.totalDays
                val workoutDays = plan.dailySchedule.values.count { !it.isRestDay }
                val restDays = totalDays - workoutDays
                val completedDays = plan.dailySchedule.values.count {
                    it.progress == PlanProgressStatus.COMPLETED
                }
                val completionRate = if (totalDays > 0) {
                    (completedDays.toFloat() / totalDays) * 100f
                } else {
                    0f
                }

                val summary = PlanCalendarSummary(
                    planId = plan.id,
                    planName = plan.name.toString(),
                    totalDays = totalDays,
                    workoutDays = workoutDays,
                    restDays = restDays,
                    completedDays = completedDays,
                    completionRate = completionRate,
                    targetGoal = plan.targetGoal,
                    estimatedDuration = plan.estimatedDuration,
                )

                ModernResult.Success(summary)
            } catch (e: Exception) {
                logger.e("获取 Plan 日历摘要失败", e)
                ModernResult.Error(
                    ModernDataError(
                        operationName = "getPlanCalendarSummary",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("获取 Plan 日历摘要失败"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 计算周数
         */
        private fun calculateWeekNumber(dayNumber: Int, daysPerWeek: Int): Int {
            return ((dayNumber - 1) / daysPerWeek) + 1
        }
    }

/**
 * Plan 日历条目数据类（Domain 层）
 */
data class PlanCalendarEntry(
    val date: LocalDate,
    val status: PlanCalendarEntryStatus,
    val planId: String,
    val planName: String,
    val planWeek: Int,
    val planDay: Int,
    val templates: List<PlanCalendarTemplate>,
    val estimatedDuration: Int?,
    val targetGoal: String?,
    val notes: String?,
    val isRestDay: Boolean,
)

/**
 * Plan 日历模板数据类
 */
data class PlanCalendarTemplate(
    val id: String,
    val name: String,
    val estimatedDuration: Int?,
)

/**
 * Plan 日历条目状态枚举
 */
enum class PlanCalendarEntryStatus {
    SCHEDULED, // 已安排（对应 NOT_STARTED）
    IN_PROGRESS, // 进行中
    COMPLETED, // 已完成
}

/**
 * Plan 日历摘要数据类
 */
data class PlanCalendarSummary(
    val planId: String,
    val planName: String,
    val totalDays: Int,
    val workoutDays: Int,
    val restDays: Int,
    val completedDays: Int,
    val completionRate: Float, // 0-100
    val targetGoal: String?,
    val estimatedDuration: Int?,
)
