package com.example.gymbro.designSystem.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.LightMode
import androidx.compose.material.icons.filled.PhoneAndroid
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.R
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 主题模式枚举
 *
 * 定义应用支持的主题模式，避免对业务层ThemeManager的直接依赖
 */
enum class ThemeMode(val value: String) {
    LIGHT("light"),
    DARK("dark"),
    SYSTEM("system"),
    ;

    companion object {
        fun fromString(value: String): ThemeMode = when (value) {
            LIGHT.value -> LIGHT
            DARK.value -> DARK
            else -> SYSTEM
        }
    }
}

/**
 * 统一的主题切换按钮组件
 *
 * 支持浅色/深色/跟随系统三种模式循环切换，提供一致的主题切换体验。
 * 该组件被Home和Profile页面共同使用，确保视觉和交互的一致性。
 *
 * 架构改进：
 * - 移除对业务层ThemeManager的直接依赖
 * - 使用UiText系统替代硬编码字符串
 * - 支持更好的无障碍访问
 * - 优化暗色主题的波纹效果
 *
 * @param currentThemeMode 当前主题模式
 * @param onThemeToggle 主题切换回调函数
 * @param modifier 修饰符
 */
@Composable
fun gymBroThemeToggleButton(
    currentThemeMode: ThemeMode,
    onThemeToggle: () -> Unit,
    modifier: Modifier = Modifier,
) {
    // 根据当前主题模式确定图标和描述
    // 图标表示当前状态，描述表示点击后的下一个状态
    val (icon, contentDescription) = when (currentThemeMode) {
        ThemeMode.LIGHT -> Icons.Default.LightMode to UiText.StringResource(R.string.theme_switch_to_dark, emptyList())
        ThemeMode.DARK -> Icons.Default.DarkMode to UiText.StringResource(R.string.theme_switch_to_system, emptyList())
        ThemeMode.SYSTEM -> Icons.Default.PhoneAndroid to UiText.StringResource(
            R.string.theme_switch_to_light,
            emptyList(),
        )
    }

    IconButton(
        onClick = onThemeToggle,
        modifier = modifier,
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription.asString(),
            tint = MaterialTheme.colorScheme.onSurface,
        )
    }
}

/**
 * 兼容性函数：支持字符串参数的主题切换按钮
 *
 * 为了保持向后兼容性，支持传入字符串形式的主题模式
 */
@Composable
fun gymBroThemeToggleButton(
    currentThemeMode: String,
    onThemeToggle: () -> Unit,
    modifier: Modifier = Modifier,
) {
    gymBroThemeToggleButton(
        currentThemeMode = ThemeMode.fromString(currentThemeMode),
        onThemeToggle = onThemeToggle,
        modifier = modifier,
    )
}

// === Preview组件 - 标准化预览 ===

@GymBroPreview
@Composable
private fun gymBroThemeToggleButtonLightPreview() {
    GymBroTheme {
        gymBroThemeToggleButton(
            currentThemeMode = ThemeMode.LIGHT,
            onThemeToggle = {},
        )
    }
}

@GymBroPreview
@Composable
private fun gymBroThemeToggleButtonDarkPreview() {
    GymBroTheme(darkTheme = true) {
        gymBroThemeToggleButton(
            currentThemeMode = ThemeMode.DARK,
            onThemeToggle = {},
        )
    }
}

@GymBroPreview
@Composable
private fun gymBroThemeToggleButtonSystemPreview() {
    GymBroTheme {
        gymBroThemeToggleButton(
            currentThemeMode = ThemeMode.SYSTEM,
            onThemeToggle = {},
        )
    }
}

@GymBroPreview
@Composable
private fun gymBroThemeToggleButtonStringCompatPreview() {
    GymBroTheme {
        gymBroThemeToggleButton(
            currentThemeMode = "light",
            onThemeToggle = {},
        )
    }
}
