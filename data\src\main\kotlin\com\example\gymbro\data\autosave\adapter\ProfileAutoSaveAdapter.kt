package com.example.gymbro.data.autosave.adapter

import com.example.gymbro.core.autosave.config.AutoSaveConfig
import com.example.gymbro.core.autosave.storage.AutoSaveStorage
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.autosave.AutoSaveRepository
import com.example.gymbro.data.autosave.AutoSaveSession
import com.example.gymbro.data.autosave.strategy.ImmediateSaveStrategy
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase
import com.example.gymbro.domain.profile.usecase.UpdateUserProfileUseCase
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json

/**
 * Profile模块自动保存适配器
 *
 * 🎯 功能特性：
 * - 为Profile模块提供自动保存功能
 * - 集成现有的UpdateUserProfileUseCase和GetUserProfileUseCase
 * - 使用即时保存策略，确保用户资料及时保存
 * - 支持UserProfile类型的序列化和反序列化
 * - 保持与现有MVI架构的兼容性
 *
 * @param autoSaveRepository 自动保存仓库
 * @param updateUserProfileUseCase 更新用户资料用例
 * @param getUserProfileUseCase 获取用户资料用例
 * @param json Json序列化器
 * @param logger 日志记录器
 */
@Singleton
class ProfileAutoSaveAdapter
    @Inject
    constructor(
        private val autoSaveRepository: AutoSaveRepository,
        private val updateUserProfileUseCase: UpdateUserProfileUseCase,
        private val getUserProfileUseCase: GetUserProfileUseCase,
        private val json: Json,
        private val logger: Logger,
    ) {
        companion object {
            private const val CONFIG_ID = "profile_autosave"
            private const val SESSION_PREFIX = "profile_session_"
        }

        /**
         * 创建Profile自动保存会话
         *
         * @param userId 用户ID
         * @param scope 协程作用域
         * @return 自动保存会话ID
         */
        suspend fun createAutoSave(
            userId: String,
            scope: CoroutineScope,
        ): ModernResult<String> =
            try {
                logger.d("ProfileAutoSaveAdapter", "创建Profile自动保存会话: $userId")

                // 创建存储后端
                val storage = createProfileStorage(userId)

                // 创建保存策略（即时保存）
                val strategy = ImmediateSaveStrategy.create<UserProfile>(logger)

                // 创建自动保存配置
                val config =
                    AutoSaveConfig(
                        id = "$CONFIG_ID$userId",
                        strategy = strategy,
                        storage = storage,
                        enableRecovery = true,
                    )

                // 创建会话
                val sessionResult = autoSaveRepository.createSession(config, scope)

                when (sessionResult) {
                    is ModernResult.Success -> {
                        logger.d("ProfileAutoSaveAdapter", "Profile自动保存会话创建成功: ${sessionResult.data}")
                        sessionResult
                    }

                    is ModernResult.Error -> {
                        logger.e("ProfileAutoSaveAdapter", "创建Profile自动保存会话失败", sessionResult.error.cause)
                        sessionResult
                    }

                    is ModernResult.Loading -> {
                        logger.d("ProfileAutoSaveAdapter", "Profile自动保存会话创建中...")
                        sessionResult
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "创建Profile自动保存会话异常: $userId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveAdapter.createAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }

        /**
         * 获取Profile自动保存会话
         *
         * @param sessionId 会话ID
         * @return 自动保存会话
         */
        fun getAutoSaveSession(sessionId: String): AutoSaveSession<UserProfile>? =
            try {
                val session = autoSaveRepository.getSession<UserProfile>(sessionId)
                if (session != null) {
                    logger.d("ProfileAutoSaveAdapter", "获取Profile自动保存会话成功: $sessionId")
                } else {
                    logger.w("ProfileAutoSaveAdapter", "未找到Profile自动保存会话: $sessionId")
                }
                session
            } catch (e: Exception) {
                logger.e(e, "获取Profile自动保存会话失败: $sessionId")
                null
            }

        /**
         * 启动Profile自动保存
         *
         * @param sessionId 会话ID
         * @param userId 用户ID
         */
        suspend fun startAutoSave(
            sessionId: String,
            userId: String,
        ) {
            try {
                logger.d("ProfileAutoSaveAdapter", "启动Profile自动保存: $sessionId")

                val session = getAutoSaveSession(sessionId)
                if (session == null) {
                    logger.e("ProfileAutoSaveAdapter", "会话不存在，无法启动自动保存: $sessionId")
                    return
                }

                // 获取当前用户资料作为初始数据
                val profileResult = getUserProfileUseCase()
                when (profileResult) {
                    is ModernResult.Success -> {
                        val profile = profileResult.data
                        if (profile != null) {
                            session.start(profile)
                            logger.d("ProfileAutoSaveAdapter", "Profile自动保存启动成功: $sessionId")
                        } else {
                            logger.w("ProfileAutoSaveAdapter", "用户资料为空，无法启动自动保存: $userId")
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("ProfileAutoSaveAdapter", "获取用户资料失败，无法启动自动保存", profileResult.error.cause)
                    }

                    is ModernResult.Loading -> {
                        logger.d("ProfileAutoSaveAdapter", "正在获取用户资料...")
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "启动Profile自动保存异常: $sessionId")
            }
        }

        /**
         * 更新Profile数据
         *
         * @param sessionId 会话ID
         * @param profile 用户资料
         */
        fun updateProfile(
            sessionId: String,
            profile: UserProfile,
        ) {
            try {
                val session = getAutoSaveSession(sessionId)
                if (session != null) {
                    session.update(profile)
                    logger.d("ProfileAutoSaveAdapter", "Profile数据已更新: $sessionId")
                } else {
                    logger.w("ProfileAutoSaveAdapter", "会话不存在，无法更新Profile: $sessionId")
                }
            } catch (e: Exception) {
                logger.e(e, "更新Profile数据失败: $sessionId")
            }
        }

        /**
         * 立即保存Profile
         *
         * @param sessionId 会话ID
         */
        suspend fun saveNow(sessionId: String): Result<Unit> =
            try {
                val session = getAutoSaveSession(sessionId)
                if (session != null) {
                    val result = session.saveNow()
                    logger.d("ProfileAutoSaveAdapter", "Profile立即保存完成: $sessionId")
                    result
                } else {
                    logger.w("ProfileAutoSaveAdapter", "会话不存在，无法立即保存: $sessionId")
                    Result.failure(IllegalStateException("会话不存在"))
                }
            } catch (e: Exception) {
                logger.e(e, "Profile立即保存失败: $sessionId")
                Result.failure(e)
            }

        /**
         * 停止Profile自动保存
         *
         * @param sessionId 会话ID
         */
        suspend fun stopAutoSave(sessionId: String) {
            try {
                val result = autoSaveRepository.deleteSession(sessionId)
                when (result) {
                    is ModernResult.Success -> {
                        logger.d("ProfileAutoSaveAdapter", "Profile自动保存已停止: $sessionId")
                    }

                    is ModernResult.Error -> {
                        logger.e("ProfileAutoSaveAdapter", "停止Profile自动保存失败", result.error.cause)
                    }

                    is ModernResult.Loading -> {
                        logger.d("ProfileAutoSaveAdapter", "正在停止Profile自动保存...")
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "停止Profile自动保存异常: $sessionId")
            }
        }

        /**
         * 创建Profile存储后端
         */
        private fun createProfileStorage(userId: String): AutoSaveStorage<UserProfile> {
            return object : AutoSaveStorage<UserProfile> {
                override suspend fun save(
                    id: String,
                    data: UserProfile,
                ) {
                    logger.d("ProfileAutoSaveAdapter", "保存Profile到UseCase: ${data.userId}")

                    val result = updateUserProfileUseCase(data)
                    when (result) {
                        is ModernResult.Success -> {
                            logger.d("ProfileAutoSaveAdapter", "Profile保存成功: ${data.userId}")
                        }

                        is ModernResult.Error -> {
                            logger.e("ProfileAutoSaveAdapter", "Profile保存失败", result.error.cause)
                            throw Exception("Profile保存失败: ${result.error}")
                        }

                        is ModernResult.Loading -> {
                            logger.d("ProfileAutoSaveAdapter", "Profile保存中...")
                        }
                    }
                }

                override suspend fun restore(id: String): UserProfile? {
                    logger.d("ProfileAutoSaveAdapter", "从UseCase恢复Profile: $userId")

                    val result = getUserProfileUseCase()
                    return when (result) {
                        is ModernResult.Success -> {
                            logger.d("ProfileAutoSaveAdapter", "Profile恢复成功: $userId")
                            result.data
                        }

                        is ModernResult.Error -> {
                            logger.e("ProfileAutoSaveAdapter", "Profile恢复失败", result.error.cause)
                            null
                        }

                        is ModernResult.Loading -> {
                            logger.d("ProfileAutoSaveAdapter", "Profile恢复中...")
                            null
                        }
                    }
                }

                override suspend fun clear(id: String) {
                    logger.d("ProfileAutoSaveAdapter", "清除Profile缓存: $id")
                    // Profile数据不支持清除，只记录日志
                }

                override suspend fun exists(id: String): Boolean {
                    val result = getUserProfileUseCase()
                    return when (result) {
                        is ModernResult.Success -> result.data != null
                        else -> false
                    }
                }

                override suspend fun getSize(id: String): Long =
                    try {
                        val profile = restore(id)
                        if (profile != null) {
                            json.encodeToString(profile).length.toLong()
                        } else {
                            0L
                        }
                    } catch (e: Exception) {
                        0L
                    }
            }
        }
    }
