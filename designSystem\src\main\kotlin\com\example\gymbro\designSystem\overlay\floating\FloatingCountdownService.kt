package com.example.gymbro.designSystem.overlay.floating

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 轻量级悬浮倒计时服务
 *
 * 提供简单的全局悬浮倒计时功能，作为现有倒计时的增强功能
 * 不替换现有倒计时逻辑，而是提供可选的悬浮显示
 */
@Singleton
class FloatingCountdownService
    @Inject
    constructor(
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) {
        private val _state = MutableStateFlow<FloatingCountdownState?>(null)
        val state: StateFlow<FloatingCountdownState?> = _state.asStateFlow()

        private var countdownJob: Job? = null
        private val serviceScope = CoroutineScope(ioDispatcher + SupervisorJob())

        /**
         * 启动悬浮倒计时
         *
         * @param title 倒计时标题
         * @param seconds 倒计时秒数
         * @param onComplete 完成回调
         */
        fun startFloating(
            title: String,
            seconds: Int,
            onComplete: (() -> Unit)? = null,
        ) {
            stopFloating() // 停止现有倒计时

            _state.value = FloatingCountdownState(
                title = title,
                totalSeconds = seconds,
                remainingSeconds = seconds,
                isRunning = true,
                onComplete = onComplete,
            )

            startCountdown()
        }

        /**
         * 停止悬浮倒计时
         */
        fun stopFloating() {
            countdownJob?.cancel()
            _state.value = null
        }

        /**
         * 暂停/继续倒计时
         */
        fun togglePause() {
            val currentState = _state.value ?: return

            if (currentState.isRunning) {
                pauseFloating()
            } else {
                resumeFloating()
            }
        }

        /**
         * 暂停悬浮倒计时
         */
        fun pauseFloating() {
            val currentState = _state.value ?: return
            if (currentState.isRunning) {
                countdownJob?.cancel()
                _state.value = currentState.copy(isRunning = false)
            }
        }

        /**
         * 恢复悬浮倒计时
         */
        fun resumeFloating() {
            val currentState = _state.value ?: return
            if (!currentState.isRunning && currentState.remainingSeconds > 0) {
                _state.value = currentState.copy(isRunning = true)
                startCountdown()
            }
        }

        /**
         * 调整倒计时时间
         *
         * @param adjustment 调整的秒数（正数增加，负数减少）
         */
        fun adjustTime(adjustment: Int) {
            val currentState = _state.value ?: return
            val newRemaining = (currentState.remainingSeconds + adjustment).coerceAtLeast(0)

            _state.value = currentState.copy(
                remainingSeconds = newRemaining,
                totalSeconds = maxOf(currentState.totalSeconds, newRemaining),
            )
        }

        /**
         * 获取当前是否有活动的倒计时
         */
        val isActive: Boolean
            get() = _state.value != null

        private fun startCountdown() {
            countdownJob = serviceScope.launch {
                while (_state.value?.let { it.remainingSeconds > 0 && it.isRunning } == true) {
                    delay(1000)
                    _state.value = _state.value?.let { current ->
                        val newRemaining = current.remainingSeconds - 1
                        if (newRemaining <= 0) {
                            // 倒计时完成
                            current.onComplete?.invoke()
                            null // 清除状态
                        } else {
                            current.copy(remainingSeconds = newRemaining)
                        }
                    }
                }
            }
        }

        /**
         * 清理资源
         */
        fun cleanup() {
            countdownJob?.cancel()
            serviceScope.cancel()
            _state.value = null
        }
    }

/**
 * 悬浮倒计时状态
 *
 * 简单的数据类，包含倒计时的基本信息
 */
data class FloatingCountdownState(
    val title: String,
    val totalSeconds: Int,
    val remainingSeconds: Int,
    val isRunning: Boolean,
    val onComplete: (() -> Unit)? = null,
) {
    /**
     * 倒计时是否已完成
     */
    val isFinished: Boolean
        get() = remainingSeconds <= 0 && totalSeconds > 0

    /**
     * 倒计时进度 (0.0 - 1.0)
     */
    val progress: Float
        get() = if (totalSeconds > 0) {
            (totalSeconds - remainingSeconds).toFloat() / totalSeconds.toFloat()
        } else {
            0f
        }

    /**
     * 格式化显示时间 (MM:SS)
     */
    val formattedTime: String
        get() {
            val minutes = remainingSeconds / 60
            val seconds = remainingSeconds % 60
            return String.format("%02d:%02d", minutes, seconds)
        }

    /**
     * 简短格式化时间 (M:SS)
     */
    val formattedTimeShort: String
        get() {
            val minutes = remainingSeconds / 60
            val seconds = remainingSeconds % 60
            return if (minutes > 0) {
                "$minutes:${String.format("%02d", seconds)}"
            } else {
                "0:${String.format("%02d", seconds)}"
            }
        }
}
