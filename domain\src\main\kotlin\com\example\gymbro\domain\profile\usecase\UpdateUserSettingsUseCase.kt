package com.example.gymbro.domain.profile.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.UserSessionManager
import com.example.gymbro.domain.profile.model.settings.NotificationPreferences
import com.example.gymbro.domain.profile.model.settings.UserPreferences
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import com.example.gymbro.domain.profile.repository.user.UserAggregateRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 更新用户设置用例
 *
 * 负责更新当前用户的设置信息
 */
@Singleton
class UpdateUserSettingsUseCase
    @Inject
    constructor(
        private val userAggregateRepository: UserAggregateRepository,
        private val userSessionManager: UserSessionManager,
        private val logger: Logger,
    ) {
        /**
         * 更新用户设置
         * @param userSettings 要更新的用户设置
         * @return ModernResult<Unit> 更新结果
         */
        suspend operator fun invoke(userSettings: UserSettings): ModernResult<Unit> {
            logger.d("UpdateUserSettingsUseCase: 开始更新用户设置")

            val userId = userSessionManager.getCurrentUserId()
            if (userId == null) {
                logger.w("UpdateUserSettingsUseCase: 用户未登录，无法更新设置")
                return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "updateUserSettings",
                        message = UiText.DynamicString("用户未登录，无法更新设置"),
                        metadataMap = mapOf("errorType" to "UNAUTHORIZED"),
                    ),
                )
            }

            if (userSettings.userId != userId) {
                logger.w("UpdateUserSettingsUseCase: 用户ID不匹配，无法更新设置")
                return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "updateUserSettings",
                        message = UiText.DynamicString("用户ID不匹配，无法更新设置"),
                        metadataMap =
                            mapOf(
                                "currentUserId" to userId,
                                "settingsUserId" to userSettings.userId,
                            ),
                    ),
                )
            }

            return try {
                // 将UserSettings转换为UserPreferences
                val userPreferences = userSettings.toUserPreferences()

                // 使用UserAggregateRepository更新用户偏好设置
                val updateResult = userAggregateRepository.updateUserPreferences(userId, userPreferences)

                when (updateResult) {
                    is ModernResult.Success -> {
                        logger.d("UpdateUserSettingsUseCase: 成功更新用户设置")
                        ModernResult.Success(Unit)
                    }

                    is ModernResult.Error -> {
                        logger.e("UpdateUserSettingsUseCase: 更新用户设置失败", updateResult.error.cause)
                        updateResult
                    }

                    is ModernResult.Loading -> {
                        // 不应该发生，但为了完整性
                        ModernResult.Error(
                            BusinessErrors.BusinessError.rule(
                                operationName = "updateUserSettings",
                                message = UiText.DynamicString("更新用户设置操作未完成"),
                            ),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("UpdateUserSettingsUseCase: 更新用户设置时发生异常", e)
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "updateUserSettings",
                        message = UiText.DynamicString("更新用户设置失败"),
                        metadataMap = mapOf("userId" to userId, "error" to e.message.orEmpty()),
                    ),
                )
            }
        }
    }

/**
 * 将UserSettings转换为UserPreferences
 */
private fun UserSettings.toUserPreferences(): UserPreferences {
    return UserPreferences(
        userId = this.userId,
        favoriteWorkouts = emptyList(), // UserSettings中没有此字段，使用默认值
        darkModeEnabled = this.darkModeEnabled,
        restTimeBetweenSets = 90, // UserSettings中没有此字段，使用默认值
        showCalories = true, // UserSettings中没有此字段，使用默认值
        weightUnit = UserPreferences.WeightUnit.KG, // 根据measurementUnit转换
        heightUnit = UserPreferences.HeightUnit.CM, // 根据measurementUnit转换
        distanceUnit = UserPreferences.DistanceUnit.KM, // 根据measurementUnit转换
        notificationPreferences = NotificationPreferences(
            workoutRemindersEnabled = this.notifications.workout,
            progressUpdatesEnabled = this.notifications.systemUpdates,
            achievementsEnabled = this.notifications.featureAnnouncements,
            friendActivityEnabled = this.notifications.friendRequest,
            tipsAndAdviceEnabled = this.notifications.featureAnnouncements,
            quietHoursEnabled = false, // UserSettings中没有此字段，使用默认值
            quietHoursStart = 22,
            quietHoursEnd = 8,
        ),
        dashboardLayout = UserPreferences.DashboardLayout.DEFAULT,
        lastUpdated = System.currentTimeMillis(),
    )
}
