package com.example.gymbro.data.repository.auth

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.error.types.auth.AuthErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.dao.auth.TokenDao
import com.example.gymbro.data.local.dao.user.UserDao
import com.example.gymbro.data.remote.firebase.auth.FirebaseAuthService
import com.example.gymbro.data.remote.firebase.auth.PhoneVerificationService
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.model.auth.Credentials
import com.google.firebase.auth.FirebaseUser
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.*
import timber.log.Timber

/**
 * 认证状态同步功能仓库
 *
 * 负责处理认证状态观察、验证、凭证链接等状态同步相关功能
 *
 * Phase 5 错误处理统一：移除errorHandlingUtils依赖，直接处理错误
 */
@Singleton
class AuthStateRepository
    @Inject
    constructor(
        private val tokenDao: TokenDao,
        private val userDao: UserDao,
        private val firebaseAuthService: FirebaseAuthService,
        private val phoneVerificationService: PhoneVerificationService,
        private val userManagementRepository: UserManagementRepository,
        private val logger: Logger,
    ) {
        /**
         * 观察当前认证状态
         * 修复Flow异常透明性违反问题
         */
        fun observeAuthState(): Flow<ModernResult<AuthUser?>> =
            tokenDao
                .getToken()
                .map { tokenEntity ->
                    when {
                        tokenEntity == null -> {
                            // 无token，用户未登录
                            ModernResult.Success(null)
                        }

                        else -> {
                            // 有token，获取当前用户信息
                            try {
                                val userEntity = userDao.getCurrentUser().first()
                                if (userEntity != null) {
                                    // 直接从userEntity创建AuthUser，不使用mapper
                                    val authUser =
                                        AuthUser(
                                            uid = userEntity.userId ?: "",
                                            displayName = userEntity.displayName ?: userEntity.username,
                                            email = userEntity.email,
                                            phoneNumber = userEntity.phoneNumber,
                                            isAnonymous = userEntity.isAnonymous,
                                        )
                                    ModernResult.Success(authUser)
                                } else {
                                    // 有token但无用户信息，可能是token过期或用户数据不一致
                                    ModernResult.Success(null)
                                }
                            } catch (e: Exception) {
                                Timber.e(e, "获取用户信息失败")
                                ModernResult.Error(
                                    AuthErrors.AuthError.unknown(
                                        operationName = "observeAuthState.getUserInfo",
                                        message = UiText.DynamicString("获取用户信息失败"),
                                        metadataMap =
                                            mapOf(
                                                StandardKeys.EXCEPTION.key to e.toString(),
                                                StandardKeys.OPERATION_TYPE.key to "auth_state",
                                                "cause" to e.message.orEmpty(),
                                            ),
                                    ),
                                )
                            }
                        }
                    }
                }.catch { e ->
                    // ✅ 使用Flow.catch{}操作符处理异常，符合Flow异常透明性原则
                    Timber.e(e, "观察认证状态失败")
                    emit(
                        ModernResult.Error(
                            AuthErrors.AuthError.unknown(
                                operationName = "observeAuthState",
                                message = UiText.DynamicString("观察认证状态失败"),
                                metadataMap =
                                    mapOf(
                                        StandardKeys.EXCEPTION.key to e.toString(),
                                        StandardKeys.OPERATION_TYPE.key to "auth_state",
                                        "cause" to e.message.orEmpty(),
                                    ),
                            ),
                        ),
                    )
                }.onStart {
                    Timber.d("开始观察认证状态")
                }

        /**
         * 检查用户是否已登录
         */
        fun isLoggedIn(): Flow<ModernResult<Boolean>> = tokenDao.getToken().map {
            ModernResult.Success(
                it != null,
            )
        }

        /**
         * 链接凭证到当前用户
         */
        suspend fun linkCredential(credential: Credentials): ModernResult<Unit> =
            try {
                when (credential) {
                    is Credentials.EmailPassword -> {
                        linkEmailCredential(credential.email, credential.password)
                    }

                    is Credentials.Phone -> {
                        linkPhoneCredential(
                            credential.phoneNumber,
                            credential.verificationId ?: "",
                            credential.verificationCode,
                        )
                    }

                    else ->
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "linkCredential",
                                errorType = GlobalErrorType.Auth.General,
                                uiMessage = UiText.DynamicString(
                                    "不支持的凭证类型: ${credential::class.java.simpleName}",
                                ),
                                cause = IllegalArgumentException("不支持的凭证类型"),
                            ),
                        )
                }
            } catch (e: Exception) {
                logger.e(e, "链接凭证失败")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "linkCredential",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("链接凭证失败"),
                        cause = e,
                    ),
                )
            }

        /**
         * 链接邮箱凭证
         */
        suspend fun linkEmailCredential(
            email: String,
            password: String,
        ): ModernResult<Unit> =
            try {
                val uidResult = userManagementRepository.getCurrentUserId().first()
                val uid =
                    (uidResult as? ModernResult.Success)?.data
                        ?: throw (uidResult as? ModernResult.Error)?.error ?: ModernDataError(
                            operationName = "linkEmailCredential.noUser",
                            errorType = GlobalErrorType.Auth.General,
                            uiMessage = UiText.DynamicString("获取当前用户ID失败以链接邮箱"),
                        )

                Timber.d("链接邮箱: %s 到用户: %s", email, uid)

                // 使用Firebase Auth链接邮箱凭证
                val linkResult = firebaseAuthService.linkEmailCredential(uid, email, password)

                when (linkResult) {
                    is ModernResult.Success -> {
                        Timber.d("邮箱链接成功")
                        ModernResult.Success(Unit)
                    }

                    is ModernResult.Error -> {
                        ModernResult.Error(linkResult.error)
                    }

                    is ModernResult.Loading ->
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "linkEmailCredential",
                                errorType = GlobalErrorType.Auth.General,
                                uiMessage = UiText.DynamicString("链接邮箱失败：意外的加载状态"),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                }
            } catch (e: Exception) {
                logger.e(e, "链接邮箱失败")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "linkEmailCredential",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("链接邮箱失败"),
                        cause = e,
                    ),
                )
            }

        /**
         * 链接电话凭证
         */
        suspend fun linkPhoneCredential(
            phoneNumber: String,
            verificationId: String,
            verificationCode: String,
        ): ModernResult<Unit> =
            try {
                val uidResult = userManagementRepository.getCurrentUserId().first()
                val uid =
                    (uidResult as? ModernResult.Success)?.data
                        ?: throw (uidResult as? ModernResult.Error)?.error ?: ModernDataError(
                            operationName = "linkPhoneCredential.noUser",
                            errorType = GlobalErrorType.Auth.General,
                            uiMessage = UiText.DynamicString("获取当前用户ID失败以链接手机号"),
                        )

                Timber.d("链接手机号: %s 到用户: %s", phoneNumber, uid)

                // 使用Firebase Auth链接手机凭证
                val linkResult = firebaseAuthService.linkPhoneCredential(
                    uid,
                    verificationId,
                    verificationCode,
                )

                when (linkResult) {
                    is ModernResult.Success -> {
                        Timber.d("手机号链接成功")
                        ModernResult.Success(Unit)
                    }

                    is ModernResult.Error -> {
                        ModernResult.Error(linkResult.error)
                    }

                    is ModernResult.Loading ->
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "linkPhoneCredential",
                                errorType = GlobalErrorType.Auth.General,
                                uiMessage = UiText.DynamicString("链接手机号失败：意外的加载状态"),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                }
            } catch (e: Exception) {
                logger.e(e, "链接手机号失败")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "linkPhoneCredential",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("链接手机号失败"),
                        cause = e,
                    ),
                )
            }

        /**
         * 验证手机验证码
         */
        suspend fun verifyPhoneCode(
            verificationId: String,
            code: String,
        ): ModernResult<AuthUser> {
            Timber.d("验证手机验证码")

            return try {
                // 验证输入参数
                if (verificationId.isBlank() || code.isBlank()) {
                    return ModernResult.Error(
                        DataErrors.Validation.required(
                            field = "verificationId",
                            operationName = "verifyPhoneCode.validation",
                            message = UiText.DynamicString("验证ID和验证码不能为空"),
                            metadataMap =
                                mapOf(
                                    StandardKeys.OPERATION_TYPE.key to "phone_verification",
                                ),
                        ),
                    )
                }

                // 使用Firebase Phone Auth验证
                val verifyResult = phoneVerificationService.verifyPhoneCode(verificationId, code)

                when (verifyResult) {
                    is ModernResult.Success -> {
                        val credential = verifyResult.data

                        // 使用凭据登录Firebase获取用户ID
                        val signInResult = firebaseAuthService.loginWithPhone(credential)
                        when (signInResult) {
                            is ModernResult.Success -> {
                                signInResult.data

                                // 获取完整的Firebase用户信息
                                val firebaseUserResult = firebaseAuthService.getCurrentUser()
                                when (firebaseUserResult) {
                                    is ModernResult.Success -> {
                                        val firebaseUser = firebaseUserResult.data
                                        if (firebaseUser != null) {
                                            val authUser = createAuthUserFromFirebaseUser(firebaseUser)
                                            Timber.d("手机验证成功: userId=%s", authUser.uid)
                                            ModernResult.Success(authUser)
                                        } else {
                                            ModernResult.Error(
                                                AuthErrors.AuthError.unknown(
                                                    operationName = "verifyPhoneCode.noFirebaseUser",
                                                    message = UiText.DynamicString("登录成功但无法获取用户信息"),
                                                ),
                                            )
                                        }
                                    }

                                    is ModernResult.Error -> ModernResult.Error(firebaseUserResult.error)
                                    is ModernResult.Loading -> ModernResult.Loading
                                }
                            }

                            is ModernResult.Error -> {
                                Timber.e("Firebase登录失败")
                                ModernResult.Error(signInResult.error)
                            }

                            is ModernResult.Loading -> ModernResult.Loading
                        }
                    }

                    is ModernResult.Error -> {
                        Timber.e("手机验证失败")
                        ModernResult.Error(verifyResult.error)
                    }

                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                Timber.e(e, "验证手机验证码失败")
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "verifyPhoneCode.exception",
                        message = UiText.DynamicString("验证失败: ${e.message}"),
                        cause = e,
                        metadataMap =
                            mapOf(
                                StandardKeys.EXCEPTION.key to e,
                                StandardKeys.OPERATION_TYPE.key to "phone_verification",
                            ),
                    ),
                )
            }
        }

        /**
         * 将匿名用户升级为邮箱用户
         */
        suspend fun upgradeAnonymousWithEmail(
            email: String,
            password: String,
        ): ModernResult<Unit> =
            try {
                Timber.d("开始将匿名用户升级为邮箱用户: %s", email)

                // 使用Firebase Auth升级匿名用户
                val upgradeResult = firebaseAuthService.upgradeAnonymousWithEmail(email, password)

                when (upgradeResult) {
                    is ModernResult.Success -> {
                        Timber.d("匿名用户升级成功")
                        ModernResult.Success(Unit)
                    }

                    is ModernResult.Error -> {
                        ModernResult.Error(upgradeResult.error)
                    }

                    is ModernResult.Loading ->
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "upgradeAnonymousWithEmail",
                                errorType = GlobalErrorType.Auth.General,
                                uiMessage = UiText.DynamicString("匿名用户升级失败：意外的加载状态"),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                }
            } catch (e: Exception) {
                logger.e(e, "匿名用户升级失败")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "upgradeAnonymousWithEmail",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("匿名用户升级失败"),
                        cause = e,
                    ),
                )
            }

        /**
         * 将匿名用户升级为Google用户
         */
        suspend fun upgradeAnonymousWithGoogle(idToken: String): ModernResult<Unit> =
            try {
                Timber.d("开始将匿名用户升级为Google用户")

                // 使用Firebase Auth升级匿名用户
                val upgradeResult = firebaseAuthService.upgradeAnonymousWithGoogle(idToken)

                when (upgradeResult) {
                    is ModernResult.Success -> {
                        Timber.d("匿名用户升级成功")
                        ModernResult.Success(Unit)
                    }

                    is ModernResult.Error -> {
                        ModernResult.Error(upgradeResult.error)
                    }

                    is ModernResult.Loading ->
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "upgradeAnonymousWithGoogle",
                                errorType = GlobalErrorType.Auth.General,
                                uiMessage = UiText.DynamicString("匿名用户升级失败：意外的加载状态"),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                }
            } catch (e: Exception) {
                logger.e(e, "匿名用户升级失败")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "upgradeAnonymousWithGoogle",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("匿名用户升级失败"),
                        cause = e,
                    ),
                )
            }

        /**
         * 获取当前用户（同步版本）
         */
        suspend fun getCurrentUser(): ModernResult<AuthUser?> =
            try {
                val firebaseUserResult = firebaseAuthService.getCurrentUser()

                when (firebaseUserResult) {
                    is ModernResult.Success -> {
                        val authUser = convertFirebaseUserToAuthUser(firebaseUserResult.data)
                        ModernResult.Success(authUser)
                    }

                    is ModernResult.Error -> {
                        ModernResult.Error(firebaseUserResult.error)
                    }

                    is ModernResult.Loading ->
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "getCurrentUser",
                                errorType = GlobalErrorType.Auth.General,
                                uiMessage = UiText.DynamicString("获取当前用户失败：意外的加载状态"),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                }
            } catch (e: Exception) {
                logger.e(e, "获取当前用户失败")
                ModernResult.Error(
                    ModernDataError(
                        operationName = "getCurrentUser",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("获取当前用户失败"),
                        cause = e,
                    ),
                )
            }

        private fun createAuthUserFromFirebaseUser(firebaseUser: FirebaseUser): AuthUser =
            AuthUser(
                uid = firebaseUser.uid,
                email = firebaseUser.email,
                displayName = firebaseUser.displayName,
                phoneNumber = firebaseUser.phoneNumber,
                isEmailVerified = firebaseUser.isEmailVerified,
                photoUrl = firebaseUser.photoUrl?.toString(),
                isAnonymous = firebaseUser.isAnonymous,
            )

        private fun convertFirebaseUserToAuthUser(firebaseUser: FirebaseUser?): AuthUser? = firebaseUser?.let {
            createAuthUserFromFirebaseUser(it)
        }
    }
