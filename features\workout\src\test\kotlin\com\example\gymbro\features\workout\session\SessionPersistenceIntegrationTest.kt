package com.example.gymbro.features.workout.session

import com.example.gymbro.core.testing.rule.MainDispatcherRule
import com.example.gymbro.domain.workout.model.Exercise
import com.example.gymbro.domain.workout.model.session.SessionExercise
import com.example.gymbro.domain.workout.model.session.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.usecase.session.SaveWorkoutSessionUseCase
import com.example.gymbro.domain.workout.usecase.stats.CreateDailyStatsUseCase
import com.example.gymbro.features.workout.session.internal.reducer.SessionReducer
import io.mockk.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

/**
 * Session模块数据持久化集成测试
 *
 * 测试目标：
 * 1. 验证session数据能正确保存到数据库
 * 2. 验证与stats模块的数据连接
 * 3. 验证完整的数据流：UI → ViewModel → UseCase → Repository → Database
 * 4. 验证数据一致性和完整性
 */
@ExtendWith(MainDispatcherRule::class)
class SessionPersistenceIntegrationTest {

    // Mock dependencies
    private lateinit var mockSessionRepository: SessionRepository
    private lateinit var mockSaveWorkoutSessionUseCase: SaveWorkoutSessionUseCase
    private lateinit var mockCreateDailyStatsUseCase: CreateDailyStatsUseCase

    // Test subjects
    private lateinit var sessionReducer: SessionReducer
    private lateinit var initialState: SessionContract.State

    @BeforeEach
    fun setup() {
        MockKAnnotations.init(this)

        // Setup mocks
        mockSessionRepository = mockk()
        mockSaveWorkoutSessionUseCase = mockk()
        mockCreateDailyStatsUseCase = mockk()

        // Create reducer
        sessionReducer = SessionReducer()

        // Create initial state with realistic data
        initialState = SessionContract.State(
            sessionId = "test_session_integration_001",
            exercises = persistentListOf(),
            totalExercises = 0,
            currentExerciseIndex = 0,
            hasUnsavedChanges = false,
            isLoading = false,
            workoutSourceState = SessionContract.WorkoutSourceState(),
            scrollState = SessionContract.ScrollState(),
            countdownInfo = SessionContract.CountdownInfo(),
            showCompleteWorkoutDialog = false,
            showTemplateSwitcher = false,
            availableTemplateVersions = persistentListOf(),
        )
    }

    @Test
    fun `complete workout session persistence flow should save data correctly`() = runTest {
        // Given - 创建一个完整的训练会话
        val testExercises = createTestExercises()
        val sessionWithExercises = addExercisesToSession(initialState, testExercises)
        val completedSession = completeWorkoutSession(sessionWithExercises)

        // Mock successful save operations
        coEvery { mockSaveWorkoutSessionUseCase(any()) } returns com.example.gymbro.core.error.types.ModernResult.Success(
            Unit,
        )
        coEvery { mockCreateDailyStatsUseCase(any()) } returns com.example.gymbro.core.error.types.ModernResult.Success(
            Unit,
        )

        // When - 完成并保存训练会话
        val saveIntent = SessionContract.Intent.CompleteSession
        val result = sessionReducer.reduce(saveIntent, completedSession)

        // Then - 验证状态更新和副作用
        assertNotNull(result.effect)
        assertTrue(result.effect is SessionContract.Effect.SaveSession)

        val saveEffect = result.effect as SessionContract.Effect.SaveSession
        verifySessionData(saveEffect.session)
    }

    @Test
    fun `session data should persist exercise sets correctly`() = runTest {
        // Given - 带有训练组数据的会话
        val exerciseWithSets = createExerciseWithSets()
        val sessionState = initialState.copy(
            exercises = persistentListOf(exerciseWithSets),
            totalExercises = 1,
            hasUnsavedChanges = true,
        )

        // When - 保存带有sets数据的exercise
        val updateIntent = SessionContract.Intent.UpdateExerciseWithSets(
            exerciseId = exerciseWithSets.exerciseId,
            sessionUpdateData = createSessionUpdateData(),
        )
        val result = sessionReducer.reduce(updateIntent, sessionState)

        // Then - 验证sets数据正确保存
        val updatedExercise = result.state.exercises.find { it.exerciseId == exerciseWithSets.exerciseId }
        assertNotNull(updatedExercise)
        assertTrue(updatedExercise.sets.isNotEmpty())

        // 验证sets数据完整性
        updatedExercise.sets.forEach { set ->
            assertTrue(set.weight > 0f)
            assertTrue(set.reps > 0)
            assertNotNull(set.id)
        }
    }

    @Test
    fun `session completion should trigger stats calculation`() = runTest {
        // Given - 完成的训练会话
        val completedSession = createCompletedSession()

        // Mock stats use case
        coEvery { mockCreateDailyStatsUseCase(any()) } returns com.example.gymbro.core.error.types.ModernResult.Success(
            Unit,
        )

        // When - 触发stats计算
        val completeIntent = SessionContract.Intent.CompleteSession
        val result = sessionReducer.reduce(completeIntent, completedSession)

        // Then - 验证触发了stats计算的副作用
        assertTrue(result.effect is SessionContract.Effect.CalculateDailyStats)

        val statsEffect = result.effect as SessionContract.Effect.CalculateDailyStats
        assertEquals(completedSession.sessionId, statsEffect.sessionId)
    }

    @Test
    fun `batch exercise addition should maintain data consistency`() = runTest {
        // Given - 批量添加的动作列表
        val exercisesToAdd = listOf(
            createTestExercise("1", "俯卧撑", "CHEST"),
            createTestExercise("2", "深蹲", "QUADRICEPS"),
            createTestExercise("3", "硬拉", "HAMSTRINGS"),
        )

        // When - 批量添加动作
        val addIntent = SessionContract.Intent.AddExercises(exercisesToAdd)
        val result = sessionReducer.reduce(addIntent, initialState)

        // Then - 验证数据一致性
        assertEquals(3, result.state.exercises.size)
        assertEquals(3, result.state.totalExercises)
        assertTrue(result.state.hasUnsavedChanges)

        // 验证每个exercise的数据完整性
        result.state.exercises.forEachIndexed { index, exercise ->
            assertEquals(exercisesToAdd[index].id, exercise.exerciseId)
            assertEquals(exercisesToAdd[index].name, exercise.sessionExercise.name)
            assertEquals(index, exercise.sessionExercise.order)
            assertEquals(initialState.sessionId, exercise.sessionExercise.sessionId)
            assertTrue(exercise.sessionExercise.sets.isEmpty()) // 初始sets为空
        }
    }

    @Test
    fun `session pause and resume should preserve workout state`() = runTest {
        // Given - 进行中的训练会话
        val activeSession = initialState.copy(
            exercises = createTestExercises().map { createSessionExerciseUiModel(it) }.toImmutableList(),
            currentExerciseIndex = 1,
            elapsedTimeMs = 180000L, // 3分钟
            startedAt = System.currentTimeMillis() - 180000L,
            isPaused = false,
        )

        // When - 暂停训练
        val pauseResult = sessionReducer.reduce(SessionContract.Intent.PauseSession, activeSession)

        // Then - 验证暂停状态保存
        assertTrue(pauseResult.state.isPaused)
        assertEquals(activeSession.currentExerciseIndex, pauseResult.state.currentExerciseIndex)
        assertEquals(activeSession.exercises.size, pauseResult.state.exercises.size)

        // When - 恢复训练
        val resumeResult = sessionReducer.reduce(SessionContract.Intent.ResumeSession, pauseResult.state)

        // Then - 验证恢复状态
        assertFalse(resumeResult.state.isPaused)
        assertEquals(activeSession.currentExerciseIndex, resumeResult.state.currentExerciseIndex)
        assertEquals(activeSession.exercises.size, resumeResult.state.exercises.size)
    }

    @Test
    fun `session data validation should prevent invalid states`() = runTest {
        // Given - 尝试添加无效数据
        val emptyExercises = emptyList<Exercise>()
        val invalidSessionId = ""

        // When - 尝试添加空的exercise列表
        val emptyAddResult = sessionReducer.reduce(
            SessionContract.Intent.AddExercises(emptyExercises),
            initialState,
        )

        // Then - 应该保持原状态不变
        assertEquals(initialState.exercises.size, emptyAddResult.state.exercises.size)
        assertEquals(initialState.totalExercises, emptyAddResult.state.totalExercises)

        // When - 尝试在空会话ID上操作
        val invalidState = initialState.copy(sessionId = invalidSessionId)
        val completeResult = sessionReducer.reduce(
            SessionContract.Intent.CompleteSession,
            invalidState,
        )

        // Then - 应该处理错误情况
        // 这里应该根据实际的错误处理逻辑进行验证
        assertNotNull(completeResult.state)
    }

    // === 辅助方法 ===

    private fun createTestExercises(): List<Exercise> {
        return listOf(
            createTestExercise("ex1", "俯卧撑", "CHEST"),
            createTestExercise("ex2", "深蹲", "QUADRICEPS"),
            createTestExercise("ex3", "硬拉", "HAMSTRINGS"),
        )
    }

    private fun createTestExercise(id: String, name: String, muscleGroup: String): Exercise {
        return Exercise(
            id = id,
            name = name,
            categoryId = "strength",
            description = "测试动作：$name",
            difficulty = 2,
            muscleGroups = listOf(muscleGroup),
            equipmentRequired = listOf("barbell"),
            thumbnailUrl = "https://example.com/$id.jpg",
            videoUrl = "https://example.com/$id.mp4",
            isCustom = false,
        )
    }

    private fun createSessionExerciseUiModel(exercise: Exercise): SessionContract.SessionExerciseUiModel {
        val sessionExercise = SessionExercise(
            sessionId = initialState.sessionId,
            exerciseId = exercise.id,
            order = 0,
            sets = emptyList(),
            name = exercise.name,
            targetSets = 3,
            completedSets = 0,
            status = "NOT_STARTED",
            isCompleted = false,
        )

        return SessionContract.SessionExerciseUiModel(
            sessionExercise = sessionExercise,
            exercise = exercise,
        )
    }

    private fun addExercisesToSession(
        state: SessionContract.State,
        exercises: List<Exercise>,
    ): SessionContract.State {
        val addIntent = SessionContract.Intent.AddExercises(exercises)
        return sessionReducer.reduce(addIntent, state).state
    }

    private fun completeWorkoutSession(state: SessionContract.State): SessionContract.State {
        return state.copy(
            completedExercises = state.totalExercises,
            completedSetsCount = state.exercises.sumOf { it.sessionExercise.targetSets },
            elapsedTimeMs = 3600000L, // 1小时
            hasUnsavedChanges = true,
        )
    }

    private fun createExerciseWithSets(): SessionContract.SessionExerciseUiModel {
        val exercise = createTestExercise("ex_with_sets", "卧推", "CHEST")
        return createSessionExerciseUiModel(exercise)
    }

    private fun createSessionUpdateData(): com.example.gymbro.features.workout.json.processor.SessionExerciseUpdateData {
        // 这里需要根据实际的SessionExerciseUpdateData结构创建测试数据
        // 暂时返回mock数据，实际实现需要根据真实结构调整
        return mockk()
    }

    private fun createCompletedSession(): SessionContract.State {
        val exercises = createTestExercises().map { createSessionExerciseUiModel(it) }
        return initialState.copy(
            exercises = exercises.toImmutableList(),
            totalExercises = exercises.size,
            completedExercises = exercises.size,
            completedSetsCount = exercises.size * 3, // 假设每个动作3组
            elapsedTimeMs = 3600000L,
            hasUnsavedChanges = true,
        )
    }

    private fun verifySessionData(session: WorkoutSession) {
        assertNotNull(session.id)
        assertNotNull(session.name)
        assertTrue(session.exercises.isNotEmpty())
        assertTrue(session.startTime > 0)
        assertTrue(session.endTime >= session.startTime)

        // 验证exercises数据完整性
        session.exercises.forEach { exercise ->
            assertNotNull(exercise.id)
            assertNotNull(exercise.exerciseId)
            assertNotNull(exercise.sessionId)
            assertTrue(exercise.order >= 0)
        }
    }
}
