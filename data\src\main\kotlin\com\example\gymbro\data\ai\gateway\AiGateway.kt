package com.example.gymbro.data.ai.gateway

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.common.CommonFeatureErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.config.AiProviderManager
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.first
import kotlinx.datetime.Clock
import timber.log.Timber

/**
 * AI网关
 *
 * 三层闸门系统的第二层，负责：
 * - AI服务提供商路由选择
 * - 服务健康检查
 * - 负载均衡策略
 * - 降级处理
 */
@Singleton
class AiGateway
    @Inject
    constructor(
        private val aiProviderManager: AiProviderManager,
    ) {
        // 服务健康状态缓存
        private val providerHealthStatus = mutableMapOf<String, ProviderHealth>()

        // 降级策略配置
        private val fallbackConfig =
            FallbackConfig(
                enableFallback = true,
                maxRetries = 2,
                fallbackOrder = listOf("openai", "grok", "proxy"),
            )

        /**
         * 路由AI请求到最佳可用服务
         */
        suspend fun routeRequest(
            userId: String,
            requestType: AiRequestType,
            priority: RequestPriority = RequestPriority.NORMAL,
        ): ModernResult<GatewayDecision> {
            Timber.d("AiGateway: 开始路由请求 userId=$userId, type=$requestType, priority=$priority")

            return try {
                // 1. 获取当前配置的提供商
                val currentProvider = aiProviderManager.currentProvider.first()
                Timber.d("AiGateway: 当前提供商 ${currentProvider.name}")

                // 2. 检查提供商健康状态
                val healthCheck = checkProviderHealth(currentProvider.name)

                if (healthCheck.isHealthy) {
                    Timber.d("AiGateway: 提供商${currentProvider.name}健康，直接路由")
                    return ModernResult.Success(
                        GatewayDecision(
                            selectedProvider = currentProvider.name,
                            routingReason = "primary_healthy",
                            fallbackUsed = false,
                            estimatedCost = calculateCost(requestType, currentProvider.name),
                            metadata =
                                mapOf(
                                    "provider_health" to healthCheck.healthScore,
                                    "routing_time" to Clock.System.now().toEpochMilliseconds(),
                                ),
                        ),
                    )
                }

                // 3. 如果主要提供商不健康，尝试降级
                if (fallbackConfig.enableFallback) {
                    Timber.w("AiGateway: 主要提供商${currentProvider.name}不健康，尝试降级")
                    val fallbackResult = findHealthyFallback(currentProvider.name, requestType)

                    return when (fallbackResult) {
                        is ModernResult.Success -> {
                            Timber.i("AiGateway: 降级到提供商${fallbackResult.data.selectedProvider}")
                            fallbackResult
                        }

                        is ModernResult.Error -> {
                            Timber.e("AiGateway: 所有提供商都不可用")
                            fallbackResult
                        }

                        is ModernResult.Loading -> {
                            ModernResult.Error(
                                CommonFeatureErrors.CoachError.processingFailed(
                                    operationName = "AiGateway.unexpectedLoading",
                                    message = UiText.DynamicString("服务路由异常"),
                                    processType = "gateway_routing",
                                    reason = "unexpected_loading_state",
                                ),
                            )
                        }
                    }
                } else {
                    Timber.e("AiGateway: 降级被禁用，无法提供服务")
                    return ModernResult.Error(
                        CommonFeatureErrors.CoachError.processingFailed(
                            operationName = "AiGateway.noFallback",
                            message = UiText.DynamicString("AI服务暂时不可用"),
                            processType = "gateway_routing",
                            reason = "fallback_disabled",
                            metadataMap =
                                mapOf(
                                    "primary_provider" to currentProvider.name,
                                    "health_score" to healthCheck.healthScore,
                                ),
                        ),
                    )
                }
            } catch (e: Exception) {
                Timber.e(e, "AiGateway: 路由过程异常")
                ModernResult.Error(
                    CommonFeatureErrors.CoachError.processingFailed(
                        operationName = "AiGateway.routingException",
                        message = UiText.DynamicString("服务路由失败"),
                        processType = "gateway_routing",
                        reason = "exception",
                        cause = e,
                        metadataMap =
                            mapOf(
                                "exception_type" to e.javaClass.simpleName,
                            ),
                    ),
                )
            }
        }

        /**
         * 检查提供商健康状态
         */
        private suspend fun checkProviderHealth(providerName: String): ProviderHealth {
            val cachedHealth = providerHealthStatus[providerName]
            val now = Clock.System.now().toEpochMilliseconds()

            // 如果缓存有效（5分钟内），直接返回
            if (cachedHealth != null && (now - cachedHealth.lastChecked) < 300_000) {
                return cachedHealth
            }

            // 执行健康检查
            val health = performHealthCheck(providerName)
            providerHealthStatus[providerName] = health

            return health
        }

        /**
         * 执行实际的健康检查
         */
        private suspend fun performHealthCheck(providerName: String): ProviderHealth =
            try {
                val config = aiProviderManager.getProviderConfig(providerName)
                if (config == null) {
                    ProviderHealth(
                        providerName = providerName,
                        isHealthy = false,
                        healthScore = 0,
                        lastChecked = Clock.System.now().toEpochMilliseconds(),
                        issues = listOf("provider_not_configured"),
                    )
                } else {
                    // 简化的健康检查：基于配置完整性
                    val healthScore = calculateHealthScore(config.baseUrl, config.headers)
                    ProviderHealth(
                        providerName = providerName,
                        isHealthy = healthScore >= 70,
                        healthScore = healthScore,
                        lastChecked = Clock.System.now().toEpochMilliseconds(),
                        issues = if (healthScore < 70) listOf("low_health_score") else emptyList(),
                    )
                }
            } catch (e: Exception) {
                Timber.w(e, "AiGateway: 健康检查异常 provider=$providerName")
                ProviderHealth(
                    providerName = providerName,
                    isHealthy = false,
                    healthScore = 0,
                    lastChecked = Clock.System.now().toEpochMilliseconds(),
                    issues = listOf("health_check_failed", e.message ?: "unknown_error"),
                )
            }

        /**
         * 计算健康分数
         */
        private fun calculateHealthScore(
            baseUrl: String,
            headers: Map<String, String>,
        ): Int {
            var score = 0

            // URL配置检查 (40分)
            if (baseUrl.isNotBlank() && (baseUrl.startsWith("http://") || baseUrl.startsWith("https://"))) {
                score += 40
            }

            // 认证配置检查 (30分)
            if (headers.containsKey("Authorization") && headers["Authorization"]?.isNotBlank() == true) {
                score += 30
            }

            // 基础配置检查 (30分)
            if (headers.containsKey("Content-Type")) {
                score += 15
            }
            if (headers.containsKey("Accept")) {
                score += 15
            }

            return score
        }

        /**
         * 寻找健康的降级服务
         */
        private suspend fun findHealthyFallback(
            excludeProvider: String,
            requestType: AiRequestType,
        ): ModernResult<GatewayDecision> {
            for (fallbackProvider in fallbackConfig.fallbackOrder) {
                if (fallbackProvider == excludeProvider) continue

                val healthCheck = checkProviderHealth(fallbackProvider)
                if (healthCheck.isHealthy) {
                    // 尝试切换到降级提供商
                    val switchSuccess = aiProviderManager.switchToProvider(fallbackProvider)
                    if (switchSuccess) {
                        Timber.i("AiGateway: 成功切换到降级提供商 $fallbackProvider")
                        return ModernResult.Success(
                            GatewayDecision(
                                selectedProvider = fallbackProvider,
                                routingReason = "fallback_healthy",
                                fallbackUsed = true,
                                estimatedCost = calculateCost(requestType, fallbackProvider),
                                metadata =
                                    mapOf(
                                        "original_provider" to excludeProvider,
                                        "fallback_health" to healthCheck.healthScore,
                                        "routing_time" to Clock.System.now().toEpochMilliseconds(),
                                    ),
                            ),
                        )
                    } else {
                        Timber.w("AiGateway: 切换到降级提供商失败 $fallbackProvider")
                    }
                }
            }

            return ModernResult.Error(
                CommonFeatureErrors.CoachError.processingFailed(
                    operationName = "AiGateway.noHealthyProvider",
                    message = UiText.DynamicString("所有AI服务都不可用"),
                    processType = "fallback_search",
                    reason = "no_healthy_providers",
                    metadataMap =
                        mapOf(
                            "excluded_provider" to excludeProvider,
                            "fallback_order" to fallbackConfig.fallbackOrder,
                        ),
                ),
            )
        }

        /**
         * 计算预估成本
         */
        private fun calculateCost(
            requestType: AiRequestType,
            providerName: String,
        ): Double {
            // 基础成本计算（简化版）
            val baseCost =
                when (requestType) {
                    AiRequestType.CHAT -> 0.002
                    AiRequestType.COMPLETION -> 0.001
                    AiRequestType.EMBEDDING -> 0.0001
                }

            // 提供商成本倍数
            val providerMultiplier =
                when (providerName) {
                    "openai" -> 1.0
                    "grok" -> 0.8
                    "proxy" -> 0.5
                    else -> 1.0
                }

            return baseCost * providerMultiplier
        }
    }

/**
 * 网关决策结果
 */
data class GatewayDecision(
    val selectedProvider: String,
    val routingReason: String,
    val fallbackUsed: Boolean,
    val estimatedCost: Double,
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * AI请求类型
 */
enum class AiRequestType {
    CHAT, // 聊天对话
    COMPLETION, // 文本补全
    EMBEDDING, // 嵌入向量
}

/**
 * 请求优先级
 */
enum class RequestPriority {
    LOW,
    NORMAL,
    HIGH,
    CRITICAL,
}

/**
 * 提供商健康状态
 */
data class ProviderHealth(
    val providerName: String,
    val isHealthy: Boolean,
    val healthScore: Int, // 0-100
    val lastChecked: Long,
    val issues: List<String> = emptyList(),
)

/**
 * 降级配置
 */
private data class FallbackConfig(
    val enableFallback: Boolean,
    val maxRetries: Int,
    val fallbackOrder: List<String>,
)
