package com.example.gymbro.core.arch.mvi

import com.example.gymbro.core.ui.text.UiText
import timber.log.Timber

/**
 * MVI架构 - 状态归约器抽象 v6.0-ENHANCED
 *
 * 🔥 v6.0核心增强：
 * 1. ModernResult深度集成 - 自动处理异步操作结果
 * 2. UiText错误转换 - 标准化错误消息处理
 * 3. 便捷的Reducer构建器 - DSL风格的状态转换
 * 4. 性能优化 - 减少不必要的状态创建
 * 5. 类型安全 - 编译时检查状态转换正确性
 *
 * Reducer负责处理Intent并产生新的State和Effect
 * 这是MVI架构的核心，确保状态变更的纯函数性和可预测性
 *
 * @since v6.0 - ModernResult集成与性能优化
 */
interface Reducer<Intent : AppIntent, State : UiState, Effect : UiEffect> {

    /**
     * 根据当前状态和意图，产生新的状态和副作用
     *
     * @param intent 用户意图或系统事件
     * @param currentState 当前UI状态
     * @return 包含新状态和副作用的结果
     */
    fun reduce(intent: Intent, currentState: State): ReduceResult<State, Effect>
}

/**
 * Reducer处理结果 - v6.0优化版
 * 包含新的状态和可能产生的副作用列表
 *
 * 🔥 改进：
 * - 性能优化的构建方法
 * - ModernResult集成
 * - 便捷的链式调用
 */
data class ReduceResult<State : UiState, Effect : UiEffect>(
    val newState: State,
    val effects: List<Effect> = emptyList(),
) {
    companion object {
        /**
         * 创建只包含状态变更的结果
         */
        fun <State : UiState, Effect : UiEffect> stateOnly(newState: State): ReduceResult<State, Effect> {
            return ReduceResult(newState, emptyList())
        }

        /**
         * 创建包含单个副作用的结果
         */
        fun <State : UiState, Effect : UiEffect> withEffect(
            newState: State,
            effect: Effect,
        ): ReduceResult<State, Effect> {
            return ReduceResult(newState, listOf(effect))
        }

        /**
         * 创建包含多个副作用的结果
         */
        fun <State : UiState, Effect : UiEffect> withEffects(
            newState: State,
            effects: List<Effect>,
        ): ReduceResult<State, Effect> {
            return ReduceResult(newState, effects)
        }

        /**
         * 🔥 新增：保持当前状态不变
         */
        fun <State : UiState, Effect : UiEffect> noChange(currentState: State): ReduceResult<State, Effect> {
            return ReduceResult(currentState, emptyList())
        }

        /**
         * 🔥 新增：只发送Effect，状态不变
         */
        fun <State : UiState, Effect : UiEffect> effectOnly(
            currentState: State,
            effect: Effect,
        ): ReduceResult<State, Effect> {
            return ReduceResult(currentState, listOf(effect))
        }

        /**
         * 🔥 新增：发送多个Effect，状态不变
         */
        fun <State : UiState, Effect : UiEffect> effectsOnly(
            currentState: State,
            effects: List<Effect>,
        ): ReduceResult<State, Effect> {
            return ReduceResult(currentState, effects)
        }
    }

    /**
     * 🔥 新增：链式调用 - 添加额外的Effect
     */
    fun addEffect(effect: Effect): ReduceResult<State, Effect> {
        return copy(effects = effects + effect)
    }

    /**
     * 🔥 新增：链式调用 - 添加多个Effect
     */
    fun addEffects(additionalEffects: List<Effect>): ReduceResult<State, Effect> {
        return copy(effects = effects + additionalEffects)
    }

    /**
     * 🔥 新增：条件性添加Effect
     */
    fun addEffectIf(condition: Boolean, effect: Effect): ReduceResult<State, Effect> {
        return if (condition) addEffect(effect) else this
    }
}

/**
 * 抽象Reducer基类 - v6.0增强版
 * 提供通用的错误处理、日志记录和ModernResult集成功能
 */
abstract class BaseReducer<Intent : AppIntent, State : UiState, Effect : UiEffect> :
    Reducer<Intent, State, Effect> {

    /**
     * 安全的reduce操作，包含异常处理和性能优化
     */
    final override fun reduce(intent: Intent, currentState: State): ReduceResult<State, Effect> {
        val intentName = intent::class.simpleName ?: "UnknownIntent"

        return try {
            Timber.v("🔄 [${this::class.simpleName}] Reducing: $intentName")

            val result = reduceInternal(intent, currentState)

            // 🔥 性能优化：只在状态实际改变时记录日志
            if (result.newState != currentState) {
                Timber.v("✅ [${this::class.simpleName}] State changed by: $intentName")
            }

            if (result.effects.isNotEmpty()) {
                Timber.v(
                    "📤 [${this::class.simpleName}] Effects: ${result.effects.map { it::class.simpleName }}",
                )
            }

            result
        } catch (e: Exception) {
            Timber.e("❌ [${this::class.simpleName}] Reduce failed for: $intentName", e)
            handleReduceError(e, intent, currentState)
        }
    }

    /**
     * 具体的reduce实现，由子类重写
     */
    protected abstract fun reduceInternal(intent: Intent, currentState: State): ReduceResult<State, Effect>

    /**
     * 处理Reducer中的异常 - v6.0增强版
     *
     * 🔥 改进：
     * - 智能错误恢复
     * - UiText错误转换
     * - 日志改进
     */
    protected open fun handleReduceError(
        error: Exception,
        intent: Intent,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 尝试创建错误Effect（如果State支持）
        val errorEffect = createErrorEffect(error)

        return if (errorEffect != null) {
            ReduceResult.effectOnly(currentState, errorEffect)
        } else {
            // 默认策略：保持当前状态
            ReduceResult.noChange(currentState)
        }
    }

    /**
     * 🔥 新增：创建错误Effect
     * 子类可重写此方法来创建特定的错误Effect
     */
    protected open fun createErrorEffect(error: Exception): Effect? {
        return null // 默认不创建Effect
    }

    /**
     * 🔥 新增：便捷的状态更新方法
     */
    protected fun updateState(
        currentState: State,
        update: (State) -> State,
    ): ReduceResult<State, Effect> {
        return ReduceResult.stateOnly(update(currentState))
    }

    /**
     * 🔥 新增：便捷的状态+Effect方法
     */
    protected fun updateStateWithEffect(
        currentState: State,
        update: (State) -> State,
        effect: Effect,
    ): ReduceResult<State, Effect> {
        return ReduceResult.withEffect(update(currentState), effect)
    }

    /**
     * 🔥 新增：便捷的状态+多Effect方法
     */
    protected fun updateStateWithEffects(
        currentState: State,
        update: (State) -> State,
        effects: List<Effect>,
    ): ReduceResult<State, Effect> {
        return ReduceResult.withEffects(update(currentState), effects)
    }
}

/**
 * 🔥 新增：ModernResult专用Reducer基类
 * 专门处理ModernResult的状态转换
 */
abstract class ModernResultReducer<Intent : AppIntent, State : BaseUiState, Effect : UiEffect, T> :
    BaseReducer<Intent, State, Effect>() {

    /**
     * 处理ModernResult的标准模式
     * 子类只需要实现特定的成功、错误、加载处理逻辑
     */
    protected fun handleModernResult(
        result: com.example.gymbro.core.error.types.ModernResult<T>,
        currentState: State,
        onSuccess: (T, State) -> ReduceResult<State, Effect>,
        onError: ((UiText, State) -> ReduceResult<State, Effect>)? = null,
        onLoading: ((State) -> ReduceResult<State, Effect>)? = null,
    ): ReduceResult<State, Effect> {
        return when (result) {
            is com.example.gymbro.core.error.types.ModernResult.Success -> {
                onSuccess(result.data, currentState)
            }

            is com.example.gymbro.core.error.types.ModernResult.Error -> {
                val errorMessage = result.error.toString().let { UiText.DynamicString(it) }
                onError?.invoke(errorMessage, currentState)
                    ?: createDefaultErrorResult(errorMessage, currentState)
            }

            is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                onLoading?.invoke(currentState)
                    ?: createDefaultLoadingResult(currentState)
            }
        }
    }

    /**
     * 默认的错误结果处理
     */
    private fun createDefaultErrorResult(error: UiText, currentState: State): ReduceResult<State, Effect> {
        val newState = updateStateWithError(currentState, error)
        val errorEffect = createErrorEffect(error)
        return if (errorEffect != null) {
            ReduceResult.withEffect(newState, errorEffect)
        } else {
            ReduceResult.stateOnly(newState)
        }
    }

    /**
     * 默认的加载结果处理
     */
    private fun createDefaultLoadingResult(currentState: State): ReduceResult<State, Effect> {
        val newState = updateStateWithLoading(currentState, true)
        return ReduceResult.stateOnly(newState)
    }

    /**
     * 更新状态的错误信息 - 子类可重写
     */
    protected abstract fun updateStateWithError(currentState: State, error: UiText): State

    /**
     * 更新状态的加载信息 - 子类可重写
     */
    protected abstract fun updateStateWithLoading(currentState: State, isLoading: Boolean): State

    /**
     * 创建错误Effect - 子类可重写
     */
    protected open fun createErrorEffect(error: UiText): Effect? {
        return null
    }
}

/**
 * 🔥 新增：Reducer构建器 - DSL风格的状态转换
 * 提供类型安全的Reducer构建方式
 */
class ReducerBuilder<Intent : AppIntent, State : UiState, Effect : UiEffect>(
    private val initialState: State,
) {
    internal val intentHandlers = mutableMapOf<
        Class<out Intent>,
        (
            Intent,
            State,
        ) -> ReduceResult<State, Effect>,
        >()

    /**
     * 注册Intent处理器
     */
    inline fun <reified I : Intent> on(
        noinline handler: (I, State) -> ReduceResult<State, Effect>,
    ) {
        getIntentHandlers()[I::class.java] = { intent, state ->
            @Suppress("UNCHECKED_CAST")
            handler(intent as I, state)
        }
    }

    /**
     * 获取Intent处理器映射 - 供inline函数访问
     */
    fun getIntentHandlers() = intentHandlers

    /**
     * 构建Reducer
     */
    fun build(): Reducer<Intent, State, Effect> {
        return object : BaseReducer<Intent, State, Effect>() {
            override fun reduceInternal(intent: Intent, currentState: State): ReduceResult<State, Effect> {
                val handler = intentHandlers[intent::class.java]
                return handler?.invoke(intent, currentState)
                    ?: ReduceResult.noChange(currentState)
            }
        }
    }
}

/**
 * 🔥 新增：便捷的Reducer构建函数
 */
fun <Intent : AppIntent, State : UiState, Effect : UiEffect> buildReducer(
    initialState: State,
    builder: ReducerBuilder<Intent, State, Effect>.() -> Unit,
): Reducer<Intent, State, Effect> {
    val reducerBuilder = ReducerBuilder<Intent, State, Effect>(initialState)
    reducerBuilder.builder()
    return reducerBuilder.build()
}

/**
 * 🔥 新增：ModernResult便捷处理扩展
 */
fun <T> com.example.gymbro.core.error.types.ModernResult<T>.toUiText(): UiText {
    return when (this) {
        is com.example.gymbro.core.error.types.ModernResult.Success -> UiText.DynamicString("操作成功")
        is com.example.gymbro.core.error.types.ModernResult.Error -> UiText.DynamicString(
            this.error.toString(),
        )

        is com.example.gymbro.core.error.types.ModernResult.Loading -> UiText.DynamicString("加载中...")
    }
}

/**
 * 🔥 新增：状态更新扩展函数
 * 为BaseUiState提供便捷的状态更新方法
 */
fun <T : BaseUiState> T.withLoading(isLoading: Boolean): T {
    return when (this) {
        is SimpleUiState -> this.copy(isLoading = isLoading) as T
        else -> this // 对于自定义状态，需要子类自己实现
    }
}

fun <T : BaseUiState> T.withError(error: UiText?): T {
    return when (this) {
        is SimpleUiState -> this.copy(error = error) as T
        else -> this // 对于自定义状态，需要子类自己实现
    }
}

fun <T : BaseUiState> T.clearError(): T {
    return withError(null)
}

/**
 * 🔥 新增：ReduceResult扩展函数
 * 提供更便捷的结果处理方式
 */
fun <State : UiState, Effect : UiEffect> State.asResult(): ReduceResult<State, Effect> {
    return ReduceResult.stateOnly(this)
}

fun <State : UiState, Effect : UiEffect> State.withEffect(effect: Effect): ReduceResult<State, Effect> {
    return ReduceResult.withEffect(this, effect)
}

fun <State : UiState, Effect : UiEffect> State.withEffects(effects: List<Effect>): ReduceResult<State, Effect> {
    return ReduceResult.withEffects(this, effects)
}

/**
 * 🔥 新增：条件性状态更新
 */
inline fun <State : UiState, Effect : UiEffect> State.updateIf(
    condition: Boolean,
    crossinline update: (State) -> State,
): State {
    return if (condition) update(this) else this
}

/**
 * 🔥 新增：批量状态更新
 */
fun <State : UiState> State.update(vararg updates: (State) -> State): State {
    return updates.fold(this) { currentState, update -> update(currentState) }
}
