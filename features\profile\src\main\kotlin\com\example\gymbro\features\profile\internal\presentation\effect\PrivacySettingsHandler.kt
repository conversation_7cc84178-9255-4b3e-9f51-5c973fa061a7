package com.example.gymbro.features.profile.internal.presentation.effect

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.profile.model.user.enums.ProfileVisibility
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import com.example.gymbro.domain.profile.usecase.UpdateUserSettingsUseCase
import com.example.gymbro.features.profile.internal.presentation.contract.PrivacyContract.Effect
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

/**
 * Privacy功能的设置处理器
 * 专门负责隐私设置的更新操作
 */
@Singleton
internal class PrivacySettingsHandler
    @Inject
    constructor(
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val updateUserSettingsUseCase: UpdateUserSettingsUseCase,
        private val logger: Logger,
    ) {
        /**
         * 处理隐私设置更新
         */
        fun handleUpdatePrivacySettings(
            isContactRecommendEnabled: Boolean,
            isFriendsVisibilityEnabled: Boolean,
            isProfilePublic: Boolean,
            coroutineScope: CoroutineScope,
            sendEffect: (Effect) -> Unit,
            onError: (String, Exception) -> Unit,
        ) {
            // 使用黄金范式：onEach/catch/launchIn
            getCurrentUserIdUseCase()
                .onEach { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val userId = result.data
                            if (userId != null) {
                                updateSettingsForUser(
                                    userId,
                                    isContactRecommendEnabled,
                                    isFriendsVisibilityEnabled,
                                    isProfilePublic,
                                    coroutineScope,
                                    sendEffect,
                                    onError,
                                )
                            } else {
                                logger.e("PrivacySettingsHandler", "User ID is null")
                                sendEffect(Effect.ShowSnackbar(UiText.DynamicString("操作失败，请重试")))
                            }
                        }

                        is ModernResult.Error -> {
                            logger.e(
                                "PrivacySettingsHandler",
                                "Unable to get current user ID for privacy settings",
                            )
                            sendEffect(Effect.ShowSnackbar(UiText.DynamicString("操作失败，请重试")))
                        }

                        is ModernResult.Loading -> {
                            // 保持加载状态
                        }
                    }
                }.catch { exception ->
                    logger.e("PrivacySettingsHandler", "Error updating privacy settings", exception)
                    onError("UpdatePrivacySettings", Exception(exception.message, exception))
                }.launchIn(coroutineScope)
        }

        private fun updateSettingsForUser(
            currentUserId: String,
            isContactRecommendEnabled: Boolean,
            isFriendsVisibilityEnabled: Boolean,
            isProfilePublic: Boolean,
            coroutineScope: CoroutineScope,
            sendEffect: (Effect) -> Unit,
            onError: (String, Exception) -> Unit,
        ) {
            coroutineScope.launch {
                try {
                    // 简化方案：直接使用一次性操作
                    val defaultSettings = UserSettings(userId = currentUserId)
                    val updatedPrivacySettings =
                        defaultSettings.privacySettings.copy(
                            allowDataCollection = isContactRecommendEnabled,
                            showActivityHistory = isFriendsVisibilityEnabled,
                            profileVisibility = if (isProfilePublic) ProfileVisibility.PUBLIC else ProfileVisibility.PRIVATE,
                        )
                    val updatedSettings = defaultSettings.copy(privacySettings = updatedPrivacySettings)

                    // 直接更新设置 - UpdateUserSettingsUseCase返回ModernResult<Unit>，不是Flow
                    val result = updateUserSettingsUseCase(updatedSettings)
                    when (result) {
                        is ModernResult.Success -> {
                            sendEffect(Effect.ShowSnackbar(UiText.DynamicString("隐私设置已更新")))
                        }

                        is ModernResult.Error -> {
                            onError("UpdateSettingsForUser", Exception(result.error.message))
                        }

                        is ModernResult.Loading -> {
                            // 处理加载状态
                        }
                    }
                } catch (e: Exception) {
                    onError("UpdateSettingsForUser", e)
                }
            }
        }
    }
