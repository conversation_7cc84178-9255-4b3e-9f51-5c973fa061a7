package com.example.gymbro.buildlogic.detekt

import com.example.gymbro.buildlogic.detekt.design.*
import com.example.gymbro.buildlogic.detekt.documentation.*
import com.example.gymbro.buildlogic.detekt.logging.*
import com.example.gymbro.buildlogic.detekt.mvi.*
import com.example.gymbro.buildlogic.detekt.quality.*
import io.gitlab.arturbosch.detekt.api.*

/**
 * GymBro 项目自定义 Detekt 规则集
 *
 * 包含以下规则分类：
 * 1. MVI 架构规则 - 确保 MVI 模式的正确实现
 * 2. Design Token 规则 - 强制使用设计系统令牌
 * 3. 日志规则 - 规范化日志使用
 * 4. 文档规则 - 自动修复和检查 KDoc 格式
 * 5. 质量规则 - 项目特定的代码质量要求
 *
 * 规则集 ID: gymbro-rules
 * 版本: 1.0.0
 * 兼容 Detekt: 1.23.8
 */
class GymBroRuleSetProvider : RuleSetProvider {

    override val ruleSetId: String = "gymbro-rules"

    override fun instance(config: Config): RuleSet = RuleSet(
        ruleSetId,
        listOf(
            // MVI 架构规则
            MviStateImmutability(config),
            ImmutableStateClass(config),
            MviIntentNaming(config),

            // Design Token 规则
            NoHardcodedDimension(config),
            NoHardcodedColor(config),
            NoHardcodedDesignValues(config),
            UseWorkoutColors(config),

            // 日志规则
            MaxTimberLogsPerFile(config),
            MaxTimberLogsPerFunction(config),
            LoggingModuleRestriction(config),

            // 文档规则
            KDocAutoFix(config),
            KDocFormatter(config),
            KDocCompleteness(config),

            // 质量规则
            NoTodoOrFixme(config)
        )
    )
}
