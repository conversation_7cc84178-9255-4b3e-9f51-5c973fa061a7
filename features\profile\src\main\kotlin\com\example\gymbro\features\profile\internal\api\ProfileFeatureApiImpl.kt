package com.example.gymbro.features.profile.internal.api

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.userdata.api.UserDataCenterApi
import com.example.gymbro.core.userdata.api.model.UserDataState
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.model.ai.UserAiContext
import com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase
import com.example.gymbro.domain.profile.usecase.UpdateUserProfileUseCase
import com.example.gymbro.features.profile.api.ProfileFeatureApi
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map

/**
 * Profile 模块功能 API 的具体实现
 *
 * 实现 ProfileFeatureApi 接口，提供 Profile 模块对外的核心功能。
 * 此类负责将内部的业务逻辑封装为稳定的 API 接口。
 *
 * 遵循 Clean Architecture 原则：
 * - 实现类放在 internal 包下，对外部不可见
 * - 通过 DI 注入，实现接口与实现的解耦
 * - 只依赖 domain 层的 UseCase 和模型
 * - 提供响应式的数据流和一次性的操作方法
 */
@Singleton
internal class ProfileFeatureApiImpl
    @Inject
    constructor(
        private val userDataCenterApi: UserDataCenterApi,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val getUserProfileUseCase: GetUserProfileUseCase,
        private val updateUserProfileUseCase: UpdateUserProfileUseCase,
        private val logger: Logger,
    ) : ProfileFeatureApi {
        /**
         * 观察用户的 AI 上下文信息
         *
         * 通过 UserDataCenter 获取统一用户数据，并转换为 AI 上下文格式。
         * 当用户数据更新时，会自动发出最新的上下文信息。
         *
         * @return Flow<UserAiContext> 用户 AI 上下文数据流
         */
        override fun observeUserAiContext(): Flow<UserAiContext> {
            logger.d("ProfileFeatureApiImpl", "开始观察用户AI上下文 - 使用UserDataCenter")

            return userDataCenterApi.observeUserData().map { userDataState ->
                when (userDataState) {
                    is UserDataState.Success -> {
                        val unifiedUserData = userDataState.data
                        logger.d(
                            "ProfileFeatureApiImpl",
                            "从UserDataCenter获取到用户数据: userId=${unifiedUserData.userId}, complete=${unifiedUserData.isComplete}",
                        )

                        // 转换 UnifiedUserData 到 UserAiContext
                        UserAiContext(
                            userId = unifiedUserData.userId,
                            displayName = unifiedUserData.displayName,
                            fitnessLevel = unifiedUserData.fitnessLevel,
                            height = unifiedUserData.height,
                            weight = unifiedUserData.weight,
                            gender = unifiedUserData.gender,
                            fitnessGoals = unifiedUserData.fitnessGoals,
                            workoutDays = unifiedUserData.workoutDays,
                            allowPartnerMatching = unifiedUserData.allowPartnerMatching,
                            bio = unifiedUserData.bio,
                            totalActivityCount = unifiedUserData.totalActivityCount,
                            weeklyActiveMinutes = unifiedUserData.weeklyActiveMinutes,
                            lastUpdated = unifiedUserData.lastUpdated,
                        )
                    }

                    is UserDataState.Loading -> {
                        logger.d("ProfileFeatureApiImpl", "用户数据加载中，返回空上下文")
                        UserAiContext.empty("loading")
                    }

                    is UserDataState.Error -> {
                        logger.w("ProfileFeatureApiImpl", "用户数据加载失败: ${userDataState.error}")
                        UserAiContext.empty("error")
                    }

                    is UserDataState.Empty -> {
                        logger.d("ProfileFeatureApiImpl", "用户未登录，返回空上下文")
                        UserAiContext.empty("anonymous")
                    }

                    is UserDataState.Syncing -> {
                        val unifiedUserData = userDataState.currentData
                        logger.d("ProfileFeatureApiImpl", "用户数据同步中，使用当前数据")

                        // 即使在同步中，也返回当前可用的数据
                        UserAiContext(
                            userId = unifiedUserData.userId,
                            displayName = unifiedUserData.displayName,
                            fitnessLevel = unifiedUserData.fitnessLevel,
                            height = unifiedUserData.height,
                            weight = unifiedUserData.weight,
                            gender = unifiedUserData.gender,
                            fitnessGoals = unifiedUserData.fitnessGoals,
                            workoutDays = unifiedUserData.workoutDays,
                            allowPartnerMatching = unifiedUserData.allowPartnerMatching,
                            bio = unifiedUserData.bio,
                            totalActivityCount = unifiedUserData.totalActivityCount,
                            weeklyActiveMinutes = unifiedUserData.weeklyActiveMinutes,
                            lastUpdated = unifiedUserData.lastUpdated,
                        )
                    }
                }
            }
        }

        /**
         * 强制刷新用户配置
         *
         * 触发用户数据的重新加载，确保获取到最新的用户信息。
         * 这是一个一次性的操作，适用于需要立即同步最新数据的场景。
         *
         * @return ModernResult<Unit, ModernDataError> 操作结果
         */
        override suspend fun forceRefreshSettings(): ModernResult<Unit> {
            return try {
                logger.d("ProfileFeatureApiImpl", "开始强制刷新用户配置")

                // 获取当前用户ID
                val userIdResult = getCurrentUserIdUseCase().firstOrNull()
                val userId =
                    when (userIdResult) {
                        is ModernResult.Success -> userIdResult.data
                        is ModernResult.Error -> {
                            logger.e("ProfileFeatureApiImpl", "获取用户ID失败: ${userIdResult.error}")
                            return ModernResult.Error(userIdResult.error)
                        }

                        else -> {
                            logger.w("ProfileFeatureApiImpl", "用户ID获取超时或未返回结果")
                            return ModernResult.Error(
                                BusinessErrors.BusinessError.rule(
                                    operationName = "forceRefreshSettings",
                                    message = UiText.DynamicString("获取用户ID超时"),
                                    recoverable = true,
                                ),
                            )
                        }
                    }

                if (userId == null) {
                    logger.w("ProfileFeatureApiImpl", "用户ID为空，无法刷新配置")
                    return ModernResult.Error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "forceRefreshSettings",
                            message = UiText.DynamicString("用户未登录"),
                            recoverable = true,
                        ),
                    )
                }

                // 强制重新获取用户资料（这会触发缓存刷新）
                val profileResult = getUserProfileUseCase()
                when (profileResult) {
                    is ModernResult.Success -> {
                        logger.d("ProfileFeatureApiImpl", "用户配置刷新成功")
                        ModernResult.Success(Unit)
                    }

                    is ModernResult.Error -> {
                        logger.e("ProfileFeatureApiImpl", "刷新用户配置失败: ${profileResult.error}")
                        ModernResult.Error(profileResult.error)
                    }

                    else -> {
                        logger.w("ProfileFeatureApiImpl", "刷新用户配置超时")
                        ModernResult.Error(
                            BusinessErrors.BusinessError.rule(
                                operationName = "forceRefreshSettings",
                                message = UiText.DynamicString("刷新配置超时"),
                                recoverable = true,
                            ),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "ProfileFeatureApiImpl", "强制刷新用户配置时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "forceRefreshSettings",
                        message = UiText.DynamicString("刷新配置时发生异常: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }
