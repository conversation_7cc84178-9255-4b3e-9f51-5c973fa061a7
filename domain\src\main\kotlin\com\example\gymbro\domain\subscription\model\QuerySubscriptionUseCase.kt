package com.example.gymbro.domain.usecase.subscription

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.*
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.auth.repository.UserSessionManager
import com.example.gymbro.domain.subscription.model.Subscription
import com.example.gymbro.domain.subscription.model.plan.SubscriptionPlan
import com.example.gymbro.domain.subscription.model.status.SubscriptionStatus
import com.example.gymbro.domain.subscription.repository.SubscriptionRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*

/**
 * 订阅查询用例
 *
 * 负责所有与订阅信息相关的查询操作，例如获取计划、状态、详情等。
 */
@Singleton
class QuerySubscriptionUseCase
    @Inject
    constructor(
        private val subscriptionRepository: SubscriptionRepository,
        private val authRepository: AuthRepository,
        private val userSessionManager: UserSessionManager,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {
        /**
         * 获取所有可用的订阅计划
         * @return 包含订阅计划列表或错误的 Flow<ModernResult>
         */
        fun getAvailablePlans(): Flow<ModernResult<List<SubscriptionPlan>>> {
            logger.d("QuerySubscriptionUseCase: 正在获取所有可用订阅计划")
            return subscriptionRepository
                .getAvailablePlans()
                .catch { e ->
                    logger.e("QuerySubscriptionUseCase: 获取可用订阅计划失败", e)
                    emit(
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "getAvailablePlans",
                                errorType = GlobalErrorType.System.General,
                                category = ErrorCategory.SYSTEM,
                                uiMessage = UiText.DynamicString("获取可用订阅计划失败"),
                                severity = ErrorSeverity.ERROR,
                                cause = e,
                            ),
                        ),
                    )
                }.also { _ ->
                    logger.d("QuerySubscriptionUseCase: getAvailablePlans flow created.")
                }
        }

        /**
         * 根据ID获取订阅计划
         * @param planId 计划ID
         * @return Flow<ModernResult<SubscriptionPlan?>> 包含订阅计划（如果存在）或 null，或错误
         */
        @OptIn(ExperimentalCoroutinesApi::class)
        fun getPlanById(planId: String): Flow<ModernResult<SubscriptionPlan?>> {
            logger.d("QuerySubscriptionUseCase: 正在获取订阅计划: $planId")
            return flow {
                emit(ModernResult.Loading)
                try {
                    val result = subscriptionRepository.getPlanById(planId)
                    if (result is ModernResult.Error &&
                        result.error.errorType == GlobalErrorType.Business.NotFound
                    ) {
                        logger.w("QuerySubscriptionUseCase: 未找到订阅计划: $planId, 返回 Success(null)")
                        emit(ModernResult.Success(null))
                    } else {
                        emit(result)
                    }
                } catch (e: Exception) {
                    logger.e("QuerySubscriptionUseCase: 获取订阅计划 ID: $planId 失败", e)
                    emit(
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "getPlanById",
                                errorType = GlobalErrorType.System.General,
                                category = ErrorCategory.SYSTEM,
                                uiMessage = UiText.DynamicString("获取订阅计划失败: $planId"),
                                severity = ErrorSeverity.ERROR,
                                cause = e,
                            ),
                        ),
                    )
                }
            }
        }

        /**
         * 获取当前用户的订阅信息
         *
         * @return 包含当前用户订阅的Flow<ModernResult<Subscription?>>
         */
        fun getCurrentUserSubscription(): Flow<ModernResult<Subscription?>> {
            logger.d("QuerySubscriptionUseCase: 正在获取当前用户订阅")
            val userId = userSessionManager.getCurrentUserId()
            if (userId == null) {
                logger.w("QuerySubscriptionUseCase: 用户未登录，无法获取订阅")
                return flowOf(ModernResult.Error(createUnauthenticatedError()))
            }
            return subscriptionRepository
                .getUserSubscription(userId)
                .catch { e ->
                    logger.e("QuerySubscriptionUseCase: 获取用户 $userId 的订阅失败", e)
                    emit(
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "getCurrentUserSubscription",
                                errorType = GlobalErrorType.System.General,
                                category = ErrorCategory.SYSTEM,
                                uiMessage = UiText.DynamicString("获取用户订阅失败: $userId"),
                                severity = ErrorSeverity.ERROR,
                                cause = e,
                            ),
                        ),
                    )
                }
        }

        /**
         * 获取订阅详情
         * @param subscriptionId 订阅ID
         * @return 包含订阅详情的Flow<ModernResult<Subscription>>
         */
        fun getSubscriptionDetails(subscriptionId: String): Flow<ModernResult<Subscription>> {
            logger.d("QuerySubscriptionUseCase: 正在获取订阅详情: $subscriptionId")
            // 由于SubscriptionRepository没有直接提供getSubscriptionDetails方法，
            // 我们需要从getUserSubscription获取当前用户的订阅并过滤
            return flowOf(
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "getSubscriptionDetails",
                        message = UiText.DynamicString("暂不支持获取订阅详情"),
                        metadataMap = mapOf("subscriptionId" to subscriptionId),
                    ),
                ),
            )
        }

        /**
         * 监听当前登录用户的订阅状态
         * @return 订阅状态流 Flow<ModernResult<SubscriptionStatus>>
         */
        @OptIn(ExperimentalCoroutinesApi::class)
        fun observeCurrentUserSubscriptionStatus(): Flow<ModernResult<SubscriptionStatus>> {
            logger.d("QuerySubscriptionUseCase: 开始监听当前用户订阅状态")
            return authRepository.getCurrentUserId().flatMapLatest { userIdResult ->
                when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (userId.isNullOrBlank()) {
                            logger.w("QuerySubscriptionUseCase: 用户ID为空或未登录，无法监听订阅状态")
                            flowOf(
                                ModernResult.Error(
                                    BusinessErrors.BusinessError.rule(
                                        operationName = "observeCurrentUserSubscriptionStatus",
                                        message = UiText.DynamicString("用户未登录"),
                                        metadataMap =
                                            mapOf(
                                                "errorType" to "UNAUTHORIZED",
                                                "category" to ErrorCategory.AUTH.name,
                                            ),
                                    ),
                                ),
                            )
                        } else {
                            logger.d("QuerySubscriptionUseCase: 监视用户 $userId 的订阅状态")
                            // 由于接口中没有直接提供observeSubscriptionStatus方法，使用流的方式获取状态
                            // 这里需要转换为从getUserSubscription获取的订阅信息并提取状态
                            subscriptionRepository
                                .getUserSubscription(userId)
                                .map { result ->
                                    when (result) {
                                        is ModernResult.Success -> {
                                            val subscription = result.data
                                            if (subscription != null) {
                                                ModernResult.Success(subscription.status)
                                            } else {
                                                ModernResult.Success(SubscriptionStatus.NONE)
                                            }
                                        }

                                        is ModernResult.Error -> ModernResult.Error(result.error)
                                        is ModernResult.Loading -> ModernResult.Loading
                                    }
                                }.catch { e ->
                                    logger.e("QuerySubscriptionUseCase: 订阅状态监听发生错误 for user $userId", e)
                                    emit(
                                        ModernResult.Error(
                                            ModernDataError(
                                                operationName = "observeCurrentUserSubscriptionStatus",
                                                errorType = GlobalErrorType.System.General,
                                                category = ErrorCategory.SYSTEM,
                                                uiMessage = UiText.DynamicString("订阅状态监听失败: $userId"),
                                                severity = ErrorSeverity.ERROR,
                                                cause = e,
                                            ),
                                        ),
                                    )
                                }
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("QuerySubscriptionUseCase: 获取用户ID失败，无法监听订阅状态", userIdResult.error)
                        flowOf(ModernResult.Error(userIdResult.error))
                    }

                    is ModernResult.Loading -> {
                        logger.d("QuerySubscriptionUseCase: 获取用户ID进行中，透传Loading状态")
                        flowOf(ModernResult.Loading)
                    }
                }
            }
        }

        private fun createUnauthenticatedError(): ModernDataError =
            BusinessErrors.BusinessError.rule(
                operationName = "querySubscription",
                message = UiText.DynamicString("用户未登录，无法执行此操作"),
                metadataMap =
                    mapOf(
                        "errorType" to "UNAUTHORIZED",
                        "category" to ErrorCategory.AUTH.name,
                    ),
            )
    }
