package com.example.gymbro.core.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.compositionLocalOf
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber

/**
 * 主题持久化接口
 * 定义主题配置的持久化操作，由data层实现
 * 遵循Clean Architecture的依赖反转原则
 */
interface ThemePersistence {
    /**
     * 保存深色主题设置
     */
    suspend fun saveDarkTheme(isDark: Boolean): Boolean

    /**
     * 保存动态颜色设置
     */
    suspend fun saveDynamicColor(enable: Boolean): Boolean

    /**
     * 保存颜色模式设置（新增）
     */
    suspend fun saveColorMode(mode: String): Boolean

    /**
     * 深色主题设置流
     */
    fun darkThemeFlow(): Flow<Boolean>

    /**
     * 动态颜色设置流
     */
    fun dynamicColorFlow(): Flow<Boolean>

    /**
     * 颜色模式设置流（新增）
     */
    fun colorModeFlow(): Flow<String>
}

/**
 * 主题风格枚举
 * 定义应用可用的主题风格
 */
enum class ThemeStyle {
    /**
     * 科技风格主题
     * 现代化、简洁的设计风格
     */
    GROK,

    /**
     * 简约风格主题
     * 经典、专业的设计风格
     */
    CHATGPT,

    /**
     * DeepSeek风格主题
     * 深度思考、专业的设计风格
     */
    DEEPSEEK,

    ;

    /**
     * 获取该主题风格的显示名称
     */
    fun getDisplayName(): String =
        when (this) {
            GROK -> "科技"
            CHATGPT -> "简约"
            DEEPSEEK -> "DeepSeek"
        }

    companion object {
        /**
         * 获取默认主题风格
         */
        fun getDefault(): ThemeStyle = GROK

        /**
         * 从字符串创建ThemeStyle
         */
        fun fromString(value: String): ThemeStyle =
            when (value.lowercase()) {
                "grok" -> GROK
                "chatgpt" -> CHATGPT
                "deepseek" -> DEEPSEEK
                else -> getDefault()
            }
    }
}

/**
 * 颜色模式枚举
 * 定义应用可用的颜色模式
 */
enum class ColorMode {
    /**
     * 跟随系统
     * 应用会根据系统的亮色/暗色设置自动切换主题
     */
    SYSTEM,

    /**
     * 亮色模式
     * 固定使用亮色主题
     */
    LIGHT,

    /**
     * 暗色模式
     * 固定使用暗色主题
     */
    DARK,

    ;

    /**
     * 获取该颜色模式的显示名称
     */
    fun getDisplayName(): String =
        when (this) {
            SYSTEM -> "跟随系统"
            LIGHT -> "亮色模式"
            DARK -> "暗色模式"
        }

    companion object {
        /**
         * 获取默认颜色模式
         */
        fun getDefault(): ColorMode = SYSTEM

        /**
         * 从字符串创建ColorMode
         */
        fun fromString(value: String): ColorMode =
            when (value.lowercase()) {
                "light" -> LIGHT
                "dark" -> DARK
                "system" -> SYSTEM
                else -> getDefault()
            }
    }
}

/**
 * 动态主题配置
 * 包含完整的主题配置信息
 */
data class ThemeConfig(
    val themeStyle: ThemeStyle = ThemeStyle.getDefault(),
    val colorMode: ColorMode = ColorMode.getDefault(),
    val useDynamicColor: Boolean = false, // 默认禁用动态颜色，避免不可预测的颜色
) {
    /**
     * 根据系统暗色模式状态计算实际的暗色主题状态
     */
    fun shouldUseDarkTheme(isSystemInDarkMode: Boolean): Boolean =
        when (colorMode) {
            ColorMode.LIGHT -> false
            ColorMode.DARK -> true
            ColorMode.SYSTEM -> isSystemInDarkMode
        }

    companion object {
        /**
         * 获取默认主题配置
         */
        fun getDefault(): ThemeConfig = ThemeConfig()
    }
}

/**
 * 主题模式枚举（向后兼容）
 *
 * @deprecated 使用 ColorMode 替代
 */
@Deprecated("使用 ColorMode 替代", ReplaceWith("ColorMode"))
enum class ThemeMode {
    SYSTEM, LIGHT, DARK;

    fun getDisplayName(): String =
        when (this) {
            SYSTEM -> "跟随系统"
            LIGHT -> "亮色主题"
            DARK -> "暗色主题"
        }

    companion object {
        fun getDefault(): ThemeMode = SYSTEM

        /**
         * 从字符串创建ThemeMode
         */
        fun fromString(value: String): ThemeMode =
            when (value.lowercase()) {
                "light" -> LIGHT
                "dark" -> DARK
                "system" -> SYSTEM
                else -> getDefault()
            }
    }
}

/**
 * 动态主题管理器
 *
 * 负责管理应用程序的完整主题配置，包括主题风格和颜色模式。
 * 支持瞬时主题切换和主题偏好持久化。
 */
@Singleton
class ThemeManager
    @Inject
    constructor(
        private val persistence: ThemePersistence,
    ) {
        companion object {
            const val THEME_LIGHT = "light"
            const val THEME_DARK = "dark"
            const val THEME_SYSTEM = "system"
            private const val INITIALIZATION_TIMEOUT_MS = 3000L
        }

        // === 新的动态主题系统 ===

        // 内部主题配置状态
        private val _themeConfig = MutableStateFlow(ThemeConfig.getDefault())

        // 公开的只读主题配置流
        val themeConfig: StateFlow<ThemeConfig> = _themeConfig.asStateFlow()

        // 暗色主题状态流，根据主题配置和系统暗色模式状态计算
        private val _isDarkTheme = MutableStateFlow(false)
        val isDarkTheme: StateFlow<Boolean> = _isDarkTheme.asStateFlow()

        // 系统暗色模式状态，默认为false
        private var systemInDarkMode = false
            set(value) {
                field = value
                updateDarkThemeState()
            }

        // 初始化标志，确保只初始化一次
        private var isInitialized = false

        /**
         * 设置完整的主题配置
         *
         * @param config 要设置的主题配置
         */
        suspend fun setThemeConfig(config: ThemeConfig) {
            _themeConfig.value = config
            updateDarkThemeState()
            saveThemeConfig(config)
        }

        /**
         * 设置完整的主题配置（同步版本，不保存到持久化存储）
         *
         * @param config 要设置的主题配置
         */
        fun setThemeConfigSync(config: ThemeConfig) {
            _themeConfig.value = config
            updateDarkThemeState()
        }

        /**
         * 设置主题风格
         *
         * @param style 要设置的主题风格
         */
        suspend fun setThemeStyle(style: ThemeStyle) {
            val newConfig = _themeConfig.value.copy(themeStyle = style)
            _themeConfig.value = newConfig
            updateDarkThemeState()
            saveThemeConfig(newConfig)
        }

        /**
         * 设置颜色模式
         *
         * @param mode 要设置的颜色模式
         */
        suspend fun setColorMode(mode: ColorMode) {
            val newConfig = _themeConfig.value.copy(colorMode = mode)
            _themeConfig.value = newConfig
            updateDarkThemeState()
            saveThemeConfig(newConfig)
        }

        /**
         * 设置动态颜色
         *
         * @param enabled 是否启用动态颜色
         */
        suspend fun setDynamicColor(enabled: Boolean) {
            val newConfig = _themeConfig.value.copy(useDynamicColor = enabled)
            _themeConfig.value = newConfig
            saveThemeConfig(newConfig)
        }

        /**
         * 获取当前主题配置
         *
         * @return 当前主题配置
         */
        fun getCurrentThemeConfig(): ThemeConfig = _themeConfig.value

        /**
         * 切换主题风格
         * 在科技、简约和DeepSeek风格之间切换
         */
        suspend fun toggleThemeStyle() {
            val currentStyle = _themeConfig.value.themeStyle
            val nextStyle = when (currentStyle) {
                ThemeStyle.GROK -> ThemeStyle.CHATGPT
                ThemeStyle.CHATGPT -> ThemeStyle.DEEPSEEK
                ThemeStyle.DEEPSEEK -> ThemeStyle.GROK
            }
            setThemeStyle(nextStyle)
        }

        /**
         * 切换颜色模式
         * 循环顺序：浅色 -> 深色 -> 跟随系统 -> 浅色
         */
        suspend fun toggleColorMode() {
            val currentMode = _themeConfig.value.colorMode
            val nextMode = when (currentMode) {
                ColorMode.LIGHT -> ColorMode.DARK
                ColorMode.DARK -> ColorMode.SYSTEM
                ColorMode.SYSTEM -> ColorMode.LIGHT
            }
            setColorMode(nextMode)
        }

        /**
         * 更新系统暗色模式状态
         *
         * @param isSystemInDarkMode 系统是否处于暗色模式
         */
        fun updateSystemDarkMode(isSystemInDarkMode: Boolean) {
            systemInDarkMode = isSystemInDarkMode
        }

        /**
         * 更新暗色主题状态
         * 根据当前主题配置和系统暗色模式状态计算实际的暗色主题状态
         */
        private fun updateDarkThemeState() {
            _isDarkTheme.value = _themeConfig.value.shouldUseDarkTheme(systemInDarkMode)
        }

        /**
         * 保存主题配置到持久化存储
         *
         * @param config 要保存的主题配置
         */
        private suspend fun saveThemeConfig(config: ThemeConfig) {
            try {
                Timber.d("保存主题配置: $config")

                // 保存颜色模式设置（修复：正确保存SYSTEM模式）
                val colorModeString = when (config.colorMode) {
                    ColorMode.LIGHT -> THEME_LIGHT
                    ColorMode.DARK -> THEME_DARK
                    ColorMode.SYSTEM -> THEME_SYSTEM
                }

                val colorModeSuccess = persistence.saveColorMode(colorModeString)
                if (!colorModeSuccess) {
                    Timber.e("保存颜色模式设置失败")
                    return
                }

                // 保存动态颜色设置
                val dynamicColorSuccess = persistence.saveDynamicColor(config.useDynamicColor)
                if (!dynamicColorSuccess) {
                    Timber.e("保存动态颜色设置失败")
                    return
                }

                // 向后兼容：保存当前实际的暗色主题状态
                val currentIsDarkTheme = _isDarkTheme.value
                val darkThemeSuccess = persistence.saveDarkTheme(currentIsDarkTheme)
                if (!darkThemeSuccess) {
                    Timber.e("保存深色主题设置失败")
                    return
                }

                Timber.i("主题配置保存成功")
            } catch (e: Exception) {
                Timber.e(e, "保存主题配置时发生异常")
            }
        }

        /**
         * 初始化主题管理器
         * 从持久化存储加载主题配置
         */
        suspend fun initialize() {
            if (isInitialized) return

            try {
                Timber.d("初始化主题管理器，从持久化存储加载配置")

                // 修复：使用超时机制防止挂起，优先尝试新的颜色模式流
                val result = withTimeoutOrNull(INITIALIZATION_TIMEOUT_MS) {
                    try {
                        // 尝试使用新的颜色模式持久化
                        combine(
                            persistence.colorModeFlow(),
                            persistence.dynamicColorFlow(),
                        ) { colorModeString: String, useDynamicColor: Boolean ->
                            val colorMode = when (colorModeString) {
                                THEME_LIGHT -> ColorMode.LIGHT
                                THEME_DARK -> ColorMode.DARK
                                THEME_SYSTEM -> ColorMode.SYSTEM
                                else -> ColorMode.getDefault()
                            }

                            val loadedConfig = ThemeConfig(
                                themeStyle = ThemeStyle.getDefault(),
                                colorMode = colorMode,
                                useDynamicColor = useDynamicColor,
                            )

                            setThemeConfigSync(loadedConfig)
                            Timber.i("主题配置加载成功: $loadedConfig")
                            isInitialized = true
                            Unit
                        }.firstOrNull()
                    } catch (e: Exception) {
                        Timber.w(e, "新版本颜色模式加载失败，回退到旧版本")
                        // 回退到旧版本逻辑
                        combine(
                            persistence.darkThemeFlow(),
                            persistence.dynamicColorFlow(),
                        ) { isDarkTheme: Boolean, useDynamicColor: Boolean ->
                            // 根据保存的设置重建主题配置
                            val colorMode = if (isDarkTheme) ColorMode.DARK else ColorMode.LIGHT
                            val loadedConfig = ThemeConfig(
                                themeStyle = ThemeStyle.getDefault(),
                                colorMode = colorMode,
                                useDynamicColor = useDynamicColor,
                            )

                            setThemeConfigSync(loadedConfig)
                            Timber.i("主题配置加载成功(向后兼容): $loadedConfig")
                            isInitialized = true
                            Unit
                        }.firstOrNull()
                    }
                }

                if (result == null) {
                    Timber.w("初始化超时，使用默认配置")
                    setThemeConfigSync(ThemeConfig.getDefault())
                    isInitialized = true
                }
            } catch (e: Exception) {
                Timber.e(e, "初始化主题管理器时发生异常，使用默认配置")
                setThemeConfigSync(ThemeConfig.getDefault())
                isInitialized = true
            }
        }

        // === 向后兼容的API ===

        // 兼容旧的主题状态流
        @Deprecated("使用 themeConfig 替代", ReplaceWith("themeConfig"))
        val currentTheme: StateFlow<ThemeMode> = _themeConfig.map { config ->
            when (config.colorMode) {
                ColorMode.LIGHT -> ThemeMode.LIGHT
                ColorMode.DARK -> ThemeMode.DARK
                ColorMode.SYSTEM -> ThemeMode.SYSTEM
            }
        }.stateIn(
            scope = CoroutineScope(Dispatchers.Main),
            started = SharingStarted.Eagerly,
            initialValue = ThemeMode.SYSTEM,
        )

        /**
         * 设置应用主题（向后兼容）
         *
         * @param themeMode 要设置的主题模式 (ThemeMode 枚举)
         */
        @Deprecated("使用 setColorMode 替代", ReplaceWith("setColorMode(ColorMode.valueOf(themeMode.name))"))
        fun setTheme(themeMode: ThemeMode) {
            val colorMode = when (themeMode) {
                ThemeMode.LIGHT -> ColorMode.LIGHT
                ThemeMode.DARK -> ColorMode.DARK
                ThemeMode.SYSTEM -> ColorMode.SYSTEM
            }
            // 使用同步版本避免suspend函数调用
            _themeConfig.value = _themeConfig.value.copy(colorMode = colorMode)
            updateDarkThemeState()
        }

        /**
         * 获取当前主题（向后兼容）
         *
         * @return 当前主题模式 (ThemeMode 枚举)
         */
        @Deprecated("使用 getCurrentThemeConfig().colorMode 替代")
        fun getCurrentTheme(): ThemeMode {
            return when (_themeConfig.value.colorMode) {
                ColorMode.LIGHT -> ThemeMode.LIGHT
                ColorMode.DARK -> ThemeMode.DARK
                ColorMode.SYSTEM -> ThemeMode.SYSTEM
            }
        }
    }

/**
 * LocalThemeManager CompositionLocal
 *
 * 提供ThemeManager实例给Compose UI层使用
 * 支持动态主题系统的瞬时切换功能
 */
val LocalThemeManager =
    compositionLocalOf<ThemeManager> {
        error("ThemeManager not provided. 请确保在应用根部提供ThemeManager实例。")
    }

/**
 * 可选的LocalThemeManager CompositionLocal
 * 如果ThemeManager未提供，返回null而不是抛出异常
 */
val LocalThemeManagerOptional =
    compositionLocalOf<ThemeManager?> { null }

/**
 * 安全获取LocalThemeManager的函数
 * 如果ThemeManager未提供，返回null而不是抛出异常
 */
@Composable
fun getLocalThemeManagerOrNull(): ThemeManager? {
    return LocalThemeManagerOptional.current
}
