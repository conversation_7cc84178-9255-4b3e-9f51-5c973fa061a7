package com.example.gymbro.buildlogic.detekt.logging

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.KtCallExpression
import org.jetbrains.kotlin.psi.KtDotQualifiedExpression
import org.jetbrains.kotlin.psi.KtFile
import org.jetbrains.kotlin.psi.KtTreeVisitorVoid

/**
 * GymBro 自定义规则：限制每个文件中的 Timber 日志调用数量。
 *
 * 目的：防止过度使用日志，特别是避免token级别的日志记录。
 * 这有助于：
 * 1. 避免日志洪水
 * 2. 提高性能
 * 3. 促进更好的日志策略
 * 4. 减少不必要的调试输出
 *
 * 配置：threshold - 每个文件允许的最大日志调用数（默认：5）。
 */
class MaxTimberLogsPerFile(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "每个文件最多只能有指定数量的 Timber 日志调用。",
        Debt.TEN_MINS
    )

    private val threshold: Int by config(5)
    private val logSignatures = listOf("Timber.d", "Timber.i", "Timber.w", "Timber.e", "Timber.v", "Timber.wtf")

    override fun visitKtFile(file: KtFile) {
        super.visitKtFile(file)

        val timberCallCount = countTimberCallsInFile(file)

        if (timberCallCount > threshold) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(file),
                    "文件 '${file.name}' 包含 $timberCallCount 个 Timber 日志调用，" +
                        "超过了限制的 $threshold 个。请减少日志调用数量或将功能拆分到多个文件中。" +
                        "\n建议：考虑使用更高级别的日志策略，避免在循环或高频调用中记录日志。"
                )
            )
        }
    }

    private fun countTimberCallsInFile(file: KtFile): Int {
        var count = 0

        file.accept(object : KtTreeVisitorVoid() {
            override fun visitCallExpression(expression: KtCallExpression) {
                super.visitCallExpression(expression)

                val callText = expression.text
                if (isTimberCall(callText)) {
                    count++
                }
            }

            override fun visitDotQualifiedExpression(expression: KtDotQualifiedExpression) {
                super.visitDotQualifiedExpression(expression)

                val text = expression.text
                if (logSignatures.any { signature -> text.startsWith(signature) }) {
                    count++
                }
            }
        })

        return count
    }

    private fun isTimberCall(text: String): Boolean {
        return logSignatures.any { signature ->
            text.contains("$signature(") || text.contains("${signature.lowercase()}(")
        }
    }
}
