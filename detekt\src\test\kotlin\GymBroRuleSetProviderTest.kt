package com.example.gymbro.buildlogic.detekt

import io.gitlab.arturbosch.detekt.api.Config
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/**
 * GymBro 自定义规则集测试。
 */
class GymBroRuleSetProviderTest {

    private val provider = GymBroRuleSetProvider()

    @Test
    fun `should provide correct rule set id`() {
        assertEquals("gymbro-rules", provider.ruleSetId)
    }

    @Test
    fun `should create rule set with all rules`() {
        val ruleSet = provider.instance(Config.empty)

        assertNotNull(ruleSet)
        assertEquals("gymbro-rules", ruleSet.id)

        // 验证包含所有预期的规则
        val ruleNames = ruleSet.rules.map { it.javaClass.simpleName }

        // MVI 架构规则
        assert(ruleNames.contains("MviStateImmutability"))
        assert(ruleNames.contains("ImmutableStateClass"))
        assert(ruleNames.contains("MviIntentNaming"))

        // Design Token 规则
        assert(ruleNames.contains("NoHardcodedDimension"))
        assert(ruleNames.contains("NoHardcodedColor"))
        assert(ruleNames.contains("NoHardcodedDesignValues"))
        assert(ruleNames.contains("UseWorkoutColors"))

        // 日志规则
        assert(ruleNames.contains("MaxTimberLogsPerFile"))
        assert(ruleNames.contains("MaxTimberLogsPerFunction"))
        assert(ruleNames.contains("LoggingModuleRestriction"))

        // 文档规则
        assert(ruleNames.contains("KDocAutoFix"))
        assert(ruleNames.contains("KDocFormatter"))
        assert(ruleNames.contains("KDocCompleteness"))

        // 质量规则
        assert(ruleNames.contains("NoTodoOrFixme"))

        println("所有规则已正确注册: ${ruleNames.joinToString(", ")}")
    }
}
