package com.example.gymbro.domain.shared.network

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.common.model.NetworkStatus
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map

/**
 * 网络状态检测用例
 *
 * 负责监控网络连接状态，为AI功能提供网络可用性检测
 * 遵循Clean Architecture原则，封装网络状态相关的业务逻辑
 *
 * 🧹 技术债务清理：直接使用NetworkMonitor，移除Repository层
 *
 * @param networkMonitor 网络状态监控器
 */
@Singleton
class NetworkStatusUseCase
    @Inject
    constructor(
        private val networkMonitor: NetworkMonitor,
    ) {
        /**
         * 观察网络状态变化
         *
         * 提供实时的网络连接状态监控，用于：
         * - AI功能的网络可用性检查
         * - 网络错误的预防性检测
         * - 用户界面的网络状态显示
         *
         * @return 网络状态的Flow，包含连接状态和类型信息
         */
        fun observeNetworkStatus(): Flow<ModernResult<NetworkStatus>> =
            networkMonitor.networkState
                .map { networkState ->
                    ModernResult.Success(
                        when (networkState) {
                            is NetworkState.Available -> {
                                NetworkStatus.wifi() // 简化：可用即为WiFi
                            }

                            else -> {
                                NetworkStatus.disconnected()
                            }
                        },
                    ) as ModernResult<NetworkStatus>
                }.catch { exception ->
                    emit(
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "observeNetworkStatus",
                                errorType = GlobalErrorType.Network.General,
                                uiMessage = null,
                                cause = exception,
                            ),
                        ),
                    )
                }

        /**
         * 检查当前网络是否可用
         *
         * 同步检查网络连接状态，用于：
         * - AI请求前的快速网络检查
         * - 错误处理中的网络状态判断
         *
         * @return 当前网络状态，如果获取失败返回Error
         */
        suspend fun checkNetworkAvailability(): ModernResult<Boolean> =
            try {
                // 直接使用NetworkMonitor，简化架构
                val isOnline = networkMonitor.isOnline
                ModernResult.Success(isOnline)
            } catch (e: Exception) {
                ModernResult.Error(
                    ModernDataError(
                        operationName = "checkNetworkAvailability",
                        errorType = GlobalErrorType.Network.Connection,
                        uiMessage = UiText.DynamicString("网络检查失败"),
                        cause = e,
                        recoverable = true,
                    ),
                )
            }

        /**
         * 判断是否为网络相关错误
         *
         * 分析异常类型，判断是否由网络问题引起
         * 用于错误处理中的错误分类和用户提示
         *
         * @param throwable 要分析的异常
         * @return 是否为网络相关错误
         */
        fun isNetworkError(throwable: Throwable): Boolean =
            when (throwable) {
                is SocketTimeoutException,
                is ConnectException,
                is UnknownHostException,
                is IOException,
                -> true

                else -> false
            }

        /**
         * 获取网络错误的用户友好提示
         *
         * 根据网络错误类型，返回合适的用户提示信息
         *
         * @param throwable 网络相关异常
         * @return 用户友好的错误提示
         */
        fun getNetworkErrorMessage(throwable: Throwable): UiText =
            when (throwable) {
                is SocketTimeoutException -> {
                    UiText.DynamicString("网络连接超时，请检查网络连接")
                }

                is ConnectException -> {
                    UiText.DynamicString("无法连接到服务器，请检查网络连接")
                }

                is UnknownHostException -> {
                    UiText.DynamicString("网络连接失败，请检查网络设置")
                }

                else -> {
                    UiText.DynamicString("网络错误，请稍后重试")
                }
            }
    }
