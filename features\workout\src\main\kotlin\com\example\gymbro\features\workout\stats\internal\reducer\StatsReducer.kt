package com.example.gymbro.features.workout.stats.internal.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.domain.workout.model.stats.TimeRange
import com.example.gymbro.features.workout.stats.StatsContract
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.collections.immutable.toImmutableList

/**
 * Stats Reducer - 纯函数状态变换器
 *
 * 负责处理所有Stats相关的Intent，执行纯函数状态变换。
 * 遵循MVI 2.0架构模式，确保状态变换的可预测性和可测试性。
 *
 * 核心职责：
 * - 处理所有StatsContract.Intent
 * - 执行纯函数状态变换
 * - 生成相应的Effect
 * - 维护状态的不可变性
 *
 * 设计原则：
 * - 所有方法都是纯函数
 * - 无副作用，副作用交给EffectHandler处理
 * - 状态变换逻辑清晰明了
 * - 完备的Intent处理覆盖
 */
@Singleton
internal class StatsReducer
    @Inject
    constructor() :
    Reducer<StatsContract.Intent, StatsContract.State, StatsContract.Effect> {

        override fun reduce(
            intent: StatsContract.Intent,
            currentState: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            return when (intent) {
                // === 数据加载相关 ===
                is StatsContract.Intent.LoadStats -> handleLoadStats(intent, currentState)
                is StatsContract.Intent.RefreshStats -> handleRefreshStats(currentState)
                is StatsContract.Intent.RefreshCompleted -> handleRefreshCompleted(currentState)

                // === 时间范围操作 ===
                is StatsContract.Intent.SelectTimeRange -> handleSelectTimeRange(intent, currentState)
                is StatsContract.Intent.SetCustomDateRange -> handleSetCustomDateRange(intent, currentState)

                // === Tab和视图切换 ===
                is StatsContract.Intent.SelectTab -> handleSelectTab(intent, currentState)
                is StatsContract.Intent.ShowStatsDetail -> handleShowStatsDetail(intent, currentState)
                is StatsContract.Intent.HideStatsDetail -> handleHideStatsDetail(currentState)

                // === 图表操作 ===
                is StatsContract.Intent.ChangeChartType -> handleChangeChartType(intent, currentState)
                is StatsContract.Intent.ToggleComparison -> handleToggleComparison(currentState)
                is StatsContract.Intent.ToggleChartAnimation -> handleToggleChartAnimation(currentState)

                // === 筛选和排序 ===
                is StatsContract.Intent.ChangeSortOption -> handleChangeSortOption(intent, currentState)
                is StatsContract.Intent.ChangeIntensityFilter -> handleChangeIntensityFilter(intent, currentState)
                is StatsContract.Intent.ToggleHighQualityFilter -> handleToggleHighQualityFilter(currentState)
                is StatsContract.Intent.ClearAllFilters -> handleClearAllFilters(currentState)

                // === UI交互 ===
                is StatsContract.Intent.ShowDatePicker -> handleShowDatePicker(currentState)
                is StatsContract.Intent.HideDatePicker -> handleHideDatePicker(currentState)
                is StatsContract.Intent.ShowDailyStatsDetail -> handleShowDailyStatsDetail(intent, currentState)

                // === 导航 ===
                is StatsContract.Intent.NavigateBack -> handleNavigateBack(currentState)
                is StatsContract.Intent.NavigateToSessionDetail -> handleNavigateToSessionDetail(
                    intent,
                    currentState,
                )

                is StatsContract.Intent.NavigateToExerciseStats -> handleNavigateToExerciseStats(
                    intent,
                    currentState,
                )

                // === 错误处理 ===
                is StatsContract.Intent.ClearError -> handleClearError(currentState)
                is StatsContract.Intent.ShowError -> handleShowError(intent, currentState)

                // === AI分析相关 ===
                is StatsContract.Intent.RequestAiAnalysis -> handleRequestAiAnalysis(intent, currentState)
                is StatsContract.Intent.AiAnalysisReceived -> handleAiAnalysisReceived(intent, currentState)
                is StatsContract.Intent.AiAnalysisError -> handleAiAnalysisError(intent, currentState)
                is StatsContract.Intent.ClearAiAnalysis -> handleClearAiAnalysis(currentState)

                // === 内部Intent ===
                is StatsContract.Intent.StatsLoaded -> handleStatsLoaded(intent, currentState)
                is StatsContract.Intent.StatsLoadError -> handleStatsLoadError(intent, currentState)
                is StatsContract.Intent.TrendCalculated -> handleTrendCalculated(intent, currentState)
                is StatsContract.Intent.ChartDataUpdated -> handleChartDataUpdated(intent, currentState)

                // === 数据操作 ===
                is StatsContract.Intent.RecalculateStats -> handleRecalculateStats(intent, currentState)
                is StatsContract.Intent.ExportStats -> handleExportStats(currentState)
                is StatsContract.Intent.ImportStats -> handleImportStats(currentState)

                // === UI操作 ===
                is StatsContract.Intent.ShowMoreOptions -> handleShowMoreOptions(currentState)
                is StatsContract.Intent.NavigateToStartWorkout -> handleNavigateToStartWorkout(currentState)
            }
        }

        // ==================== 数据加载相关处理器 ====================

        private fun handleLoadStats(
            intent: StatsContract.Intent.LoadStats,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                isLoading = true,
                error = null,
                currentRange = intent.timeRange,
                customStartDate = intent.startDate,
                customEndDate = intent.endDate,
            )

            val effect = StatsContract.Effect.LoadStatsData(
                timeRange = intent.timeRange,
                startDate = intent.startDate,
                endDate = intent.endDate,
                includeComparison = state.showComparison,
            )

            return ReduceResult(newState, listOf(effect))
        }

        private fun handleRefreshStats(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                isRefreshing = true,
                error = null,
            )

            val effect = StatsContract.Effect.LoadStatsData(
                timeRange = state.currentRange,
                startDate = state.customStartDate,
                endDate = state.customEndDate,
                includeComparison = state.showComparison,
            )

            return ReduceResult(newState, listOf(effect))
        }

        private fun handleRefreshCompleted(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                isRefreshing = false,
                lastRefreshTime = System.currentTimeMillis(),
            )

            return ReduceResult(newState, emptyList())
        }

        // ==================== 时间范围操作处理器 ====================

        private fun handleSelectTimeRange(
            intent: StatsContract.Intent.SelectTimeRange,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            if (intent.timeRange == state.currentRange) {
                return ReduceResult(state, emptyList())
            }

            val newState = state.copy(
                currentRange = intent.timeRange,
                customStartDate = null,
                customEndDate = null,
                isLoading = true,
                error = null,
            )

            val effects = listOf(
                StatsContract.Effect.LoadStatsData(
                    timeRange = intent.timeRange,
                    includeComparison = state.showComparison,
                ),
                StatsContract.Effect.HapticFeedback,
            )

            return ReduceResult(newState, effects)
        }

        private fun handleSetCustomDateRange(
            intent: StatsContract.Intent.SetCustomDateRange,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                currentRange = TimeRange.CUSTOM,
                customStartDate = intent.startDate,
                customEndDate = intent.endDate,
                showDatePicker = false,
                isLoading = true,
                error = null,
            )

            val effect = StatsContract.Effect.LoadStatsData(
                timeRange = TimeRange.CUSTOM,
                startDate = intent.startDate,
                endDate = intent.endDate,
                includeComparison = state.showComparison,
            )

            return ReduceResult(newState, listOf(effect))
        }

        // ==================== Tab和视图切换处理器 ====================

        private fun handleSelectTab(
            intent: StatsContract.Intent.SelectTab,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            if (intent.tab == state.selectedTab) {
                return ReduceResult(state, emptyList())
            }

            val newState = state.copy(
                selectedTab = intent.tab,
                showStatsDetail = false,
                selectedDetailType = null,
            )

            val effects = mutableListOf<StatsContract.Effect>()
            effects.add(StatsContract.Effect.HapticFeedback)

            // 如果切换到趋势或分析tab，可能需要更新图表数据
            if (intent.tab == StatsContract.StatsTab.TRENDS || intent.tab == StatsContract.StatsTab.ANALYSIS) {
                effects.add(StatsContract.Effect.UpdateChartData(state.chartData))
            }

            return ReduceResult(newState, effects)
        }

        private fun handleShowStatsDetail(
            intent: StatsContract.Intent.ShowStatsDetail,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                showStatsDetail = true,
                selectedDetailType = intent.detailType,
            )

            return ReduceResult(newState, listOf(StatsContract.Effect.HapticFeedback))
        }

        private fun handleHideStatsDetail(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                showStatsDetail = false,
                selectedDetailType = null,
            )

            return ReduceResult(newState, emptyList())
        }

        // ==================== 图表操作处理器 ====================

        private fun handleChangeChartType(
            intent: StatsContract.Intent.ChangeChartType,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            if (intent.chartType == state.chartType) {
                return ReduceResult(state, emptyList())
            }

            val newState = state.copy(chartType = intent.chartType)

            val effects = listOf(
                StatsContract.Effect.AnimateChart(intent.chartType),
                StatsContract.Effect.UpdateChartData(state.chartData),
                StatsContract.Effect.HapticFeedback,
            )

            return ReduceResult(newState, effects)
        }

        private fun handleToggleComparison(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newShowComparison = !state.showComparison
            val newState = state.copy(showComparison = newShowComparison)

            val effects = mutableListOf<StatsContract.Effect>()
            effects.add(StatsContract.Effect.HapticFeedback)

            // 如果开启对比且没有对比数据，需要加载
            if (newShowComparison && state.comparisonStats == null) {
                effects.add(
                    StatsContract.Effect.LoadStatsData(
                        timeRange = state.currentRange,
                        startDate = state.customStartDate,
                        endDate = state.customEndDate,
                        includeComparison = true,
                    ),
                )
            }

            return ReduceResult(newState, effects)
        }

        private fun handleToggleChartAnimation(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(animateCharts = !state.animateCharts)
            return ReduceResult(newState, listOf(StatsContract.Effect.HapticFeedback))
        }

        // ==================== 筛选和排序处理器 ====================

        private fun handleChangeSortOption(
            intent: StatsContract.Intent.ChangeSortOption,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            if (intent.sortBy == state.sortBy) {
                return ReduceResult(state, emptyList())
            }

            val newState = state.copy(sortBy = intent.sortBy)
            return ReduceResult(newState, listOf(StatsContract.Effect.HapticFeedback))
        }

        private fun handleChangeIntensityFilter(
            intent: StatsContract.Intent.ChangeIntensityFilter,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            if (intent.filter == state.filterIntensity) {
                return ReduceResult(state, emptyList())
            }

            val newState = state.copy(filterIntensity = intent.filter)
            return ReduceResult(newState, listOf(StatsContract.Effect.HapticFeedback))
        }

        private fun handleToggleHighQualityFilter(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(showOnlyHighQuality = !state.showOnlyHighQuality)
            return ReduceResult(newState, listOf(StatsContract.Effect.HapticFeedback))
        }

        private fun handleClearAllFilters(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                sortBy = StatsContract.SortOption.DATE_DESC,
                filterIntensity = StatsContract.IntensityFilter.ALL,
                showOnlyHighQuality = false,
            )

            val effect = StatsContract.Effect.ShowToast(
                com.example.gymbro.core.ui.text.UiText.DynamicString("已清除所有筛选条件"),
            )

            return ReduceResult(newState, listOf(effect))
        }

        // ==================== UI交互处理器 ====================

        private fun handleShowDatePicker(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(showDatePicker = true)
            return ReduceResult(newState, emptyList())
        }

        private fun handleHideDatePicker(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(showDatePicker = false)
            return ReduceResult(newState, emptyList())
        }

        private fun handleShowDailyStatsDetail(
            intent: StatsContract.Intent.ShowDailyStatsDetail,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val effect = StatsContract.Effect.ShowBottomSheet(
                StatsContract.BottomSheetContent.DAILY_STATS_DETAIL,
            )

            return ReduceResult(state, listOf(effect))
        }

        // ==================== 导航处理器 ====================

        private fun handleNavigateBack(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            return ReduceResult(state, listOf(StatsContract.Effect.NavigateBack))
        }

        private fun handleNavigateToSessionDetail(
            intent: StatsContract.Intent.NavigateToSessionDetail,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val effect = StatsContract.Effect.NavigateToSessionDetail(intent.sessionId)
            return ReduceResult(state, listOf(effect))
        }

        private fun handleNavigateToExerciseStats(
            intent: StatsContract.Intent.NavigateToExerciseStats,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val effect = StatsContract.Effect.NavigateToExerciseStats(intent.exerciseId)
            return ReduceResult(state, listOf(effect))
        }

        // ==================== 错误处理器 ====================

        private fun handleClearError(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(error = null)
            return ReduceResult(newState, emptyList())
        }

        private fun handleShowError(
            intent: StatsContract.Intent.ShowError,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                error = intent.error,
                isLoading = false,
                isRefreshing = false,
            )

            return ReduceResult(newState, emptyList())
        }

        // ==================== 内部Intent处理器 ====================

        private fun handleStatsLoaded(
            intent: StatsContract.Intent.StatsLoaded,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                stats = intent.stats,
                dailyStats = intent.dailyStats.toImmutableList(),
                comparisonStats = intent.comparisonStats,
                isLoading = false,
                isRefreshing = false,
                error = null,
                lastRefreshTime = System.currentTimeMillis(),
                preloadedRanges = state.preloadedRanges + state.currentRange,
            )

            return ReduceResult(newState, emptyList())
        }

        private fun handleStatsLoadError(
            intent: StatsContract.Intent.StatsLoadError,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                error = intent.error,
                isLoading = false,
                isRefreshing = false,
            )

            return ReduceResult(newState, emptyList())
        }

        private fun handleTrendCalculated(
            intent: StatsContract.Intent.TrendCalculated,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(progressTrend = intent.trend)
            return ReduceResult(newState, emptyList())
        }

        private fun handleChartDataUpdated(
            intent: StatsContract.Intent.ChartDataUpdated,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(chartData = intent.chartData)
            return ReduceResult(newState, emptyList())
        }

        // ==================== 数据操作处理器 ====================

        private fun handleRecalculateStats(
            intent: StatsContract.Intent.RecalculateStats,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(isLoading = true, error = null)

            val effect = StatsContract.Effect.LoadStatsData(
                timeRange = TimeRange.CUSTOM,
                startDate = intent.startDate,
                endDate = intent.endDate,
                includeComparison = false,
            )

            return ReduceResult(newState, listOf(effect))
        }

        private fun handleExportStats(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            if (state.dailyStats.isEmpty()) {
                val effect = StatsContract.Effect.ShowToast(
                    com.example.gymbro.core.ui.text.UiText.DynamicString("没有可导出的数据"),
                )
                return ReduceResult(state, listOf(effect))
            }

            val effect = StatsContract.Effect.ExportStatsData(
                stats = state.dailyStats,
                format = StatsContract.ExportFormat.CSV,
            )

            return ReduceResult(state, listOf(effect))
        }

        private fun handleImportStats(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val effect = StatsContract.Effect.ShowBottomSheet(
                StatsContract.BottomSheetContent.EXPORT_OPTIONS,
            )

            return ReduceResult(state, listOf(effect))
        }

        // ==================== UI操作处理器 ====================

        private fun handleShowMoreOptions(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val effect = StatsContract.Effect.ShowBottomSheet(
                StatsContract.BottomSheetContent.MORE_OPTIONS,
            )

            return ReduceResult(state, listOf(effect))
        }

        private fun handleNavigateToStartWorkout(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val effect = StatsContract.Effect.NavigateToStartWorkout

            return ReduceResult(state, listOf(effect))
        }

        // ==================== AI分析相关处理器 ====================

        /**
         * 处理AI分析请求
         *
         * 🔥 【硬指标】检查是否已经分析过相同数据，防止重复发送
         */
        private fun handleRequestAiAnalysis(
            intent: StatsContract.Intent.RequestAiAnalysis,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            // 🔥 【硬指标】生成分析请求的唯一标识
            val requestKey = "${intent.timeRange.name}_${intent.dailyStats.size}_${intent.model}"

            // 检查是否已经在分析中或已完成
            if (state.isAnalyzing) {
                return ReduceResult(state, emptyList())
            }

            val newState = state.copy(
                isAnalyzing = true,
                analysisError = null,
            )

            val effect = StatsContract.Effect.StartAiAnalysis(
                dailyStats = intent.dailyStats,
                timeRange = intent.timeRange,
                model = intent.model,
            )

            return ReduceResult(newState, listOf(effect))
        }

        /**
         * 处理AI分析响应接收
         *
         * 🔥 【硬指标】记录已完成的分析ID，防止重复处理
         */
        private fun handleAiAnalysisReceived(
            intent: StatsContract.Intent.AiAnalysisReceived,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                isAnalyzing = false,
                analysisId = intent.analysisId,
                analysisError = null,
                completedAnalysisIds = state.completedAnalysisIds + intent.analysisId,
            )

            return ReduceResult(newState, emptyList())
        }

        /**
         * 处理AI分析错误
         */
        private fun handleAiAnalysisError(
            intent: StatsContract.Intent.AiAnalysisError,
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                isAnalyzing = false,
                analysisError = intent.error,
                analysisId = null,
            )

            return ReduceResult(newState, emptyList())
        }

        /**
         * 清理AI分析状态
         */
        private fun handleClearAiAnalysis(
            state: StatsContract.State,
        ): ReduceResult<StatsContract.State, StatsContract.Effect> {
            val newState = state.copy(
                isAnalyzing = false,
                analysisId = null,
                analysisError = null,
            )

            return ReduceResult(newState, emptyList())
        }
    }
