package com.example.gymbro.features.coach.aicoach.internal.components.input

import android.content.Context
import androidx.compose.runtime.*
import com.example.gymbro.features.coach.shared.utils.GlobalPerformanceConfig
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 输入性能优化器 - MVI 2.0架构标准实现
 *
 * 🔥 【架构重构】完全重构为符合MVI 2.0架构标准的组件：
 * 1. 使用Hilt依赖注入替代object单例
 * 2. 清晰的职责边界：只负责输入防抖和性能优化
 * 3. 正确的协程作用域管理和状态管理
 * 4. 与GlobalPerformanceConfig集成
 *
 * 职责范围：
 * - 输入防抖处理（debounce）
 * - 大文本性能优化建议
 * - 自适应延迟计算
 * - 内存安全的协程管理
 */
@Singleton
class InputOptimizer
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
    ) {

        // 🔥 使用GlobalPerformanceConfig中的配置
        private val largeTextThreshold = GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD

        // 内部状态管理 - 用于调试和监控
        private val _isOptimizing = MutableStateFlow(false)
        val isOptimizing: StateFlow<Boolean> = _isOptimizing.asStateFlow()

        /**
         * 智能防抖输入处理器 - MVI 2.0标准实现
         *
         * 特点：
         * - 类型安全的协程管理
         * - 自动清理debounce job
         * - 内存泄漏防护
         * - 自适应延迟策略
         */
        @Stable
        @Composable
        fun rememberDebouncedInputHandler(
            baseDelayMs: Long = GlobalPerformanceConfig.Input.BASE_DEBOUNCE_MS,
            onValueChange: (String) -> Unit,
        ): (String) -> Unit {
            val currentOnValueChange by rememberUpdatedState(onValueChange)
            val coroutineScope = rememberCoroutineScope()

            // 🔥 正确的debounce job管理
            var debounceJob by remember { mutableStateOf<Job?>(null) }

            // 🔥 清理Effect：组件销毁时取消job
            DisposableEffect(coroutineScope) {
                onDispose {
                    debounceJob?.cancel()
                }
            }

            return remember(coroutineScope, baseDelayMs) {
                {
                        value: String ->
                    // 取消之前的debounce任务
                    debounceJob?.cancel()

                    if (value.length < GlobalPerformanceConfig.Input.SHORT_TEXT_THRESHOLD) {
                        // 短文本直接处理，无需防抖
                        currentOnValueChange(value)
                    } else {
                        // 长文本使用自适应防抖
                        debounceJob = coroutineScope.launch {
                            delay(calculateAdaptiveDelay(value.length, baseDelayMs))
                            currentOnValueChange(value)
                        }
                    }
                }
            }
        }

        /**
         * 计算自适应延迟时间 - 纯函数实现
         *
         * 根据文本长度动态调整防抖延迟：
         * - 短文本：快速响应
         * - 中等文本：标准延迟
         * - 长文本：较长延迟，减少性能开销
         */
        private fun calculateAdaptiveDelay(textLength: Int, baseDelay: Long): Long {
            return when {
                textLength < GlobalPerformanceConfig.Input.SHORT_TEXT_THRESHOLD -> baseDelay / 4
                textLength < GlobalPerformanceConfig.Input.MEDIUM_TEXT_THRESHOLD -> baseDelay
                textLength < GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD ->
                    (baseDelay * GlobalPerformanceConfig.Input.ADAPTIVE_DELAY_MULTIPLIER_MEDIUM).toLong()

                else ->
                    (baseDelay * GlobalPerformanceConfig.Input.ADAPTIVE_DELAY_MULTIPLIER_LARGE).toLong()
            }
        }

        /**
         * 大文本优化器 - 性能建议计算器
         *
         * 提供基于文本长度的UI优化建议，如：
         * - 建议的最大显示行数
         * - 适合的防抖延迟
         * - 是否需要虚拟化等
         */
        @Stable
        @Composable
        fun rememberTextOptimizer(
            text: String,
            threshold: Int = largeTextThreshold,
        ): TextOptimizer {
            // 🔥 性能优化：只在阈值状态变化时重新计算
            return remember(text.length > threshold) {
                _isOptimizing.value = text.length > threshold

                TextOptimizer(
                    isLargeText = text.length > threshold,
                    textLength = text.length,
                    threshold = threshold,
                )
            }
        }

        /**
         * 文本性能优化器数据类
         *
         * 🔥 【架构改进】从内部类提升为独立数据类，提高可测试性
         * 包含所有与文本性能优化相关的计算逻辑
         */
        @Stable
        data class TextOptimizer(
            val isLargeText: Boolean,
            val textLength: Int,
            val threshold: Int,
        ) {
            /**
             * 建议的最大显示行数
             *
             * 大文本时减少显示行数以提高滚动性能
             */
            val suggestedMaxLines: Int by lazy {
                if (isLargeText) {
                    kotlin.math.max(4, 20 - (textLength / GlobalPerformanceConfig.Input.MEDIUM_TEXT_THRESHOLD))
                } else {
                    8
                }
            }

            /**
             * 建议的防抖延迟时间
             *
             * 🔥 基于GlobalPerformanceConfig的搜索防抖配置
             * 根据文本长度自适应调整
             */
            val suggestedDebounceMs: Long by lazy {
                val baseDebounce = GlobalPerformanceConfig.SEARCH.DEBOUNCE_MS
                when {
                    textLength < 100 -> baseDebounce / 2
                    textLength < GlobalPerformanceConfig.Input.MEDIUM_TEXT_THRESHOLD -> baseDebounce
                    textLength < GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD -> baseDebounce * 2
                    else -> baseDebounce * 3
                }
            }

            /**
             * 是否需要启用虚拟化
             *
             * 超大文本时建议使用LazyColumn等虚拟化组件
             */
            val shouldUseVirtualization: Boolean by lazy {
                textLength > GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD * 2
            }

            /**
             * 性能等级评估
             *
             * 用于UI展示性能提示和优化建议
             */
            val performanceLevel: PerformanceLevel by lazy {
                when {
                    textLength < GlobalPerformanceConfig.Input.SHORT_TEXT_THRESHOLD -> PerformanceLevel.OPTIMAL
                    textLength < GlobalPerformanceConfig.Input.MEDIUM_TEXT_THRESHOLD -> PerformanceLevel.GOOD
                    textLength < GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD -> PerformanceLevel.MODERATE
                    else -> PerformanceLevel.NEEDS_OPTIMIZATION
                }
            }
        }

        /**
         * 性能等级枚举
         */
        enum class PerformanceLevel {
            OPTIMAL, // 最佳性能
            GOOD, // 良好性能
            MODERATE, // 中等性能
            NEEDS_OPTIMIZATION, // 需要优化
        }

        /**
         * 获取性能监控状态
         *
         * 供外部组件查询当前优化状态
         */
        fun getCurrentOptimizationState(): Boolean = _isOptimizing.value

        /**
         * 手动触发性能分析
         *
         * 供外部组件主动触发性能分析和优化建议
         */
        fun analyzeTextPerformance(text: String): TextOptimizer {
            return TextOptimizer(
                isLargeText = text.length > largeTextThreshold,
                textLength = text.length,
                threshold = largeTextThreshold,
            )
        }
    }

// ========================================
// 使用说明和最佳实践
// ========================================

/**
 * InputOptimizer使用示例
 *
 * 在Composable中的正确使用方式：
 *
 * ```kotlin
 * @Composable
 * fun ChatInput(
 *     inputOptimizer: InputOptimizer = hiltViewModel<SomeViewModel>().inputOptimizer
 * ) {
 *     var text by remember { mutableStateOf("") }
 *
 *     // 1. 使用防抖处理器
 *     val debouncedHandler = inputOptimizer.rememberDebouncedInputHandler(
 *         onValueChange = { newText ->
 *             // 这里会被防抖调用
 *             viewModel.updateInputText(newText)
 *         }
 *     )
 *
 *     // 2. 使用文本优化器
 *     val textOptimizer = inputOptimizer.rememberTextOptimizer(text)
 *
 *     TextField(
 *         value = text,
 *         onValueChange = { newText ->
 *             text = newText
 *             debouncedHandler(newText) // 防抖处理
 *         },
 *         maxLines = textOptimizer.suggestedMaxLines // 使用优化建议
 *     )
 *
 *     // 3. 性能提示UI
 *     if (textOptimizer.performanceLevel == PerformanceLevel.NEEDS_OPTIMIZATION) {
 *         Text("建议使用语音输入以获得更好性能")
 *     }
 * }
 * ```
 */

/**
 * 架构重构总结
 *
 * ✅ 解决的问题：
 * 1. 编译错误：debounceJob未定义、null引用等
 * 2. 架构违规：object单例 -> Hilt注入的Singleton类
 * 3. 类型安全：正确的lambda类型和协程管理
 * 4. 职责分离：明确的性能优化职责边界
 * 5. 内存安全：DisposableEffect自动清理协程
 *
 * 🎯 MVI 2.0合规性：
 * - ✅ 使用Hilt依赖注入
 * - ✅ 清晰的职责边界
 * - ✅ 无状态Composable设计
 * - ✅ 正确的协程生命周期管理
 * - ✅ 符合项目的GlobalPerformanceConfig规范
 *
 * 📈 质量改进：
 * - 完整的类型安全
 * - 性能监控能力
 * - 可测试的纯函数设计
 * - 详细的文档和使用示例
 */
