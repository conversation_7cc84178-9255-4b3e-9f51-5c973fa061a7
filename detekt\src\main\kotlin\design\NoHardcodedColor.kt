package com.example.gymbro.buildlogic.detekt.design

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.KtCallExpression
import org.jetbrains.kotlin.psi.KtDotQualifiedExpression
import org.jetbrains.kotlin.psi.KtElement
import org.jetbrains.kotlin.psi.KtStringTemplateExpression

/**
 * GymBro 自定义规则：禁止硬编码颜色值。
 *
 * 目的：强制使用 Design System 的颜色 Tokens，确保主题一致性。
 * 这有助于：
 * 1. 保持颜色主题的一致性
 * 2. 支持深色/浅色主题切换
 * 3. 便于品牌颜色的统一管理
 * 4. 提高可访问性
 *
 * 检测模式：
 * - Color(0xFF123456)
 * - Color.Red
 * - Color(red = 255, green = 0, blue = 0)
 * - "#FF0000".toColor()
 */
class NoHardcodedColor(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "禁止使用硬编码的颜色值，请使用 Design System 的颜色 Tokens。",
        Debt.FIVE_MINS
    )

    private val allowedColorPatterns = setOf(
        "Tokens.Color.",
        "Tokens.Colors.",
        "designSystem.color.",
        "designSystem.colors.",
        "theme.color.",
        "theme.colors.",
        "MaterialTheme.colors.",
        "MaterialTheme.colorScheme.",
        "LocalColors.current.",
        "Color.Transparent",
        "Color.Unspecified"
    )

    private val hardcodedColorPatterns = listOf(
        // Hex colors: Color(0xFF123456)
        Regex("""Color\s*\(\s*0x[0-9A-Fa-f]{8}\s*\)"""),
        // RGB colors: Color(red = 255, green = 0, blue = 0)
        Regex("""Color\s*\(\s*red\s*=\s*\d+"""),
        // String hex colors: "#FF0000".toColor()
        Regex(""""#[0-9A-Fa-f]{6,8}"\.toColor\(\)"""),
        // Predefined Color constants: Color.Red, Color.Blue, etc.
        Regex("""Color\.[A-Z][a-zA-Z]+""")
    )

    override fun visitCallExpression(expression: KtCallExpression) {
        super.visitCallExpression(expression)

        val callText = expression.text

        // 检查硬编码颜色模式
        for (pattern in hardcodedColorPatterns) {
            val matches = pattern.findAll(callText)
            for (match in matches) {
                if (!isUsingDesignTokens(callText)) {
                    reportHardcodedColor(expression, match.value)
                }
            }
        }
    }

    override fun visitDotQualifiedExpression(expression: KtDotQualifiedExpression) {
        super.visitDotQualifiedExpression(expression)

        val text = expression.text

        // 检查 Color.* 静态颜色引用
        if (text.matches(Regex("""Color\.[A-Z][a-zA-Z]+"""))) {
            if (!isAllowedColorReference(text) && !isUsingDesignTokens(text)) {
                reportHardcodedColor(expression, text)
            }
        }
    }

    override fun visitStringTemplateExpression(expression: KtStringTemplateExpression) {
        super.visitStringTemplateExpression(expression)

        val text = expression.text

        // 检查字符串形式的十六进制颜色
        if (text.matches(Regex(""""#[0-9A-Fa-f]{6,8}""""))) {
            // 检查是否紧跟 .toColor() 或类似的转换方法
            val parent = expression.parent
            if (parent is KtDotQualifiedExpression && parent.text.contains("toColor")) {
                reportHardcodedColor(expression, text)
            }
        }
    }

    private fun reportHardcodedColor(element: KtElement, colorValue: String) {
        report(
            CodeSmell(
                issue,
                Entity.from(element),
                "发现硬编码颜色值 '$colorValue'。" +
                    "请使用 Design System 的颜色 Tokens。" +
                    "\n建议的替换：" +
                    "\n- 主要颜色：Tokens.Color.primary" +
                    "\n- 次要颜色：Tokens.Color.secondary" +
                    "\n- 背景颜色：Tokens.Color.background" +
                    "\n- 表面颜色：Tokens.Color.surface" +
                    "\n- 文本颜色：Tokens.Color.onSurface" +
                    "\n- 错误颜色：Tokens.Color.error" +
                    "\n- 成功颜色：Tokens.Color.success" +
                    "\n\n如果需要透明度，使用：Tokens.Color.primary.copy(alpha = 0.5f)"
            )
        )
    }

    private fun isUsingDesignTokens(text: String): Boolean {
        return allowedColorPatterns.any { pattern ->
            text.contains(pattern)
        }
    }

    private fun isAllowedColorReference(text: String): Boolean {
        // 允许的 Color 静态引用
        val allowedColorReferences = setOf(
            "Color.Transparent",
            "Color.Unspecified"
        )
        return allowedColorReferences.contains(text)
    }
}
