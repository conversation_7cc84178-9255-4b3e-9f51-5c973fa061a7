package com.example.gymbro.domain.exercise.model

import com.example.gymbro.shared.models.exercise.Equipment
import java.util.*
import kotlinx.serialization.Serializable

/**
 * 动作器械关系领域模型
 * 表示动作与器械的多对多关系，支持同一动作的多种器械版本
 */
@Serializable
data class ExerciseEquipment(
    val id: String = UUID.randomUUID().toString(),
    val exerciseId: String,
    val equipment: Equipment,
    val isRequired: Boolean = false, // 是否必需
    val importance: EquipmentImportance = EquipmentImportance.OPTIONAL, // 重要程度
    val alternatives: List<Equipment> = emptyList(), // 替代器械
    val notes: String? = null, // 使用说明
    val order: Int = 0, // 显示顺序
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
) {
    /**
     * 获取器械类别
     * @return 器械所属类别
     */
    fun getCategory(): EquipmentCategory = when (equipment) {
        Equipment.BARBELL, Equipment.DUMBBELL, Equipment.KETTLEBELL -> EquipmentCategory.FREE_WEIGHTS
        Equipment.MACHINE, Equipment.CABLES, Equipment.CABLE -> EquipmentCategory.MACHINES
        Equipment.RESISTANCE_BAND, Equipment.FOAM_ROLLER, Equipment.MEDICINE_BALL,
        Equipment.STABILITY_BALL, Equipment.BATTLE_ROPES, Equipment.BATTLE_ROPE, Equipment.TRX,
        -> EquipmentCategory.ACCESSORIES

        Equipment.NONE -> EquipmentCategory.BODYWEIGHT
        else -> EquipmentCategory.OTHER
    }

    /**
     * 判断是否为自由重量器械
     * @return 是否为自由重量
     */
    fun isFreeWeight(): Boolean = getCategory() == EquipmentCategory.FREE_WEIGHTS

    /**
     * 判断是否为固定器械
     * @return 是否为固定器械
     */
    fun isMachine(): Boolean = equipment == Equipment.MACHINE || equipment == Equipment.CABLES

    /**
     * 判断是否为有氧器械
     * @return 是否为有氧器械
     */
    fun isCardioEquipment(): Boolean = equipment in listOf(
        Equipment.TREADMILL,
        Equipment.BIKE,
        Equipment.STATIONARY_BIKE,
        Equipment.ELLIPTICAL,
        Equipment.ROWER,
        Equipment.ROWING_MACHINE,
    )

    /**
     * 获取完整描述（包含重要性和备注）
     * @return 格式化的描述字符串
     */
    fun getFullDescription(): String {
        val baseDesc = equipment.getDisplayName()
        val importanceDesc = if (isRequired) "(必需)" else "(${importance.displayName})"
        val noteDesc = notes?.let { " - $it" } ?: ""
        return "$baseDesc$importanceDesc$noteDesc"
    }

    /**
     * 获取替代器械的显示文本
     * @return 替代器械的描述
     */
    fun getAlternativesText(): String = when {
        alternatives.isEmpty() -> "无替代器械"
        alternatives.size == 1 -> "可用 ${alternatives.first().getDisplayName()} 替代"
        else -> "可用 ${alternatives.joinToString("、") { it.getDisplayName() }} 替代"
    }

    companion object {
        /**
         * 创建必需器械关系
         * @param exerciseId 动作ID
         * @param equipment 器械
         * @param alternatives 替代器械列表
         * @param notes 使用说明
         * @return 必需器械关系对象
         */
        fun createRequired(
            exerciseId: String,
            equipment: Equipment,
            alternatives: List<Equipment> = emptyList(),
            notes: String? = null,
        ): ExerciseEquipment = ExerciseEquipment(
            exerciseId = exerciseId,
            equipment = equipment,
            isRequired = true,
            importance = EquipmentImportance.REQUIRED,
            alternatives = alternatives,
            notes = notes,
            order = 0,
        )

        /**
         * 创建推荐器械关系
         * @param exerciseId 动作ID
         * @param equipment 器械
         * @param alternatives 替代器械列表
         * @param notes 使用说明
         * @return 推荐器械关系对象
         */
        fun createRecommended(
            exerciseId: String,
            equipment: Equipment,
            alternatives: List<Equipment> = emptyList(),
            notes: String? = null,
        ): ExerciseEquipment = ExerciseEquipment(
            exerciseId = exerciseId,
            equipment = equipment,
            isRequired = false,
            importance = EquipmentImportance.RECOMMENDED,
            alternatives = alternatives,
            notes = notes,
            order = 1,
        )

        /**
         * 创建可选器械关系
         * @param exerciseId 动作ID
         * @param equipment 器械
         * @param alternatives 替代器械列表
         * @param notes 使用说明
         * @return 可选器械关系对象
         */
        fun createOptional(
            exerciseId: String,
            equipment: Equipment,
            alternatives: List<Equipment> = emptyList(),
            notes: String? = null,
        ): ExerciseEquipment = ExerciseEquipment(
            exerciseId = exerciseId,
            equipment = equipment,
            isRequired = false,
            importance = EquipmentImportance.OPTIONAL,
            alternatives = alternatives,
            notes = notes,
            order = 2,
        )

        /**
         * 为动作批量创建器械关系
         * @param exerciseId 动作ID
         * @param requiredEquipment 必需器械列表
         * @param optionalEquipment 可选器械列表
         * @return 器械关系列表
         */
        fun createEquipmentRelations(
            exerciseId: String,
            requiredEquipment: List<Equipment>,
            optionalEquipment: List<Equipment> = emptyList(),
        ): List<ExerciseEquipment> {
            val result = mutableListOf<ExerciseEquipment>()

            // 添加必需器械
            requiredEquipment.forEachIndexed { index, equipment ->
                result.add(
                    ExerciseEquipment(
                        exerciseId = exerciseId,
                        equipment = equipment,
                        isRequired = true,
                        importance = EquipmentImportance.REQUIRED,
                        order = index,
                    ),
                )
            }

            // 添加可选器械
            optionalEquipment.forEachIndexed { index, equipment ->
                result.add(
                    ExerciseEquipment(
                        exerciseId = exerciseId,
                        equipment = equipment,
                        isRequired = false,
                        importance = EquipmentImportance.OPTIONAL,
                        order = requiredEquipment.size + index,
                    ),
                )
            }

            return result
        }
    }
}

/**
 * 器械类别枚举
 */
@Serializable
enum class EquipmentCategory(
    val code: String,
    val displayName: String,
) {
    FREE_WEIGHTS("free_weights", "自由重量"),
    MACHINES("machines", "固定器械"),
    ACCESSORIES("accessories", "辅助器械"),
    BODYWEIGHT("bodyweight", "自重训练"),
    CARDIO("cardio", "有氧器械"),
    OTHER("other", "其他"),
    ;

    companion object {
        fun fromCode(code: String?): EquipmentCategory? = values().find { it.code == code }
    }
}

/**
 * 器械重要程度枚举
 */
@Serializable
enum class EquipmentImportance(
    val code: String,
    val displayName: String,
    val level: Int,
) {
    REQUIRED("required", "必需", 5),
    RECOMMENDED("recommended", "推荐", 4),
    HELPFUL("helpful", "有帮助", 3),
    OPTIONAL("optional", "可选", 2),
    ALTERNATIVE("alternative", "替代", 1),
    ;

    companion object {
        fun fromCode(code: String?): EquipmentImportance? = values().find { it.code == code }
    }
}
