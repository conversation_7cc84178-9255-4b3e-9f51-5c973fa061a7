package com.example.gymbro.data.autosave

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.autosave.adapter.ProfileAutoSaveAdapter
import com.example.gymbro.data.autosave.adapter.WorkoutAutoSaveAdapter
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * 统一自动保存系统使用示例
 *
 * 本文件包含了各种使用场景的完整代码示例，包括：
 * 1. Profile模块集成示例
 * 2. Workout模块集成示例
 * 3. 自定义数据类型集成示例
 * 4. 错误处理和最佳实践
 * 5. 性能优化技巧
 */

// ================================
// 1. Profile模块集成示例
// ================================

/**
 * Profile模块ViewModel示例
 * 展示如何集成ProfileAutoSaveAdapter
 */
@HiltViewModel
class ProfileViewModelExample
    @Inject
    constructor(
        private val profileAdapter: ProfileAutoSaveAdapter,
        private val logger: Logger,
    ) : ViewModel() {
        data class ProfileState(
            val currentProfile: UserProfile? = null,
            val autoSaveEnabled: Boolean = false,
            val autoSaveSessionId: String? = null,
            val autoSavePaused: Boolean = false,
            val lastSaveTime: Long = 0L,
            val autoSaveError: String? = null,
            val isLoading: Boolean = false,
        )

        private val _state = MutableStateFlow(ProfileState())
        val state: StateFlow<ProfileState> = _state.asStateFlow()

        private var autoSaveSessionId: String? = null

        /**
         * 启动Profile自动保存
         */
        fun startAutoSave(userId: String) {
            viewModelScope.launch {
                _state.update { it.copy(isLoading = true) }

                try {
                    // 创建自动保存会话
                    val createResult = profileAdapter.createAutoSave(userId, viewModelScope)

                    when (createResult) {
                        is ModernResult.Success -> {
                            autoSaveSessionId = createResult.data

                            // 启动自动保存
                            profileAdapter.startAutoSave(createResult.data, userId)

                            _state.update {
                                it.copy(
                                    autoSaveEnabled = true,
                                    autoSaveSessionId = createResult.data,
                                    isLoading = false,
                                    autoSaveError = null,
                                )
                            }

                            logger.d("ProfileViewModelExample", "自动保存已启动: ${createResult.data}")
                        }

                        is ModernResult.Error -> {
                            _state.update {
                                it.copy(
                                    isLoading = false,
                                    autoSaveError = "启动失败: ${createResult.error}",
                                )
                            }
                            logger.e("ProfileViewModelExample", "启动自动保存失败", createResult.error.cause)
                        }

                        is ModernResult.Loading -> {
                            // 保持loading状态
                        }
                    }
                } catch (e: Exception) {
                    _state.update {
                        it.copy(
                            isLoading = false,
                            autoSaveError = "启动异常: ${e.message}",
                        )
                    }
                    logger.e(e, "启动自动保存异常")
                }
            }
        }

        /**
         * 更新Profile数据
         */
        fun updateProfile(profile: UserProfile) {
            // 更新本地状态
            _state.update { it.copy(currentProfile = profile) }

            // 触发自动保存（即时保存策略）
            autoSaveSessionId?.let { sessionId ->
                profileAdapter.updateProfile(sessionId, profile)
                logger.d("ProfileViewModelExample", "Profile数据已更新: ${profile.userId}")
            }
        }

        /**
         * 处理UI事件示例
         */
        fun onDisplayNameChanged(displayName: String) {
            val currentProfile = _state.value.currentProfile ?: return
            val updatedProfile = currentProfile.copy(displayName = displayName)
            updateProfile(updatedProfile)
        }

        fun onWeightChanged(weight: Float) {
            val currentProfile = _state.value.currentProfile ?: return
            val updatedProfile = currentProfile.copy(weight = weight)
            updateProfile(updatedProfile)
        }

        /**
         * 立即保存
         */
        fun saveNow() {
            autoSaveSessionId?.let { sessionId ->
                viewModelScope.launch {
                    val result = profileAdapter.saveNow(sessionId)
                    result.fold(
                        onSuccess = {
                            _state.update {
                                it.copy(
                                    lastSaveTime = System.currentTimeMillis(),
                                    autoSaveError = null,
                                )
                            }
                            logger.d("ProfileViewModelExample", "立即保存成功")
                        },
                        onFailure = { error ->
                            _state.update {
                                it.copy(autoSaveError = "保存失败: ${error.message}")
                            }
                            logger.e(error, "立即保存失败")
                        },
                    )
                }
            }
        }

        /**
         * 停止自动保存
         */
        fun stopAutoSave() {
            autoSaveSessionId?.let { sessionId ->
                viewModelScope.launch {
                    profileAdapter.stopAutoSave(sessionId)
                    autoSaveSessionId = null
                    _state.update {
                        it.copy(
                            autoSaveEnabled = false,
                            autoSaveSessionId = null,
                        )
                    }
                    logger.d("ProfileViewModelExample", "自动保存已停止")
                }
            }
        }

        override fun onCleared() {
            super.onCleared()
            stopAutoSave()
        }
    }

// ================================
// 2. Workout模块集成示例
// ================================

/**
 * Workout模块ViewModel示例
 * 展示如何集成WorkoutAutoSaveAdapter
 */
@HiltViewModel
class WorkoutViewModelExample
    @Inject
    constructor(
        private val workoutAdapter: WorkoutAutoSaveAdapter,
        private val logger: Logger,
    ) : ViewModel() {
        data class WorkoutState(
            val currentDraft: WorkoutTemplate? = null,
            val draftId: String? = null,
            val autoSaveEnabled: Boolean = false,
            val autoSaveSessionId: String? = null,
            val autoSavePaused: Boolean = false,
            val lastSaveTime: Long = 0L,
            val autoSaveError: String? = null,
            val hasUnsavedChanges: Boolean = false,
            val showRecoveryDialog: Boolean = false,
        )

        private val _state = MutableStateFlow(WorkoutState())
        val state: StateFlow<WorkoutState> = _state.asStateFlow()

        private var autoSaveSessionId: String? = null

        /**
         * 启动Workout自动保存
         */
        fun startAutoSave(draftId: String) {
            viewModelScope.launch {
                try {
                    // 创建自动保存会话（使用定时保存策略，3秒间隔）
                    val createResult = workoutAdapter.createAutoSave(draftId, viewModelScope)

                    when (createResult) {
                        is ModernResult.Success -> {
                            autoSaveSessionId = createResult.data

                            // 启动自动保存
                            workoutAdapter.startAutoSave(createResult.data, draftId)

                            _state.update {
                                it.copy(
                                    autoSaveEnabled = true,
                                    autoSaveSessionId = createResult.data,
                                    draftId = draftId,
                                    autoSaveError = null,
                                )
                            }

                            logger.d("WorkoutViewModelExample", "自动保存已启动: ${createResult.data}")
                        }

                        is ModernResult.Error -> {
                            _state.update {
                                it.copy(autoSaveError = "启动失败: ${createResult.error}")
                            }
                            logger.e("WorkoutViewModelExample", "启动自动保存失败", createResult.error.cause)
                        }

                        is ModernResult.Loading -> {
                            // 保持loading状态
                            logger.d("WorkoutViewModelExample", "正在创建自动保存会话...")
                        }
                    }
                } catch (e: Exception) {
                    _state.update {
                        it.copy(autoSaveError = "启动异常: ${e.message}")
                    }
                    logger.e(e, "启动自动保存异常")
                }
            }
        }

        /**
         * 更新WorkoutTemplate数据
         */
        fun updateDraft(draft: WorkoutTemplate) {
            // 更新本地状态
            _state.update {
                it.copy(
                    currentDraft = draft,
                    hasUnsavedChanges = true,
                )
            }

            // 触发自动保存（定时保存策略，3秒间隔）
            autoSaveSessionId?.let { sessionId ->
                workoutAdapter.updateTemplate(sessionId, draft)
                logger.d("WorkoutViewModelExample", "WorkoutTemplate数据已更新: ${draft.id}")
            }
        }

        /**
         * 暂停自动保存
         */
        fun pauseAutoSave() {
            autoSaveSessionId?.let { sessionId ->
                workoutAdapter.pauseAutoSave(sessionId)
                _state.update { it.copy(autoSavePaused = true) }
                logger.d("WorkoutViewModelExample", "自动保存已暂停")
            }
        }

        /**
         * 恢复自动保存
         */
        fun resumeAutoSave() {
            autoSaveSessionId?.let { sessionId ->
                workoutAdapter.resumeAutoSave(sessionId)
                _state.update { it.copy(autoSavePaused = false) }
                logger.d("WorkoutViewModelExample", "自动保存已恢复")
            }
        }

        /**
         * 恢复缓存数据
         */
        fun restoreFromCache() {
            autoSaveSessionId?.let { sessionId ->
                workoutAdapter.restoreFromCache(sessionId)
                _state.update {
                    it.copy(
                        showRecoveryDialog = false,
                        hasUnsavedChanges = false,
                    )
                }
                logger.d("WorkoutViewModelExample", "正在恢复缓存数据")
            }
        }

        /**
         * 丢弃缓存数据
         */
        fun discardCache() {
            autoSaveSessionId?.let { sessionId ->
                workoutAdapter.discardCache(sessionId)
                _state.update { it.copy(showRecoveryDialog = false) }
                logger.d("WorkoutViewModelExample", "缓存数据已丢弃")
            }
        }

        /**
         * 立即保存
         */
        fun saveNow() {
            autoSaveSessionId?.let { sessionId ->
                viewModelScope.launch {
                    val result = workoutAdapter.saveNow(sessionId)
                    result.fold(
                        onSuccess = {
                            _state.update {
                                it.copy(
                                    lastSaveTime = System.currentTimeMillis(),
                                    hasUnsavedChanges = false,
                                    autoSaveError = null,
                                )
                            }
                            logger.d("WorkoutViewModelExample", "立即保存成功")
                        },
                        onFailure = { error ->
                            _state.update {
                                it.copy(autoSaveError = "保存失败: ${error.message}")
                            }
                            logger.e(error, "立即保存失败")
                        },
                    )
                }
            }
        }

        /**
         * 停止自动保存
         */
        fun stopAutoSave() {
            autoSaveSessionId?.let { sessionId ->
                viewModelScope.launch {
                    workoutAdapter.stopAutoSave(sessionId)
                    autoSaveSessionId = null
                    _state.update {
                        it.copy(
                            autoSaveEnabled = false,
                            autoSaveSessionId = null,
                            autoSavePaused = false,
                        )
                    }
                    logger.d("WorkoutViewModelExample", "自动保存已停止")
                }
            }
        }

        override fun onCleared() {
            super.onCleared()
            stopAutoSave()
        }
    }

// ================================
// 3. 防抖工具类示例
// ================================

/**
 * 防抖工具类
 * 用于减少高频UI事件的处理
 */
class Debouncer(
    private val delayMs: Long,
    private val scope: kotlinx.coroutines.CoroutineScope,
) {
    private var debounceJob: kotlinx.coroutines.Job? = null

    @OptIn(kotlinx.coroutines.DelicateCoroutinesApi::class)
    fun debounce(action: suspend () -> Unit) {
        debounceJob?.cancel()
        debounceJob = scope.launch {
            kotlinx.coroutines.delay(delayMs)
            action()
        }
    }
}

// ================================
// 4. 使用示例：在Compose UI中集成
// ================================

/**
 * Compose UI集成示例
 */
/*
@Composable
fun ProfileScreen(
    viewModel: ProfileViewModelExample = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()

    LaunchedEffect(Unit) {
        // 启动自动保存
        viewModel.startAutoSave("user123")
    }

    Column {
        // 显示自动保存状态
        if (state.autoSaveEnabled) {
            Text("自动保存已启用")
        }

        if (state.autoSaveError != null) {
            Text(
                text = "错误: ${state.autoSaveError}",
                color = MaterialTheme.colors.error
            )
        }

        // 输入字段
        OutlinedTextField(
            value = state.currentProfile?.name ?: "",
            onValueChange = { viewModel.onNameChanged(it) },
            label = { Text("姓名") }
        )

        // 立即保存按钮
        Button(
            onClick = { viewModel.saveNow() }
        ) {
            Text("立即保存")
        }
    }
}

@Composable
fun WorkoutScreen(
    viewModel: WorkoutViewModelExample = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.startAutoSave("draft123")
    }

    Column {
        // 显示自动保存状态
        Row {
            if (state.autoSaveEnabled) {
                Icon(Icons.Default.Save, contentDescription = "自动保存")
            }

            if (state.autoSavePaused) {
                Icon(Icons.Default.Pause, contentDescription = "已暂停")
            }

            if (state.hasUnsavedChanges) {
                Text("有未保存的更改", color = MaterialTheme.colors.secondary)
            }
        }

        // 控制按钮
        Row {
            Button(onClick = { viewModel.pauseAutoSave() }) {
                Text("暂停")
            }

            Button(onClick = { viewModel.resumeAutoSave() }) {
                Text("恢复")
            }

            Button(onClick = { viewModel.saveNow() }) {
                Text("立即保存")
            }
        }

        // 缓存恢复对话框
        if (state.showRecoveryDialog) {
            AlertDialog(
                onDismissRequest = { viewModel.discardCache() },
                title = { Text("发现缓存数据") },
                text = { Text("是否恢复之前未保存的数据？") },
                confirmButton = {
                    TextButton(onClick = { viewModel.restoreFromCache() }) {
                        Text("恢复")
                    }
                },
                dismissButton = {
                    TextButton(onClick = { viewModel.discardCache() }) {
                        Text("丢弃")
                    }
                }
            )
        }
    }
}
*/
