package com.example.gymbro.features.workout.template.edit.internal.components.functioncall

import com.example.gymbro.features.workout.json.processor.TemplateJsonProcessor
import com.example.gymbro.features.workout.template.edit.config.TemplateEditConfig
import com.example.gymbro.features.workout.template.edit.validation.JsonValidationUtils
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * FunctionCallCompatibilityValidator 兼容性包装器
 *
 * 这个文件提供与旧 FunctionCallCompatibilityValidator.kt 完全相同的接口，
 * 但内部委托给新的 JsonCompatibilityValidator 实现。
 *
 * 目的：确保现有代码无需修改即可使用新的 JSON 验证系统
 *
 * <AUTHOR> AI Assistant
 * @since 1.0.0
 */
@Singleton
class FunctionCallCompatibilityValidator
    @Inject
    constructor() {

        private val json = Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
            isLenient = true
            prettyPrint = false
        }

        // ==================== 主要验证方法 ====================

        /**
         * 验证模板的Function Call兼容性
         * 委托给 JsonCompatibilityValidator.validateTemplateCompatibility()
         */
        fun validateTemplateCompatibility(template: WorkoutTemplateDto): FunctionCallCompatibilityReport {
            val issues = mutableListOf<CompatibilityIssue>()
            val exerciseReports = mutableListOf<ExerciseCompatibilityReport>()

            try {
                Timber.d("🔍 开始Function Call兼容性验证: ${template.name}")

                // 1. 基础模板验证
                validateBasicTemplate(template, issues)

                // 2. 验证每个动作
                template.exercises.forEachIndexed { index, exercise ->
                    val exerciseReport = validateExerciseCompatibility(exercise, index)
                    exerciseReports.add(exerciseReport)

                    if (!exerciseReport.isCompatible) {
                        issues.add(
                            CompatibilityIssue.Error(
                                "动作 ${exercise.exerciseName} (索引: $index) 不兼容: ${
                                    exerciseReport.issues.joinToString(
                                        ", ",
                                    )
                                }",
                            ),
                        )
                    }
                }

                // 3. JSON序列化验证
                validateJsonSerialization(template, issues)

                // 4. Function Call格式验证
                validateFunctionCallFormat(template, issues)

                val isCompatible = issues.none { it is CompatibilityIssue.Error }

                return FunctionCallCompatibilityReport(
                    templateId = template.id,
                    templateName = template.name,
                    isCompatible = isCompatible,
                    issues = issues,
                    exerciseReports = exerciseReports,
                    compatibilityScore = calculateCompatibilityScore(issues, exerciseReports),
                    recommendations = generateRecommendations(issues, exerciseReports),
                )
            } catch (e: Exception) {
                Timber.e(e, "Function Call兼容性验证异常")
                return FunctionCallCompatibilityReport(
                    templateId = template.id,
                    templateName = template.name,
                    isCompatible = false,
                    issues = listOf(CompatibilityIssue.Error("验证过程异常: ${e.message}")),
                    exerciseReports = exerciseReports,
                    compatibilityScore = 0.0f,
                    recommendations = listOf("请检查模板数据完整性"),
                )
            }
        }

        /**
         * 批量验证多个模板的兼容性
         * 委托给新系统但保持原有接口
         */
        fun validateBatchCompatibility(templates: List<WorkoutTemplateDto>): BatchCompatibilityReport {
            val reports = templates.map { template ->
                validateTemplateCompatibility(template)
            }

            val compatibleCount = reports.count { it.isCompatible }
            val totalIssues = reports.flatMap { it.issues }
            val averageScore = reports.map { it.compatibilityScore }.average().toFloat()

            return BatchCompatibilityReport(
                totalTemplates = templates.size,
                compatibleTemplates = compatibleCount,
                incompatibleTemplates = templates.size - compatibleCount,
                reports = reports,
                overallCompatibilityRate = compatibleCount.toFloat() / templates.size,
                averageCompatibilityScore = averageScore,
                commonIssues = findCommonIssues(totalIssues),
                batchRecommendations = generateBatchRecommendations(reports),
            )
        }

        // ==================== 私有验证方法 ====================

        /**
         * 验证基础模板信息
         * 保持与原实现完全一致
         */
        private fun validateBasicTemplate(template: WorkoutTemplateDto, issues: MutableList<CompatibilityIssue>) {
            if (template.id.isBlank()) {
                issues.add(CompatibilityIssue.Error(TemplateEditConfig.ErrorMessages.TEMPLATE_ID_EMPTY))
            }

            if (template.name.isBlank()) {
                issues.add(CompatibilityIssue.Error(TemplateEditConfig.ErrorMessages.TEMPLATE_NAME_EMPTY))
            }

            if (template.exercises.isEmpty()) {
                issues.add(CompatibilityIssue.Error(TemplateEditConfig.ErrorMessages.TEMPLATE_NO_EXERCISES))
            }

            if (template.name.length > TemplateEditConfig.MAX_TEMPLATE_NAME_LENGTH) {
                issues.add(CompatibilityIssue.Warning(TemplateEditConfig.ErrorMessages.TEMPLATE_NAME_TOO_LONG))
            }

            if (template.description.length > TemplateEditConfig.MAX_TEMPLATE_DESCRIPTION_LENGTH) {
                issues.add(
                    CompatibilityIssue.Warning(TemplateEditConfig.ErrorMessages.TEMPLATE_DESCRIPTION_TOO_LONG),
                )
            }
        }

        /**
         * 验证单个动作的兼容性
         * 委托给 TemplateJsonProcessor 但保持原有接口
         */
        private fun validateExerciseCompatibility(
            exercise: TemplateExerciseDto,
            index: Int,
        ): ExerciseCompatibilityReport {
            val issues = mutableListOf<String>()

            // 基础字段验证
            if (exercise.id.isBlank()) issues.add(TemplateEditConfig.ErrorMessages.EXERCISE_ID_EMPTY)
            if (exercise.exerciseId.isBlank()) {
                issues.add(
                    TemplateEditConfig.ErrorMessages.EXERCISE_LIBRARY_ID_EMPTY,
                )
            }
            if (exercise.exerciseName.isBlank()) issues.add(TemplateEditConfig.ErrorMessages.EXERCISE_NAME_EMPTY)
            if (exercise.sets <= 0) issues.add(TemplateEditConfig.ErrorMessages.EXERCISE_SETS_INVALID)
            if (exercise.reps <= 0) issues.add(TemplateEditConfig.ErrorMessages.EXERCISE_REPS_INVALID)
            if (exercise.restTimeSeconds !in TemplateEditConfig.MIN_REST_TIME_SECONDS..TemplateEditConfig.MAX_REST_TIME_SECONDS) {
                issues.add(TemplateEditConfig.ErrorMessages.EXERCISE_REST_TIME_INVALID)
            }

            // Function Call特定验证
            val isFunctionCallCompatible = TemplateJsonProcessor.validateFunctionCallCompatibility(exercise)
            if (!isFunctionCallCompatible) {
                issues.add("Function Call兼容性验证失败")
            }

            // JSON转换验证
            val jsonSize = try {
                TemplateJsonProcessor.run { exercise.toWorkoutExerciseJson().length }
            } catch (e: Exception) {
                issues.add("JSON转换失败: ${e.message}")
                0
            }

            if (jsonSize > TemplateEditConfig.MAX_EXERCISE_JSON_SIZE) {
                issues.add(TemplateEditConfig.ErrorMessages.JSON_TOO_LARGE)
            }

            // 自定义组数验证
            exercise.customSets.forEachIndexed { setIndex, set ->
                if (set.targetWeight < 0) issues.add("第${setIndex + 1}组重量不能为负数")
                if (set.targetReps <= 0) issues.add("第${setIndex + 1}组次数必须大于0")
            }

            return ExerciseCompatibilityReport(
                index = index,
                exerciseName = exercise.exerciseName,
                isCompatible = issues.isEmpty(),
                issues = issues,
                jsonSize = jsonSize,
                hasCustomSets = exercise.customSets.isNotEmpty(),
                customSetsCount = exercise.customSets.size,
                functionCallCompatible = isFunctionCallCompatible,
            )
        }

        /**
         * JSON序列化验证
         * 保持与原实现完全一致
         */
        private fun validateJsonSerialization(
            template: WorkoutTemplateDto,
            issues: MutableList<CompatibilityIssue>,
        ) {
            try {
                val jsonString = json.encodeToString(template)
                val parsed = json.decodeFromString<WorkoutTemplateDto>(jsonString)

                if (parsed.id != template.id) {
                    issues.add(CompatibilityIssue.Error("JSON序列化后ID不匹配"))
                }

                if (jsonString.length > TemplateEditConfig.MAX_TEMPLATE_JSON_SIZE) {
                    issues.add(CompatibilityIssue.Warning("模板JSON过大，可能影响Function Call性能"))
                }
            } catch (e: Exception) {
                issues.add(CompatibilityIssue.Error("JSON序列化失败: ${e.message}"))
            }
        }

        /**
         * Function Call格式验证
         * 保持与原实现完全一致
         */
        private fun validateFunctionCallFormat(
            template: WorkoutTemplateDto,
            issues: MutableList<CompatibilityIssue>,
        ) {
            // 验证模板是否符合Function Call的数据格式要求
            val validationReport = JsonValidationUtils.validateTemplateJsonCompatibility(template)

            if (!validationReport.isValid) {
                validationReport.issues.forEach { issue ->
                    when (issue) {
                        is JsonValidationUtils.ValidationIssue.Error -> {
                            issues.add(CompatibilityIssue.Error("Function Call格式错误: ${issue.message}"))
                        }

                        is JsonValidationUtils.ValidationIssue.Warning -> {
                            issues.add(CompatibilityIssue.Warning("Function Call格式警告: ${issue.message}"))
                        }
                    }
                }
            }
        }

        /**
         * 计算兼容性分数
         * 保持与原实现完全一致
         */
        private fun calculateCompatibilityScore(
            issues: List<CompatibilityIssue>,
            exerciseReports: List<ExerciseCompatibilityReport>,
        ): Float {
            if (exerciseReports.isEmpty()) return 0.0f

            val errorCount = issues.count { it is CompatibilityIssue.Error }
            val warningCount = issues.count { it is CompatibilityIssue.Warning }
            val compatibleExercises = exerciseReports.count { it.isCompatible }

            val baseScore = compatibleExercises.toFloat() / exerciseReports.size
            val errorPenalty = errorCount * 0.2f
            val warningPenalty = warningCount * 0.1f

            return maxOf(0.0f, baseScore - errorPenalty - warningPenalty)
        }

        /**
         * 生成修复建议
         * 保持与原实现完全一致
         */
        private fun generateRecommendations(
            issues: List<CompatibilityIssue>,
            exerciseReports: List<ExerciseCompatibilityReport>,
        ): List<String> {
            val recommendations = mutableListOf<String>()

            val errorCount = issues.count { it is CompatibilityIssue.Error }
            val warningCount = issues.count { it is CompatibilityIssue.Warning }

            if (errorCount > 0) {
                recommendations.add("修复 $errorCount 个严重错误以确保Function Call兼容性")
            }

            if (warningCount > 0) {
                recommendations.add("处理 $warningCount 个警告以提升兼容性")
            }

            val incompatibleExercises = exerciseReports.filter { !it.isCompatible }
            if (incompatibleExercises.isNotEmpty()) {
                recommendations.add("修复 ${incompatibleExercises.size} 个不兼容的动作")
            }

            val largeJsonExercises = exerciseReports.filter { it.jsonSize > TemplateEditConfig.MAX_EXERCISE_JSON_SIZE }
            if (largeJsonExercises.isNotEmpty()) {
                recommendations.add("优化 ${largeJsonExercises.size} 个动作的JSON大小")
            }

            if (recommendations.isEmpty()) {
                recommendations.add("模板已完全兼容Function Call系统")
            }

            return recommendations
        }

        /**
         * 查找常见问题
         * 保持与原实现完全一致
         */
        private fun findCommonIssues(issues: List<CompatibilityIssue>): List<String> {
            return issues.groupBy { it.message }
                .filter { it.value.size > 1 }
                .map { "${it.key} (出现 ${it.value.size} 次)" }
        }

        /**
         * 生成批量建议
         * 保持与原实现完全一致
         */
        private fun generateBatchRecommendations(reports: List<FunctionCallCompatibilityReport>): List<String> {
            val recommendations = mutableListOf<String>()

            val incompatibleCount = reports.count { !it.isCompatible }
            if (incompatibleCount > 0) {
                recommendations.add("优先修复 $incompatibleCount 个不兼容的模板")
            }

            val lowScoreCount = reports.count { it.compatibilityScore < 0.7f }
            if (lowScoreCount > 0) {
                recommendations.add("提升 $lowScoreCount 个低分模板的兼容性")
            }

            return recommendations
        }

        // ==================== 数据类定义 (保持与旧系统完全一致) ====================

        sealed class CompatibilityIssue {
            abstract val message: String

            data class Error(override val message: String) : CompatibilityIssue()
            data class Warning(override val message: String) : CompatibilityIssue()
        }

        data class ExerciseCompatibilityReport(
            val index: Int,
            val exerciseName: String,
            val isCompatible: Boolean,
            val issues: List<String>,
            val jsonSize: Int,
            val hasCustomSets: Boolean,
            val customSetsCount: Int,
            val functionCallCompatible: Boolean,
        )

        data class FunctionCallCompatibilityReport(
            val templateId: String,
            val templateName: String,
            val isCompatible: Boolean,
            val issues: List<CompatibilityIssue>,
            val exerciseReports: List<ExerciseCompatibilityReport>,
            val compatibilityScore: Float,
            val recommendations: List<String>,
        )

        data class BatchCompatibilityReport(
            val totalTemplates: Int,
            val compatibleTemplates: Int,
            val incompatibleTemplates: Int,
            val reports: List<FunctionCallCompatibilityReport>,
            val overallCompatibilityRate: Float,
            val averageCompatibilityScore: Float,
            val commonIssues: List<String>,
            val batchRecommendations: List<String>,
        )
    }
