package com.example.gymbro.features.workout.template.cache

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * 模板缓存管理器 - 完整版
 *
 * 🎯 功能:
 * - 自动保存编辑中的模板
 * - 应用重启后恢复未保存的更改
 * - 网络错误时的本地缓存
 * - 智能缓存清理机制
 * - 版本控制和冲突解决
 */
@Singleton
class TemplateCacheManager
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
        private val json: Json,
    ) {

        companion object {
            private const val CACHE_PREFIX = "template_cache_"
            private const val CACHE_METADATA_PREFIX = "template_meta_"
            private const val MAX_CACHE_AGE_MS = 7 * 24 * 60 * 60 * 1000L // 7天
            private const val MAX_CACHE_ENTRIES = 50

            private val Context.templateCacheDataStore: DataStore<Preferences> by preferencesDataStore(
                name = "template_cache",
            )
        }

        private val dataStore = context.templateCacheDataStore

        /**
         * 保存模板到缓存
         */
        suspend fun saveToCache(template: WorkoutTemplateDto) {
            try {
                val cacheKey = stringPreferencesKey("${CACHE_PREFIX}${template.id}")
                val metaKey = stringPreferencesKey("${CACHE_METADATA_PREFIX}${template.id}")

                val templateJson = json.encodeToString(template)
                val metadata = CacheMetadata(
                    templateId = template.id,
                    templateName = template.name,
                    timestamp = System.currentTimeMillis(),
                    version = template.version,
                    changeCount = 1,
                )
                val metadataJson = json.encodeToString(metadata)

                dataStore.edit { preferences ->
                    preferences[cacheKey] = templateJson
                    preferences[metaKey] = metadataJson
                }

                Timber.d("Template cached successfully: ${template.id} - ${template.name}")

                // 清理过期缓存
                cleanupExpiredCache()
            } catch (e: Exception) {
                Timber.e(e, "Failed to cache template: ${template.id}")
            }
        }

        /**
         * 从缓存恢复模板
         */
        suspend fun restoreFromCache(templateId: String): WorkoutTemplateDto? {
            return try {
                val cacheKey = stringPreferencesKey("${CACHE_PREFIX}$templateId")
                val preferences = dataStore.data.first()
                val templateJson = preferences[cacheKey]

                if (templateJson != null) {
                    val template = json.decodeFromString<WorkoutTemplateDto>(templateJson)
                    Timber.d("Template restored from cache: $templateId - ${template.name}")
                    template
                } else {
                    Timber.d("No cached template found for: $templateId")
                    null
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to restore template from cache: $templateId")
                null
            }
        }

        /**
         * 获取所有缓存的模板
         */
        suspend fun getCachedTemplates(): List<WorkoutTemplateDto> {
            return try {
                val preferences = dataStore.data.first()
                val cachedTemplates = mutableListOf<WorkoutTemplateDto>()

                preferences.asMap().forEach { (key, value) ->
                    if (key.name.startsWith(CACHE_PREFIX) && value is String) {
                        try {
                            val template = json.decodeFromString<WorkoutTemplateDto>(value)
                            cachedTemplates.add(template)
                        } catch (e: Exception) {
                            Timber.w(e, "Failed to decode cached template: ${key.name}")
                        }
                    }
                }

                Timber.d("Retrieved ${cachedTemplates.size} cached templates")
                cachedTemplates.sortedByDescending { it.updatedAt }
            } catch (e: Exception) {
                Timber.e(e, "Failed to get cached templates")
                emptyList()
            }
        }

        /**
         * 获取缓存元数据
         */
        suspend fun getCacheMetadata(): List<CacheMetadata> {
            return try {
                val preferences = dataStore.data.first()
                val metadataList = mutableListOf<CacheMetadata>()

                preferences.asMap().forEach { (key, value) ->
                    if (key.name.startsWith(CACHE_METADATA_PREFIX) && value is String) {
                        try {
                            val metadata = json.decodeFromString<CacheMetadata>(value)
                            metadataList.add(metadata)
                        } catch (e: Exception) {
                            Timber.w(e, "Failed to decode cache metadata: ${key.name}")
                        }
                    }
                }

                metadataList.sortedByDescending { it.timestamp }
            } catch (e: Exception) {
                Timber.e(e, "Failed to get cache metadata")
                emptyList()
            }
        }

        /**
         * 清除指定模板的缓存
         */
        suspend fun clearCache(templateId: String) {
            try {
                val cacheKey = stringPreferencesKey("${CACHE_PREFIX}$templateId")
                val metaKey = stringPreferencesKey("${CACHE_METADATA_PREFIX}$templateId")

                dataStore.edit { preferences ->
                    preferences.remove(cacheKey)
                    preferences.remove(metaKey)
                }

                Timber.d("Cache cleared for template: $templateId")
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear cache for template: $templateId")
            }
        }

        /**
         * 清除所有缓存
         */
        suspend fun clearAllCache() {
            try {
                dataStore.edit { preferences ->
                    val keysToRemove = preferences.asMap().keys.filter { key ->
                        key.name.startsWith(CACHE_PREFIX) || key.name.startsWith(CACHE_METADATA_PREFIX)
                    }
                    keysToRemove.forEach { key ->
                        preferences.remove(key)
                    }
                }

                Timber.d("All template cache cleared")
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear all cache")
            }
        }

        /**
         * 检查是否有缓存可用
         */
        fun hasCacheAvailable(): Flow<Boolean> {
            return dataStore.data.map { preferences ->
                preferences.asMap().keys.any { key ->
                    key.name.startsWith(CACHE_PREFIX)
                }
            }
        }

        /**
         * 获取缓存大小
         */
        suspend fun getCacheSize(): Int {
            return try {
                val preferences = dataStore.data.first()
                preferences.asMap().keys.count { key ->
                    key.name.startsWith(CACHE_PREFIX)
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to get cache size")
                0
            }
        }

        /**
         * 清理过期缓存
         */
        private suspend fun cleanupExpiredCache() {
            try {
                val currentTime = System.currentTimeMillis()
                val metadata = getCacheMetadata()
                val expiredEntries = metadata.filter {
                    currentTime - it.timestamp > MAX_CACHE_AGE_MS
                }

                // 清理过期条目
                expiredEntries.forEach { entry ->
                    clearCache(entry.templateId)
                }

                // 如果缓存条目过多，清理最旧的
                val remainingMetadata = metadata - expiredEntries.toSet()
                if (remainingMetadata.size > MAX_CACHE_ENTRIES) {
                    val entriesToRemove = remainingMetadata
                        .sortedBy { it.timestamp }
                        .take(remainingMetadata.size - MAX_CACHE_ENTRIES)

                    entriesToRemove.forEach { entry ->
                        clearCache(entry.templateId)
                    }
                }

                if (expiredEntries.isNotEmpty()) {
                    Timber.d("Cleaned up ${expiredEntries.size} expired cache entries")
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to cleanup expired cache")
            }
        }

        /**
         * 更新缓存条目的变更计数
         */
        suspend fun incrementChangeCount(templateId: String) {
            try {
                val metaKey = stringPreferencesKey("${CACHE_METADATA_PREFIX}$templateId")
                val preferences = dataStore.data.first()
                val metadataJson = preferences[metaKey]

                if (metadataJson != null) {
                    val metadata = json.decodeFromString<CacheMetadata>(metadataJson)
                    val updatedMetadata = metadata.copy(
                        changeCount = metadata.changeCount + 1,
                        timestamp = System.currentTimeMillis(),
                    )

                    dataStore.edit { prefs ->
                        prefs[metaKey] = json.encodeToString(updatedMetadata)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to increment change count for: $templateId")
            }
        }

        /**
         * 检查缓存版本冲突
         */
        suspend fun checkVersionConflict(template: WorkoutTemplateDto): Boolean {
            return try {
                val metaKey = stringPreferencesKey("${CACHE_METADATA_PREFIX}${template.id}")
                val preferences = dataStore.data.first()
                val metadataJson = preferences[metaKey]

                if (metadataJson != null) {
                    val cachedMetadata = json.decodeFromString<CacheMetadata>(metadataJson)
                    cachedMetadata.version != template.version
                } else {
                    false
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to check version conflict for: ${template.id}")
                false
            }
        }
    }

/**
 * 缓存元数据
 */
@Serializable
data class CacheMetadata(
    val templateId: String,
    val templateName: String,
    val timestamp: Long,
    val version: Int,
    val changeCount: Int,
)
