package com.example.gymbro.navigation.deeplink

import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 深度链接ViewModel
 * 负责管理深度链接的UI状态和业务逻辑
 */
@HiltViewModel
class DeepLinkViewModel
    @Inject
    constructor(
        val deepLinkManager: DeepLinkManager,
    ) : ViewModel() {

        // UI状态
        private val _uiState = MutableStateFlow(DeepLinkUiState())
        val uiState: StateFlow<DeepLinkUiState> = _uiState.asStateFlow()

        // 组合状态流
        val combinedState = combine(
            deepLinkManager.processingState,
            deepLinkManager.history,
            _uiState,
        ) { processingState, history, uiState ->
            uiState.copy(
                isProcessing = processingState.isProcessing,
                lastProcessedLink = processingState.lastProcessedLink,
                lastResult = processingState.lastResult,
                processingError = processingState.error,
                history = history,
            )
        }

        init {
            Timber.d("DeepLinkViewModel initialized")
        }

        /**
         * 处理Intent深度链接
         */
        fun handleIntent(intent: Intent) {
            viewModelScope.launch {
                try {
                    Timber.d("ViewModel开始处理Intent深度链接")
                    _uiState.value = _uiState.value.copy(
                        isHandlingIntent = true,
                        intentError = null,
                    )

                    val result = deepLinkManager.handleIntent(intent)

                    when (result) {
                        is ModernResult.Success -> {
                            val deepLinkResult = result.data
                            _uiState.value = _uiState.value.copy(
                                isHandlingIntent = false,
                                lastIntentResult = deepLinkResult,
                            )

                            if (deepLinkResult.success) {
                                Timber.d("Intent深度链接处理成功")
                                showSuccessMessage(deepLinkResult.message)
                            } else {
                                Timber.w("Intent深度链接处理失败: ${deepLinkResult.message}")
                                showErrorMessage(deepLinkResult.message)
                            }
                        }

                        is ModernResult.Error -> {
                            Timber.e("Intent深度链接处理失败: ${result.error}")
                            _uiState.value = _uiState.value.copy(
                                isHandlingIntent = false,
                                intentError = result.error.uiMessage,
                            )
                            showErrorMessage(result.error.uiMessage)
                        }

                        is ModernResult.Loading -> {
                            // 保持处理状态
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "ViewModel处理Intent深度链接时发生异常")
                    _uiState.value = _uiState.value.copy(
                        isHandlingIntent = false,
                        intentError = UiText.DynamicString("处理深度链接时发生异常: ${e.message}"),
                    )
                    showErrorMessage(UiText.DynamicString("处理深度链接时发生异常: ${e.message}"))
                }
            }
        }

        /**
         * 直接处理深度链接字符串
         */
        fun handleDeepLink(deepLink: String) {
            viewModelScope.launch {
                try {
                    Timber.d("ViewModel开始处理深度链接: $deepLink")

                    val result = deepLinkManager.handleDeepLink(deepLink)

                    when (result) {
                        is ModernResult.Success -> {
                            val deepLinkResult = result.data
                            if (deepLinkResult.success) {
                                Timber.d("深度链接处理成功")
                                showSuccessMessage(deepLinkResult.message)
                            } else {
                                Timber.w("深度链接处理失败: ${deepLinkResult.message}")
                                showErrorMessage(deepLinkResult.message)
                            }
                        }

                        is ModernResult.Error -> {
                            Timber.e("深度链接处理失败: ${result.error}")
                            showErrorMessage(result.error.uiMessage)
                        }

                        is ModernResult.Loading -> {
                            // 保持处理状态
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "ViewModel处理深度链接时发生异常: $deepLink")
                    showErrorMessage(UiText.DynamicString("处理深度链接时发生异常: ${e.message}"))
                }
            }
        }

        /**
         * 验证深度链接
         */
        fun validateDeepLink(deepLink: String): Boolean {
            return deepLinkManager.isValidDeepLink(deepLink)
        }

        /**
         * 生成深度链接
         */
        fun generateDeepLink(type: DeepLinkType, params: Map<String, String> = emptyMap()): String {
            return deepLinkManager.generateDeepLink(type, params)
        }

        /**
         * 获取支持的深度链接类型
         */
        fun getSupportedTypes(): List<DeepLinkType> {
            return deepLinkManager.getSupportedDeepLinkTypes()
        }

        /**
         * 清除处理状态
         */
        fun clearProcessingState() {
            deepLinkManager.clearProcessingState()
            _uiState.value = _uiState.value.copy(
                intentError = null,
                lastIntentResult = null,
                successMessage = null,
                errorMessage = null,
            )
        }

        /**
         * 清除历史记录
         */
        fun clearHistory() {
            deepLinkManager.clearHistory()
        }

        /**
         * 获取最近历史记录
         */
        fun getRecentHistory(limit: Int = 10): List<DeepLinkHistoryItem> {
            return deepLinkManager.getRecentHistory(limit)
        }

        /**
         * 显示成功消息
         */
        private fun showSuccessMessage(message: UiText?) {
            _uiState.value = _uiState.value.copy(
                successMessage = message,
                errorMessage = null,
            )
        }

        /**
         * 显示错误消息
         */
        private fun showErrorMessage(message: UiText?) {
            _uiState.value = _uiState.value.copy(
                errorMessage = message,
                successMessage = null,
            )
        }

        /**
         * 清除消息
         */
        fun clearMessages() {
            _uiState.value = _uiState.value.copy(
                successMessage = null,
                errorMessage = null,
            )
        }

        /**
         * 重试最后一个失败的深度链接
         */
        fun retryLastDeepLink() {
            val lastLink = _uiState.value.lastProcessedLink
            if (lastLink != null) {
                handleDeepLink(lastLink)
            }
        }

        /**
         * 测试深度链接
         */
        fun testDeepLink(deepLink: String) {
            viewModelScope.launch {
                try {
                    Timber.d("开始测试深度链接: $deepLink")
                    _uiState.value = _uiState.value.copy(isTesting = true)

                    // 首先验证格式
                    val isValid = validateDeepLink(deepLink)
                    if (!isValid) {
                        _uiState.value = _uiState.value.copy(
                            isTesting = false,
                            testResult = DeepLinkTestResult(
                                deepLink = deepLink,
                                isValid = false,
                                error = UiText.DynamicString("深度链接格式无效"),
                            ),
                        )
                        return@launch
                    }

                    // 然后尝试处理
                    val result = deepLinkManager.handleDeepLink(deepLink)

                    when (result) {
                        is ModernResult.Success -> {
                            _uiState.value = _uiState.value.copy(
                                isTesting = false,
                                testResult = DeepLinkTestResult(
                                    deepLink = deepLink,
                                    isValid = true,
                                    result = result.data,
                                ),
                            )
                        }

                        is ModernResult.Error -> {
                            _uiState.value = _uiState.value.copy(
                                isTesting = false,
                                testResult = DeepLinkTestResult(
                                    deepLink = deepLink,
                                    isValid = true,
                                    error = result.error.uiMessage,
                                ),
                            )
                        }

                        is ModernResult.Loading -> {
                            // 保持测试状态
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "测试深度链接时发生异常: $deepLink")
                    _uiState.value = _uiState.value.copy(
                        isTesting = false,
                        testResult = DeepLinkTestResult(
                            deepLink = deepLink,
                            isValid = false,
                            error = UiText.DynamicString("测试时发生异常: ${e.message}"),
                        ),
                    )
                }
            }
        }

        /**
         * 清除测试结果
         */
        fun clearTestResult() {
            _uiState.value = _uiState.value.copy(testResult = null)
        }
    }

/**
 * 深度链接UI状态
 */
data class DeepLinkUiState(
    // 处理状态
    val isProcessing: Boolean = false,
    val isHandlingIntent: Boolean = false,
    val isTesting: Boolean = false,

    // 结果状态
    val lastProcessedLink: String? = null,
    val lastResult: DeepLinkResult? = null,
    val lastIntentResult: DeepLinkResult? = null,
    val testResult: DeepLinkTestResult? = null,

    // 错误状态
    val processingError: UiText? = null,
    val intentError: UiText? = null,

    // 消息状态
    val successMessage: UiText? = null,
    val errorMessage: UiText? = null,

    // 历史记录
    val history: List<DeepLinkHistoryItem> = emptyList(),
)

/**
 * 深度链接测试结果
 */
data class DeepLinkTestResult(
    val deepLink: String,
    val isValid: Boolean,
    val result: DeepLinkResult? = null,
    val error: UiText? = null,
    val timestamp: Long = System.currentTimeMillis(),
)
