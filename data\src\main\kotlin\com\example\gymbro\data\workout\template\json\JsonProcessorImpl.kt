package com.example.gymbro.data.workout.template.json

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.common.CommonFeatureErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.mapper.toDomain
import com.example.gymbro.domain.workout.mapper.toDto
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.port.JsonDataType
import com.example.gymbro.domain.workout.port.JsonProcessorPort
import com.example.gymbro.shared.models.workout.PlanCalendarData
import com.example.gymbro.shared.models.workout.WorkoutSessionDto
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * JSON处理器实现（Data层）
 * 实现Domain层定义的JsonProcessorPort接口
 *
 * 这个实现类将原有的TemplateJsonDataProcessor重构为符合
 * Clean Architecture的接口实现，确保依赖关系正确
 */
@Singleton
class JsonProcessorImpl
    @Inject
    constructor() : JsonProcessorPort {

        private val json = Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
            isLenient = true
            prettyPrint = true
            explicitNulls = true
            coerceInputValues = true
        }

        override suspend fun serializeTemplate(template: WorkoutTemplate): ModernResult<String> {
            return try {
                val dto = template.toDto()
                val jsonString = json.encodeToString(dto)
                Timber.d("Template序列化成功: ${template.id}")
                ModernResult.success(jsonString)
            } catch (e: SerializationException) {
                Timber.e(e, "Template序列化失败: ${template.id}")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.serializeTemplate",
                        message = UiText.DynamicString("锻炼模板序列化失败"),
                        dataType = "WorkoutTemplate",
                        cause = e,
                        metadataMap = mapOf("templateId" to template.id),
                    ),
                )
            } catch (e: Exception) {
                Timber.e(e, "Template序列化意外错误: ${template.id}")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.serializeTemplate",
                        message = UiText.DynamicString("锻炼模板序列化意外错误"),
                        dataType = "WorkoutTemplate",
                        cause = e,
                        metadataMap = mapOf("templateId" to template.id),
                    ),
                )
            }
        }

        override suspend fun deserializeTemplate(jsonString: String): ModernResult<WorkoutTemplate> {
            return try {
                val dto = json.decodeFromString<WorkoutTemplateDto>(jsonString)

                // 调试日志：仅在数据异常时记录
                if (dto.exercises.isEmpty()) {
                    Timber.tag("WK-DEBUG").w("⚠️ [JSON-TO-DTO] 解析DTO成功但无动作数据")
                } else {
                    val exercisesWithoutSets = dto.exercises.count { it.customSets.isEmpty() }
                    if (exercisesWithoutSets > 0) {
                        Timber.tag("WK-DEBUG").w("⚠️ [JSON-TO-DTO] $exercisesWithoutSets 个动作缺少customSets数据")
                    }
                }

                val domain = dto.toDomain()

                // 调试日志：仅在数据异常时记录
                if (domain.exercises.isEmpty()) {
                    Timber.tag("WK-DEBUG").w("⚠️ [DTO-TO-DOMAIN] 转换Domain成功但无动作数据")
                } else {
                    val exercisesWithoutSets = domain.exercises.count { it.customSets.isEmpty() }
                    if (exercisesWithoutSets > 0) {
                        Timber.tag("WK-DEBUG").w("⚠️ [DTO-TO-DOMAIN] $exercisesWithoutSets 个动作缺少customSets数据")
                    }
                }

                ModernResult.success(domain)
            } catch (e: SerializationException) {
                Timber.e(e, "Template反序列化失败")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.deserializationError(
                        operationName = "JsonProcessorImpl.deserializeTemplate",
                        message = UiText.DynamicString("锻炼模板反序列化失败"),
                        dataType = "WorkoutTemplate",
                        cause = e,
                        metadataMap = mapOf("jsonLength" to jsonString.length),
                    ),
                )
            } catch (e: Exception) {
                Timber.e(e, "Template反序列化意外错误")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.deserializationError(
                        operationName = "JsonProcessorImpl.deserializeTemplate",
                        message = UiText.DynamicString("锻炼模板反序列化意外错误"),
                        dataType = "WorkoutTemplate",
                        cause = e,
                        metadataMap = mapOf("jsonLength" to jsonString.length),
                    ),
                )
            }
        }

        override suspend fun validateJson(jsonString: String): ModernResult<Boolean> {
            return try {
                // 尝试解析JSON来验证格式
                json.parseToJsonElement(jsonString)
                ModernResult.success(true)
            } catch (e: SerializationException) {
                Timber.w("JSON验证失败: ${e.message}")
                ModernResult.success(false)
            } catch (e: Exception) {
                Timber.e(e, "JSON验证意外错误")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.deserializationError(
                        operationName = "JsonProcessorImpl.validateJson",
                        message = UiText.DynamicString("JSON验证失败"),
                        dataType = "JSON",
                        cause = e,
                        metadataMap = mapOf("jsonLength" to jsonString.length),
                    ),
                )
            }
        }

        override suspend fun formatJson(jsonString: String): ModernResult<String> {
            return try {
                val jsonElement = json.parseToJsonElement(jsonString)
                val formattedJson = Json {
                    prettyPrint = true
                    ignoreUnknownKeys = true
                }.encodeToString(jsonElement)
                ModernResult.success(formattedJson)
            } catch (e: SerializationException) {
                Timber.e(e, "JSON格式化失败")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.formatJson",
                        message = UiText.DynamicString("JSON格式化失败"),
                        dataType = "JSON",
                        cause = e,
                        metadataMap = mapOf("jsonLength" to jsonString.length),
                    ),
                )
            } catch (e: Exception) {
                Timber.e(e, "JSON格式化意外错误")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.formatJson",
                        message = UiText.DynamicString("JSON格式化意外错误"),
                        dataType = "JSON",
                        cause = e,
                        metadataMap = mapOf("jsonLength" to jsonString.length),
                    ),
                )
            }
        }

        // ==================== Session JSON 处理方法 ====================

        override suspend fun serializeSession(session: WorkoutSessionDto): ModernResult<String> {
            return try {
                val jsonString = json.encodeToString(session)
                Timber.d("Session序列化成功: ${session.id}")
                ModernResult.success(jsonString)
            } catch (e: SerializationException) {
                Timber.e(e, "Session序列化失败: ${session.id}")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.serializeSession",
                        message = UiText.DynamicString("训练会话序列化失败"),
                        dataType = "WorkoutSessionDto",
                        cause = e,
                        metadataMap = mapOf("sessionId" to session.id),
                    ),
                )
            }
        }

        override suspend fun deserializeSession(jsonString: String): ModernResult<WorkoutSessionDto> {
            return try {
                val session = json.decodeFromString<WorkoutSessionDto>(jsonString)
                Timber.d("Session反序列化成功: ${session.id}")
                ModernResult.success(session)
            } catch (e: SerializationException) {
                Timber.e(e, "Session反序列化失败")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.deserializationError(
                        operationName = "JsonProcessorImpl.deserializeSession",
                        message = UiText.DynamicString("训练会话反序列化失败"),
                        dataType = "WorkoutSessionDto",
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun batchUpdateSession(jsonString: String, updates: List<Any>): ModernResult<String> {
            return try {
                // 简单实现：直接返回原始JSON，实际应该根据updates进行修改
                Timber.d("Session批量更新: ${updates.size}个更新")
                ModernResult.success(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "Session批量更新失败")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.batchUpdateSession",
                        message = UiText.DynamicString("训练会话批量更新失败"),
                        dataType = "WorkoutSessionDto",
                        cause = e,
                    ),
                )
            }
        }

        // ==================== Plan JSON 处理方法 ====================

        override suspend fun serializePlan(plan: WorkoutPlan): ModernResult<String> {
            return try {
                val jsonString = json.encodeToString(plan)
                Timber.d("Plan序列化成功: ${plan.id}")
                ModernResult.success(jsonString)
            } catch (e: SerializationException) {
                Timber.e(e, "Plan序列化失败: ${plan.id}")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.serializePlan",
                        message = UiText.DynamicString("训练计划序列化失败"),
                        dataType = "WorkoutPlan",
                        cause = e,
                        metadataMap = mapOf("planId" to plan.id),
                    ),
                )
            }
        }

        override suspend fun deserializePlan(jsonString: String): ModernResult<WorkoutPlan> {
            return try {
                val plan = json.decodeFromString<WorkoutPlan>(jsonString)
                Timber.d("Plan反序列化成功: ${plan.id}")
                ModernResult.success(plan)
            } catch (e: SerializationException) {
                Timber.e(e, "Plan反序列化失败")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.deserializationError(
                        operationName = "JsonProcessorImpl.deserializePlan",
                        message = UiText.DynamicString("训练计划反序列化失败"),
                        dataType = "WorkoutPlan",
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun generatePlanCalendar(plan: WorkoutPlan, startDate: String): ModernResult<PlanCalendarData> {
            return try {
                // 简单实现：创建空的日历数据
                val calendarData = PlanCalendarData(
                    planInfo = com.example.gymbro.shared.models.workout.PlanCalendarInfo(
                        planId = plan.id,
                        planName = plan.name.toString(),
                        description = plan.description?.toString(),
                        totalDays = plan.totalDays,
                        workoutDays = plan.getTotalWorkoutDays(),
                        restDays = plan.getTotalRestDays(),
                        createdAt = plan.createdAt,
                        updatedAt = plan.updatedAt,
                    ),
                    calendarEntries = emptyList(),
                )
                Timber.d("Plan日历生成成功: ${plan.id}")
                ModernResult.success(calendarData)
            } catch (e: Exception) {
                Timber.e(e, "Plan日历生成失败: ${plan.id}")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.generatePlanCalendar",
                        message = UiText.DynamicString("训练计划日历生成失败"),
                        dataType = "PlanCalendarData",
                        cause = e,
                        metadataMap = mapOf("planId" to plan.id, "startDate" to startDate),
                    ),
                )
            }
        }

        override suspend fun batchUpdatePlan(jsonString: String, updates: List<Any>): ModernResult<String> {
            return try {
                // 简单实现：直接返回原始JSON，实际应该根据updates进行修改
                Timber.d("Plan批量更新: ${updates.size}个更新")
                ModernResult.success(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "Plan批量更新失败")
                ModernResult.error(
                    CommonFeatureErrors.JsonError.serializationError(
                        operationName = "JsonProcessorImpl.batchUpdatePlan",
                        message = UiText.DynamicString("训练计划批量更新失败"),
                        dataType = "WorkoutPlan",
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun validateJsonByType(jsonString: String, dataType: JsonDataType): ModernResult<Boolean> {
            return try {
                val isValid = when (dataType) {
                    JsonDataType.TEMPLATE -> {
                        json.decodeFromString<WorkoutTemplateDto>(jsonString)
                        true
                    }

                    JsonDataType.SESSION -> {
                        json.decodeFromString<WorkoutSessionDto>(jsonString)
                        true
                    }

                    JsonDataType.PLAN -> {
                        json.decodeFromString<WorkoutPlan>(jsonString)
                        true
                    }

                    else -> false
                }
                Timber.d("JSON类型验证成功: $dataType = $isValid")
                ModernResult.success(isValid)
            } catch (e: Exception) {
                Timber.d("JSON类型验证失败: $dataType")
                ModernResult.success(false)
            }
        }
    }
