package com.example.gymbro.domain.workout.usecase.calendar

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.workout.model.calendar.CalendarDayInfo
import com.example.gymbro.domain.workout.repository.CalendarRepository
import com.example.gymbro.domain.workout.repository.PlanRepository
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.datetime.LocalDate

/**
 * 获取日历数据用例
 *
 * 此用例从仓库获取指定日期范围的日历数据，并返回包含日历数据的Flow
 */
class GetCalendarDataUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val calendarRepository: CalendarRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernFlowUseCase<GetCalendarDataUseCase.Params, Map<LocalDate, CalendarDayInfo>>(
        dispatcher,
        logger,
    ) {
        /**
         * 参数类
         *
         * @property startDate 开始日期
         * @property endDate 结束日期
         */
        data class Params(
            val startDate: LocalDate,
            val endDate: LocalDate,
        )

        /**
         * 执行用例，获取日历数据
         *
         * @param parameters 包含开始和结束日期的参数
         * @return 包含日历数据的Flow
         */
        override fun createFlow(parameters: Params): Flow<ModernResult<Map<LocalDate, CalendarDayInfo>>> =
            flow {
                val userIdResult = getCurrentUserIdUseCase().first()

                when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (userId == null) {
                            emit(
                                ModernResult.Error(
                                    BusinessErrors.BusinessError.rule(
                                        operationName = "getCalendarData",
                                        message = UiText.DynamicString("User not logged in"),
                                        recoverable = true,
                                    ),
                                ),
                            )
                            return@flow
                        }

                        // 使用CalendarRepository获取真实数据
                        val result = calendarRepository.getCalendarData(
                            startDate = parameters.startDate,
                            endDate = parameters.endDate,
                            userId = userId,
                        )
                        emit(result)
                    }

                    is ModernResult.Error -> {
                        emit(userIdResult)
                    }

                    is ModernResult.Loading -> {
                        emit(ModernResult.Loading)
                    }
                }
            }
    }
