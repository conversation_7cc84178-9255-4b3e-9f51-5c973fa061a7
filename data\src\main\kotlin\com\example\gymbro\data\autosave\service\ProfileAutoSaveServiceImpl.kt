package com.example.gymbro.data.autosave.service

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.autosave.ProfileAutoSaveService
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.repository.user.UserAggregateRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json

/**
 * Profile自动保存服务实现
 *
 * 基于DataStore的Profile数据自动保存功能
 * 使用即时保存策略，适合重要的用户资料数据
 */
@Singleton
class ProfileAutoSaveServiceImpl
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
        private val dataStore: DataStore<Preferences>,
        private val userAggregateRepository: UserAggregateRepository,
        private val json: Json,
        private val logger: Logger,
    ) : ProfileAutoSaveService {
        private val activeSessions = ConcurrentHashMap<String, ProfileAutoSaveSession>()

        companion object {
            const val PROFILE_CACHE_PREFIX = "profile_cache_"
            const val SAVE_DELAY_MS = 1000L // 1秒防抖延迟
        }

        override suspend fun createAutoSave(
            userId: String,
            scope: CoroutineScope,
        ): ModernResult<String> =
            try {
                val sessionId = "profile_session_${userId}_${System.currentTimeMillis()}"
                logger.d("ProfileAutoSaveServiceImpl", "创建Profile自动保存会话: $sessionId")

                val session =
                    ProfileAutoSaveSession(
                        sessionId = sessionId,
                        userId = userId,
                        scope = scope,
                        dataStore = dataStore,
                        userAggregateRepository = userAggregateRepository,
                        json = json,
                        logger = logger,
                    )

                activeSessions[sessionId] = session

                logger.d("ProfileAutoSaveServiceImpl", "Profile自动保存会话创建成功: $sessionId")
                ModernResult.Success(sessionId)
            } catch (e: Exception) {
                logger.e(e, "创建Profile自动保存会话失败")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.createAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }

        override suspend fun startAutoSave(
            sessionId: String,
            userId: String,
            initialProfile: UserProfile?,
        ): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    logger.e("ProfileAutoSaveServiceImpl", "会话不存在: $sessionId")
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "ProfileAutoSaveServiceImpl.startAutoSave",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                session.start(initialProfile)
                logger.d("ProfileAutoSaveServiceImpl", "Profile自动保存启动成功: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "启动Profile自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.startAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun updateProfile(
            sessionId: String,
            profile: UserProfile,
        ): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    logger.w("ProfileAutoSaveServiceImpl", "会话不存在，无法更新Profile: $sessionId")
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "ProfileAutoSaveServiceImpl.updateProfile",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                session.updateProfile(profile)
                logger.d("ProfileAutoSaveServiceImpl", "Profile数据已更新: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "更新Profile数据失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.updateProfile",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun saveNow(sessionId: String): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "ProfileAutoSaveServiceImpl.saveNow",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                session.saveNow()
                logger.d("ProfileAutoSaveServiceImpl", "Profile立即保存完成: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "Profile立即保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.saveNow",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun pauseAutoSave(sessionId: String): ModernResult<Unit> =
            try {
                val session = activeSessions[sessionId]
                session?.pause()
                logger.d("ProfileAutoSaveServiceImpl", "Profile自动保存已暂停: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "暂停Profile自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.pauseAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }

        override suspend fun resumeAutoSave(sessionId: String): ModernResult<Unit> =
            try {
                val session = activeSessions[sessionId]
                session?.resume()
                logger.d("ProfileAutoSaveServiceImpl", "Profile自动保存已恢复: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "恢复Profile自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.resumeAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }

        override suspend fun stopAutoSave(sessionId: String): ModernResult<Unit> =
            try {
                val session = activeSessions[sessionId]
                if (session != null) {
                    session.stop()
                    activeSessions.remove(sessionId)
                }
                logger.d("ProfileAutoSaveServiceImpl", "Profile自动保存已停止: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "停止Profile自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.stopAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }

        override suspend fun restoreFromCache(sessionId: String): ModernResult<UserProfile?> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "ProfileAutoSaveServiceImpl.restoreFromCache",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                val cachedProfile = session.restoreFromCache()
                logger.d("ProfileAutoSaveServiceImpl", "Profile缓存数据恢复: $sessionId")
                ModernResult.Success(cachedProfile)
            } catch (e: Exception) {
                logger.e(e, "恢复Profile缓存数据失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.restoreFromCache",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun discardCache(sessionId: String): ModernResult<Unit> =
            try {
                val session = activeSessions[sessionId]
                session?.discardCache()
                logger.d("ProfileAutoSaveServiceImpl", "Profile缓存数据已丢弃: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "丢弃Profile缓存数据失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "ProfileAutoSaveServiceImpl.discardCache",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
    }

/**
 * Profile自动保存会话
 *
 * 管理单个Profile的自动保存生命周期
 * 使用即时保存策略（1秒防抖）
 */
private class ProfileAutoSaveSession(
    val sessionId: String,
    private val userId: String,
    private val scope: CoroutineScope,
    private val dataStore: DataStore<Preferences>,
    private val userAggregateRepository: UserAggregateRepository,
    private val json: Json,
    private val logger: Logger,
) {
    private var currentProfile: UserProfile? = null
    private var isActive = false
    private var isPaused = false
    private var saveJob: kotlinx.coroutines.Job? = null

    private val cacheKey = "${ProfileAutoSaveServiceImpl.PROFILE_CACHE_PREFIX}$userId"

    fun start(initialProfile: UserProfile?) {
        if (isActive) {
            logger.w("ProfileAutoSaveSession", "会话已启动: $sessionId")
            return
        }

        currentProfile = initialProfile
        isActive = true
        isPaused = false

        logger.d("ProfileAutoSaveSession", "Profile自动保存会话启动: $sessionId")
    }

    fun updateProfile(profile: UserProfile) {
        if (!isActive || isPaused) {
            logger.d("ProfileAutoSaveSession", "会话未激活或已暂停，忽略更新: $sessionId")
            return
        }

        currentProfile = profile

        // 取消之前的保存任务
        saveJob?.cancel()

        // 启动新的防抖保存任务
        saveJob =
            scope.launch {
                delay(ProfileAutoSaveServiceImpl.SAVE_DELAY_MS)
                performSave()
            }

        logger.d(
            "ProfileAutoSaveSession",
            "Profile数据已更新，将在${ProfileAutoSaveServiceImpl.SAVE_DELAY_MS}ms后保存: $sessionId",
        )
    }

    suspend fun saveNow() {
        if (!isActive) {
            logger.w("ProfileAutoSaveSession", "会话未激活，无法立即保存: $sessionId")
            return
        }

        saveJob?.cancel()
        performSave()
    }

    fun pause() {
        isPaused = true
        saveJob?.cancel()
        logger.d("ProfileAutoSaveSession", "Profile自动保存已暂停: $sessionId")
    }

    fun resume() {
        isPaused = false
        logger.d("ProfileAutoSaveSession", "Profile自动保存已恢复: $sessionId")
    }

    fun stop() {
        isActive = false
        isPaused = false
        saveJob?.cancel()
        saveJob = null
        currentProfile = null
        logger.d("ProfileAutoSaveSession", "Profile自动保存会话已停止: $sessionId")
    }

    suspend fun restoreFromCache(): UserProfile? =
        try {
            val prefKey = stringPreferencesKey(cacheKey)
            val jsonString = dataStore.data.first()[prefKey]

            if (jsonString != null) {
                val profile = json.decodeFromString<UserProfile>(jsonString)
                logger.d("ProfileAutoSaveSession", "从缓存恢复Profile数据: $sessionId")
                profile
            } else {
                logger.d("ProfileAutoSaveSession", "未找到缓存的Profile数据: $sessionId")
                null
            }
        } catch (e: Exception) {
            logger.e(e, "恢复Profile缓存数据失败: $sessionId")
            null
        }

    suspend fun discardCache() {
        try {
            val prefKey = stringPreferencesKey(cacheKey)
            dataStore.edit { preferences ->
                preferences.remove(prefKey)
            }
            logger.d("ProfileAutoSaveSession", "Profile缓存数据已丢弃: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "丢弃Profile缓存数据失败: $sessionId")
        }
    }

    private suspend fun performSave() {
        val profile = currentProfile
        if (profile == null) {
            logger.w("ProfileAutoSaveSession", "没有Profile数据可保存: $sessionId")
            return
        }

        try {
            logger.d("ProfileAutoSaveSession", "开始保存Profile数据: $sessionId")

            // 1. 保存到缓存
            val jsonString = json.encodeToString(profile)
            val prefKey = stringPreferencesKey(cacheKey)
            dataStore.edit { preferences ->
                preferences[prefKey] = jsonString
            }

            // 2. 保存到Repository（持久化）
            val result = userAggregateRepository.updateUserProfile(userId, profile)
            when (result) {
                is ModernResult.Success -> {
                    logger.d("ProfileAutoSaveSession", "Profile数据保存成功: $sessionId")
                }

                is ModernResult.Error -> {
                    logger.e("ProfileAutoSaveSession", "Profile数据保存失败", result.error.cause)
                }

                is ModernResult.Loading -> {
                    logger.d("ProfileAutoSaveSession", "Profile数据保存中...")
                }
            }
        } catch (e: Exception) {
            logger.e(e, "保存Profile数据异常: $sessionId")
        }
    }
}
