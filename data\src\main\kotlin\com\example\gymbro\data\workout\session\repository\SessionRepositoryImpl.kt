package com.example.gymbro.data.workout.session.repository

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.workout.session.dao.*
import com.example.gymbro.data.workout.session.mapper.*
import com.example.gymbro.domain.workout.model.*
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.domain.workout.usecase.template.TemplateVersionUseCase
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * SessionRepository 实现 - SessionDB 数据访问
 *
 * 基于 05_Final_Database_Architecture.md 和 04_Implementation_Plan_CleanSlate_SessionDB.md 设计
 * 负责训练记录与统计，包含自动保存功能，统一使用 safeCatch 和 ModernResult 封装异常
 * Phase 4: Session层适配 - 引用TemplateVersion确保历史追溯性
 */
@Singleton
class SessionRepositoryImpl
    @Inject
    constructor(
        private val sessionDao: SessionDao,
        private val sessionExerciseDao: SessionExerciseDao,
        private val sessionSetDao: SessionSetDao,
        private val exerciseHistoryStatsDao: ExerciseHistoryStatsDao,
        private val sessionAutoSaveDao: SessionAutoSaveDao,
        private val templateRepository: TemplateRepository,
        private val templateVersionUseCase: TemplateVersionUseCase,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) : SessionRepository {

        // ==================== Session 基础操作 ====================

        override suspend fun getSessionById(sessionId: String): ModernResult<WorkoutSession?> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("获取训练会话: sessionId=$sessionId")
                    val sessionEntity = sessionDao.getSessionById(sessionId)
                    if (sessionEntity != null) {
                        // 加载会话的所有动作
                        val exerciseEntities = sessionExerciseDao.getSessionExercisesSync(sessionId)
                        val exercises = mutableListOf<SessionExercise>()

                        // 为每个动作加载组数
                        for (exerciseEntity in exerciseEntities) {
                            val setEntities = sessionSetDao.getExerciseSetsSync(exerciseEntity.id)
                            val exercise = when (
                                val result = exerciseEntity.toDomainWithSetsSafely(
                                    setEntities,
                                )
                            ) {
                                is ModernResult.Success -> result.data
                                is ModernResult.Error -> throw Exception(result.error.message)
                                is ModernResult.Loading -> throw Exception("Unexpected loading state")
                            }
                            exercises.add(exercise)
                        }

                        when (val result = sessionEntity.toDomainWithExercisesSafely(exercises)) {
                            is ModernResult.Success -> result.data
                            is ModernResult.Error -> throw Exception(result.error.message)
                            is ModernResult.Loading -> throw Exception("Unexpected loading state")
                        }
                    } else {
                        null
                    }
                }
            }

        override fun getUserSessions(userId: String): Flow<ModernResult<List<WorkoutSession>>> =
            sessionDao.getUserSessions(userId)
                .map { entities ->
                    entities.toDomainSafely()
                }
                .catch { e ->
                    Timber.e(e, "获取用户训练会话失败: userId=$userId")
                    emit(
                        ModernResult.Error(
                            e.toModernDataError(
                                operationName = "getUserSessions",
                                uiMessage = UiText.DynamicString("获取训练记录失败"),
                            ),
                        ),
                    )
                }
                .flowOn(ioDispatcher)

        override suspend fun getActiveSession(userId: String): ModernResult<WorkoutSession?> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("获取活动训练会话: userId=$userId")
                    val sessionEntity = sessionDao.getActiveSession(userId)
                    if (sessionEntity != null) {
                        // 加载完整的会话数据
                        when (val result = getSessionById(sessionEntity.id)) {
                            is ModernResult.Success -> result.data
                            is ModernResult.Error -> throw Exception(result.error.message)
                            is ModernResult.Loading -> throw Exception("Unexpected loading state")
                        }
                    } else {
                        null
                    }
                }
            }

        override suspend fun saveSession(session: WorkoutSession): ModernResult<String> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("保存训练会话: sessionId=${session.id}")

                    // 保存会话基本信息
                    sessionDao.insertSession(session.toEntity())

                    // 保存会话中的动作
                    for (exercise in session.exercises) {
                        sessionExerciseDao.insertSessionExercise(exercise.toEntity())

                        // 保存动作中的组数
                        for (set in exercise.sets) {
                            sessionSetDao.insertSet(set.toEntity())
                        }
                    }

                    session.id
                }
            }

        override suspend fun createSessionFromTemplate(templateId: String, userId: String): ModernResult<String> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("从模板创建训练会话: templateId=$templateId, userId=$userId")

                    // Phase 4适配: 获取Template的最新TemplateVersion
                    val templateVersionResult = templateVersionUseCase.getLatestVersionWithAutoCreate.invoke(
                        TemplateVersionUseCase.GetLatestVersionWithAutoCreateParams(
                            templateId = templateId,
                            autoCreateIfNotExist = true,
                        ),
                    )

                    val templateVersion = when (templateVersionResult) {
                        is ModernResult.Success -> templateVersionResult.data
                        is ModernResult.Error -> {
                            Timber.e("获取Template最新版本失败: ${templateVersionResult.error}")
                            throw Exception("获取模板版本失败: ${templateVersionResult.error.message}")
                        }

                        is ModernResult.Loading -> {
                            throw Exception("获取模板版本超时")
                        }
                    }

                    // 从TemplateVersion获取模板内容（确保使用版本快照）
                    val template = templateVersion.content
                    Timber.d(
                        "使用TemplateVersion: templateId=$templateId, versionNumber=${templateVersion.versionNumber}, versionId=${templateVersion.id}",
                    )

                    val sessionId = com.example.gymbro.core.util.CompactIdGenerator.generateId("session")
                    val currentTime = System.currentTimeMillis()

                    // 创建Session实体（记录确切的TemplateVersion信息）
                    val sessionEntity = com.example.gymbro.data.workout.session.entity.SessionEntity(
                        id = sessionId,
                        userId = userId,
                        templateId = templateId, // 保留原Template ID用于查询
                        templateVersion = templateVersion.versionNumber, // ✅ 记录确切的版本号
                        planId = null,
                        name = template.name,
                        status = "IN_PROGRESS",
                        startTime = currentTime,
                        endTime = null,
                        totalDuration = null,
                        totalVolume = null,
                        caloriesBurned = null,
                        notes = "Based on Template v${templateVersion.versionNumber} (${templateVersion.id})",
                        rating = null,
                        lastAutosaveTime = currentTime,
                    )

                    // 保存Session
                    sessionDao.insertSession(sessionEntity)

                    // 复制TemplateVersion的动作到Session（保留restSeconds数据）
                    template.exercises.forEachIndexed { index, templateExercise ->
                        val sessionExerciseEntity = com.example.gymbro.data.workout.session.entity.SessionExerciseEntity(
                            id = UUID.randomUUID().toString(),
                            sessionId = sessionId,
                            exerciseId = templateExercise.exerciseId,
                            order = index,
                            name = "Exercise_${templateExercise.exerciseId}", // ✅ 简化处理，后续可从Exercise表获取
                            targetSets = templateExercise.sets,
                            completedSets = 0,
                            // ✅ 复制TemplateVersion的休息时间数据
                            restSeconds = templateExercise.restSeconds,
                            restSecondsOverride = null, // 初始无覆盖
                            // ✅ 媒资字段暂时为空，后续可从Exercise表获取
                            imageUrl = null,
                            videoUrl = null,
                            status = "NOT_STARTED",
                            startTime = null,
                            endTime = null,
                            notes = templateExercise.notes,
                            isCompleted = false,
                        )

                        sessionExerciseDao.insertSessionExercise(sessionExerciseEntity)

                        // 为每个动作创建初始的组数记录（根据sets数量）
                        repeat(templateExercise.sets) { setIndex ->
                            val sessionSetEntity = com.example.gymbro.data.workout.session.entity.SessionSetEntity(
                                id = UUID.randomUUID().toString(),
                                sessionExerciseId = sessionExerciseEntity.id,
                                setNumber = setIndex + 1,
                                weight = null, // 初始为空，用户在训练时填入
                                weightUnit = "kg",
                                reps = null, // 初始为空，用户在训练时填入
                                timeSeconds = null,
                                rpe = null,
                                isCompleted = false,
                                isWarmupSet = false,
                                notes = null,
                                timestamp = currentTime,
                            )

                            sessionSetDao.insertSet(sessionSetEntity)
                        }
                    }

                    Timber.d(
                        "成功从TemplateVersion创建Session: sessionId=$sessionId, templateVersion=${templateVersion.versionNumber}",
                    )
                    sessionId
                }
            }

        override suspend fun updateSession(session: WorkoutSession): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("更新训练会话: sessionId=${session.id}")
                    sessionDao.updateSession(session.toEntity())
                }
            }

        override suspend fun deleteSession(sessionId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("删除训练会话: sessionId=$sessionId")
                    sessionDao.deleteSession(sessionId)
                    // 由于设置了 CASCADE，相关的 SessionExercise 和 SessionSet 会自动删除
                }
            }

        // ==================== Session 状态管理 ====================

        override suspend fun updateSessionStatus(sessionId: String, status: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("更新训练会话状态: sessionId=$sessionId, status=$status")
                    sessionDao.updateSessionStatus(sessionId, status)
                }
            }

        override suspend fun completeSession(sessionId: String, endTime: Long, duration: Long): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("完成训练会话: sessionId=$sessionId")
                    sessionDao.completeSession(sessionId, endTime, duration)
                }
            }

        override suspend fun startExercise(sessionId: String, exerciseId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("开始动作: sessionId=$sessionId, exerciseId=$exerciseId")
                    sessionExerciseDao.startExercise(exerciseId, System.currentTimeMillis())
                }
            }

        override suspend fun completeExercise(exerciseId: String, completedSets: Int): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("完成动作: exerciseId=$exerciseId, completedSets=$completedSets")
                    sessionExerciseDao.completeExercise(exerciseId, completedSets, System.currentTimeMillis())
                }
            }

        // ==================== SessionSet 操作 ====================

        override suspend fun completeSet(
            setId: String,
            weight: Double?,
            reps: Int?,
            timeSeconds: Int?,
            rpe: Double?,
        ): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("完成组数: setId=$setId, weight=$weight, reps=$reps")
                    sessionSetDao.completeSetWithDetails(
                        setId = setId,
                        weight = weight,
                        reps = reps,
                        timeSeconds = timeSeconds,
                        rpe = rpe,
                        timestamp = System.currentTimeMillis(),
                    )
                }
            }

        override suspend fun addSetToExercise(exerciseId: String, set: SessionSet): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("添加组数到动作: exerciseId=$exerciseId, setNumber=${set.setNumber}")
                    sessionSetDao.insertSet(set.toEntity())
                }
            }

        override fun getExerciseSets(exerciseId: String): Flow<ModernResult<List<SessionSet>>> =
            sessionSetDao.getExerciseSets(exerciseId)
                .map { entities ->
                    entities.toSessionSetDomainSafely()
                }
                .catch { e ->
                    Timber.e(e, "获取动作组数失败: exerciseId=$exerciseId")
                    emit(
                        ModernResult.Error(
                            e.toModernDataError(
                                operationName = "getExerciseSets",
                                uiMessage = UiText.DynamicString("获取组数记录失败"),
                            ),
                        ),
                    )
                }
                .flowOn(ioDispatcher)

        // ==================== 自动保存功能 ====================

        override suspend fun saveSessionSnapshot(sessionId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("保存训练会话快照: sessionId=$sessionId")
                    sessionDao.updateAutoSaveTime(sessionId, System.currentTimeMillis())
                }
            }

        override suspend fun saveAutoSave(autoSave: SessionAutoSave): ModernResult<String> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("保存自动保存记录: sessionId=${autoSave.sessionId}")
                    sessionAutoSaveDao.insertAutoSave(autoSave.toEntity())
                    autoSave.id
                }
            }

        override suspend fun getLatestAutoSave(sessionId: String): ModernResult<SessionAutoSave?> =
            withContext(ioDispatcher) {
                safeCatch {
                    sessionAutoSaveDao.getLatestAutoSave(sessionId)?.toDomain()
                }
            }

        override suspend fun getRecoverableSessions(userId: String): ModernResult<List<SessionAutoSave>> =
            withContext(ioDispatcher) {
                safeCatch {
                    sessionAutoSaveDao.getRecoverableSessions(userId).map { it.toDomain() }
                }
            }

        // ==================== 统计功能 ====================

        override suspend fun updateExerciseStats(stats: ExerciseHistoryStats): ModernResult<Unit> =
            withContext(ioDispatcher) {
                safeCatch {
                    Timber.d("更新动作统计: exerciseId=${stats.exerciseId}")
                    exerciseHistoryStatsDao.insertStats(stats.toEntity())
                }
            }

        override suspend fun getExerciseStats(userId: String, exerciseId: String): ModernResult<ExerciseHistoryStats?> =
            withContext(ioDispatcher) {
                safeCatch {
                    exerciseHistoryStatsDao.getStatsByUserAndExercise(userId, exerciseId)?.toDomain()
                }
            }

        override fun getUserStats(userId: String): Flow<ModernResult<List<ExerciseHistoryStats>>> =
            exerciseHistoryStatsDao.getUserStats(userId)
                .map { entities ->
                    safeCatch { entities.map { it.toDomain() } }
                }
                .catch { e ->
                    Timber.e(e, "获取用户统计失败: userId=$userId")
                    emit(
                        ModernResult.Error(
                            e.toModernDataError(
                                operationName = "getUserStats",
                                uiMessage = UiText.DynamicString("获取训练统计失败"),
                            ),
                        ),
                    )
                }
                .flowOn(ioDispatcher)

        // ==================== 查询统计 ====================

        override suspend fun getCompletedSessionCount(userId: String): ModernResult<Int> =
            withContext(ioDispatcher) {
                safeCatch {
                    sessionDao.getCompletedSessionCount(userId)
                }
            }

        override suspend fun getTotalVolumeInPeriod(userId: String, startTime: Long): ModernResult<Double> =
            withContext(ioDispatcher) {
                safeCatch {
                    sessionDao.getTotalVolumeInPeriod(userId, startTime) ?: 0.0
                }
            }

        override suspend fun getAverageSessionDuration(userId: String): ModernResult<Long> =
            withContext(ioDispatcher) {
                safeCatch {
                    sessionDao.getAverageSessionDuration(userId) ?: 0L
                }
            }
    }
