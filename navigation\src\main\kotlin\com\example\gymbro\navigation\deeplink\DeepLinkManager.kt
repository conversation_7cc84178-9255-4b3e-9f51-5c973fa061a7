package com.example.gymbro.navigation.deeplink

import android.content.Intent
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber

/**
 * 深度链接管理器
 * 负责统一管理应用的深度链接功能，包括处理、状态管理和历史记录
 */
@Singleton
class DeepLinkManager
    @Inject
    constructor(
        private val deepLinkHandler: DeepLinkHandler,
    ) {

        // 深度链接处理状态
        private val _processingState = MutableStateFlow(DeepLinkProcessingState())
        val processingState: StateFlow<DeepLinkProcessingState> = _processingState.asStateFlow()

        // 深度链接历史记录
        private val _history = MutableStateFlow<List<DeepLinkHistoryItem>>(emptyList())
        val history: StateFlow<List<DeepLinkHistoryItem>> = _history.asStateFlow()

        /**
         * 处理Intent中的深度链接
         * @param intent 包含深度链接的Intent
         * @return 处理结果
         */
        suspend fun handleIntent(intent: Intent): ModernResult<DeepLinkResult> {
            return try {
                Timber.d("开始处理Intent深度链接")
                _processingState.value = _processingState.value.copy(isProcessing = true, error = null)

                val deepLink = extractDeepLinkFromIntent(intent)
                if (deepLink == null) {
                    Timber.d("Intent中未找到深度链接")
                    _processingState.value = _processingState.value.copy(isProcessing = false)
                    return ModernResult.success(
                        DeepLinkResult(
                            success = false,
                            navigated = false,
                            targetRoute = null,
                            message = UiText.DynamicString("Intent中未包含深度链接"),
                        ),
                    )
                }

                val result = handleDeepLink(deepLink)
                when (result) {
                    is ModernResult.Success -> {
                        // 记录到历史
                        addToHistory(deepLink, result.data)
                        _processingState.value = _processingState.value.copy(
                            isProcessing = false,
                            lastProcessedLink = deepLink,
                            lastResult = result.data,
                        )
                    }

                    is ModernResult.Error -> {
                        _processingState.value = _processingState.value.copy(
                            isProcessing = false,
                            error = result.error.uiMessage,
                        )
                    }

                    is ModernResult.Loading -> {
                        // 保持处理状态
                    }
                }

                result
            } catch (e: Exception) {
                Timber.e(e, "处理Intent深度链接时发生异常")
                _processingState.value = _processingState.value.copy(
                    isProcessing = false,
                    error = UiText.DynamicString("处理深度链接时发生异常: ${e.message}"),
                )
                ModernResult.error(
                    ModernDataError(
                        operationName = "handleIntent",
                        errorType = GlobalErrorType.System.General,
                        category = ErrorCategory.SYSTEM,
                        uiMessage = UiText.DynamicString("处理Intent深度链接时发生异常: ${e.message}"),
                        metadataMap = mapOf("intent" to intent.toString()),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 直接处理深度链接字符串
         * @param deepLink 深度链接URL
         * @return 处理结果
         */
        suspend fun handleDeepLink(deepLink: String): ModernResult<DeepLinkResult> {
            return try {
                Timber.d("开始处理深度链接: $deepLink")
                _processingState.value = _processingState.value.copy(isProcessing = true, error = null)

                val result = deepLinkHandler.handleDeepLink(deepLink)

                when (result) {
                    is ModernResult.Success -> {
                        addToHistory(deepLink, result.data)
                        _processingState.value = _processingState.value.copy(
                            isProcessing = false,
                            lastProcessedLink = deepLink,
                            lastResult = result.data,
                        )
                    }

                    is ModernResult.Error -> {
                        _processingState.value = _processingState.value.copy(
                            isProcessing = false,
                            error = result.error.uiMessage,
                        )
                    }

                    is ModernResult.Loading -> {
                        // 保持处理状态
                    }
                }

                result
            } catch (e: Exception) {
                Timber.e(e, "处理深度链接时发生异常: $deepLink")
                _processingState.value = _processingState.value.copy(
                    isProcessing = false,
                    error = UiText.DynamicString("处理深度链接时发生异常: ${e.message}"),
                )
                ModernResult.error(
                    ModernDataError(
                        operationName = "handleDeepLink",
                        errorType = GlobalErrorType.System.General,
                        category = ErrorCategory.SYSTEM,
                        uiMessage = UiText.DynamicString("处理深度链接时发生异常: ${e.message}"),
                        metadataMap = mapOf("deepLink" to deepLink),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 验证深度链接是否有效
         * @param deepLink 深度链接URL
         * @return 是否有效
         */
        fun isValidDeepLink(deepLink: String): Boolean {
            return deepLinkHandler.isValidDeepLink(deepLink)
        }

        /**
         * 获取支持的深度链接类型
         * @return 支持的深度链接类型列表
         */
        fun getSupportedDeepLinkTypes(): List<DeepLinkType> {
            return deepLinkHandler.getSupportedDeepLinkTypes()
        }

        /**
         * 生成深度链接URL
         * @param type 深度链接类型
         * @param params 参数映射
         * @return 生成的深度链接URL
         */
        fun generateDeepLink(type: DeepLinkType, params: Map<String, String> = emptyMap()): String {
            return type.buildUrl(params)
        }

        /**
         * 清除处理状态
         */
        fun clearProcessingState() {
            _processingState.value = DeepLinkProcessingState()
        }

        /**
         * 清除历史记录
         */
        fun clearHistory() {
            _history.value = emptyList()
            Timber.d("深度链接历史记录已清除")
        }

        /**
         * 获取最近的深度链接历史
         * @param limit 返回的最大数量
         * @return 最近的深度链接历史列表
         */
        fun getRecentHistory(limit: Int = 10): List<DeepLinkHistoryItem> {
            return _history.value.take(limit)
        }

        // ==================== 私有方法 ====================

        /**
         * 从Intent中提取深度链接
         */
        private fun extractDeepLinkFromIntent(intent: Intent): String? {
            return when (intent.action) {
                Intent.ACTION_VIEW -> {
                    intent.data?.toString()
                }

                else -> {
                    // 检查其他可能包含深度链接的字段
                    intent.getStringExtra("deep_link")
                        ?: intent.getStringExtra("url")
                        ?: intent.dataString
                }
            }
        }

        /**
         * 添加到历史记录
         */
        private fun addToHistory(deepLink: String, result: DeepLinkResult) {
            val historyItem = DeepLinkHistoryItem(
                deepLink = deepLink,
                result = result,
                timestamp = System.currentTimeMillis(),
            )

            val currentHistory = _history.value.toMutableList()
            currentHistory.add(0, historyItem) // 添加到列表开头

            // 限制历史记录数量
            if (currentHistory.size > MAX_HISTORY_SIZE) {
                currentHistory.removeAt(currentHistory.size - 1)
            }

            _history.value = currentHistory
            Timber.d("深度链接已添加到历史记录: $deepLink")
        }

        companion object {
            private const val MAX_HISTORY_SIZE = 50
        }
    }

/**
 * 深度链接处理状态
 */
data class DeepLinkProcessingState(
    val isProcessing: Boolean = false,
    val lastProcessedLink: String? = null,
    val lastResult: DeepLinkResult? = null,
    val error: UiText? = null,
)

/**
 * 深度链接历史记录项
 */
data class DeepLinkHistoryItem(
    val deepLink: String,
    val result: DeepLinkResult,
    val timestamp: Long,
) {
    /**
     * 获取格式化的时间戳
     */
    fun getFormattedTimestamp(): String {
        val date = java.util.Date(timestamp)
        val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
        return formatter.format(date)
    }

    /**
     * 是否处理成功
     */
    val isSuccess: Boolean
        get() = result.success

    /**
     * 是否成功导航
     */
    val isNavigated: Boolean
        get() = result.navigated
}
