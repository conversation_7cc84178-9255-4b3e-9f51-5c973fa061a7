package com.example.gymbro.features.workout.shared.utils

import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.WorkoutPlan as DomainWorkoutPlan
import com.example.gymbro.domain.workout.model.plan.DayPlan as DomainDayPlan
import com.example.gymbro.shared.models.workout.DayPlan
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import com.example.gymbro.shared.models.workout.PlanType
import com.example.gymbro.shared.models.workout.WorkoutPlan

/**
 * Plan测试数据工厂
 *
 * 🎯 为Plan JSON序列化测试提供标准化测试数据
 * - 包含多个训练日和休息日的复杂Plan
 * - 包含Template引用关系的DayPlan
 * - 边界情况和异常数据
 * - 符合Template_Naming_Convention_Baseline.md规范
 *
 * 测试数据类型：
 * - 简单Plan（基础测试）
 * - 复杂Plan（多天训练+休息日混合）
 * - 边界情况Plan（空数据、极大数据）
 * - Template引用Plan（包含templateVersionIds）
 * - 异常数据Plan（用于容错测试）
 */
object PlanTestDataFactory {

    // ==================== 基础测试数据常量 ====================

    const val TEST_USER_ID = "test_user_123"
    const val TEST_PLAN_ID_SIMPLE = "plan_simple_001"
    const val TEST_PLAN_ID_COMPLEX = "plan_complex_002"
    const val TEST_PLAN_ID_BOUNDARY = "plan_boundary_003"
    const val TEST_TEMPLATE_ID_1 = "template_chest_001"
    const val TEST_TEMPLATE_ID_2 = "template_back_002"
    const val TEST_TEMPLATE_ID_3 = "template_legs_003"
    const val TEST_TEMPLATE_VERSION_ID_1 = "template_version_001"
    const val TEST_TEMPLATE_VERSION_ID_2 = "template_version_002"
    const val TEST_TEMPLATE_VERSION_ID_3 = "template_version_003"

    // ==================== Shared Models (用于JSON序列化测试) ====================

    /**
     * 创建简单的训练计划
     * 包含3天：2天训练 + 1天休息
     */
    fun createSimplePlan(): WorkoutPlan {
        val dailySchedule = mapOf(
            1 to DayPlan(
                dayNumber = 1,
                templateIds = listOf(TEST_TEMPLATE_ID_1),
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_1),
                isRestDay = false,
                dayNotes = "胸部训练日",
                progress = PlanProgressStatus.NOT_STARTED,
            ),
            2 to DayPlan(
                dayNumber = 2,
                templateIds = emptyList(),
                templateVersionIds = emptyList(),
                isRestDay = true,
                dayNotes = "休息日",
                progress = PlanProgressStatus.NOT_STARTED,
            ),
            3 to DayPlan(
                dayNumber = 3,
                templateIds = listOf(TEST_TEMPLATE_ID_2),
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_2),
                isRestDay = false,
                dayNotes = "背部训练日",
                progress = PlanProgressStatus.NOT_STARTED,
            ),
        )

        return WorkoutPlan(
            id = TEST_PLAN_ID_SIMPLE,
            name = "简单训练计划",
            description = "适合初学者的3天计划",
            userId = TEST_USER_ID,
            targetGoal = "增肌",
            difficultyLevel = 1,
            estimatedDuration = 60,
            planType = PlanType.LINEAR,
            dailySchedule = dailySchedule,
            totalDays = 3,
            tags = listOf("初学者", "增肌"),
            isPublic = false,
            isTemplate = false,
            isFavorite = false,
            createdAt = 1690000000000L,
            updatedAt = 1690000001000L,
            isActive = true,
        )
    }

    /**
     * 创建复杂的训练计划
     * 包含7天：5天训练 + 2天休息，多Template混合
     */
    fun createComplexPlan(): WorkoutPlan {
        val dailySchedule = mapOf(
            1 to DayPlan(
                dayNumber = 1,
                templateIds = listOf(TEST_TEMPLATE_ID_1, TEST_TEMPLATE_ID_2),
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_1, TEST_TEMPLATE_VERSION_ID_2),
                isRestDay = false,
                dayNotes = "上肢综合训练",
                estimatedDuration = 90,
                progress = PlanProgressStatus.IN_PROGRESS,
            ),
            2 to DayPlan(
                dayNumber = 2,
                templateIds = listOf(TEST_TEMPLATE_ID_3),
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_3),
                isRestDay = false,
                dayNotes = "腿部训练",
                estimatedDuration = 75,
                progress = PlanProgressStatus.COMPLETED,
            ),
            3 to DayPlan.createRestDay(3, "积极恢复日"),
            4 to DayPlan(
                dayNumber = 4,
                templateIds = listOf(TEST_TEMPLATE_ID_1),
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_1),
                isRestDay = false,
                dayNotes = "胸部重点训练",
                estimatedDuration = 80,
                progress = PlanProgressStatus.NOT_STARTED,
            ),
            5 to DayPlan(
                dayNumber = 5,
                templateIds = listOf(TEST_TEMPLATE_ID_2, TEST_TEMPLATE_ID_3),
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_2, TEST_TEMPLATE_VERSION_ID_3),
                isRestDay = false,
                dayNotes = "背部+腿部强化",
                estimatedDuration = 100,
                progress = PlanProgressStatus.NOT_STARTED,
            ),
            6 to DayPlan.createRestDay(6, "完全休息日"),
            7 to DayPlan(
                dayNumber = 7,
                templateIds = listOf(TEST_TEMPLATE_ID_1, TEST_TEMPLATE_ID_2, TEST_TEMPLATE_ID_3),
                templateVersionIds = listOf(
                    TEST_TEMPLATE_VERSION_ID_1,
                    TEST_TEMPLATE_VERSION_ID_2,
                    TEST_TEMPLATE_VERSION_ID_3,
                ),
                isRestDay = false,
                dayNotes = "全身综合训练",
                estimatedDuration = 120,
                progress = PlanProgressStatus.NOT_STARTED,
            ),
        )

        return WorkoutPlan(
            id = TEST_PLAN_ID_COMPLEX,
            name = "高级7天训练计划",
            description = "适合有经验者的高强度7天训练计划，包含多种训练模式组合",
            userId = TEST_USER_ID,
            targetGoal = "力量与耐力并重",
            difficultyLevel = 4,
            estimatedDuration = 90,
            planType = PlanType.CYCLIC,
            dailySchedule = dailySchedule,
            totalDays = 7,
            tags = listOf("高级", "力量", "耐力", "综合训练"),
            isPublic = true,
            isTemplate = true,
            isFavorite = true,
            createdAt = 1690000000000L,
            updatedAt = 1690000005000L,
            isActive = true,
        )
    }

    /**
     * 创建边界情况测试Plan - 空计划
     */
    fun createEmptyPlan(): WorkoutPlan {
        return WorkoutPlan(
            id = TEST_PLAN_ID_BOUNDARY,
            name = "",
            description = null,
            userId = TEST_USER_ID,
            targetGoal = null,
            difficultyLevel = 0,
            estimatedDuration = null,
            planType = PlanType.CUSTOM,
            dailySchedule = emptyMap(),
            totalDays = 0,
            tags = emptyList(),
            isPublic = false,
            isTemplate = false,
            isFavorite = false,
            createdAt = 0L,
            updatedAt = 0L,
            isActive = false,
        )
    }

    /**
     * 创建边界情况测试Plan - 极大数据
     */
    fun createLargePlan(): WorkoutPlan {
        val largeDailySchedule = (1..30).associateWith { dayNumber ->
            if (dayNumber % 7 == 0) {
                // 每周日休息
                DayPlan.createRestDay(dayNumber, "周休息日")
            } else {
                DayPlan(
                    dayNumber = dayNumber,
                    templateIds = listOf(TEST_TEMPLATE_ID_1, TEST_TEMPLATE_ID_2, TEST_TEMPLATE_ID_3),
                    templateVersionIds = listOf(
                        TEST_TEMPLATE_VERSION_ID_1,
                        TEST_TEMPLATE_VERSION_ID_2,
                        TEST_TEMPLATE_VERSION_ID_3,
                    ),
                    isRestDay = false,
                    dayNotes = "第${dayNumber}天训练 - 全身综合训练",
                    estimatedDuration = 90,
                    progress = when {
                        dayNumber <= 10 -> PlanProgressStatus.COMPLETED
                        dayNumber <= 15 -> PlanProgressStatus.IN_PROGRESS
                        else -> PlanProgressStatus.NOT_STARTED
                    },
                )
            }
        }

        return WorkoutPlan(
            id = "plan_large_30days",
            name = "30天高强度训练计划",
            description = "适合资深健身者的30天高强度训练计划，包含多种训练模式的科学组合，全面提升身体素质",
            userId = TEST_USER_ID,
            targetGoal = "全面提升身体素质",
            difficultyLevel = 5,
            estimatedDuration = 90,
            planType = PlanType.LINEAR,
            dailySchedule = largeDailySchedule,
            totalDays = 30,
            tags = listOf("高级", "30天挑战", "全身训练", "力量", "耐力", "减脂", "增肌"),
            isPublic = true,
            isTemplate = true,
            isFavorite = true,
            createdAt = 1690000000000L,
            updatedAt = 1690000010000L,
            isActive = true,
        )
    }

    /**
     * 创建异常数据Plan - 用于容错测试
     */
    fun createInvalidPlan(): WorkoutPlan {
        val invalidDailySchedule = mapOf(
            1 to DayPlan(
                dayNumber = 1,
                templateIds = listOf("invalid_template_id_1", "invalid_template_id_2"),
                templateVersionIds = listOf("invalid_version_id_1", "invalid_version_id_2"),
                isRestDay = false,
                dayNotes = "包含无效Template引用的训练日",
                progress = PlanProgressStatus.NOT_STARTED,
            ),
            2 to DayPlan(
                dayNumber = 2,
                templateIds = emptyList(),
                templateVersionIds = emptyList(),
                isRestDay = false, // 矛盾：没有模板但不是休息日
                dayNotes = "矛盾状态测试",
                progress = PlanProgressStatus.NOT_STARTED,
            ),
        )

        return WorkoutPlan(
            id = "plan_invalid_001",
            name = "异常测试计划",
            description = "用于测试异常情况处理的计划",
            userId = "invalid_user_id",
            targetGoal = "异常测试",
            difficultyLevel = -1, // 无效难度
            estimatedDuration = -30, // 无效时长
            planType = PlanType.CUSTOM,
            dailySchedule = invalidDailySchedule,
            totalDays = 2,
            tags = listOf("异常测试", "容错测试"),
            isPublic = false,
            isTemplate = false,
            isFavorite = false,
            createdAt = -1L, // 无效时间戳
            updatedAt = -1L,
            isActive = false,
        )
    }

    // ==================== Domain Models (用于Repository层测试) ====================

    /**
     * 创建Domain层简单Plan
     */
    fun createSimpleDomainPlan(): DomainWorkoutPlan {
        val dailySchedule = mapOf(
            1 to DomainDayPlan(
                dayNumber = 1,
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_1),
                isRestDay = false,
                dayNotes = UiText.DynamicString("胸部训练日"),
                isCompleted = false,
                progress = PlanProgressStatus.NOT_STARTED,
            ),
            2 to DomainDayPlan.createRestDay(2, UiText.DynamicString("休息日")),
            3 to DomainDayPlan(
                dayNumber = 3,
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_2),
                isRestDay = false,
                dayNotes = UiText.DynamicString("背部训练日"),
                isCompleted = false,
                progress = PlanProgressStatus.NOT_STARTED,
            ),
        )

        return DomainWorkoutPlan(
            id = TEST_PLAN_ID_SIMPLE,
            name = UiText.DynamicString("简单训练计划"),
            description = UiText.DynamicString("适合初学者的3天计划"),
            userId = TEST_USER_ID,
            targetGoal = "增肌",
            difficultyLevel = 1,
            estimatedDuration = 60,
            dailySchedule = dailySchedule,
            totalDays = 3,
            tags = listOf("初学者", "增肌"),
            isPublic = false,
            isTemplate = false,
            isFavorite = false,
            createdAt = 1690000000000L,
            updatedAt = 1690000001000L,
        )
    }

    /**
     * 创建Domain层复杂Plan
     */
    fun createComplexDomainPlan(): DomainWorkoutPlan {
        val dailySchedule = mapOf(
            1 to DomainDayPlan(
                dayNumber = 1,
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_1, TEST_TEMPLATE_VERSION_ID_2),
                isRestDay = false,
                dayNotes = UiText.DynamicString("上肢综合训练"),
                isCompleted = false,
                progress = PlanProgressStatus.IN_PROGRESS,
            ),
            2 to DomainDayPlan(
                dayNumber = 2,
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_3),
                isRestDay = false,
                dayNotes = UiText.DynamicString("腿部训练"),
                isCompleted = true,
                progress = PlanProgressStatus.COMPLETED,
            ),
            3 to DomainDayPlan.createRestDay(3, UiText.DynamicString("积极恢复日")),
            4 to DomainDayPlan(
                dayNumber = 4,
                templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_1),
                isRestDay = false,
                dayNotes = UiText.DynamicString("胸部重点训练"),
                isCompleted = false,
                progress = PlanProgressStatus.NOT_STARTED,
            ),
        )

        return DomainWorkoutPlan(
            id = TEST_PLAN_ID_COMPLEX,
            name = UiText.DynamicString("高级训练计划"),
            description = UiText.DynamicString("适合有经验者的高强度训练计划"),
            userId = TEST_USER_ID,
            targetGoal = "力量与耐力并重",
            difficultyLevel = 4,
            estimatedDuration = 90,
            dailySchedule = dailySchedule,
            totalDays = 4,
            tags = listOf("高级", "力量", "耐力"),
            isPublic = true,
            isTemplate = true,
            isFavorite = true,
            createdAt = 1690000000000L,
            updatedAt = 1690000005000L,
        )
    }

    // ==================== JSON字符串测试数据 ====================

    /**
     * 获取简单Plan的预期JSON字符串（符合命名规范）
     */
    fun getSimplePlanExpectedJson(): String {
        return """
        {
            "id": "$TEST_PLAN_ID_SIMPLE",
            "name": "简单训练计划",
            "description": "适合初学者的3天计划",
            "userId": "$TEST_USER_ID",
            "targetGoal": "增肌",
            "difficultyLevel": 1,
            "estimatedDuration": 60,
            "planType": "LINEAR",
            "dailySchedule": {
                "1": {
                    "dayNumber": 1,
                    "templateIds": ["$TEST_TEMPLATE_ID_1"],
                    "templateVersionIds": ["$TEST_TEMPLATE_VERSION_ID_1"],
                    "isRestDay": false,
                    "dayNotes": "胸部训练日",
                    "orderIndex": 0,
                    "estimatedDuration": null,
                    "progress": "NOT_STARTED"
                },
                "2": {
                    "dayNumber": 2,
                    "templateIds": [],
                    "templateVersionIds": [],
                    "isRestDay": true,
                    "dayNotes": "休息日",
                    "orderIndex": 0,
                    "estimatedDuration": null,
                    "progress": "NOT_STARTED"
                },
                "3": {
                    "dayNumber": 3,
                    "templateIds": ["$TEST_TEMPLATE_ID_2"],
                    "templateVersionIds": ["$TEST_TEMPLATE_VERSION_ID_2"],
                    "isRestDay": false,
                    "dayNotes": "背部训练日",
                    "orderIndex": 0,
                    "estimatedDuration": null,
                    "progress": "NOT_STARTED"
                }
            },
            "totalDays": 3,
            "tags": ["初学者", "增肌"],
            "isPublic": false,
            "isTemplate": false,
            "isFavorite": false,
            "createdAt": 1690000000000,
            "updatedAt": 1690000001000,
            "isActive": true
        }
            """.trimIndent()
    }

    /**
     * 获取无效JSON字符串 - 用于反序列化容错测试
     */
    fun getInvalidJsonString(): String {
        return """
        {
            "id": "invalid_plan",
            "name": "无效计划",
            "userId": null,
            "dailySchedule": "invalid_schedule_format",
            "totalDays": "not_a_number",
            "isActive": "not_a_boolean"
        }
            """.trimIndent()
    }

    /**
     * 获取部分字段缺失的JSON字符串
     */
    fun getPartialJsonString(): String {
        return """
        {
            "id": "partial_plan",
            "name": "部分字段计划",
            "userId": "$TEST_USER_ID"
        }
            """.trimIndent()
    }

    // ==================== 批量测试数据生成器 ====================

    /**
     * 创建多个测试Plan用于批量测试
     */
    fun createMultiplePlans(count: Int): List<WorkoutPlan> {
        return (1..count).map { index ->
            WorkoutPlan(
                id = "plan_batch_${index.toString().padStart(3, '0')}",
                name = "批量测试计划 #$index",
                description = "这是第$index 个批量测试计划",
                userId = TEST_USER_ID,
                targetGoal = if (index % 2 == 0) "增肌" else "减脂",
                difficultyLevel = (index % 5) + 1,
                estimatedDuration = 60 + (index % 4) * 15,
                planType = when (index % 3) {
                    0 -> PlanType.LINEAR
                    1 -> PlanType.CYCLIC
                    else -> PlanType.CUSTOM
                },
                dailySchedule = mapOf(
                    1 to DayPlan(
                        dayNumber = 1,
                        templateIds = listOf(TEST_TEMPLATE_ID_1),
                        templateVersionIds = listOf(TEST_TEMPLATE_VERSION_ID_1),
                        isRestDay = false,
                        dayNotes = "批量测试第$index 天训练",
                        progress = PlanProgressStatus.NOT_STARTED,
                    ),
                ),
                totalDays = 1,
                tags = listOf("批量测试", "计划#$index"),
                isPublic = index % 2 == 0,
                isTemplate = index % 3 == 0,
                isFavorite = index % 4 == 0,
                createdAt = 1690000000000L + index * 1000L,
                updatedAt = 1690000000000L + index * 1000L + 500L,
                isActive = index % 2 == 1,
            )
        }
    }

    // ==================== 性能测试数据生成器 ====================

    /**
     * 创建大型Plan用于性能测试
     * @param totalDays 总天数
     * @param templatesPerDay 每天模板数量
     */
    fun createPerformanceTestPlan(totalDays: Int, templatesPerDay: Int): WorkoutPlan {
        val templateIds = (1..templatesPerDay).map { "template_perf_$it" }
        val templateVersionIds = (1..templatesPerDay).map { "version_perf_$it" }

        val dailySchedule = (1..totalDays).associateWith { dayNumber ->
            if (dayNumber % 7 == 0) {
                DayPlan.createRestDay(dayNumber, "性能测试休息日")
            } else {
                DayPlan(
                    dayNumber = dayNumber,
                    templateIds = templateIds,
                    templateVersionIds = templateVersionIds,
                    isRestDay = false,
                    dayNotes = "性能测试第${dayNumber}天 - $templatesPerDay 个模板",
                    estimatedDuration = templatesPerDay * 30,
                    progress = PlanProgressStatus.NOT_STARTED,
                )
            }
        }

        return WorkoutPlan(
            id = "plan_performance_${totalDays}_$templatesPerDay",
            name = "性能测试计划(${totalDays}天-${templatesPerDay}模板)",
            description = "用于JSON序列化性能测试的大型计划，包含$totalDays 天训练，每天$templatesPerDay 个模板",
            userId = TEST_USER_ID,
            targetGoal = "性能测试",
            difficultyLevel = 5,
            estimatedDuration = templatesPerDay * 30,
            planType = PlanType.LINEAR,
            dailySchedule = dailySchedule,
            totalDays = totalDays,
            tags = (1..10).map { "性能测试标签$it" }, // 多个标签
            isPublic = true,
            isTemplate = false,
            isFavorite = false,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            isActive = true,
        )
    }
}
