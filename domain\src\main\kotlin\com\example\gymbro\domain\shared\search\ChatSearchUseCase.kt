package com.example.gymbro.domain.shared.search

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.shared.search.search.ChatSearchRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 聊天搜索用例
 *
 * 提供聊天历史记录的搜索功能，包括全文搜索、会话内搜索等
 */
@Singleton
class ChatSearchUseCase
    @Inject
    constructor(
        private val chatSearchRepository: ChatSearchRepository,
    ) {
        /**
         * 搜索聊天消息
         *
         * @param query 搜索关键词
         * @param limit 搜索结果限制
         * @return 搜索结果Flow
         */
        fun searchMessages(
            query: String,
            limit: Int = 40,
        ): Flow<ModernResult<List<CoachMessage>>> =
            chatSearchRepository.searchMessages(
                query = query.trim(),
                limit = limit,
            )

        /**
         * 在特定会话中搜索消息
         *
         * @param sessionId 会话ID
         * @param query 搜索关键词
         * @param limit 搜索结果限制
         * @return 搜索结果Flow
         */
        fun searchMessagesInSession(
            sessionId: String,
            query: String,
            limit: Int = 20,
        ): Flow<ModernResult<List<CoachMessage>>> =
            chatSearchRepository.searchMessagesInSession(
                sessionId = sessionId,
                query = query.trim(),
                limit = limit,
            )

        /**
         * 获取搜索建议
         *
         * @param query 搜索关键词前缀
         * @param limit 建议数量限制
         * @return 搜索建议Flow
         */
        fun getSearchSuggestions(
            query: String,
            limit: Int = 10,
        ): Flow<ModernResult<List<String>>> =
            chatSearchRepository.getSearchSuggestions(
                query = query.trim(),
                limit = limit,
            )

        /**
         * 搜索最近的聊天消息
         *
         * @param query 搜索关键词
         * @param limit 搜索结果限制
         * @return 按时间排序的搜索结果Flow
         */
        fun searchRecentMessages(
            query: String,
            limit: Int = 20,
        ): Flow<ModernResult<List<CoachMessage>>> =
            chatSearchRepository
                .searchMessages(query.trim(), limit)
                .map { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            // 按时间倒序排列，最新的在前
                            val sortedMessages = result.data.sortedByDescending { it.timestamp }
                            ModernResult.Success(sortedMessages)
                        }

                        else -> result
                    }
                }

        /**
         * 搜索用户消息
         *
         * @param query 搜索关键词
         * @param limit 搜索结果限制
         * @return 仅包含用户消息的搜索结果Flow
         */
        fun searchUserMessages(
            query: String,
            limit: Int = 20,
        ): Flow<ModernResult<List<CoachMessage.UserMessage>>> =
            chatSearchRepository
                .searchMessages(query.trim(), limit * 2) // 获取更多结果用于过滤
                .map { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val userMessages =
                                result.data
                                    .filterIsInstance<CoachMessage.UserMessage>()
                                    .take(limit)
                            ModernResult.Success(userMessages)
                        }

                        is ModernResult.Error -> ModernResult.Error(result.error)
                        is ModernResult.Loading -> ModernResult.Loading
                    }
                }

        /**
         * 搜索AI回复消息
         *
         * @param query 搜索关键词
         * @param limit 搜索结果限制
         * @return 仅包含AI消息的搜索结果Flow
         */
        fun searchAiMessages(
            query: String,
            limit: Int = 20,
        ): Flow<ModernResult<List<CoachMessage.AiMessage>>> =
            chatSearchRepository
                .searchMessages(query.trim(), limit * 2) // 获取更多结果用于过滤
                .map { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val aiMessages =
                                result.data
                                    .filterIsInstance<CoachMessage.AiMessage>()
                                    .take(limit)
                            ModernResult.Success(aiMessages)
                        }

                        is ModernResult.Error -> ModernResult.Error(result.error)
                        is ModernResult.Loading -> ModernResult.Loading
                    }
                }
    }
