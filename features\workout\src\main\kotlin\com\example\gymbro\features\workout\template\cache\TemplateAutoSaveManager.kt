package com.example.gymbro.features.workout.template.cache

import com.example.gymbro.core.di.qualifiers.ApplicationScope
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.TemplateSaver
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import timber.log.Timber

/**
 * 模板自动保存管理器 - 使用应用级别作用域的30秒自动保存
 *
 * 🎯 重构目标:
 * - 使用ApplicationScope避免生命周期问题
 * - 30秒防抖延迟
 * - 委托给TemplateSaver执行实际保存
 * - 统一的后台自动保存机制
 *
 * 🏗️ 架构原则:
 * - 无状态的防抖触发器
 * - 委托保存逻辑给TemplateSaver
 * - 使用不会被取消的ApplicationScope
 */
@Singleton
class TemplateAutoSaveManager
    @Inject
    constructor(
        private val templateSaver: TemplateSaver,
        @ApplicationScope private val applicationScope: CoroutineScope,
    ) {

        // 🔥 Phase 0: 重新实现版本守护机制，防止AutoSave竞态条件
        private var onStateChanged: ((TemplateContract.AutoSaveState) -> Unit)? = null
        private var pendingSaveJob: Job? = null
        private var isActive = false

        // 🔥 Phase 0: AutoSave 版本守护数据结构
        private data class PendingAutoSave(
            val templateSnapshot: WorkoutTemplate,
            val snapshotVersion: Long,
            val scheduledTime: Long,
        )

        private var pendingAutoSave: PendingAutoSave? = null

        // P3: 新增手动保存协调机制
        private var lastManualSaveTime: Long = 0L
        private var pendingSaveCount: Int = 0

        companion object {
            private const val AUTO_SAVE_DELAY_MS = 30_000L // 30秒自动保存延迟

            // P3: 新增防抖和协调常量
            private const val AUTO_SAVE_DEBOUNCE_MS = 2_000L // 2秒防抖间隔
            private const val MANUAL_SAVE_COOLDOWN_MS = 5_000L // 手动保存后5秒冷却期
            private const val MAX_PENDING_SAVES = 3 // 最大待处理保存数量
        }

        /**
         * 🔥 新增：设置状态变更回调
         * 用于 ViewModel 监听自动保存状态变化
         */
        fun setStateChangeCallback(callback: (TemplateContract.AutoSaveState) -> Unit) {
            onStateChanged = callback
        }

        /**
         * 🔥 新增：清除状态变更回调
         */
        fun clearStateChangeCallback() {
            onStateChanged = null
        }

        /**
         * P3: 调度自动保存操作 - 已禁用
         * 🔥 暂时禁用template自动保存功能
         *
         * @param template 要保存的模板对象
         */
        fun scheduleAutoSave(template: WorkoutTemplate) {
            // 🔥 暂时禁用template自动保存功能
            Timber.d("🚫 [AUTO-SAVE-DISABLED] Template自动保存功能已被完全禁用")
            return
        }

        /**
         * 立即执行自动保存（不等待30秒）- 已禁用
         * 🔥 暂时禁用template自动保存功能
         */
        fun saveImmediately(template: WorkoutTemplate) {
            // 🔥 暂时禁用template自动保存功能
            Timber.d("🚫 [AUTO-SAVE-DISABLED] Template立即保存功能已被完全禁用")
            return
        }

        /**
         * 取消待处理的保存任务
         */
        fun cancelPendingSaves() {
            pendingSaveJob?.cancel()
            pendingSaveJob = null
            pendingAutoSave = null // 🔥 Phase 0: 清除待处理的自动保存记录
            Timber.d("🚫 已取消待处理的自动保存")
        }

        /**
         * P3: 使待处理的自动保存失效 - 增强版本
         * 当手动保存发生时调用，防止旧的自动保存覆盖新数据
         */
        fun invalidatePendingAutoSave(reason: String = "手动保存") {
            val wasActive = pendingAutoSave != null
            pendingAutoSave = null

            // P3: 更新手动保存时间
            lastManualSaveTime = System.currentTimeMillis()

            if (wasActive) {
                Timber.tag("WK-TEMPLATE-AUTOSAVE").i("🔥 [P3-AUTOSAVE] 使待处理自动保存失效: $reason, 设置冷却期")
                onStateChanged?.invoke(TemplateContract.AutoSaveState.Inactive)
            }
        }

        /**
         * P3: 通知手动保存完成
         * 用于协调自动保存和手动保存的时机
         */
        fun notifyManualSaveCompleted() {
            lastManualSaveTime = System.currentTimeMillis()
            Timber.tag("WK-TEMPLATE-AUTOSAVE").i("🔥 [P3-AUTOSAVE] 手动保存完成，设置冷却期: ${MANUAL_SAVE_COOLDOWN_MS}ms")
        }

        /**
         * 清理资源，停止所有自动保存任务
         */
        fun cleanup() {
            cancelPendingSaves()
            Timber.d("🧹 自动保存管理器已清理")
        }
    }

// === 兼容性数据类 - 为其他文件提供支持 ===

/**
 * 恢复结果密封类
 */
sealed class RecoveryResult {
    object NotFound : RecoveryResult()

    data class Found(
        val template: com.example.gymbro.shared.models.workout.WorkoutTemplateDto,
        val isCrashRecovery: Boolean,
    ) : RecoveryResult()

    data class FoundDraft(
        val draft: com.example.gymbro.domain.workout.model.TemplateDraft,
        val isCrashRecovery: Boolean,
    ) : RecoveryResult()

    data class Error(
        val message: String,
    ) : RecoveryResult()
}
