package com.example.gymbro.domain.workout.usecase.stats

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.workout.model.stats.TimeRange
import com.example.gymbro.domain.workout.model.stats.UnifiedWorkoutStatistics
import com.example.gymbro.domain.workout.repository.StatsRepository
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.datetime.*

/**
 * 获取训练统计数据用例
 *
 * 根据Clean Architecture原则，封装统计数据获取的业务逻辑。
 * 提供不同时间范围的统计数据查询功能。
 *
 * 设计特点：
 * - 单一职责：只负责统计数据获取
 * - 响应式：返回Flow以支持实时更新
 * - 错误处理：统一的错误处理机制
 * - 线程安全：使用指定的IO调度器
 *
 * @param statsRepository 统计数据仓库
 * @param ioDispatcher IO调度器
 */
class GetStatsUseCase
    @Inject
    constructor(
        private val statsRepository: StatsRepository,
        private val ioDispatcher: CoroutineDispatcher,
    ) {
        /**
         * 获取指定时间范围的统计数据
         *
         * @param timeRange 时间范围类型
         * @param startDate 开始日期（可选，如果不指定则根据timeRange计算）
         * @param endDate 结束日期（可选，如果不指定则根据timeRange计算）
         * @return 统计数据流
         */
        operator fun invoke(
            timeRange: TimeRange,
            startDate: LocalDate? = null,
            endDate: LocalDate? = null,
        ): Flow<ModernResult<UnifiedWorkoutStatistics>> {
            val (calculatedStartDate, calculatedEndDate) = calculateDateRange(timeRange, startDate, endDate)

            return statsRepository
                .getStatsInRange(
                    startDate = calculatedStartDate,
                    endDate = calculatedEndDate,
                    timeRange = timeRange,
                ).flowOn(ioDispatcher)
        }

        /**
         * 根据时间范围类型计算具体的开始和结束日期
         */
        private fun calculateDateRange(
            timeRange: TimeRange,
            startDate: LocalDate?,
            endDate: LocalDate?,
        ): Pair<LocalDate, LocalDate> {
            val today =
                Clock.System
                    .now()
                    .toLocalDateTime(TimeZone.currentSystemDefault())
                    .date

            return when (timeRange) {
                TimeRange.WEEK -> {
                    val weekStart = today.minus(today.dayOfWeek.ordinal, DateTimeUnit.DAY)
                    val weekEnd = weekStart.plus(6, DateTimeUnit.DAY)
                    Pair(startDate ?: weekStart, endDate ?: weekEnd)
                }

                TimeRange.MONTH -> {
                    val monthStart = LocalDate(today.year, today.month, 1)
                    val monthEnd =
                        monthStart
                            .plus(1, DateTimeUnit.MONTH)
                            .minus(1, DateTimeUnit.DAY)
                    Pair(startDate ?: monthStart, endDate ?: monthEnd)
                }

                TimeRange.YEAR -> {
                    val yearStart = LocalDate(today.year, 1, 1)
                    val yearEnd = LocalDate(today.year, 12, 31)
                    Pair(startDate ?: yearStart, endDate ?: yearEnd)
                }

                TimeRange.CUSTOM -> {
                    // 自定义范围必须提供开始和结束日期
                    val defaultStart = today.minus(7, DateTimeUnit.DAY)
                    val defaultEnd = today
                    Pair(startDate ?: defaultStart, endDate ?: defaultEnd)
                }
            }
        }
    }
