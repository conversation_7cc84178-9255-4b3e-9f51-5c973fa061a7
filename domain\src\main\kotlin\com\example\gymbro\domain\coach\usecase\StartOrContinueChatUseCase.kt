package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 开始或继续AI教练对话用例
 *
 * 业务场景：用户开始或继续AI教练对话
 *
 * 功能说明：
 * - 检查用户是否有进行中的会话
 * - 若无则创建新会话
 * - 若有则返回现有会话
 *
 * @param chatSessionManagementUseCase 聊天会话管理用例
 * @param dispatcher IO调度器
 * @param logger 日志记录器
 */
@Singleton
class StartOrContinueChatUseCase
    @Inject
    constructor(
        private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<StartOrContinueChatUseCase.Params, ChatSession>(dispatcher, logger) {
        /**
         * 参数数据类
         *
         * @property userId 用户ID
         * @property initialMessage 初始消息，可选
         */
        data class Params(
            val userId: String,
            val initialMessage: String? = null,
        )

        /**
         * 执行开始或继续聊天逻辑
         *
         * @param parameters 包含用户ID和初始消息的参数
         * @return 聊天会话或错误信息
         */
        override suspend fun execute(parameters: Params): ModernResult<ChatSession> {
            logger.d("开始或继续聊天: userId=${parameters.userId}")

            return try {
                // 验证用户ID
                if (parameters.userId.isBlank()) {
                    logger.w("用户ID为空")
                    return ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "startOrContinueChat",
                            message = UiText.DynamicString("用户ID不能为空"),
                            metadataMap = mapOf<String, Any>("userId" to parameters.userId),
                        ),
                    )
                }

                // 1. 检查是否有活跃会话
                // 🔧 修复：使用withTimeout防止Flow无限挂起
                val activeSessions =
                    withTimeoutOrNull(5000L) {
                        chatSessionManagementUseCase
                            .getUserSessions(
                                userId = parameters.userId,
                                includeMessages = false,
                                limit = 1,
                                offset = 0,
                            ).first()
                    }

                if (activeSessions != null) {
                    when (activeSessions) {
                        is ModernResult.Success -> {
                            val sessions = activeSessions.data
                            if (sessions.isNotEmpty()) {
                                // 继续现有会话
                                val existingSession = sessions.first()
                                logger.d("继续现有会话: sessionId=${existingSession.id}")
                                ModernResult.success(existingSession)
                            } else {
                                // 创建新会话
                                logger.d("没有活跃会话，创建新会话")
                                createNewSession(parameters)
                            }
                        }

                        is ModernResult.Error -> {
                            logger.e("获取活跃会话失败: ${activeSessions.error}")
                            // 🔧 修复：获取会话失败时仍然尝试创建新会话
                            logger.d("获取会话失败，尝试创建新会话")
                            createNewSession(parameters)
                        }

                        is ModernResult.Loading -> {
                            logger.w("获取会话状态为Loading，创建新会话")
                            createNewSession(parameters)
                        }
                    }
                } else {
                    // 🔧 修复：超时情况下直接创建新会话
                    logger.w("获取活跃会话超时，创建新会话")
                    createNewSession(parameters)
                }
            } catch (e: Exception) {
                logger.e(e, "开始或继续聊天失败")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "startOrContinueChat",
                        message = UiText.DynamicString("开始聊天失败: ${e.message}"),
                        metadataMap = mapOf<String, Any>("userId" to parameters.userId),
                        recoverable = true,
                    ),
                )
            }
        }

        /**
         * 创建新的聊天会话
         *
         * @param parameters 参数对象
         * @return 新创建的会话或错误信息
         */
        private suspend fun createNewSession(parameters: Params): ModernResult<ChatSession> {
            logger.d("创建新聊天会话: userId=${parameters.userId}")

            return try {
                // 🔥 恢复：进入coach时创建空白历史记录，提供无感顺畅体验
                val result =
                    chatSessionManagementUseCase.createSession(
                        userId = parameters.userId,
                        title = "新的对话", // 提供默认标题
                        firstUserMessage = null, // 创建空白会话，等待用户输入
                    )

                when (result) {
                    is ModernResult.Success -> {
                        logger.d("新聊天会话创建成功: sessionId=${result.data.id}")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.e("创建聊天会话失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                logger.e(e, "创建新会话异常")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "createNewSession",
                        message = UiText.DynamicString("创建新会话失败: ${e.message}"),
                        metadataMap = mapOf<String, Any>("userId" to parameters.userId),
                        recoverable = true,
                    ),
                )
            }
        }
    }
