package com.example.gymbro.data.coach.repository.search

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.coach.dao.ChatSearchDao
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.shared.search.search.ChatSearchRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 聊天搜索仓库实现
 *
 * 提供FTS4全文搜索功能，支持聊天历史记录的快速检索
 */
@Singleton
class ChatSearchRepositoryImpl
    @Inject
    constructor(
        private val chatSearchDao: Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) : ChatSearchRepository {
        /**
         * 全文搜索聊天消息
         *
         * @param query 搜索关键词
         * @param limit 搜索结果限制
         * @return 搜索结果Flow
         */
        override fun searchMessages(
            query: String,
            limit: Int,
        ): Flow<ModernResult<List<CoachMessage>>> =
            flow {
                emit(ModernResult.Loading)

                try {
                    withContext(ioDispatcher) {
                        if (query.isBlank()) {
                            emit(ModernResult.Success(emptyList()))
                            return@withContext
                        }

                        Timber.d("开始FTS4搜索: query=$query, limit=$limit")

                        val searchResults = chatSearchDao.ftsSearch(query, limit)
                        val messages =
                            searchResults.map { result ->
                                when (result.role) {
                                    ChatRaw.ROLE_USER ->
                                        CoachMessage.UserMessage(
                                            id = result.id.toString(),
                                            content = result.content,
                                            timestamp = result.timestamp,
                                        )

                                    ChatRaw.ROLE_ASSISTANT ->
                                        CoachMessage.AiMessage(
                                            id = result.id.toString(),
                                            content = result.content,
                                            timestamp = result.timestamp,
                                        )

                                    else -> throw IllegalArgumentException(
                                        "Unknown message role: ${result.role}",
                                    )
                                }
                            }

                        Timber.d("FTS4搜索完成: 找到${messages.size}条消息")
                        emit(ModernResult.Success(messages))
                    }
                } catch (e: Exception) {
                    Timber.e(e, "FTS4搜索失败")
                    emit(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "ChatSearchRepositoryImpl.searchMessages",
                                message = UiText.DynamicString("搜索聊天消息失败"),
                                entityType = "CoachMessage",
                                cause = e,
                            ),
                        ),
                    )
                }
            }

        /**
         * 按会话ID搜索消息
         *
         * @param sessionId 会话ID
         * @param query 搜索关键词
         * @param limit 搜索结果限制
         * @return 搜索结果Flow
         */
        override fun searchMessagesInSession(
            sessionId: String,
            query: String,
            limit: Int,
        ): Flow<ModernResult<List<CoachMessage>>> =
            flow {
                emit(ModernResult.Loading)

                try {
                    withContext(ioDispatcher) {
                        if (query.isBlank()) {
                            emit(ModernResult.Success(emptyList()))
                            return@withContext
                        }

                        Timber.d("开始会话内FTS4搜索: sessionId=$sessionId, query=$query")

                        // 使用FTS4搜索，然后过滤特定会话
                        val searchResults = chatSearchDao.ftsSearch(query, limit * 2) // 获取更多结果用于过滤
                        val filteredResults = searchResults.filter { it.sessionId == sessionId }.take(limit)

                        val messages =
                            filteredResults.map { result ->
                                when (result.role) {
                                    ChatRaw.ROLE_USER ->
                                        CoachMessage.UserMessage(
                                            id = result.id.toString(),
                                            content = result.content,
                                            timestamp = result.timestamp,
                                        )

                                    ChatRaw.ROLE_ASSISTANT ->
                                        CoachMessage.AiMessage(
                                            id = result.id.toString(),
                                            content = result.content,
                                            timestamp = result.timestamp,
                                        )

                                    else -> throw IllegalArgumentException(
                                        "Unknown message role: ${result.role}",
                                    )
                                }
                            }

                        Timber.d("会话内FTS4搜索完成: 找到${messages.size}条消息")
                        emit(ModernResult.Success(messages))
                    }
                } catch (e: Exception) {
                    Timber.e(e, "会话内FTS4搜索失败")
                    emit(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "ChatSearchRepositoryImpl.searchMessagesInSession",
                                message = UiText.DynamicString("搜索会话消息失败"),
                                entityType = "CoachMessage",
                                cause = e,
                            ),
                        ),
                    )
                }
            }

        /**
         * 获取搜索建议
         *
         * @param query 搜索关键词前缀
         * @param limit 建议数量限制
         * @return 搜索建议Flow
         */
        override fun getSearchSuggestions(
            query: String,
            limit: Int,
        ): Flow<ModernResult<List<String>>> =
            flow {
                emit(ModernResult.Loading)

                try {
                    withContext(ioDispatcher) {
                        if (query.length < 2) {
                            emit(ModernResult.Success(emptyList()))
                            return@withContext
                        }

                        // 简单的搜索建议实现
                        // 可以后续扩展为更智能的建议算法
                        val searchResults = chatSearchDao.ftsSearch("$query*", limit)
                        val suggestions =
                            searchResults
                                .map { it.content }
                                .flatMap { content ->
                                    // 提取包含查询词的短语
                                    content
                                        .split(" ")
                                        .filter { it.contains(query, ignoreCase = true) }
                                        .take(3)
                                }.distinct()
                                .take(limit)

                        emit(ModernResult.Success(suggestions))
                    }
                } catch (e: Exception) {
                    Timber.e(e, "获取搜索建议失败")
                    emit(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "ChatSearchRepositoryImpl.getSearchSuggestions",
                                message = UiText.DynamicString("获取搜索建议失败"),
                                entityType = "String",
                                cause = e,
                            ),
                        ),
                    )
                }
            }
    }
