package com.example.gymbro.data.repository.region

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.ai.api.IpEchoService
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber

/**
 * 地区检测仓库
 *
 * 整合IP获取和中国IP检测功能，提供统一的地区检测服务
 * 支持多服务容错和超时控制
 */
@Singleton
class RegionDetectionRepository
    @Inject
    constructor(
        private val ipEchoService: IpEchoService,
        private val chinaIpDetector: ChinaIpDetector,
    ) {
        companion object {
            private const val IP_FETCH_TIMEOUT_MS = 3000L
            private const val DETECTION_TIMEOUT_MS = 5000L
        }

        /**
         * 检测用户当前区域
         * @return "CN" 表示中国，"INTERNATIONAL" 表示国际区域
         */
        suspend fun detectCurrentRegion(): ModernResult<String> {
            return try {
                Timber.d("开始检测用户当前区域")

                // 1. 获取当前IP地址
                val currentIp = getCurrentIp()
                if (currentIp == null) {
                    Timber.w("无法获取当前IP，返回默认国际区域")
                    return ModernResult.Success("INTERNATIONAL")
                }

                // 2. 检测IP是否属于中国
                val regionResult = detectIpRegion(currentIp)
                val region =
                    when (regionResult) {
                        is ModernResult.Success -> regionResult.data
                        is ModernResult.Error -> {
                            Timber.w("IP检测失败，返回默认国际区域: ${regionResult.error.message}")
                            return ModernResult.Success("INTERNATIONAL")
                        }

                        is ModernResult.Loading -> {
                            return ModernResult.Success("INTERNATIONAL")
                        }
                    }

                Timber.i("地区检测完成: IP=$currentIp, Region=$region")
                ModernResult.Success(region)
            } catch (e: Exception) {
                Timber.e(e, "地区检测过程中发生异常")
                ModernResult.Success("INTERNATIONAL") // 异常时返回默认值，确保应用稳定
            }
        }

        /**
         * 检测指定IP的区域
         * @param ip 要检测的IP地址
         * @return "CN" 表示中国，"INTERNATIONAL" 表示国际区域
         */
        suspend fun detectIpRegion(ip: String): ModernResult<String> =
            try {
                withTimeoutOrNull(DETECTION_TIMEOUT_MS) {
                    val isChinaIp = chinaIpDetector.isChinaIp(ip)
                    when (isChinaIp) {
                        is ModernResult.Success -> {
                            val region = if (isChinaIp.data) "CN" else "INTERNATIONAL"
                            ModernResult.Success(region)
                        }

                        is ModernResult.Error -> {
                            Timber.w("中国IP检测失败: ${isChinaIp.error}")
                            ModernResult.Success("INTERNATIONAL")
                        }

                        is ModernResult.Loading -> {
                            ModernResult.Success("INTERNATIONAL")
                        }
                    }
                } ?: ModernResult.Success("INTERNATIONAL")
            } catch (e: Exception) {
                Timber.e(e, "IP区域检测异常")
                ModernResult.Success("INTERNATIONAL")
            }

        /**
         * 获取详细的IP位置信息
         * @return 包含IP、国家、区域等信息的Map
         */
        suspend fun getDetailedLocationInfo(): ModernResult<Map<String, Any>> {
            return try {
                val currentIp = getCurrentIp()
                if (currentIp == null) {
                    return ModernResult.Success(
                        mapOf(
                            "ip" to "unknown",
                            "country" to "unknown",
                            "region" to "INTERNATIONAL",
                        ),
                    )
                }

                val regionResult = detectIpRegion(currentIp)
                val region =
                    when (regionResult) {
                        is ModernResult.Success -> regionResult.data
                        else -> "INTERNATIONAL"
                    }
                val locationInfo =
                    mapOf(
                        "ip" to currentIp,
                        "country" to if (region == "CN") "China" else "International",
                        "region" to region,
                        "detectedAt" to System.currentTimeMillis(),
                    )

                ModernResult.Success(locationInfo)
            } catch (e: Exception) {
                Timber.e(e, "获取详细位置信息失败")
                ModernResult.Success(
                    mapOf(
                        "ip" to "unknown",
                        "country" to "unknown",
                        "region" to "INTERNATIONAL",
                        "error" to e.message.orEmpty(),
                    ),
                )
            }
        }

        /**
         * 获取当前IP地址
         * 使用超时控制，确保不阻塞应用启动
         */
        private suspend fun getCurrentIp(): String? =
            try {
                withTimeoutOrNull(IP_FETCH_TIMEOUT_MS) {
                    Timber.d("🌐 开始获取IP地址...")
                    val response = ipEchoService.getIp()
                    if (response.isSuccessful) {
                        val ip = response.body()?.trim()
                        Timber.d("🌐 成功获取IP地址: $ip")
                        ip
                    } else {
                        Timber.w("🌐 IP获取失败: HTTP ${response.code()}")
                        null
                    }
                }
            } catch (e: java.net.SocketException) {
                // 🔥 专门处理Socket连接重置错误
                Timber.w(e, "🌐 网络连接重置，可能是网络不稳定或服务端限制")
                null
            } catch (e: java.net.UnknownHostException) {
                // DNS解析失败
                Timber.w(e, "🌐 无法解析域名，可能是DNS问题")
                null
            } catch (e: javax.net.ssl.SSLException) {
                // SSL握手失败
                Timber.w(e, "🌐 SSL连接失败，可能是证书问题")
                null
            } catch (e: java.net.ConnectException) {
                // 连接超时
                Timber.w(e, "🌐 连接超时，网络可能不可用")
                null
            } catch (e: Exception) {
                Timber.e(e, "🌐 获取IP地址时发生未知异常")
                null
            }
    }
