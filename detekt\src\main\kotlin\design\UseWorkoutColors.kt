package com.example.gymbro.buildlogic.detekt.design

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.KtDotQualifiedExpression
import org.jetbrains.kotlin.psi.KtCallExpression

/**
 * 检查硬编码颜色的使用。
 *
 * 规则：应该使用设计系统 tokens 而不是硬编码颜色值。
 * 检测：Color.Red, Color(0xFF...), 等硬编码颜色
 * 建议：使用 designSystem tokens
 */
class UseWorkoutColors(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.Warning,
        "应该使用设计系统 tokens 而不是硬编码颜色值。",
        Debt.FIVE_MINS
    )

    override fun visitDotQualifiedExpression(expression: KtDotQualifiedExpression) {
        super.visitDotQualifiedExpression(expression)

        val text = expression.text

        // 检查硬编码的 Color 常量
        if (text.matches(Regex("Color\\.[A-Z][a-zA-Z]*"))) {
            // 如 Color.Red, Color.Blue 等
            report(
                CodeSmell(
                    issue,
                    Entity.from(expression),
                    "检测到硬编码颜色 '$text'，建议使用设计系统 tokens。"
                )
            )
        }
    }

    override fun visitCallExpression(call: KtCallExpression) {
        super.visitCallExpression(call)

        val text = call.text

        // 检查 Color(0xFF...) 构造函数调用
        if (text.matches(Regex("Color\\s*\\(\\s*0x[0-9A-Fa-f]+.*\\)"))) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(call),
                    "检测到硬编码颜色值 '$text'，建议使用设计系统 tokens。"
                )
            )
        }
    }


}
