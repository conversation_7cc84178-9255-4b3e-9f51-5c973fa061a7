package com.example.gymbro.domain.shared.search

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.model.search.SearchQuery
import com.example.gymbro.domain.model.search.SearchResult
import com.example.gymbro.domain.model.search.SearchType
import com.example.gymbro.domain.repository.SearchRepository
import javax.inject.Inject
import kotlinx.coroutines.flow.Flow

/**
 * 混合搜索UseCase
 *
 * 提供统一的搜索入口，根据查询类型执行相应的搜索策略
 */
class HybridSearchUseCase
    @Inject
    constructor(
        private val searchRepository: SearchRepository,
    ) {
        /**
         * 执行搜索操作
         *
         * @param query 搜索查询
         * @return 搜索结果流
         */
        operator fun invoke(query: SearchQuery): Flow<ModernResult<List<SearchResult>>> =
            when (query.type) {
                SearchType.HYBRID -> searchRepository.hybridSearch(query)
                SearchType.SEMANTIC_ONLY -> TODO("将vectorSearch转换为Flow")
                SearchType.KEYWORD_ONLY -> TODO("将keywordSearch转换为Flow")
            }

        /**
         * 执行即时搜索（单次结果）
         *
         * @param query 搜索查询
         * @return 搜索结果
         */
        suspend fun searchOnce(query: SearchQuery): ModernResult<List<SearchResult>> =
            when (query.type) {
                SearchType.HYBRID -> {
                    // 对于混合搜索，我们需要组合关键词和语义搜索结果
                    val keywordResults = searchRepository.keywordSearch(query)
                    val semanticResults = searchRepository.vectorSearch(query)

                    combineResults(keywordResults, semanticResults, query.semanticWeight)
                }

                SearchType.SEMANTIC_ONLY -> searchRepository.vectorSearch(query)
                SearchType.KEYWORD_ONLY -> searchRepository.keywordSearch(query)
            }

        /**
         * 合并关键词和语义搜索结果
         *
         * @param keywordResults 关键词搜索结果
         * @param semanticResults 语义搜索结果
         * @param semanticWeight 语义搜索权重
         * @return 合并后的搜索结果
         */
        private fun combineResults(
            keywordResults: ModernResult<List<SearchResult>>,
            semanticResults: ModernResult<List<SearchResult>>,
            semanticWeight: Float,
        ): ModernResult<List<SearchResult>> {
            if (keywordResults.isError && semanticResults.isError) {
                return ModernResult.error(
                    DataErrors.DataError.query(
                        operationName = "HybridSearchUseCase.combineResults",
                        message = UiText.DynamicString("关键词和语义搜索都失败"),
                        entityType = "SearchResult",
                        metadataMap =
                            mapOf(
                                "searchType" to "hybrid",
                                "errorSubtype" to "SEARCH_ENGINE_FAILURE",
                            ),
                    ),
                )
            }

            val keywordList = keywordResults.getOrNull() ?: emptyList()
            val semanticList = semanticResults.getOrNull() ?: emptyList()

            // 创建ID到结果的映射
            val keywordMap = keywordList.associateBy { it.id }
            val semanticMap = semanticList.associateBy { it.id }
            val allIds = (keywordMap.keys + semanticMap.keys).distinct()

            // 合并和重新打分
            val combinedResults =
                allIds.mapNotNull { id ->
                    val keywordResult = keywordMap[id]
                    val semanticResult = semanticMap[id]

                    when {
                        keywordResult != null && semanticResult != null -> {
                            // 同时在两个结果中，合并分数
                            val combinedScore =
                                (1 - semanticWeight) * keywordResult.score +
                                    semanticWeight * semanticResult.score
                            keywordResult.copy(
                                score = combinedScore,
                                matchDetails =
                                    keywordResult.matchDetails?.copy(
                                        semanticScore = semanticResult.score,
                                    ),
                            )
                        }

                        keywordResult != null -> {
                            // 只在关键词结果中
                            keywordResult.copy(score = (1 - semanticWeight) * keywordResult.score)
                        }

                        semanticResult != null -> {
                            // 只在语义结果中
                            semanticResult.copy(score = semanticWeight * semanticResult.score)
                        }

                        else -> null
                    }
                }

            // 按分数排序并返回
            val sortedResults =
                combinedResults
                    .sortedByDescending { it.score }
                    .take(keywordResults.getOrNull()?.size ?: semanticResults.getOrNull()?.size ?: 20)

            return ModernResult.success(sortedResults)
        }
    }
