package com.example.gymbro.features.profile.internal.presentation.notifications

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.style.TextAlign
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.core.resources.AndroidResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroSettingsGroup
import com.example.gymbro.designSystem.components.base.scaffold.ProfileScaffold
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.domain.profile.model.user.settings.NotificationSettings
import com.example.gymbro.features.profile.internal.presentation.base.ModernUserProfileViewModelMVI
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract
import com.example.gymbro.features.profile.internal.presentation.util.ProfileStrings
import kotlinx.coroutines.launch

/**
 * 系统中的通知类型常量，用于访问userSettings?.notificationSettings
 */
// @Deprecated("使用 com.example.gymbro.domain.model.NotificationType 代替", ReplaceWith("NotificationType"))
// private object NotificationTypeOld {
//     const val WORKOUT_REMINDER = "workout_reminder"
//     const val WORKOUT_COMPLETION = "workout_completion"
//     const val ACHIEVEMENT = "achievement"
//     const val WORKOUT_SUGGESTION = "workout_suggestion"
//     const val APP_UPDATES = "app_updates"
//     const val SUBSCRIPTION = "subscription"
// } // Removed deprecated object

/**
 * 基于两个布尔值生成"全部开启"/"部分开启"/"全部关闭"的状态文本
 *
 * @param setting1 第一个设置的状态
 * @param setting2 第二个设置的状态
 * @return 表示状态的文本
 */
@Composable
private fun getSettingStatusText(
    setting1: Boolean,
    setting2: Boolean,
): String {
    val context = LocalContext.current
    val resourceProvider = AndroidResourceProvider(context)
    return when {
        setting1 && setting2 -> ProfileStrings.allEnabled.asString(resourceProvider)
        !setting1 && !setting2 -> ProfileStrings.allDisabled.asString(resourceProvider)
        else -> ProfileStrings.partiallyEnabled.asString(resourceProvider)
    }
}

/**
 * 通知设置屏幕 - 完整实现版本
 * 基于Profile模块最佳实践，严格遵循Clean Architecture + MVI 2.0标准
 *
 * 功能特性：
 * - 倒计时通知设置（休息计时器通知）
 * - 训练通知设置（训练提醒、训练开始等）
 * - 日历通知设置（计划更新、日程提醒等）
 * - 通知时间设置和声音/振动配置
 * - 即时保存逻辑，移除手动保存按钮
 * - 集成主题系统和动态主题切换
 */
@Composable
internal fun NotificationsScreen(
    viewModel: ModernUserProfileViewModelMVI,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val uiState by viewModel.state.collectAsStateWithLifecycle()
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val hapticFeedback = LocalHapticFeedback.current

    // State投射 - 为不同的通知设置创建单独的状态投射，避免高频重组
    val isLoading: Boolean by remember { derivedStateOf { uiState.isLoading } }
    val userSettings = uiState.userSettings
    val notificationSettings = userSettings?.notifications ?: NotificationSettings()

    // 基础通知设置
    val workoutReminderEnabled: Boolean by remember {
        derivedStateOf { notificationSettings.workout }
    }
    val friendsActivityEnabled: Boolean by remember {
        derivedStateOf { notificationSettings.friendRequest }
    }

    // 详细通知设置
    val restTimerSettings by remember {
        derivedStateOf { notificationSettings.restTimerNotification }
    }
    val workoutReminderSettings by remember {
        derivedStateOf { notificationSettings.workoutReminder }
    }
    val calendarSyncSettings by remember {
        derivedStateOf { notificationSettings.calendarSync }
    }
    val statusBarSettings by remember {
        derivedStateOf { notificationSettings.statusBar }
    }
    val globalVibrationEnabled by remember {
        derivedStateOf { notificationSettings.vibrationEnabled }
    }
    val globalNotificationTime by remember {
        derivedStateOf { notificationSettings.notificationTime }
    }

    // Effect处理 - 正确的MVI副作用处理方式
    LaunchedEffect(Unit) {
        viewModel.effects.collect { effect ->
            when (effect) {
                is ProfileContract.Effect.ShowError -> {
                    coroutineScope.launch {
                        val message =
                            when (effect.message) {
                                is UiText.DynamicString -> effect.message.value
                                is UiText.StringResource -> "设置错误"
                                is UiText.Empty -> "发生未知错误"
                                is UiText.ErrorCode -> "系统错误: ${effect.message.errorCode.code}"
                            }
                        snackbarHostState.showSnackbar(message)
                    }
                }

                is ProfileContract.Effect.ShowSuccess -> {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar("通知设置已保存")
                    }
                }

                is ProfileContract.Effect.HapticFeedback -> {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                }

                else -> {
                    // 其他Effect处理
                }
            }
        }
    }

    ProfileScaffold(
        title = "通知设置",
        onNavigateBack = onNavigateBack,
        modifier = modifier,
        isLoading = isLoading,
        snackbarHostState = snackbarHostState,
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // === 基础通知设置区块 ===
            BasicNotificationSettingsSection(
                workoutReminderEnabled = workoutReminderEnabled,
                friendsActivityEnabled = friendsActivityEnabled,
                hapticFeedback = hapticFeedback,
                onWorkoutReminderChange = { enabled ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateWorkoutReminder(enabled))
                },
                onFriendsActivityChange = { enabled ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateFriendsActivity(enabled))
                },
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            // === 倒计时通知设置区块 ===
            RestTimerNotificationSection(
                settings = restTimerSettings,
                hapticFeedback = hapticFeedback,
                onEnabledChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateRestTimerNotification(enabled))
                },
                onSoundChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateRestTimerSound(enabled))
                },
                onVibrationChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateRestTimerVibration(enabled))
                },
                onLockScreenChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateRestTimerLockScreen(enabled))
                },
                onAutoStartChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateRestTimerAutoStart(enabled))
                },
                onIntervalChange = { interval: Int ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateRestTimerInterval(interval))
                },
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            // === 训练提醒设置区块 ===
            WorkoutReminderSection(
                settings = workoutReminderSettings,
                hapticFeedback = hapticFeedback,
                onEnabledChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateWorkoutReminderEnabled(enabled))
                },
                onDailyReminderChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateDailyReminder(enabled))
                },
                onReminderTimeChange = { time: String ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateReminderTime(time))
                },
                onWeeklyPlanReminderChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateWeeklyPlanReminder(enabled))
                },
                onRestDayReminderChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateRestDayReminder(enabled))
                },
                onMotivationalMessagesChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateMotivationalMessages(enabled))
                },
                onSoundChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateWorkoutReminderSound(enabled))
                },
                onVibrationChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateWorkoutReminderVibration(enabled))
                },
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            // === 日历同步设置区块 ===
            // TODO: 实现CalendarSyncSection
            /*
            CalendarSyncSection(
                settings = calendarSyncSettings,
                hapticFeedback = hapticFeedback,
                onEnabledChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateCalendarSyncEnabled(enabled))
                },
                onSystemCalendarSyncChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateSystemCalendarSync(enabled))
                },
                onPlanUpdateNotificationChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdatePlanUpdateNotification(enabled))
                },
                onScheduleChangeNotificationChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateScheduleChangeNotification(enabled))
                },
                onUpcomingWorkoutReminderChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateUpcomingWorkoutReminder(enabled))
                },
                onReminderMinutesBeforeChange = { minutes: Int ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateReminderMinutesBefore(minutes))
                },
                onSoundChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateCalendarSyncSound(enabled))
                },
                onVibrationChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateCalendarSyncVibration(enabled))
                },
            )
             */

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            // === 状态栏通知设置区块 ===
            StatusBarNotificationSection(
                settings = statusBarSettings,
                hapticFeedback = hapticFeedback,
                onEnabledChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateStatusBarNotificationEnabled(enabled))
                },
                onOngoingWorkoutChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateStatusBarOngoingWorkout(enabled))
                },
                onRestTimerChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateStatusBarRestTimer(enabled))
                },
                onProgressChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateStatusBarProgress(enabled))
                },
                onPriorityChange = {
                        priority: com.example.gymbro.domain.profile.model.user.settings.NotificationPriority ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateStatusBarPriority(priority))
                },
                onAutoHideChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateStatusBarAutoHide(enabled))
                },
                onAutoHideDelayChange = { delayMinutes: Int ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateStatusBarAutoHideDelay(delayMinutes))
                },
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            // === 全局通知设置区块 ===
            // TODO: 实现GlobalNotificationSection
            /*
            GlobalNotificationSection(
                vibrationEnabled = globalVibrationEnabled,
                notificationTime = globalNotificationTime,
                hapticFeedback = hapticFeedback,
                onVibrationChange = { enabled: Boolean ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateGlobalVibration(enabled))
                },
                onNotificationTimeChange = { time: String ->
                    viewModel.dispatch(ProfileContract.Intent.UpdateGlobalNotificationTime(time))
                },
            )
             */

            Spacer(modifier = Modifier.height(Tokens.Spacing.XLarge))

            // 底部提示信息
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = "通知设置会实时保存",
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center,
                )
                Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
            }
        }
    }
}

// === 🎯 性能优化: 独立的通知设置组件 ===

/**
 * 基础通知设置区块
 */
@Composable
private fun BasicNotificationSettingsSection(
    workoutReminderEnabled: Boolean,
    friendsActivityEnabled: Boolean,
    hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback,
    onWorkoutReminderChange: (Boolean) -> Unit,
    onFriendsActivityChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    GymBroSettingsGroup(modifier = modifier) {
        // 训练提醒开关项
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.XXLarge + Tokens.Spacing.Small)
                    .padding(horizontal = Tokens.Spacing.Medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                imageVector = Icons.Default.FitnessCenter,
                contentDescription = "训练提醒",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(Tokens.Spacing.Large),
            )
            Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

            Text(
                text = "训练提醒",
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(1f),
            )

            Switch(
                checked = workoutReminderEnabled,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onWorkoutReminderChange(enabled)
                },
            )
        }

        // 好友动态开关项
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(Tokens.Spacing.XXLarge + Tokens.Spacing.Small)
                    .padding(horizontal = Tokens.Spacing.Medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                imageVector = Icons.Default.People,
                contentDescription = "好友动态",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(Tokens.Spacing.Large),
            )
            Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

            Text(
                text = "好友动态",
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(1f),
            )

            Switch(
                checked = friendsActivityEnabled,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onFriendsActivityChange(enabled)
                },
            )
        }
    }
}

/**
 * 通用通知设置项组件
 */
@Composable
private fun NotificationSettingItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(Tokens.Spacing.XXLarge + Tokens.Spacing.Small)
            .padding(horizontal = Tokens.Spacing.Medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(Tokens.Spacing.Large),
        )
        Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyLarge,
            )
            Text(
                text = subtitle,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                style = MaterialTheme.typography.bodyMedium,
            )
        }

        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
        )
    }
}

/**
 * 倒计时通知设置区块
 */
@Composable
private fun RestTimerNotificationSection(
    settings: com.example.gymbro.domain.profile.model.user.settings.RestTimerNotificationSettings,
    hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback,
    onEnabledChange: (Boolean) -> Unit,
    onSoundChange: (Boolean) -> Unit,
    onVibrationChange: (Boolean) -> Unit,
    onLockScreenChange: (Boolean) -> Unit,
    onAutoStartChange: (Boolean) -> Unit,
    onIntervalChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    GymBroSettingsGroup(modifier = modifier) {
        // 区块标题
        Text(
            text = "倒计时通知",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
        )

        // 启用倒计时通知
        NotificationSettingItem(
            icon = Icons.Default.Timer,
            title = "倒计时通知",
            subtitle = "休息时间结束提醒",
            checked = settings.enabled,
            onCheckedChange = { enabled ->
                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                onEnabledChange(enabled)
            },
        )

        if (settings.enabled) {
            // 声音通知
            NotificationSettingItem(
                icon = Icons.Default.VolumeUp,
                title = "声音提醒",
                subtitle = "播放提示音",
                checked = settings.soundEnabled,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onSoundChange(enabled)
                },
            )

            // 振动通知
            NotificationSettingItem(
                icon = Icons.Default.Vibration,
                title = "振动提醒",
                subtitle = "设备振动提示",
                checked = settings.vibrationEnabled,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onVibrationChange(enabled)
                },
            )

            // 锁屏显示
            NotificationSettingItem(
                icon = Icons.Default.Lock,
                title = "锁屏显示",
                subtitle = "在锁屏界面显示通知",
                checked = settings.showOnLockScreen,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onLockScreenChange(enabled)
                },
            )

            // 自动开始下一组
            NotificationSettingItem(
                icon = Icons.Default.PlayArrow,
                title = "自动开始",
                subtitle = "休息结束后自动开始下一组",
                checked = settings.autoStartNext,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onAutoStartChange(enabled)
                },
            )

            // 提醒间隔设置
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = "提醒间隔",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(Tokens.Spacing.Large),
                )
                Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "提醒间隔",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                    )
                    Text(
                        text = "${settings.reminderInterval}秒",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }

                // 简单的间隔选择按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                ) {
                    listOf(15, 30, 60).forEach { interval ->
                        FilterChip(
                            selected = settings.reminderInterval == interval,
                            onClick = {
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                onIntervalChange(interval)
                            },
                            label = { Text("${interval}s") },
                        )
                    }
                }
            }
        }
    }
}

/**
 * 训练提醒设置区块
 */
@Composable
private fun WorkoutReminderSection(
    settings: com.example.gymbro.domain.profile.model.user.settings.WorkoutReminderSettings,
    hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback,
    onEnabledChange: (Boolean) -> Unit,
    onDailyReminderChange: (Boolean) -> Unit,
    onReminderTimeChange: (String) -> Unit,
    onWeeklyPlanReminderChange: (Boolean) -> Unit,
    onRestDayReminderChange: (Boolean) -> Unit,
    onMotivationalMessagesChange: (Boolean) -> Unit,
    onSoundChange: (Boolean) -> Unit,
    onVibrationChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    GymBroSettingsGroup(modifier = modifier) {
        // 区块标题
        Text(
            text = "训练提醒",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
        )

        // 启用训练提醒
        NotificationSettingItem(
            icon = Icons.Default.FitnessCenter,
            title = "训练提醒",
            subtitle = "定时训练提醒和计划通知",
            checked = settings.enabled,
            onCheckedChange = { enabled ->
                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                onEnabledChange(enabled)
            },
        )

        if (settings.enabled) {
            // 每日提醒
            NotificationSettingItem(
                icon = Icons.Default.Today,
                title = "每日提醒",
                subtitle = "每天定时提醒训练",
                checked = settings.dailyReminder,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onDailyReminderChange(enabled)
                },
            )

            // 提醒时间设置
            if (settings.dailyReminder) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        imageVector = Icons.Default.AccessTime,
                        contentDescription = "提醒时间",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(Tokens.Spacing.Large),
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "提醒时间",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                        )
                        Text(
                            text = settings.reminderTime,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                        )
                    }

                    // 时间选择按钮
                    OutlinedButton(
                        onClick = {
                            // TODO: 打开时间选择器
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        },
                    ) {
                        Text("修改")
                    }
                }
            }

            // 其他训练提醒选项
            NotificationSettingItem(
                icon = Icons.Default.CalendarToday,
                title = "周计划提醒",
                subtitle = "每周训练计划提醒",
                checked = settings.weeklyPlanReminder,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onWeeklyPlanReminderChange(enabled)
                },
            )

            NotificationSettingItem(
                icon = Icons.Default.Weekend,
                title = "休息日提醒",
                subtitle = "休息日恢复提醒",
                checked = settings.restDayReminder,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onRestDayReminderChange(enabled)
                },
            )

            NotificationSettingItem(
                icon = Icons.Default.Psychology,
                title = "激励消息",
                subtitle = "发送激励和鼓励消息",
                checked = settings.motivationalMessages,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onMotivationalMessagesChange(enabled)
                },
            )

            // 声音和振动设置
            NotificationSettingItem(
                icon = Icons.Default.VolumeUp,
                title = "声音提醒",
                subtitle = "播放提示音",
                checked = settings.soundEnabled,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onSoundChange(enabled)
                },
            )

            NotificationSettingItem(
                icon = Icons.Default.Vibration,
                title = "振动提醒",
                subtitle = "设备振动提示",
                checked = settings.vibrationEnabled,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onVibrationChange(enabled)
                },
            )
        }
    }
}

/**
 * 状态栏通知设置区块
 */
@Composable
private fun StatusBarNotificationSection(
    settings: com.example.gymbro.domain.profile.model.user.settings.StatusBarNotificationSettings,
    hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback,
    onEnabledChange: (Boolean) -> Unit,
    onOngoingWorkoutChange: (Boolean) -> Unit,
    onRestTimerChange: (Boolean) -> Unit,
    onProgressChange: (Boolean) -> Unit,
    onPriorityChange: (com.example.gymbro.domain.profile.model.user.settings.NotificationPriority) -> Unit,
    onAutoHideChange: (Boolean) -> Unit,
    onAutoHideDelayChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    GymBroSettingsGroup(modifier = modifier) {
        // 区块标题
        Text(
            text = "状态栏通知",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
        )

        // 启用状态栏通知
        NotificationSettingItem(
            icon = Icons.Default.Notifications,
            title = "状态栏通知",
            subtitle = "在状态栏显示训练相关通知",
            checked = settings.enabled,
            onCheckedChange = { enabled ->
                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                onEnabledChange(enabled)
            },
        )

        if (settings.enabled) {
            // 显示正在进行的训练
            NotificationSettingItem(
                icon = Icons.Default.FitnessCenter,
                title = "正在进行的训练",
                subtitle = "显示当前训练状态",
                checked = settings.showOngoingWorkout,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onOngoingWorkoutChange(enabled)
                },
            )

            // 显示休息计时器
            NotificationSettingItem(
                icon = Icons.Default.Timer,
                title = "休息计时器",
                subtitle = "显示组间休息倒计时",
                checked = settings.showRestTimer,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onRestTimerChange(enabled)
                },
            )

            // 显示训练进度
            NotificationSettingItem(
                icon = Icons.Default.TrendingUp,
                title = "训练进度",
                subtitle = "显示当前训练进度",
                checked = settings.showProgress,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onProgressChange(enabled)
                },
            )

            // 通知优先级设置
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    imageVector = Icons.Default.PriorityHigh,
                    contentDescription = "通知优先级",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(Tokens.Spacing.Large),
                )
                Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "通知优先级",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                    )
                    Text(
                        text = when (settings.priority) {
                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.LOW -> "低"
                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.DEFAULT -> "默认"
                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.HIGH -> "高"
                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.MAX -> "最高"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }

                // 优先级选择按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
                ) {
                    com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.values()
                        .forEach { priority ->
                            FilterChip(
                                selected = settings.priority == priority,
                                onClick = {
                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    onPriorityChange(priority)
                                },
                                label = {
                                    Text(
                                        when (priority) {
                                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.LOW -> "低"
                                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.DEFAULT -> "默认"
                                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.HIGH -> "高"
                                            com.example.gymbro.domain.profile.model.user.settings.NotificationPriority.MAX -> "最高"
                                        },
                                    )
                                },
                            )
                        }
                }
            }

            // 自动隐藏
            NotificationSettingItem(
                icon = Icons.Default.VisibilityOff,
                title = "自动隐藏",
                subtitle = "训练结束后自动隐藏通知",
                checked = settings.autoHide,
                onCheckedChange = { enabled ->
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    onAutoHideChange(enabled)
                },
            )

            // 自动隐藏延迟设置
            if (settings.autoHide) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "自动隐藏延迟",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(Tokens.Spacing.Large),
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "自动隐藏延迟",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                        )
                        Text(
                            text = "${settings.autoHideDelayMinutes}分钟",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                        )
                    }

                    // 延迟时间选择按钮
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                    ) {
                        listOf(1, 5, 10, 30).forEach { delay ->
                            FilterChip(
                                selected = settings.autoHideDelayMinutes == delay,
                                onClick = {
                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    onAutoHideDelayChange(delay)
                                },
                                label = { Text("${delay}分") },
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
@GymBroPreview
internal fun NotificationsScreenPreview() {
    ProfileScaffold(
        title = "通知设置",
        onNavigateBack = {},
    ) {
        Text(
            text = "通知设置页面预览",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}
