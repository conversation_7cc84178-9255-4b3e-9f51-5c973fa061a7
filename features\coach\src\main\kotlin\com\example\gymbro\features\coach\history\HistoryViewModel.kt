package com.example.gymbro.features.coach.history

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.history.HistoryFeatureLevel
import com.example.gymbro.domain.coach.history.HistoryFeatureManager
import com.example.gymbro.features.coach.history.internal.effect.HistoryEffectHandler
import com.example.gymbro.features.coach.history.internal.manager.ArchitectureHealthChecker
import com.example.gymbro.features.coach.history.internal.manager.HealthStatus
import com.example.gymbro.features.coach.history.internal.reducer.HistoryReducer
import com.example.gymbro.features.coach.shared.utils.applyNetworkOptimizations
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

/**
 * History ViewModel - 简化版MVI架构实现（基于task1.md债务偿还方案）
 *
 * 简化特性：
 * 1. 继承BaseMviViewModel - 遵循MVI标准架构
 * 2. 轻量级实现 - 仅作为Intent分发器和State holder
 * 3. 集成EffectHandler - 使用标准的initialize接口
 * 4. 防抖搜索 - 搜索逻辑在EffectHandler中处理防抖
 *
 * @since 债务偿还重构 - 解决P0架构风险
 */
@HiltViewModel
internal class HistoryViewModel
    @Inject
    constructor(
        private val historyReducer: HistoryReducer,
        private val effectHandler: HistoryEffectHandler,
        private val logger: Logger,
        private val architectureHealthChecker: ArchitectureHealthChecker,
        private val historyFeatureManager: HistoryFeatureManager,
    ) : BaseMviViewModel<HistoryContract.Intent, HistoryContract.State, HistoryContract.Effect>(
        initialState = HistoryContract.State(),
    ) {

        // 提供Reducer实例给BaseMviViewModel
        override val reducer: Reducer<HistoryContract.Intent, HistoryContract.State, HistoryContract.Effect> =
            historyReducer

        // 🔥 修复：添加初始化状态标记，防止重复初始化
        @Volatile
        private var isInitialized = false

        init {
            // 简化初始化，立即加载历史记录
            if (!isInitialized) {
                isInitialized = true

                viewModelScope.launch {
                    try {
                        // 按照AiCoach模块模式初始化EffectHandler
                        initializeEffectHandler()

                        // 立即加载历史记录
                        loadInitialData()

                        // 执行启动健康检查
                        performStartupHealthCheck()
                    } catch (e: Exception) {
                        handleInitializationError(e)
                    }
                }
            }
        }

        /**
         * 统一的初始化错误处理
         */
        private fun handleInitializationError(exception: Exception) {
            logger.e("HistoryViewModel", "初始化失败: ${exception.message}")
            dispatch(
                HistoryContract.Intent.OperationFailedResult(
                    HistoryContract.ErrorState(
                        message = UiText.DynamicString("初始化失败: ${exception.message}"),
                        type = HistoryContract.ErrorType.STORAGE,
                        retryAction = { loadInitialData() },
                    ),
                ),
            )
        }

        /**
         * 🔥 初始化EffectHandler - 基于AiCoach模块的成功模式
         */
        override fun initializeEffectHandler() {
            effectHandler.initialize(
                scope = handlerScope,
                intentSender = { intent ->
                    // 使用内部路由而不是重写dispatch
                    routeIntent(intent)
                },
            )

            // 🎯 性能优化：为Effect Flow添加背压处理
            effect
                .applyNetworkOptimizations()
                .onEach { effect ->
                    effectHandler.handle(effect)
                }.launchIn(handlerScope)
        }

        /**
         * 🔥 Intent路由方法，支持直接Intent处理
         */
        private fun routeIntent(intent: HistoryContract.Intent) {
            // 某些Intent需要直接由EffectHandler处理（如LoadHistory）
            when (intent) {
                is HistoryContract.Intent.LoadHistory,
                is HistoryContract.Intent.LoadInitialHistory,
                is HistoryContract.Intent.RefreshHistoryData,
                is HistoryContract.Intent.PerformSearch,
                is HistoryContract.Intent.StartHybridSearch,
                -> {
                    // 直接由EffectHandler处理
                    effectHandler.handleIntent(intent)
                }

                else -> {
                    // 其他Intent通过标准MVI流程处理
                    dispatch(intent)
                }
            }
        }

        /**
         * 🔥 初始化数据加载
         */
        private fun loadInitialData() {
            effectHandler.handleIntent(HistoryContract.Intent.LoadHistory)
        }

        /**
         * 执行启动健康检查并在必要时降级功能级别
         */
        private suspend fun performStartupHealthCheck() {
            val report = architectureHealthChecker.checkHistoryPipeline()
            val currentLevel = historyFeatureManager.currentLevel.value
            var newLevel = currentLevel

            when (report.overallStatus) {
                HealthStatus.Critical -> {
                    newLevel = HistoryFeatureLevel.Essential
                    handleHealthDegradation("关键错误，降级到基础功能", report, HistoryContract.ErrorType.STORAGE)
                }

                HealthStatus.Warning -> {
                    if (currentLevel == HistoryFeatureLevel.Full) {
                        newLevel = HistoryFeatureLevel.Basic
                        handleHealthDegradation("性能警告，降级到基本功能", report, HistoryContract.ErrorType.STORAGE)
                    }
                }

                HealthStatus.Healthy -> {
                    // 健康状态，无需处理
                }
            }

            if (newLevel != currentLevel) {
                historyFeatureManager.degradeToLevel(
                    newLevel,
                    "启动健康检查: ${report.overallStatus}",
                )
            }
        }

        /**
         * 统一的健康降级处理
         */
        private fun handleHealthDegradation(
            message: String,
            report: com.example.gymbro.features.coach.history.internal.manager.PipelineHealthReport,
            errorType: HistoryContract.ErrorType,
        ) {
            logger.w("HistoryViewModel", "$message. 建议: ${report.recommendations.joinToString()}")
            dispatch(
                HistoryContract.Intent.OperationFailedResult(
                    HistoryContract.ErrorState(
                        message = UiText.DynamicString("$message ${report.recommendations.firstOrNull() ?: ""}"),
                        type = errorType,
                    ),
                ),
            )
        }

        // ==================== 便捷API方法 ====================

        /**
         * 执行搜索（防抖处理在EffectHandler中）
         */
        fun performSearch(query: String) = dispatch(
            HistoryContract.Intent.PerformSearch(
                query = query,
                mode = HistoryContract.SearchMode.HYBRID,
            ),
        )

        /**
         * 选择会话
         */
        fun selectSession(sessionId: String) = dispatch(HistoryContract.Intent.SelectSession(sessionId))

        /**
         * 刷新历史记录
         */
        fun refreshHistory() = dispatch(HistoryContract.Intent.RefreshHistoryData)

        /**
         * 兼容性方法 - 查询变更（用于搜索框）
         */
        fun onQueryChanged(text: String) = performSearch(text)

        // ==================== BGE-RAG功能方法 ====================

        /**
         * 处理待处理的嵌入向量
         */
        fun processPendingEmbeddings(limit: Int = 50) = dispatch(
            HistoryContract.Intent.ProcessPendingEmbeddings(limit),
        )

        /**
         * 更新RAG搜索查询
         */
        fun updateRagQuery(query: String) {
            // 通过Intent更新状态，遵循MVI架构
            dispatch(HistoryContract.Intent.UpdateRagQuery(query))
        }

        /**
         * 检索上下文消息
         */
        fun retrieveContext(query: String, limit: Int = 12) = dispatch(
            HistoryContract.Intent.RetrieveContext(
                query = query,
                limit = limit,
                sessionId = null,
            ),
        )

        /**
         * 切换上下文消息选择状态
         */
        fun toggleContextSelection(messageId: String) {
            // 通过Intent更新状态，遵循MVI架构
            dispatch(HistoryContract.Intent.ToggleContextSelection(messageId))
        }

        /**
         * 生成消息嵌入向量
         */
        fun generateEmbedding(messageId: Long) = dispatch(
            HistoryContract.Intent.GenerateEmbedding(messageId),
        )

        /**
         * 注入选中的上下文到提示
         */
        fun injectContextToPrompt(selectedMessageIds: List<Long>) = dispatch(
            HistoryContract.Intent.InjectContextToPrompt(selectedMessageIds),
        )

        override fun onCleared() {
            super.onCleared()
            // ViewModel清理完成 - 移除冗余日志
        }
    }
