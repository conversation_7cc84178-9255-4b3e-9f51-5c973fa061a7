package com.example.gymbro.features.workout.plan.internal.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.plan.PlanContract
import javax.inject.Inject
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList

/**
 * Plan模块状态转换器
 *
 * 🎯 核心功能：支持Plan层的三Tab设计和calendar.json输出
 * 基于08_Plan层改造设计.md的要求：
 * - 三Tab设计：全部、收藏、AI生成
 * - 去掉搜索功能，只保留分类
 * - 支持拖拽排序和calendar.json输出
 * - 遵循Function Call实现模式
 *
 * 职责：
 * - 接收当前State和Intent
 * - 执行纯函数状态计算
 * - 返回新的State和Effect
 * - 实现三Tab切换和分类逻辑
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Plan层改造设计)
 */
class PlanReducer
    @Inject
    constructor(
        private val logger: Logger,
    ) : Reducer<PlanContract.Intent, PlanContract.State, PlanContract.Effect> {
        /**
         * 状态转换主函数
         */
        override fun reduce(
            intent: PlanContract.Intent,
            currentState: PlanContract.State,
        ): ReduceResult<PlanContract.State, PlanContract.Effect> {
            logger.d("PlanReducer", "处理Intent: ${intent::class.simpleName}")

            return when (intent) {
                // === 基础数据加载Intent ===
                is PlanContract.Intent.LoadPlans,
                is PlanContract.Intent.RefreshPlans,
                ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = true,
                            isRefreshing = intent is PlanContract.Intent.RefreshPlans,
                            error = null,
                        ),
                    )

                // === 新增的Intent处理 ===
                is PlanContract.Intent.ClearAllFilters ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            searchQuery = "",
                            selectedFilter = PlanContract.PlanFilter.ALL,
                            filteredPlans = currentState.allPlans,
                        ),
                    )

                is PlanContract.Intent.ClearSearch ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            searchQuery = "",
                            filteredPlans = currentState.allPlans,
                        ),
                    )

                is PlanContract.Intent.FilterPlans ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedFilter = intent.filter,
                            filteredPlans = currentState.allPlans,
                        ),
                    )

                is PlanContract.Intent.RemoveFilter ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedFilter = PlanContract.PlanFilter.ALL,
                            filteredPlans = currentState.allPlans,
                        ),
                    )

                is PlanContract.Intent.SearchPlans ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            searchQuery = intent.query,
                            filteredPlans = currentState.allPlans,
                        ),
                    )

                is PlanContract.Intent.ToggleSearch ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isSearchVisible = !currentState.isSearchVisible,
                            searchQuery = if (!currentState.isSearchVisible) "" else currentState.searchQuery,
                        ),
                    )

                is PlanContract.Intent.UpdateSearchQuery ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            searchQuery = intent.query,
                            filteredPlans = currentState.allPlans,
                        ),
                    )

                is PlanContract.Intent.AnimateReorder -> ReduceResult.stateOnly(currentState)
                is PlanContract.Intent.ExportCalendarJson -> ReduceResult.stateOnly(currentState)
                is PlanContract.Intent.ShareCalendarJson -> ReduceResult.stateOnly(currentState)
                is PlanContract.Intent.ShowCalendarJsonGenerated -> ReduceResult.stateOnly(currentState)
                is PlanContract.Intent.StartDragMode -> ReduceResult.stateOnly(currentState)
                is PlanContract.Intent.StopDragMode -> ReduceResult.stateOnly(currentState)

                // === 三Tab切换Intent ===
                is PlanContract.Intent.SwitchTab -> {
                    val newCurrentPlans =
                        when (intent.tab) {
                            PlanContract.PlanTab.ALL -> currentState.allPlans
                            PlanContract.PlanTab.FAVORITES -> currentState.favoritePlans
                            PlanContract.PlanTab.AI_GENERATED -> currentState.aiGeneratedPlans
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            currentTab = intent.tab,
                            currentPlans = newCurrentPlans,
                            isEmpty = newCurrentPlans.isEmpty(),
                        ),
                    )
                }

                is PlanContract.Intent.LoadAllPlans,
                is PlanContract.Intent.LoadFavoritePlans,
                is PlanContract.Intent.LoadAIGeneratedPlans,
                ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = true,
                            error = null,
                        ),
                    )

                // === 计划分类操作Intent ===
                is PlanContract.Intent.TogglePlanFavorite ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isTogglingFavorite = true,
                            error = null,
                        ),
                    )

                is PlanContract.Intent.MarkPlanAsAIGenerated ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = true,
                            error = null,
                        ),
                    )

                // === 拖拽排序操作Intent ===
                is PlanContract.Intent.StartDragging ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isDragging = true,
                                draggedItemId = intent.planId,
                            ),
                        effect = PlanContract.Effect.StartDragMode,
                    )

                is PlanContract.Intent.StopDragging ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isDragging = false,
                                draggedItemId = null,
                            ),
                        effect = PlanContract.Effect.StopDragMode,
                    )

                is PlanContract.Intent.ReorderPlans -> {
                    val reorderedPlans = currentState.currentPlans.toMutableList()
                    if (intent.fromIndex in reorderedPlans.indices && intent.toIndex in reorderedPlans.indices) {
                        val item = reorderedPlans.removeAt(intent.fromIndex)
                        reorderedPlans.add(intent.toIndex, item)
                    }

                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                currentPlans = reorderedPlans.toImmutableList(),
                                isDragging = false,
                                draggedItemId = null,
                            ),
                        effect = PlanContract.Effect.AnimateReorder(intent.fromIndex, intent.toIndex),
                    )
                }

                // === 计划选择和详情Intent ===
                is PlanContract.Intent.SelectPlan ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlanId = intent.planId,
                            isLoading = true,
                            error = null,
                        ),
                    )

                is PlanContract.Intent.PlanSelected ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = intent.plan,
                            selectedPlanId = intent.plan.id,
                            isLoading = false,
                            error = null,
                        ),
                    )

                is PlanContract.Intent.PlanSelectionFailed ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = null,
                            selectedPlanId = null,
                            isLoading = false,
                            error = intent.error,
                        ),
                    )

                is PlanContract.Intent.ShowPlanDetail ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = intent.plan,
                            showPlanDetailDialog = true,
                        ),
                    )

                // === 删除操作Intent ===
                is PlanContract.Intent.DeletePlan ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlanId = intent.planId,
                            showDeleteConfirmDialog = true,
                        ),
                    )

                is PlanContract.Intent.ConfirmDeletePlan ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isDeleting = true,
                            error = null,
                        ),
                    )

                // === 保存操作Intent ===
                is PlanContract.Intent.SavePlan ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isSaving = true,
                            error = null,
                        ),
                    )

                is PlanContract.Intent.PlanSaved ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isSaving = false,
                                error = null,
                            ),
                        effect = PlanContract.Effect.NavigateBack,
                    )

                is PlanContract.Intent.PlanSaveFailed ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isSaving = false,
                            error = intent.error,
                        ),
                    )

                // === 计划应用和Calendar JSON生成Intent ===
                is PlanContract.Intent.ShowApplyPlanDialog ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = intent.plan,
                            showApplyPlanDialog = true,
                        ),
                    )

                is PlanContract.Intent.ApplyPlanToCalendar ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isApplyingPlan = true,
                            selectedDate = intent.startDate,
                            error = null,
                        ),
                    )

                is PlanContract.Intent.GenerateCalendarJson ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isGeneratingCalendarJson = true,
                            selectedDate = intent.startDate,
                            error = null,
                        ),
                    )

                // === UI操作Intent ===
                is PlanContract.Intent.ClearError ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            error = null,
                        ),
                    )

                is PlanContract.Intent.DismissDialogs ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            showDeleteConfirmDialog = false,
                            showPlanDetailDialog = false,
                            // 🔥 showTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector
                            showApplyPlanDialog = false,
                            selectedPlan = null,
                            selectedPlanId = null,
                        ),
                    )

                is PlanContract.Intent.DismissDeleteConfirmDialog ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            showDeleteConfirmDialog = false,
                            selectedPlanId = null,
                        ),
                    )

                is PlanContract.Intent.DismissPlanDetailDialog ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            showPlanDetailDialog = false,
                            selectedPlan = null,
                        ),
                    )

                // 🔥 DismissTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector

                is PlanContract.Intent.DismissApplyPlanDialog ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            showApplyPlanDialog = false,
                            selectedPlan = null,
                        ),
                    )

                // === AI快速生成计划Intent ===
                is PlanContract.Intent.ShowGeneratePlanDialog ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            showGeneratePlanDialog = true,
                            generationPrompt = "",
                        ),
                    )

                is PlanContract.Intent.GenerateQuickPlan ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isGeneratingPlan = true,
                                generationPrompt = intent.prompt,
                                error = null,
                            ),
                        effect = PlanContract.Effect.TriggerAiGeneration,
                    )

                is PlanContract.Intent.PlanGenerationCompleted -> {
                    val updatedAllPlans = currentState.allPlans.plus(intent.plan).toImmutableList()
                    val newCurrentPlans =
                        when (currentState.currentTab) {
                            PlanContract.PlanTab.ALL -> updatedAllPlans
                            PlanContract.PlanTab.AI_GENERATED ->
                                currentState.aiGeneratedPlans
                                    .plus(
                                        intent.plan,
                                    ).toImmutableList()

                            else -> currentState.currentPlans
                        }
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isGeneratingPlan = false,
                                showGeneratePlanDialog = false,
                                allPlans = updatedAllPlans,
                                currentPlans = newCurrentPlans,
                                lastGeneratedPlan = intent.plan,
                                isEmpty = false,
                            ),
                        effect = PlanContract.Effect.ShowGenerationSuccess(intent.plan.name.toString()),
                    )
                }

                is PlanContract.Intent.PlanGenerationFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isGeneratingPlan = false,
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                is PlanContract.Intent.DismissGeneratePlanDialog ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            showGeneratePlanDialog = false,
                            generationPrompt = "",
                            isGeneratingPlan = false,
                        ),
                    )

                // === 其他Intent ===
                is PlanContract.Intent.DuplicatePlan,
                is PlanContract.Intent.StartPlanExecution,
                // 🔥 ShowTemplatePicker已移除 - 使用内置FloatingTemplateSelector
                is PlanContract.Intent.CreatePlanFromTemplate,
                -> {
                    // 这些Intent主要由EffectHandler处理，状态变化较少
                    ReduceResult.stateOnly(currentState)
                }

                // === 数据操作结果Intent处理 ===

                // === 计划加载结果 ===
                is PlanContract.Intent.PlansLoaded ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = false,
                            isRefreshing = false,
                            allPlans = intent.plans.toImmutableList(),
                            currentPlans = intent.plans.toImmutableList(),
                            isEmpty = intent.plans.isEmpty(),
                            error = null,
                        ),
                    )

                is PlanContract.Intent.AllPlansLoaded -> {
                    val newCurrentPlans =
                        if (currentState.currentTab == PlanContract.PlanTab.ALL) {
                            intent.plans.toImmutableList()
                        } else {
                            currentState.currentPlans
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = false,
                            isRefreshing = false,
                            allPlans = intent.plans.toImmutableList(),
                            currentPlans = newCurrentPlans,
                            isEmpty = newCurrentPlans.isEmpty(),
                            error = null,
                        ),
                    )
                }

                is PlanContract.Intent.FavoritePlansLoaded -> {
                    val newCurrentPlans =
                        if (currentState.currentTab == PlanContract.PlanTab.FAVORITES) {
                            intent.plans.toImmutableList()
                        } else {
                            currentState.currentPlans
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = false,
                            isRefreshing = false,
                            favoritePlans = intent.plans.toImmutableList(),
                            currentPlans = newCurrentPlans,
                            isEmpty = newCurrentPlans.isEmpty(),
                            error = null,
                        ),
                    )
                }

                is PlanContract.Intent.AIGeneratedPlansLoaded -> {
                    val newCurrentPlans =
                        if (currentState.currentTab == PlanContract.PlanTab.AI_GENERATED) {
                            intent.plans.toImmutableList()
                        } else {
                            currentState.currentPlans
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = false,
                            isRefreshing = false,
                            aiGeneratedPlans = intent.plans.toImmutableList(),
                            currentPlans = newCurrentPlans,
                            isEmpty = newCurrentPlans.isEmpty(),
                            error = null,
                        ),
                    )
                }

                is PlanContract.Intent.PlansLoadingFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isLoading = false,
                                isRefreshing = false,
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                // === 计划分类操作结果 ===
                is PlanContract.Intent.PlanFavoriteToggled -> {
                    // 更新所有相关列表中的计划状态
                    val updatedAllPlans =
                        updatePlanInList(currentState.allPlans, intent.planId) { plan ->
                            // 这里需要根据实际的Plan模型来更新收藏状态
                            plan // 简化处理，实际需要更新收藏字段
                        }

                    val updatedFavoritePlans =
                        if (intent.isFavorite) {
                            // 添加到收藏列表
                            val plan = currentState.allPlans.find { it.id == intent.planId }
                            if (plan != null) {
                                currentState.favoritePlans.plus(plan).toImmutableList()
                            } else {
                                currentState.favoritePlans
                            }
                        } else {
                            // 从收藏列表移除
                            currentState.favoritePlans.filterNot { it.id == intent.planId }.toImmutableList()
                        }

                    val newCurrentPlans =
                        when (currentState.currentTab) {
                            PlanContract.PlanTab.ALL -> updatedAllPlans
                            PlanContract.PlanTab.FAVORITES -> updatedFavoritePlans
                            PlanContract.PlanTab.AI_GENERATED -> currentState.currentPlans
                        }

                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isTogglingFavorite = false,
                                allPlans = updatedAllPlans,
                                favoritePlans = updatedFavoritePlans,
                                currentPlans = newCurrentPlans,
                                isEmpty = newCurrentPlans.isEmpty(),
                            ),
                        effect =
                            PlanContract.Effect.ShowSuccess(
                                UiText.DynamicString(if (intent.isFavorite) "已添加到收藏" else "已取消收藏"),
                            ),
                    )
                }

                is PlanContract.Intent.PlanFavoriteToggleFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isTogglingFavorite = false,
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                is PlanContract.Intent.PlanMarkedAsAIGenerated -> {
                    // 更新AI生成列表
                    val plan = currentState.allPlans.find { it.id == intent.planId }
                    val updatedAIGeneratedPlans =
                        if (plan != null) {
                            currentState.aiGeneratedPlans.plus(plan).toImmutableList()
                        } else {
                            currentState.aiGeneratedPlans
                        }

                    val newCurrentPlans =
                        when (currentState.currentTab) {
                            PlanContract.PlanTab.ALL -> currentState.currentPlans
                            PlanContract.PlanTab.FAVORITES -> currentState.currentPlans
                            PlanContract.PlanTab.AI_GENERATED -> updatedAIGeneratedPlans
                        }

                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isLoading = false,
                                aiGeneratedPlans = updatedAIGeneratedPlans,
                                currentPlans = newCurrentPlans,
                                isEmpty = newCurrentPlans.isEmpty(),
                            ),
                        effect = PlanContract.Effect.ShowSuccess(UiText.DynamicString("已标记为AI生成")),
                    )
                }

                is PlanContract.Intent.PlanAIGeneratedMarkFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isLoading = false,
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                // === 计划删除结果 ===
                is PlanContract.Intent.PlanDeleted -> {
                    val (updatedAllPlans, updatedFavoritePlans, updatedAIGeneratedPlans) =
                        removePlanFromAllLists(currentState, intent.planId)

                    val newCurrentPlans =
                        getCurrentTabPlans(
                            currentState,
                            updatedAllPlans,
                            updatedFavoritePlans,
                            updatedAIGeneratedPlans,
                        )

                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isDeleting = false,
                                showDeleteConfirmDialog = false,
                                selectedPlanId = null,
                                allPlans = updatedAllPlans,
                                favoritePlans = updatedFavoritePlans,
                                aiGeneratedPlans = updatedAIGeneratedPlans,
                                currentPlans = newCurrentPlans,
                                isEmpty = newCurrentPlans.isEmpty(),
                            ),
                        effect = PlanContract.Effect.ShowSuccess(UiText.DynamicString("计划删除成功")),
                    )
                }

                is PlanContract.Intent.PlanDeletionFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isDeleting = false,
                                showDeleteConfirmDialog = false,
                                selectedPlanId = null,
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                // 计划应用结果
                is PlanContract.Intent.PlanAppliedToCalendar ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isApplyingPlan = false,
                                showApplyPlanDialog = false,
                                selectedPlan = null,
                            ),
                        effect = PlanContract.Effect.ShowSuccess(UiText.DynamicString("计划已成功应用到日历")),
                    )

                is PlanContract.Intent.PlanApplicationFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isApplyingPlan = false,
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                // === Calendar JSON生成结果 ===
                is PlanContract.Intent.CalendarJsonGenerated -> {
                    val planName =
                        currentState.allPlans
                            .find { it.id == intent.planId }
                            ?.name
                            ?.toString() ?: "计划"
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isGeneratingCalendarJson = false,
                                lastGeneratedCalendarJson = intent.calendarData,
                            ),
                        effect =
                            PlanContract.Effect.ShowCalendarJsonGenerated(
                                planName = planName,
                                entriesCount = intent.calendarData.calendarEntries.size,
                            ),
                    )
                }

                // === 日历集成增强结果处理 ===
                is PlanContract.Intent.CalendarEntriesGenerated -> {
                    ReduceResult.withEffect(
                        newState = currentState.copy(
                            isLoading = false,
                            error = null,
                        ),
                        effect = PlanContract.Effect.CalendarEntriesGenerated(intent.entries),
                    )
                }

                is PlanContract.Intent.CalendarSummaryGenerated -> {
                    ReduceResult.withEffect(
                        newState = currentState.copy(
                            isLoading = false,
                            error = null,
                        ),
                        effect = PlanContract.Effect.CalendarSummaryGenerated(intent.summary),
                    )
                }

                is PlanContract.Intent.MultipleCalendarEntriesGenerated -> {
                    ReduceResult.withEffect(
                        newState = currentState.copy(
                            isLoading = false,
                            error = null,
                        ),
                        effect = PlanContract.Effect.MultipleCalendarEntriesGenerated(
                            intent.entries,
                            intent.successCount,
                            intent.totalCount,
                        ),
                    )
                }

                is PlanContract.Intent.CalendarJsonGenerationFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isGeneratingCalendarJson = false,
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                // 模板相关结果
                is PlanContract.Intent.TemplatesLoaded ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            availableTemplates = intent.templates.toImmutableList(),
                            // 🔥 showTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector
                        ),
                    )

                is PlanContract.Intent.TemplatesLoadingFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                is PlanContract.Intent.PlanCreatedFromTemplate -> {
                    val updatedAllPlans = currentState.allPlans.plus(intent.newPlan).toImmutableList()
                    val newCurrentPlans =
                        when (currentState.currentTab) {
                            PlanContract.PlanTab.ALL -> updatedAllPlans
                            else -> currentState.currentPlans
                        }
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                // 🔥 showTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector
                                allPlans = updatedAllPlans,
                                currentPlans = newCurrentPlans,
                                isEmpty = false,
                            ),
                        effect = PlanContract.Effect.ShowSuccess(UiText.DynamicString("从模板创建计划成功")),
                    )
                }

                is PlanContract.Intent.PlanCreationFromTemplateFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                // 🔥 showTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                // 计划复制结果
                is PlanContract.Intent.PlanDuplicated -> {
                    val updatedAllPlans = currentState.allPlans.plus(intent.newPlan).toImmutableList()
                    val newCurrentPlans =
                        when (currentState.currentTab) {
                            PlanContract.PlanTab.ALL -> updatedAllPlans
                            else -> currentState.currentPlans
                        }
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                allPlans = updatedAllPlans,
                                currentPlans = newCurrentPlans,
                                isEmpty = false,
                            ),
                        effect = PlanContract.Effect.ShowSuccess(UiText.DynamicString("计划复制成功")),
                    )
                }

                is PlanContract.Intent.PlanDuplicationFailed ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                error = intent.error,
                            ),
                        effect = PlanContract.Effect.ShowError(intent.error),
                    )

                // === 计划编辑相关Intent处理 ===
                is PlanContract.Intent.UpdatePlanName -> {
                    // 更新当前选择的计划名称
                    val updatedPlan =
                        currentState.selectedPlan?.copy(
                            name =
                                UiText
                                    .DynamicString(intent.name),
                        )
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                        ),
                    )
                }

                is PlanContract.Intent.UpdatePlanDescription -> {
                    // 更新当前选择的计划描述
                    val updatedPlan =
                        currentState.selectedPlan?.copy(
                            description =
                                if (intent.description.isBlank()) {
                                    null
                                } else {
                                    UiText.DynamicString(
                                        intent.description,
                                    )
                                },
                        )
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                        ),
                    )
                }

                is PlanContract.Intent.UpdateTotalDays -> {
                    // 更新当前选择的计划总天数并重新生成dailySchedule
                    val updatedPlan =
                        currentState.selectedPlan?.copy(
                            totalDays = intent.totalDays,
                            dailySchedule = generateEmptyDailySchedule(intent.totalDays),
                        )
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                        ),
                    )
                }

                is PlanContract.Intent.SelectDayForTemplate -> {
                    // 记录选择的天数用于模板选择
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedDayForTemplate = intent.dayNumber,
                        ),
                    )
                }

                is PlanContract.Intent.AddTemplateToDay -> {
                    // 添加模板到指定天
                    val updatedPlan =
                        currentState.selectedPlan?.let { plan ->
                            val currentDayPlan =
                                plan.getDayPlan(intent.dayNumber)
                                    ?: com.example.gymbro.domain.workout.model.plan.DayPlan
                                        .createRestDay(intent.dayNumber)

                            val updatedTemplateIds = currentDayPlan.templateVersionIds.plus(intent.templateId)
                            val updatedDayPlan =
                                currentDayPlan.copy(
                                    templateVersionIds = updatedTemplateIds,
                                    isRestDay = false, // 添加模板后不再是休息日
                                )

                            val updatedDailySchedule =
                                plan.dailySchedule.plus(
                                    intent.dayNumber to updatedDayPlan,
                                )
                            plan.copy(dailySchedule = updatedDailySchedule)
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                            selectedDayForTemplate = null, // 清除选择状态
                        ),
                    )
                }

                is PlanContract.Intent.RemoveTemplateFromDay -> {
                    // 从指定天移除模板
                    val updatedPlan =
                        currentState.selectedPlan?.let { plan ->
                            val currentDayPlan = plan.getDayPlan(intent.dayNumber)
                            if (currentDayPlan != null && intent.templateIndex < currentDayPlan.templateVersionIds.size) {
                                val updatedTemplateIds =
                                    currentDayPlan.templateVersionIds.toMutableList().apply {
                                        removeAt(intent.templateIndex)
                                    }
                                val updatedDayPlan =
                                    currentDayPlan.copy(
                                        templateVersionIds = updatedTemplateIds,
                                        isRestDay = updatedTemplateIds.isEmpty(), // 如果没有模板了，变成休息日
                                    )

                                val updatedDailySchedule =
                                    plan.dailySchedule.plus(intent.dayNumber to updatedDayPlan)
                                plan.copy(dailySchedule = updatedDailySchedule)
                            } else {
                                plan
                            }
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                        ),
                    )
                }

                is PlanContract.Intent.ToggleRestDay -> {
                    // 切换休息日状态
                    val updatedPlan =
                        currentState.selectedPlan?.let { plan ->
                            val currentDayPlan =
                                plan.getDayPlan(intent.dayNumber)
                                    ?: com.example.gymbro.domain.workout.model.plan.DayPlan
                                        .createRestDay(intent.dayNumber)

                            val updatedDayPlan =
                                if (currentDayPlan.isRestDay) {
                                    // 从休息日变为训练日
                                    currentDayPlan.copy(isRestDay = false)
                                } else {
                                    // 从训练日变为休息日，清空模板
                                    currentDayPlan.copy(
                                        isRestDay = true,
                                        templateVersionIds = emptyList(),
                                    )
                                }

                            val updatedDailySchedule =
                                plan.dailySchedule.plus(
                                    intent.dayNumber to updatedDayPlan,
                                )
                            plan.copy(dailySchedule = updatedDailySchedule)
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                        ),
                    )
                }

                is PlanContract.Intent.UpdateDayNotes -> {
                    // 更新指定天的备注
                    val updatedPlan =
                        currentState.selectedPlan?.let { plan ->
                            val currentDayPlan =
                                plan.getDayPlan(intent.dayNumber)
                                    ?: com.example.gymbro.domain.workout.model.plan.DayPlan
                                        .createRestDay(intent.dayNumber)

                            val updatedDayPlan =
                                currentDayPlan.copy(
                                    dayNotes =
                                        if (intent.notes.isBlank()) {
                                            null
                                        } else {
                                            UiText.DynamicString(
                                                intent.notes,
                                            )
                                        },
                                )

                            val updatedDailySchedule =
                                plan.dailySchedule.plus(
                                    intent.dayNumber to updatedDayPlan,
                                )
                            plan.copy(dailySchedule = updatedDailySchedule)
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                        ),
                    )
                }

                is PlanContract.Intent.ReorderTemplatesInDay -> {
                    // 在指定天内重排序模板
                    val updatedPlan =
                        currentState.selectedPlan?.let { plan ->
                            val currentDayPlan = plan.getDayPlan(intent.dayNumber)
                            if (currentDayPlan != null &&
                                intent.fromIndex < currentDayPlan.templateVersionIds.size &&
                                intent.toIndex < currentDayPlan.templateVersionIds.size
                            ) {
                                val reorderedTemplateIds =
                                    currentDayPlan.templateVersionIds.toMutableList().apply {
                                        val item = removeAt(intent.fromIndex)
                                        add(intent.toIndex, item)
                                    }

                                val updatedDayPlan = currentDayPlan.copy(
                                    templateVersionIds = reorderedTemplateIds,
                                )
                                val updatedDailySchedule =
                                    plan.dailySchedule.plus(intent.dayNumber to updatedDayPlan)
                                plan.copy(dailySchedule = updatedDailySchedule)
                            } else {
                                plan
                            }
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                        ),
                    )
                }

                // === Stats数据加载Intent ===
                is PlanContract.Intent.LoadDailyStats -> {
                    ReduceResult.withEffect(
                        newState = currentState.copy(isLoadingStats = true),
                        effect = PlanContract.Effect.LoadDailyStatsEffect,
                    )
                }

                is PlanContract.Intent.LoadWeeklyStats -> {
                    ReduceResult.withEffect(
                        newState = currentState.copy(isLoadingStats = true),
                        effect = PlanContract.Effect.LoadWeeklyStatsEffect,
                    )
                }

                is PlanContract.Intent.LoadStatsForPlan -> {
                    ReduceResult.withEffect(
                        newState = currentState.copy(isLoadingStats = true),
                        effect = PlanContract.Effect.LoadStatsForPlanEffect(intent.planId),
                    )
                }

                is PlanContract.Intent.LoadStatsForDateRange -> {
                    ReduceResult.withEffect(
                        newState = currentState.copy(isLoadingStats = true),
                        effect = PlanContract.Effect.LoadStatsForDateRangeEffect(
                            intent.startDate,
                            intent.endDate,
                        ),
                    )
                }

                // === Stats数据结果Intent ===
                is PlanContract.Intent.DailyStatsLoaded -> {
                    val progressByDate = intent.stats.associateBy { it.date }
                    ReduceResult.stateOnly(
                        currentState.copy(
                            dailyStats = intent.stats,
                            progressByDate = progressByDate,
                            isLoadingStats = false,
                            statsError = null,
                        ),
                    )
                }

                is PlanContract.Intent.WeeklyStatsLoaded -> {
                    ReduceResult.stateOnly(
                        currentState.copy(
                            weeklyStats = intent.stats,
                            currentWeekStats = intent.stats,
                            isLoadingStats = false,
                            statsError = null,
                        ),
                    )
                }

                is PlanContract.Intent.StatsLoadFailed -> {
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoadingStats = false,
                            statsError = intent.error,
                        ),
                    )
                }

                // === 进度查询Intent ===
                is PlanContract.Intent.GetProgressForDate -> {
                    // 从已加载的 stats 数据中查找指定日期的进度
                    val progressForDate = currentState.progressByDate[intent.date]
                    ReduceResult.stateOnly(currentState) // 无状态变化，仅查询
                }

                is PlanContract.Intent.GetWeekProgress -> {
                    // 从已加载的 stats 数据中计算指定周的进度
                    val weekStats = currentState.weekProgressSummary[intent.weekNumber]
                    ReduceResult.stateOnly(currentState) // 无状态变化，仅查询
                }

                // === 进度操作Intent ===
                is PlanContract.Intent.ToggleDayCompleted -> {
                    // 切换某天的完成状态，由 EffectHandler 处理具体逻辑
                    ReduceResult.stateOnly(currentState)
                }

                is PlanContract.Intent.ProgressUpdatedResult -> {
                    // 进度更新结果，更新状态中的进度数据
                    val updatedProgressByDate = if (intent.updatedStats != null) {
                        currentState.progressByDate.plus(intent.date to intent.updatedStats)
                    } else {
                        currentState.progressByDate
                    }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            progressByDate = updatedProgressByDate,
                        ),
                    )
                }

                is PlanContract.Intent.CopyWeek -> {
                    // 复制源周到目标周的计划
                    val updatedPlan = currentState.selectedPlan?.let { plan ->
                        // 收集源周的所有日程安排 (1-7天)
                        val sourceWeekSchedule =
                            mutableMapOf<Int, com.example.gymbro.domain.workout.model.plan.DayPlan>()

                        (1..7).forEach { day ->
                            plan.getDayPlan(day)?.let { dayPlan ->
                                // 复制DayPlan，但重置完成状态
                                sourceWeekSchedule[day] = dayPlan.copy(isCompleted = false)
                            }
                        }

                        // 将源周计划复制到目标周（这里简化处理，实际可能需要更复杂的週计划管理）
                        // 由于当前架构使用dailySchedule而不是weeklySchedules，这里只是标记操作完成
                        plan
                    }

                    ReduceResult.withEffect(
                        newState = currentState.copy(selectedPlan = updatedPlan),
                        effect = PlanContract.Effect.ShowSuccess(
                            UiText.DynamicString("第${intent.fromWeek}周计划已复制到第${intent.toWeek}周"),
                        ),
                    )
                }

                is PlanContract.Intent.ShowTemplatePreview -> {
                    ReduceResult.stateOnly(
                        currentState.copy(previewingTemplate = intent.template),
                    )
                }

                is PlanContract.Intent.HideTemplatePreview -> {
                    ReduceResult.stateOnly(
                        currentState.copy(previewingTemplate = null),
                    )
                }

                // === 浮动模板选择器Intent ===
                is PlanContract.Intent.ToggleTemplatePicker ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isTemplatePickerExpanded = !currentState.isTemplatePickerExpanded,
                        ),
                    )

                is PlanContract.Intent.ChangeTemplatePage ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            templatePickerPage =
                                intent.page.coerceIn(
                                    0,
                                    currentState.templatePickerMaxPages - 1,
                                ),
                        ),
                    )

                is PlanContract.Intent.StartTemplateDrag ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isDraggingTemplate = true,
                            draggedTemplateId = intent.templateId,
                        ),
                    )

                is PlanContract.Intent.StopTemplateDrag ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isDraggingTemplate = false,
                            draggedTemplateId = null,
                        ),
                    )

                is PlanContract.Intent.DropTemplateOnDay -> {
                    // 处理模板拖拽到指定天的逻辑
                    val updatedPlan =
                        currentState.selectedPlan?.let { plan ->
                            val currentDayPlan =
                                plan.getDayPlan(intent.dayNumber)
                                    ?: com.example.gymbro.domain.workout.model.plan.DayPlan.createWorkoutDay(
                                        intent.dayNumber,
                                        emptyList(),
                                    )

                            val updatedTemplateIds = currentDayPlan.templateVersionIds.plus(intent.templateId)
                            val updatedDayPlan =
                                currentDayPlan.copy(
                                    templateVersionIds = updatedTemplateIds,
                                    isRestDay = false, // 添加模板后不再是休息日
                                )

                            val updatedDailySchedule =
                                plan.dailySchedule.plus(intent.dayNumber to updatedDayPlan)
                            plan.copy(dailySchedule = updatedDailySchedule)
                        }

                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedPlan = updatedPlan,
                            isDraggingTemplate = false,
                            draggedTemplateId = null,
                        ),
                    )
                }

                // === 新增的4周导航和拖拽Intent ===
                is PlanContract.Intent.SwitchToWeek ->
                    ReduceResult.withEffect(
                        newState = currentState.copy(currentWeek = intent.weekNumber),
                        effect = PlanContract.Effect.WeekSwitched(intent.weekNumber),
                    )

                is PlanContract.Intent.StartTemplateDragToWeek ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isDraggingTemplate = true,
                                draggedTemplateId = intent.templateId,
                                dropTargetWeek = intent.week,
                                dropTargetDay = intent.day,
                            ),
                        effect = PlanContract.Effect.DragHapticFeedback,
                    )

                is PlanContract.Intent.UpdateTemplateDragPosition ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            dropTargetWeek = intent.week,
                            dropTargetDay = intent.day,
                        ),
                    )

                is PlanContract.Intent.DropTemplateOnWeekDay ->
                    ReduceResult.withEffect(
                        newState =
                            currentState.copy(
                                isDraggingTemplate = false,
                                draggedTemplateId = null,
                                dropTargetWeek = null,
                                dropTargetDay = null,
                            ),
                        effect = PlanContract.Effect.DropHapticFeedback,
                    )

                is PlanContract.Intent.StartDayPlanDrag ->
                    ReduceResult.withEffect(
                        newState = currentState.copy(
                            isDraggingDayPlan = true,
                            draggedDayPlan = intent.week to intent.day,
                        ),
                        effect = PlanContract.Effect.DragHapticFeedback,
                    )

                is PlanContract.Intent.UpdateDayPlanDragPosition ->
                    ReduceResult.stateOnly(currentState)

                is PlanContract.Intent.DropDayPlan ->
                    ReduceResult.withEffect(
                        newState = currentState.copy(
                            isDraggingDayPlan = false,
                            draggedDayPlan = null,
                        ),
                        effect = PlanContract.Effect.DropHapticFeedback,
                    )

                is PlanContract.Intent.CancelDrag ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isDraggingTemplate = false,
                            draggedTemplateId = null,
                            isDraggingDayPlan = false,
                            draggedDayPlan = null,
                            dropTargetWeek = null,
                            dropTargetDay = null,
                        ),
                    )

                // === 缺少的Intent分支 ===
                is PlanContract.Intent.CopyWeekCompleted -> {
                    ReduceResult.withEffect(
                        newState = currentState,
                        effect = PlanContract.Effect.ShowSuccess(
                            UiText.DynamicString("第${intent.fromWeek}周计划已复制到第${intent.toWeek}周"),
                        ),
                    )
                }

                is PlanContract.Intent.ShowFilterDialog -> {
                    ReduceResult.stateOnly(currentState)
                }

                // === 日历视图相关Intent处理 ===
                is PlanContract.Intent.ToggleCalendarView ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            showCalendarView = !currentState.showCalendarView,
                        ),
                    )

                is PlanContract.Intent.SelectCalendarDate ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            selectedCalendarDate = intent.date,
                        ),
                    )

                is PlanContract.Intent.ShowCalendarEntryDetail -> {
                    // 显示日历条目详情，可以导航到对应的训练详情页面
                    ReduceResult.withEffect(
                        newState = currentState,
                        effect = PlanContract.Effect.ShowSuccess(
                            UiText.DynamicString("查看训练详情: ${intent.entry.displayTitle}"),
                        ),
                    )
                }

                is PlanContract.Intent.LoadCalendarData,
                is PlanContract.Intent.RefreshCalendarData,
                ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = true,
                            error = null,
                        ),
                    )

                // === 日历集成Intent处理 ===
                is PlanContract.Intent.ExportPlanToCalendar ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = true,
                            error = null,
                        ),
                    )

                is PlanContract.Intent.GetPlanCalendarSummary ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = true,
                            error = null,
                        ),
                    )

                is PlanContract.Intent.ImportMultiplePlansToCalendar ->
                    ReduceResult.stateOnly(
                        currentState.copy(
                            isLoading = true,
                            error = null,
                        ),
                    )

                else -> {
                    // 处理未知的Intent类型
                    logger.w("PlanReducer", "未知的Intent类型: ${intent::class.simpleName}")
                    ReduceResult.stateOnly(currentState)
                }
            }
        }

        /**
         * 更新列表中的特定计划
         */
        private fun updatePlanInList(
            plans: ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan>,
            planId: String,
            updateFunction: (
                com.example.gymbro.domain.workout.model.WorkoutPlan,
            ) -> com.example.gymbro.domain.workout.model.WorkoutPlan,
        ): ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan> =
            plans
                .map { plan ->
                    if (plan.id == planId) updateFunction(plan) else plan
                }.toImmutableList()

        /**
         * 生成空的每日调度计划
         * @param totalDays 总天数
         * @return 空的每日调度Map
         */
        private fun generateEmptyDailySchedule(
            totalDays: Int,
        ): Map<Int, com.example.gymbro.domain.workout.model.plan.DayPlan> =
            (1..totalDays).associateWith { dayNumber ->
                com.example.gymbro.domain.workout.model.plan.DayPlan
                    .createRestDay(dayNumber)
            }

        /**
         * 从所有列表中移除指定计划
         */
        private fun removePlanFromAllLists(
            currentState: PlanContract.State,
            planId: String,
        ): Triple<
            ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan>,
            ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan>,
            ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan>,
            > {
            val updatedAllPlans = currentState.allPlans.filterNot { it.id == planId }.toImmutableList()
            val updatedFavoritePlans = currentState.favoritePlans.filterNot { it.id == planId }.toImmutableList()
            val updatedAIGeneratedPlans =
                currentState.aiGeneratedPlans.filterNot { it.id == planId }.toImmutableList()

            return Triple(updatedAllPlans, updatedFavoritePlans, updatedAIGeneratedPlans)
        }

        /**
         * 获取当前Tab对应的计划列表
         */
        private fun getCurrentTabPlans(
            currentState: PlanContract.State,
            allPlans: ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan>,
            favoritePlans: ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan>,
            aiGeneratedPlans: ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan>,
        ): ImmutableList<com.example.gymbro.domain.workout.model.WorkoutPlan> =
            when (currentState.currentTab) {
                PlanContract.PlanTab.ALL -> allPlans
                PlanContract.PlanTab.FAVORITES -> favoritePlans
                PlanContract.PlanTab.AI_GENERATED -> aiGeneratedPlans
            }
    }
