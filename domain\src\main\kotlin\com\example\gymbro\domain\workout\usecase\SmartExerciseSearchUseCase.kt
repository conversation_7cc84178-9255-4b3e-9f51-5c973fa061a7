package com.example.gymbro.domain.workout.usecase

// Removed Timber import - domain layer should not depend on logging
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ml.interfaces.ComplementType
import com.example.gymbro.core.ml.interfaces.ExerciseMatchingEngine
import com.example.gymbro.core.ml.interfaces.VectorSearchEngine
import com.example.gymbro.core.ml.interfaces.WorkoutStateVectorizer
import com.example.gymbro.core.ml.model.CandidateVector
import com.example.gymbro.core.ml.model.VectorSearchResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.json.VectorizedWorkoutState
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 智能动作搜索UseCase - Domain Layer
 *
 * Domain层协调器，负责：
 * - 基于训练状态的智能动作推荐
 * - 语义相似度搜索
 * - 上下文感知的动作匹配
 * - 搜索结果排序和过滤
 *
 * 遵循Clean Architecture：Domain层不依赖外层，通过Core-ML接口协调算法
 */
@Singleton
class SmartExerciseSearchUseCase
    @Inject
    constructor(
        private val workoutStateVectorizer: WorkoutStateVectorizer,
        private val vectorSearchEngine: VectorSearchEngine,
        private val exerciseMatchingEngine: ExerciseMatchingEngine,
    ) {

        /**
         * 基于查询和训练状态的智能动作搜索
         *
         * @param query 用户查询文本
         * @param currentState 当前训练状态（可选）
         * @param exerciseCandidates 候选动作向量列表
         * @param topK 返回结果数量
         * @return 智能搜索结果
         */
        suspend operator fun invoke(
            query: String,
            currentState: VectorizedWorkoutState? = null,
            exerciseCandidates: List<CandidateVector> = emptyList(),
            topK: Int = 5,
        ): ModernResult<SmartSearchResult> {
            return try {
                if (query.isBlank()) {
                    return ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "SmartExerciseSearchUseCase.invoke",
                            message = UiText.DynamicString("搜索查询不能为空"),
                            inputType = "empty_query",
                            value = query,
                        ),
                    )
                }

                // 1. 向量化查询文本
                val queryVectorResult = workoutStateVectorizer.vectorizeWorkoutState(query)
                val queryVector = when (queryVectorResult) {
                    is ModernResult.Success -> queryVectorResult.data.vector
                    is ModernResult.Error -> return ModernResult.Error(queryVectorResult.error)
                    is ModernResult.Loading -> return ModernResult.Loading
                }

                // 2. 基于查询进行向量搜索
                val queryMatchesResult = vectorSearchEngine.searchSimilar(
                    queryVector = queryVector,
                    candidates = exerciseCandidates,
                    topK = topK,
                    threshold = 0.6f,
                )
                val queryMatches = when (queryMatchesResult) {
                    is ModernResult.Success -> queryMatchesResult.data
                    is ModernResult.Error -> emptyList()
                    is ModernResult.Loading -> emptyList()
                }

                // 3. 基于训练状态搜索（如果有）
                val stateMatches = currentState?.let { state ->
                    val stateSearchResult = vectorSearchEngine.searchSimilar(
                        queryVector = state.vector,
                        candidates = exerciseCandidates,
                        topK = topK / 2,
                        threshold = 0.5f,
                    )
                    when (stateSearchResult) {
                        is ModernResult.Success -> stateSearchResult.data
                        is ModernResult.Error -> emptyList()
                        is ModernResult.Loading -> emptyList()
                    }
                } ?: emptyList()

                // 4. 基于当前动作的上下文推荐（如果有运动ID）
                val contextualMatches = currentState?.metadata?.get("exercise_id")?.toString()?.let { exerciseId ->
                    findContextualExercises(exerciseId, topK / 2)
                } ?: emptyList()

                // 5. 合并和排序结果
                val mergedResults = mergeSearchResults(queryMatches, stateMatches, contextualMatches)

                // 6. 转换为动作信息
                val exerciseResults = mergedResults.map { result ->
                    ExerciseSearchResult(
                        exerciseId = result.candidateId,
                        name = result.metadata["exercise_name"]?.toString() ?: result.candidateId,
                        muscleGroup = result.metadata["muscle_group"]?.toString() ?: "未知",
                        equipment = result.metadata["equipment"]?.toString() ?: "未知",
                        similarity = result.similarity,
                        searchType = result.metadata["search_type"]?.toString() ?: "query",
                        distance = result.distance,
                        searchStrategy = result.searchStrategy,
                    )
                }

                val searchResult = SmartSearchResult(
                    query = query,
                    primaryMatches = exerciseResults.filter { it.searchType == "query" },
                    contextualSuggestions = exerciseResults.filter { it.searchType == "contextual" },
                    stateBasedRecommendations = exerciseResults.filter { it.searchType == "state" },
                    searchStrategy = determineSearchStrategy(currentState),
                    totalResults = exerciseResults.size,
                    searchMetadata = mapOf(
                        "query_vector_dim" to queryVector.size,
                        "candidates_count" to exerciseCandidates.size,
                        "has_current_state" to (currentState != null),
                    ),
                )

                // Domain layer should not log - logging handled by upper layers
                ModernResult.Success(searchResult)
            } catch (e: Exception) {
                // Domain layer should not log - exception will be handled by upper layers
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "SmartExerciseSearchUseCase.invoke",
                        message = UiText.DynamicString("智能动作搜索失败"),
                        processType = "smart_exercise_search",
                        reason = "search_exception",
                        cause = e,
                        metadataMap = mapOf(
                            "query" to query,
                            "has_current_state" to (currentState != null),
                            "candidates_count" to exerciseCandidates.size,
                        ),
                    ),
                )
            }
        }

        /**
         * 基于文本查询的简化搜索
         *
         * @param queryText 查询文本
         * @param topK 返回结果数量
         * @return 搜索结果
         */
        suspend fun searchByText(
            queryText: String,
            topK: Int = 5,
        ): ModernResult<List<ExerciseSearchResult>> {
            return try {
                val searchResult = exerciseMatchingEngine.searchExercises(
                    queryText = queryText,
                    topK = topK,
                    threshold = 0.6f,
                )

                when (searchResult) {
                    is ModernResult.Success -> {
                        val exerciseResults = searchResult.data.map { matchResult ->
                            ExerciseSearchResult(
                                exerciseId = matchResult.exerciseId,
                                name = matchResult.exerciseName,
                                muscleGroup = matchResult.primaryMuscles.joinToString(", "),
                                equipment = matchResult.equipment,
                                similarity = matchResult.similarity,
                                searchType = "text_search",
                                distance = 1.0f - matchResult.similarity,
                                searchStrategy = "exercise_matching_engine",
                            )
                        }
                        ModernResult.Success(exerciseResults)
                    }

                    is ModernResult.Error -> ModernResult.Error(searchResult.error)
                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "SmartExerciseSearchUseCase.searchByText",
                        message = UiText.DynamicString("文本搜索失败"),
                        processType = "text_exercise_search",
                        reason = "search_exception",
                        cause = e,
                        metadataMap = mapOf("query_text" to queryText),
                    ),
                )
            }
        }

        /**
         * 查找上下文相关的动作
         */
        private suspend fun findContextualExercises(exerciseId: String, count: Int): List<VectorSearchResult> {
            return try {
                val complementaryResult = exerciseMatchingEngine.findComplementaryExercises(
                    exerciseId = exerciseId,
                    complementType = ComplementType.SAME_MUSCLES,
                    topK = count,
                )

                when (complementaryResult) {
                    is ModernResult.Success -> {
                        complementaryResult.data.map { matchResult ->
                            VectorSearchResult(
                                candidateId = matchResult.exerciseId,
                                similarity = matchResult.similarity,
                                distance = 1.0f - matchResult.similarity,
                                metadata = mapOf(
                                    "search_type" to "contextual",
                                    "exercise_name" to matchResult.exerciseName,
                                    "muscle_group" to matchResult.primaryMuscles.joinToString(", "),
                                    "equipment" to matchResult.equipment,
                                ),
                                searchStrategy = "complementary_exercises",
                            )
                        }
                    }

                    is ModernResult.Error -> emptyList()
                    is ModernResult.Loading -> emptyList()
                }
            } catch (e: Exception) {
                // Domain layer should not log - return empty list as fallback
                emptyList()
            }
        }

        /**
         * 合并多个搜索结果
         */
        private fun mergeSearchResults(
            queryMatches: List<VectorSearchResult>,
            stateMatches: List<VectorSearchResult>,
            contextualMatches: List<VectorSearchResult>,
        ): List<VectorSearchResult> {
            val allResults = mutableListOf<VectorSearchResult>()

            // 添加查询匹配结果（权重1.0）
            allResults.addAll(
                queryMatches.map {
                    it.copy(
                        metadata = it.metadata + ("search_type" to "query"),
                        similarity = it.similarity * 1.0f,
                    )
                },
            )

            // 添加状态匹配结果（权重0.8）
            allResults.addAll(
                stateMatches.map {
                    it.copy(
                        metadata = it.metadata + ("search_type" to "state"),
                        similarity = it.similarity * 0.8f,
                    )
                },
            )

            // 添加上下文匹配结果（权重0.6）
            allResults.addAll(
                contextualMatches.map {
                    it.copy(
                        metadata = it.metadata + ("search_type" to "contextual"),
                        similarity = it.similarity * 0.6f,
                    )
                },
            )

            // 去重并按相似度排序
            return allResults
                .distinctBy { it.candidateId }
                .sortedByDescending { it.similarity }
                .take(10) // 最多返回10个结果
        }

        /**
         * 确定搜索策略
         */
        private fun determineSearchStrategy(currentState: VectorizedWorkoutState?): String {
            return when {
                currentState != null && currentState.metadata.containsKey("exercise_id") -> "context_aware_search"
                currentState != null -> "state_based_search"
                else -> "semantic_search"
            }
        }
    }

/**
 * 智能搜索结果
 */
data class SmartSearchResult(
    val query: String,
    val primaryMatches: List<ExerciseSearchResult>,
    val contextualSuggestions: List<ExerciseSearchResult>,
    val stateBasedRecommendations: List<ExerciseSearchResult>,
    val searchStrategy: String,
    val totalResults: Int,
    val searchMetadata: Map<String, Any> = emptyMap(),
)

/**
 * 动作搜索结果
 */
data class ExerciseSearchResult(
    val exerciseId: String,
    val name: String,
    val muscleGroup: String,
    val equipment: String,
    val similarity: Float,
    val searchType: String,
    val distance: Float = 1.0f - similarity,
    val searchStrategy: String = "unknown",
)
