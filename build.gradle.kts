// GymBro 项目根构建脚本

plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.jvm) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.hilt) apply false
    alias(libs.plugins.ktlint.gradle) apply false
    alias(libs.plugins.detekt) // 应用detekt到根项目
    alias(libs.plugins.owasp.dependency.check) // 应用OWASP到根项目
    alias(libs.plugins.sonarqube) apply false

    // Firebase & Google Services
    alias(libs.plugins.google.services) apply false
    alias(libs.plugins.firebase.crashlytics) apply false

    // 应用我们的自定义CI/CD插件
    id("gymbro.security") // OWASP安全扫描
}

// 添加自定义 Detekt 规则依赖
dependencies {
    detektPlugins(project(":detekt"))
}

// 为所有项目配置公共设置
allprojects {
    // 解决protobuf依赖冲突
    configurations.all {

        resolutionStrategy {
            force("com.google.protobuf:protobuf-javalite:${libs.versions.protobuf.get()}")
            force("com.google.protobuf:protobuf-java:${libs.versions.protobuf.get()}")

            eachDependency {
                if (requested.group == "com.google.protobuf") {
                    when (requested.name) {
                        "protobuf-lite" -> {
                            useTarget("com.google.protobuf:protobuf-javalite:${libs.versions.protobuf.get()}")
                            because("protobuf-lite已被protobuf-javalite取代")
                        }
                        "protobuf-javalite" -> {
                            useVersion(libs.versions.protobuf.get())
                            because("统一protobuf-javalite版本")
                        }
                        "protobuf-java" -> {
                            useVersion(libs.versions.protobuf.get())
                            because("统一protobuf-java版本")
                        }
                    }
                }
            }
        }

        exclude(group = "com.google.protobuf", module = "protobuf-lite")
        exclude(group = "com.google.protobuf", module = "protobuf-java-util")
    }

    // 配置Kotlin编译选项 - 使用现代的compilerOptions DSL
    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
        }
    }
}

// 配置子项目
subprojects {
    afterEvaluate {
        // 为所有Android模块应用质量检查插件
        if (plugins.hasPlugin("com.android.application") ||
            plugins.hasPlugin("com.android.library")) {

            // 应用统一的质量检查插件（包含JaCoCo、Ktlint、Detekt）
            apply(plugin = "gymbro.quality")
        }

        // 为所有Kotlin项目应用质量检查
        if (plugins.hasPlugin("org.jetbrains.kotlin.jvm") ||
            plugins.hasPlugin("org.jetbrains.kotlin.android")) {

            // 为纯Kotlin JVM项目也应用质量检查，但排除 detekt 模块
            if (plugins.hasPlugin("org.jetbrains.kotlin.jvm") && name != "detekt") {
                apply(plugin = "gymbro.quality")
            }
        }
    }
}

// 根项目应用Detekt插件
apply(plugin = "gymbro.detekt")

// 根项目的 Detekt 配置（针对build-logic）
detekt {
    source.setFrom(
        "gradle/build-logic/src/main/kotlin"
    )
}

// 清理任务
tasks.register("clean", Delete::class) {
    delete(rootProject.layout.buildDirectory)
}

// 项目健康检查任务
tasks.register("projectHealth") {
    description = "检查项目健康状态"
    group = "verification"

    doLast {
        println("=== GymBro 项目健康检查 ===")
        println("Gradle版本: ${gradle.gradleVersion}")
        println("Java版本: ${System.getProperty("java.version")}")
        println("操作系统: ${System.getProperty("os.name")}")
        println("==============================")
    }
}

// 统一的代码质量检查任务
tasks.register("qualityCheckAll") {
    description = "运行所有模块的代码质量检查（Detekt + Ktlint + JaCoCo）"
    group = "verification"

    dependsOn(subprojects.mapNotNull {
        it.tasks.findByName("qualityCheck")
    })

    doLast {
        println("=== 🎉 GymBro 全项目质量检查完成 ===")
        println("✅ 所有模块的Detekt静态分析")
        println("✅ 所有模块的Ktlint格式检查")
        println("✅ 所有模块的JaCoCo覆盖率统计")
        println("📊 查看各模块报告：build/reports/")
        println("===================================")
    }
}

// 统一的代码质量验证任务（包含覆盖率阈值验证）
tasks.register("qualityVerifyAll") {
    description = "运行所有模块的代码质量验证（包含覆盖率阈值检查）"
    group = "verification"

    dependsOn(subprojects.mapNotNull {
        it.tasks.findByName("qualityVerify")
    })

    doLast {
        println("=== 🏆 GymBro 全项目质量验证完成 ===")
        println("✅ 所有模块通过质量检查")
        println("✅ 所有模块达到覆盖率阈值")
        println("🚀 代码质量达到生产就绪标准")
        println("===================================")
    }
}

// 统一的代码格式化任务
tasks.register("formatCodeAll") {
    description = "格式化所有模块的代码"
    group = "formatting"

    dependsOn(subprojects.mapNotNull {
        it.tasks.findByName("formatCode")
    })

    doLast {
        println("=== 🎨 GymBro 全项目代码格式化完成 ===")
        println("✅ 所有模块的Kotlin代码已格式化")
        println("💡 请检查格式化后的代码变更")
        println("====================================")
    }
}

// 统一的测试任务
tasks.register("testAll") {
    description = "运行所有模块的单元测试"
    group = "verification"

    dependsOn(subprojects.mapNotNull {
        it.tasks.findByName("testDebugUnitTest")
    })

    doLast {
        println("=== 🧪 GymBro 全项目测试完成 ===")
        println("✅ 所有模块的单元测试已运行")
        println("📊 查看测试报告：**/build/reports/tests/")
        println("==============================")
    }
}

// 统一的覆盖率检查任务
tasks.register("coverageCheck") {
    description = "检查测试覆盖率是否达到要求"
    group = "verification"

    dependsOn(subprojects.mapNotNull {
        it.tasks.findByName("jacocoCoverageVerification")
    })

    doLast {
        println("=== 📊 GymBro 覆盖率检查完成 ===")
        println("✅ Domain层: ≥90% (要求达标)")
        println("✅ Data层: ≥80% (要求达标)")
        println("✅ ViewModel: ≥75% (要求达标)")
        println("📈 查看覆盖率报告：**/build/reports/jacoco/")
        println("================================")
    }
}

// CI专用检查任务（符合CICD文档流程）
tasks.register("ciCheck") {
    description = "CI环境完整检查（ktlint -> detekt -> danger -> build）"
    group = "verification"

    dependsOn(subprojects.mapNotNull {
        it.tasks.findByName("ciCheck")
    })

    doLast {
        println("=== 🚀 GymBro 全项目CI检查完成 ===")
        println("✅ 符合CICD文档标准流程")
        println("✅ 所有模块质量检查通过")
        println("✅ 代码质量达到生产标准")
        println("===================================")
    }
}

// 创建Detekt基线任务
tasks.register("updateDetektBaselineAll") {
    description = "为所有模块更新Detekt基线"
    group = "verification"

    dependsOn(subprojects.mapNotNull {
        it.tasks.findByName("updateDetektBaseline")
    })

    doLast {
        println("=== 📋 GymBro Detekt基线更新完成 ===")
        println("✅ 所有模块的Detekt基线已更新")
        println("📁 基线文件：config/detekt/baseline.xml")
        println("💡 请提交更新的基线文件到版本控制")
        println("===================================")
    }
}
