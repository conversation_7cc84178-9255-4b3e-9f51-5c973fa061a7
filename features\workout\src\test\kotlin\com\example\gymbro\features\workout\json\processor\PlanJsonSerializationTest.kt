package com.example.gymbro.features.workout.json.processor

import com.example.gymbro.features.workout.shared.utils.PlanTestDataFactory
import com.example.gymbro.shared.models.workout.DayPlan
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import com.example.gymbro.shared.models.workout.WorkoutPlan
import io.mockk.MockKAnnotations
import io.mockk.clearAllMocks
import kotlin.test.*
import kotlin.time.measureTime
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Test

/**
 * Plan JSON序列化和反序列化测试
 *
 * 🎯 测试目标：
 * - Plan数据的JSON序列化准确性
 * - Plan数据的JSON反序列化准确性
 * - DayPlan的序列化/反序列化
 * - Template引用关系的JSON处理
 * - 命名规范符合性验证（camelCase ↔ snake_case映射）
 * - 边界情况和异常数据处理
 * - 序列化性能测试
 *
 * 🔍 测试覆盖范围：
 * - 简单Plan序列化/反序列化
 * - 复杂Plan（多天+多Template）
 * - 空数据和null值处理
 * - 大数据量性能测试
 * - JSON格式验证
 * - 字段命名规范验证
 * - Template引用完整性验证
 *
 * <AUTHOR> AI Assistant
 * @since 1.0.0
 */
@DisplayName("Plan JSON序列化和反序列化测试")
class PlanJsonSerializationTest {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
        prettyPrint = false
    }

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    // ==================== 基础序列化测试 ====================

    @Nested
    @DisplayName("基础序列化测试")
    inner class BasicSerializationTests {

        @Test
        @DisplayName("简单Plan序列化应该成功")
        fun `simple plan serialization should succeed`() {
            // Given
            val plan = PlanTestDataFactory.createSimplePlan()

            // When
            val jsonString = plan.toJson()

            // Then
            assertNotNull(jsonString, "序列化结果不应为null")
            assertTrue(jsonString.isNotBlank(), "序列化结果不应为空")
            assertTrue(jsonString.startsWith("{"), "JSON应以{开始")
            assertTrue(jsonString.endsWith("}"), "JSON应以}结束")

            // 验证关键字段存在
            assertTrue(jsonString.contains("\"id\":\"${PlanTestDataFactory.TEST_PLAN_ID_SIMPLE}\""), "应包含正确的ID")
            assertTrue(jsonString.contains("\"name\":\"简单训练计划\""), "应包含正确的名称")
            assertTrue(jsonString.contains("\"userId\":\"${PlanTestDataFactory.TEST_USER_ID}\""), "应包含正确的用户ID")
            assertTrue(jsonString.contains("\"dailySchedule\""), "应包含dailySchedule字段")
            assertTrue(jsonString.contains("\"totalDays\":3"), "应包含正确的总天数")
        }

        @Test
        @DisplayName("复杂Plan序列化应该成功")
        fun `complex plan serialization should succeed`() {
            // Given
            val plan = PlanTestDataFactory.createComplexPlan()

            // When
            val jsonString = plan.toJson()

            // Then
            assertNotNull(jsonString, "序列化结果不应为null")
            assertTrue(jsonString.isNotBlank(), "序列化结果不应为空")

            // 验证Template引用
            assertTrue(jsonString.contains("\"templateIds\""), "应包含templateIds字段")
            assertTrue(jsonString.contains("\"templateVersionIds\""), "应包含templateVersionIds字段")
            assertTrue(jsonString.contains(PlanTestDataFactory.TEST_TEMPLATE_ID_1), "应包含模板ID1")
            assertTrue(jsonString.contains(PlanTestDataFactory.TEST_TEMPLATE_VERSION_ID_1), "应包含模板版本ID1")

            // 验证DayPlan状态
            assertTrue(jsonString.contains("\"isRestDay\":true"), "应包含休息日标记")
            assertTrue(jsonString.contains("\"isRestDay\":false"), "应包含训练日标记")
            assertTrue(jsonString.contains("\"progress\":\"COMPLETED\""), "应包含已完成状态")
            assertTrue(jsonString.contains("\"progress\":\"IN_PROGRESS\""), "应包含进行中状态")
        }

        @Test
        @DisplayName("空Plan序列化应该成功")
        fun `empty plan serialization should succeed`() {
            // Given
            val plan = PlanTestDataFactory.createEmptyPlan()

            // When
            val jsonString = plan.toJson()

            // Then
            assertNotNull(jsonString, "序列化结果不应为null")
            assertTrue(jsonString.contains("\"dailySchedule\":{}"), "空的dailySchedule应序列化为{}")
            assertTrue(jsonString.contains("\"totalDays\":0"), "空计划的总天数应为0")
            assertTrue(jsonString.contains("\"tags\":[]"), "空标签应序列化为[]")
        }
    }

    // ==================== 基础反序列化测试 ====================

    @Nested
    @DisplayName("基础反序列化测试")
    inner class BasicDeserializationTests {

        @Test
        @DisplayName("有效JSON反序列化应该成功")
        fun `valid json deserialization should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val jsonString = originalPlan.toJson()

            // When
            val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)

            // Then
            assertNotNull(deserializedPlan, "反序列化结果不应为null")
            assertEquals(originalPlan.id, deserializedPlan.id, "ID应匹配")
            assertEquals(originalPlan.name, deserializedPlan.name, "名称应匹配")
            assertEquals(originalPlan.userId, deserializedPlan.userId, "用户ID应匹配")
            assertEquals(originalPlan.totalDays, deserializedPlan.totalDays, "总天数应匹配")
            assertEquals(originalPlan.dailySchedule.size, deserializedPlan.dailySchedule.size, "日程安排数量应匹配")
        }

        @Test
        @DisplayName("复杂Plan反序列化应该保持数据完整性")
        fun `complex plan deserialization should maintain data integrity`() {
            // Given
            val originalPlan = PlanTestDataFactory.createComplexPlan()
            val jsonString = originalPlan.toJson()

            // When
            val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)

            // Then
            assertNotNull(deserializedPlan, "反序列化结果不应为null")

            // 验证基础字段
            assertEquals(originalPlan.id, deserializedPlan.id)
            assertEquals(originalPlan.name, deserializedPlan.name)
            assertEquals(originalPlan.userId, deserializedPlan.userId)
            assertEquals(originalPlan.totalDays, deserializedPlan.totalDays)
            assertEquals(originalPlan.planType, deserializedPlan.planType)
            assertEquals(originalPlan.difficultyLevel, deserializedPlan.difficultyLevel)

            // 验证DayPlan数据
            assertEquals(originalPlan.dailySchedule.size, deserializedPlan.dailySchedule.size)
            originalPlan.dailySchedule.forEach { (dayNumber, originalDayPlan) ->
                val deserializedDayPlan = deserializedPlan.dailySchedule[dayNumber]
                assertNotNull(deserializedDayPlan, "第${dayNumber}天计划不应为null")
                assertEquals(originalDayPlan.dayNumber, deserializedDayPlan.dayNumber)
                assertEquals(originalDayPlan.isRestDay, deserializedDayPlan.isRestDay)
                assertEquals(originalDayPlan.templateIds, deserializedDayPlan.templateIds)
                assertEquals(originalDayPlan.templateVersionIds, deserializedDayPlan.templateVersionIds)
                assertEquals(originalDayPlan.progress, deserializedDayPlan.progress)
            }
        }

        @Test
        @DisplayName("无效JSON反序列化应该返回null")
        fun `invalid json deserialization should return null`() {
            // Given
            val invalidJsonString = PlanTestDataFactory.getInvalidJsonString()

            // When
            val deserializedPlan = PlanJsonProcessor.fromJson(invalidJsonString)

            // Then
            assertNull(deserializedPlan, "无效JSON反序列化应返回null")
        }

        @Test
        @DisplayName("部分字段缺失的JSON反序列化应该使用默认值")
        fun `partial json deserialization should use default values`() {
            // Given
            val partialJsonString = PlanTestDataFactory.getPartialJsonString()

            // When
            val deserializedPlan = PlanJsonProcessor.fromJson(partialJsonString)

            // Then
            assertNotNull(deserializedPlan, "部分JSON反序列化应成功")
            assertEquals("partial_plan", deserializedPlan.id)
            assertEquals("部分字段计划", deserializedPlan.name)
            assertEquals(PlanTestDataFactory.TEST_USER_ID, deserializedPlan.userId)

            // 验证默认值
            assertEquals(0, deserializedPlan.totalDays, "缺失的totalDays应使用默认值0")
            assertEquals(0, deserializedPlan.difficultyLevel, "缺失的difficultyLevel应使用默认值0")
            assertTrue(deserializedPlan.dailySchedule.isEmpty(), "缺失的dailySchedule应使用默认空Map")
            assertFalse(deserializedPlan.isPublic, "缺失的isPublic应使用默认值false")
        }
    }

    // ==================== 序列化/反序列化往返测试 ====================

    @Nested
    @DisplayName("序列化/反序列化往返测试")
    inner class RoundTripTests {

        @Test
        @DisplayName("简单Plan往返测试应该保持数据一致性")
        fun `simple plan round trip should maintain consistency`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()

            // When
            val jsonString = originalPlan.toJson()
            val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)
            val secondJsonString = deserializedPlan?.toJson()
            val secondDeserializedPlan = secondJsonString?.let { PlanJsonProcessor.fromJson(it) }

            // Then
            assertNotNull(deserializedPlan, "第一次反序列化应成功")
            assertNotNull(secondDeserializedPlan, "第二次反序列化应成功")

            // 验证数据一致性
            assertEquals(originalPlan.id, secondDeserializedPlan.id)
            assertEquals(originalPlan.name, secondDeserializedPlan.name)
            assertEquals(originalPlan.userId, secondDeserializedPlan.userId)
            assertEquals(originalPlan.totalDays, secondDeserializedPlan.totalDays)
            assertEquals(originalPlan.dailySchedule.size, secondDeserializedPlan.dailySchedule.size)
        }

        @Test
        @DisplayName("复杂Plan往返测试应该保持完整的Template引用")
        fun `complex plan round trip should maintain template references`() {
            // Given
            val originalPlan = PlanTestDataFactory.createComplexPlan()

            // When
            val jsonString = originalPlan.toJson()
            val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)

            // Then
            assertNotNull(deserializedPlan)

            // 验证Template引用完整性
            originalPlan.dailySchedule.forEach { (dayNumber, originalDayPlan) ->
                val deserializedDayPlan = deserializedPlan.dailySchedule[dayNumber]
                assertNotNull(deserializedDayPlan)

                assertEquals(
                    originalDayPlan.templateIds.size,
                    deserializedDayPlan.templateIds.size,
                    "第${dayNumber}天的templateIds数量应匹配",
                )
                assertEquals(
                    originalDayPlan.templateVersionIds.size,
                    deserializedDayPlan.templateVersionIds.size,
                    "第${dayNumber}天的templateVersionIds数量应匹配",
                )

                // 验证Template ID顺序
                assertEquals(
                    originalDayPlan.templateIds,
                    deserializedDayPlan.templateIds,
                    "第${dayNumber}天的templateIds顺序应保持一致",
                )
                assertEquals(
                    originalDayPlan.templateVersionIds,
                    deserializedDayPlan.templateVersionIds,
                    "第${dayNumber}天的templateVersionIds顺序应保持一致",
                )
            }
        }
    }

    // ==================== 字段命名规范验证测试 ====================

    @Nested
    @DisplayName("字段命名规范验证测试")
    inner class NamingConventionTests {

        @Test
        @DisplayName("JSON字段命名应符合camelCase规范")
        fun `json field naming should follow camelCase convention`() {
            // Given
            val plan = PlanTestDataFactory.createSimplePlan()

            // When
            val jsonString = plan.toJson()

            // Then - 验证camelCase字段名
            assertTrue(jsonString.contains("\"planId\"") || jsonString.contains("\"id\""), "计划ID字段应使用camelCase")
            assertTrue(
                jsonString.contains("\"planName\"") || jsonString.contains("\"name\""),
                "计划名称字段应使用camelCase",
            )
            assertTrue(jsonString.contains("\"userId\""), "用户ID字段应使用camelCase")
            assertTrue(jsonString.contains("\"totalDays\""), "总天数字段应使用camelCase")
            assertTrue(jsonString.contains("\"dailySchedule\""), "日程安排字段应使用camelCase")
            assertTrue(jsonString.contains("\"dayNumber\""), "天数字段应使用camelCase")
            assertTrue(jsonString.contains("\"isRestDay\""), "休息日标记字段应使用camelCase")
            assertTrue(jsonString.contains("\"templateIds\""), "模板ID列表字段应使用camelCase")
            assertTrue(jsonString.contains("\"templateVersionIds\""), "模板版本ID列表字段应使用camelCase")
            assertTrue(jsonString.contains("\"createdAt\""), "创建时间字段应使用camelCase")
            assertTrue(jsonString.contains("\"updatedAt\""), "更新时间字段应使用camelCase")

            // 验证不存在snake_case字段名
            assertFalse(jsonString.contains("\"plan_id\""), "不应包含snake_case的plan_id")
            assertFalse(jsonString.contains("\"user_id\""), "不应包含snake_case的user_id")
            assertFalse(jsonString.contains("\"total_days\""), "不应包含snake_case的total_days")
            assertFalse(jsonString.contains("\"daily_schedule\""), "不应包含snake_case的daily_schedule")
            assertFalse(jsonString.contains("\"day_number\""), "不应包含snake_case的day_number")
            assertFalse(jsonString.contains("\"is_rest_day\""), "不应包含snake_case的is_rest_day")
            assertFalse(jsonString.contains("\"template_ids\""), "不应包含snake_case的template_ids")
            assertFalse(jsonString.contains("\"created_at\""), "不应包含snake_case的created_at")
            assertFalse(jsonString.contains("\"updated_at\""), "不应包含snake_case的updated_at")
        }

        @Test
        @DisplayName("DayPlan字段命名应符合规范")
        fun `dayplan field naming should follow convention`() {
            // Given
            val dayPlan = DayPlan(
                dayNumber = 1,
                templateIds = listOf("template_001"),
                templateVersionIds = listOf("version_001"),
                isRestDay = false,
                dayNotes = "测试备注",
                progress = PlanProgressStatus.NOT_STARTED,
            )

            // When
            val jsonString = json.encodeToString(DayPlan.serializer(), dayPlan)

            // Then
            assertTrue(jsonString.contains("\"dayNumber\""), "应包含dayNumber字段")
            assertTrue(jsonString.contains("\"templateIds\""), "应包含templateIds字段")
            assertTrue(jsonString.contains("\"templateVersionIds\""), "应包含templateVersionIds字段")
            assertTrue(jsonString.contains("\"isRestDay\""), "应包含isRestDay字段")
            assertTrue(jsonString.contains("\"dayNotes\""), "应包含dayNotes字段")
            assertTrue(jsonString.contains("\"progress\""), "应包含progress字段")
        }
    }

    // ==================== 批量处理测试 ====================

    @Nested
    @DisplayName("批量处理测试")
    inner class BatchProcessingTests {

        @Test
        @DisplayName("Plan列表序列化应该成功")
        fun `plan list serialization should succeed`() {
            // Given
            val plans = PlanTestDataFactory.createMultiplePlans(5)

            // When
            val jsonString = PlanJsonProcessor.run { plans.toJsonArray() }

            // Then
            assertNotNull(jsonString, "批量序列化结果不应为null")
            assertTrue(jsonString.startsWith("["), "JSON数组应以[开始")
            assertTrue(jsonString.endsWith("]"), "JSON数组应以]结束")

            // 验证包含所有计划
            plans.forEach { plan ->
                assertTrue(jsonString.contains("\"id\":\"${plan.id}\""), "应包含计划${plan.id}")
            }
        }

        @Test
        @DisplayName("Plan列表反序列化应该成功")
        fun `plan list deserialization should succeed`() {
            // Given
            val originalPlans = PlanTestDataFactory.createMultiplePlans(3)
            val jsonString = PlanJsonProcessor.run { originalPlans.toJsonArray() }

            // When
            val deserializedPlans = PlanJsonProcessor.fromJsonArray(jsonString)

            // Then
            assertEquals(originalPlans.size, deserializedPlans.size, "计划数量应匹配")

            originalPlans.forEachIndexed { index, originalPlan ->
                val deserializedPlan = deserializedPlans[index]
                assertEquals(originalPlan.id, deserializedPlan.id, "第${index}个计划ID应匹配")
                assertEquals(originalPlan.name, deserializedPlan.name, "第${index}个计划名称应匹配")
                assertEquals(originalPlan.userId, deserializedPlan.userId, "第${index}个计划用户ID应匹配")
            }
        }

        @Test
        @DisplayName("空Plan列表处理应该成功")
        fun `empty plan list processing should succeed`() {
            // Given
            val emptyPlans = emptyList<WorkoutPlan>()

            // When
            val jsonString = PlanJsonProcessor.run { emptyPlans.toJsonArray() }
            val deserializedPlans = PlanJsonProcessor.fromJsonArray(jsonString)

            // Then
            assertEquals("[]", jsonString, "空列表应序列化为[]")
            assertTrue(deserializedPlans.isEmpty(), "反序列化结果应为空列表")
        }
    }

    // ==================== 边界情况和异常处理测试 ====================

    @Nested
    @DisplayName("边界情况和异常处理测试")
    inner class EdgeCaseTests {

        @Test
        @DisplayName("大型Plan序列化应该成功")
        fun `large plan serialization should succeed`() {
            // Given
            val largePlan = PlanTestDataFactory.createLargePlan()

            // When
            val jsonString = largePlan.toJson()

            // Then
            assertNotNull(jsonString, "大型Plan序列化应成功")
            assertTrue(jsonString.length > 10000, "大型Plan的JSON长度应超过10KB")
            assertTrue(jsonString.contains("\"totalDays\":30"), "应包含正确的总天数")
        }

        @Test
        @DisplayName("异常数据Plan序列化应该有容错处理")
        fun `invalid data plan serialization should have error handling`() {
            // Given
            val invalidPlan = PlanTestDataFactory.createInvalidPlan()

            // When & Then - 不应抛出异常
            val jsonString = invalidPlan.toJson()
            assertNotNull(jsonString, "即使数据异常，序列化也应返回结果")
        }

        @Test
        @DisplayName("空字符串反序列化应该返回null")
        fun `empty string deserialization should return null`() {
            // When
            val result1 = PlanJsonProcessor.fromJson("")
            val result2 = PlanJsonProcessor.fromJson("   ")
            val result3 = PlanJsonProcessor.fromJson("null")

            // Then
            assertNull(result1, "空字符串反序列化应返回null")
            assertNull(result2, "空白字符串反序列化应返回null")
            assertNull(result3, "null字符串反序列化应返回null")
        }

        @Test
        @DisplayName("格式错误的JSON反序列化应该返回null")
        fun `malformed json deserialization should return null`() {
            // Given
            val malformedJsons = listOf(
                "{invalid json}",
                "{\"id\": }",
                "{\"name\": \"test\", }",
                "[{\"invalid\": \"array\"}]",
                "not json at all",
            )

            // When & Then
            malformedJsons.forEach { malformedJson ->
                val result = PlanJsonProcessor.fromJson(malformedJson)
                assertNull(result, "格式错误的JSON [$malformedJson] 应返回null")
            }
        }
    }

    // ==================== 性能测试 ====================

    @Nested
    @DisplayName("性能测试")
    inner class PerformanceTests {

        @Test
        @DisplayName("大数据量序列化性能测试")
        fun `large data serialization performance test`() {
            // Given
            val largePlan = PlanTestDataFactory.createPerformanceTestPlan(totalDays = 100, templatesPerDay = 10)

            // When
            val duration = measureTime {
                repeat(10) {
                    largePlan.toJson()
                }
            }

            // Then
            println("大型Plan(100天-10模板)序列化10次耗时: ${duration.inWholeMilliseconds}ms")
            assertTrue(duration.inWholeMilliseconds < 5000, "性能测试: 序列化10次应在5秒内完成")
        }

        @Test
        @DisplayName("批量Plan序列化性能测试")
        fun `batch plan serialization performance test`() {
            // Given
            val batchPlans = PlanTestDataFactory.createMultiplePlans(50)

            // When
            val duration = measureTime {
                repeat(5) {
                    PlanJsonProcessor.run { batchPlans.toJsonArray() }
                }
            }

            // Then
            println("50个Plan批量序列化5次耗时: ${duration.inWholeMilliseconds}ms")
            assertTrue(duration.inWholeMilliseconds < 3000, "性能测试: 批量序列化5次应在3秒内完成")
        }

        @Test
        @DisplayName("往返性能测试")
        fun `round trip performance test`() {
            // Given
            val plan = PlanTestDataFactory.createComplexPlan()

            // When
            val duration = measureTime {
                repeat(100) {
                    val jsonString = plan.toJson()
                    PlanJsonProcessor.fromJson(jsonString)
                }
            }

            // Then
            println("复杂Plan往返100次耗时: ${duration.inWholeMilliseconds}ms")
            assertTrue(duration.inWholeMilliseconds < 2000, "性能测试: 往返100次应在2秒内完成")
        }
    }

    // ==================== JSON验证测试 ====================

    @Nested
    @DisplayName("JSON验证测试")
    inner class JsonValidationTests {

        @Test
        @DisplayName("Plan JSON完整性验证应该成功")
        fun `plan json integrity validation should succeed`() {
            // Given
            val plan = PlanTestDataFactory.createSimplePlan()
            val jsonString = plan.toJson()

            // When
            val isValid = PlanJsonProcessor.validatePlanJson(jsonString)

            // Then
            assertTrue(isValid, "有效的Plan JSON应通过验证")
        }

        @Test
        @DisplayName("无效Plan JSON验证应该失败")
        fun `invalid plan json validation should fail`() {
            // Given
            val invalidJsons = listOf(
                "{}", // 空对象
                "{\"id\":\"\", \"name\":\"\", \"userId\":\"\"}", // 空字段
                "{\"id\":\"test\", \"totalDays\":-1}", // 无效数值
                PlanTestDataFactory.getInvalidJsonString(),
            )

            // When & Then
            invalidJsons.forEach { invalidJson ->
                val isValid = PlanJsonProcessor.validatePlanJson(invalidJson)
                assertFalse(isValid, "无效JSON [$invalidJson] 应验证失败")
            }
        }

        @Test
        @DisplayName("日历数据JSON验证应该成功")
        fun `calendar data json validation should succeed`() {
            // Given
            val plan = PlanTestDataFactory.createComplexPlan()
            val calendarData = plan.toCalendarJson("2023-08-01")
            val jsonString = PlanJsonProcessor.run { calendarData.toJson() }

            // When
            val isValid = PlanJsonProcessor.validateCalendarJson(jsonString)

            // Then
            assertTrue(isValid, "有效的日历JSON应通过验证")
        }
    }
}
