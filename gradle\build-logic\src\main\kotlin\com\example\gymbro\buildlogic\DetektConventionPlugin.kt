package com.example.gymbro.buildlogic

import com.example.gymbro.buildlogic.Constants.MAX_FILE_LINES
import com.example.gymbro.buildlogic.Constants.MAX_FUNCTION_LINES
import com.example.gymbro.buildlogic.Constants.MAX_TIMBER_LOGS_PER_FUNCTION
import com.example.gymbro.buildlogic.Constants.VERIFICATION_GROUP
import io.gitlab.arturbosch.detekt.Detekt
import io.gitlab.arturbosch.detekt.DetektCreateBaselineTask
import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import java.io.File
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.withType

/**
 * GymBro项目Detekt静态代码分析统一管理插件
 *
 * 配置统一的Kotlin静态代码分析规则：
 * - 基于项目自定义detekt.yml配置
 * - 支持baseline文件管理
 * - 生成多种格式报告
 * - 零警告标准配置
 * - 强制执行：函数<80行、文件<500行、timber log限制
 */
class DetektConventionPlugin : Plugin<Project> {

    companion object {
        private fun isFunctionStart(trimmedLine: String, lines: List<String>, index: Int): Boolean {
            return trimmedLine.matches(Regex(".*fun\\s+\\w+.*\\{.*")) ||
                (trimmedLine.matches(Regex(".*fun\\s+\\w+.*")) && lines.getOrNull(index + 1)?.contains("{") == true)
        }

        private fun isTimberLogCall(trimmedLine: String): Boolean {
            return trimmedLine.contains("Timber.") || trimmedLine.contains("timber.") || trimmedLine.contains("log.")
        }

        private fun isFunctionEnd(bracketCount: Int, trimmedLine: String): Boolean {
            return bracketCount <= 0 && trimmedLine.contains("}")
        }
    }

    override fun apply(target: Project) {
        with(target) {
            applyDetektPlugin()
            configureDetektExtension()
            configureDetektTasks()
            configureBaselineTasks()
            createConvenienceTasks()
            configureFormatting()
            createValidationTasks()
        }
    }

    private fun Project.applyDetektPlugin() {
        pluginManager.apply("io.gitlab.arturbosch.detekt")
        logger.info("Detekt插件已应用，自定义规则通过 build-logic 插件提供")

        // 添加自定义规则依赖
        configureDetektDependencies()
    }

    private fun Project.configureDetektDependencies() {
        dependencies.add("detektPlugins", project(":detekt"))
    }

    private fun Project.configureDetektExtension() {
        extensions.configure<DetektExtension> {
            // 使用统一的 build-logic 配置文件
            config.setFrom(rootProject.files("gradle/build-logic/detekt.yml"))

            // 使用baseline文件（如果存在）
            baseline = rootProject.file("config/detekt/baseline.xml")

            // 启用并行执行
            parallel = true

            // 构建失败时的行为
            buildUponDefaultConfig = true
            allRules = false

            // 构建失败时的行为 - CI环境中严格检查，本地环境允许失败
            ignoreFailures = !project.hasProperty("ci")

            // 自动修正（默认关闭，仅在 detektFormat 任务中启用）
            autoCorrect = false

            // 配置源文件
            source.setFrom(
                files(
                    "src/main/kotlin",
                    "src/main/java",
                    "src/test/kotlin",
                    "src/test/java"
                )
            )
        }
    }


    private fun Project.configureDetektTasks() {
        tasks.withType<Detekt>().configureEach {
            description = "运行Detekt静态代码分析"
            group = VERIFICATION_GROUP

            // 设置JVM目标版本（直接赋值）
            jvmTarget = "17"

            // 配置报告
            configureDetektReports()

            // 排除生成的代码
            exclude("**/generated/**", "**/build/**", "**/resources/**")

            // 包含源文件
            include("**/*.kt", "**/*.kts")

            // 安全设置类路径
            configureDetektClasspath()
        }
    }

    private fun Detekt.configureDetektReports() {
        reports {
            html.required.set(true)
            xml.required.set(true)
            txt.required.set(false)
            sarif.required.set(false)
            md.required.set(false)

            // 自定义报告位置 - 生成到根项目的 build 目录
            val rootBuildDir = project.rootProject.layout.buildDirectory
            html.outputLocation.set(rootBuildDir.file("reports/detekt/${project.name}-detekt.html"))
            xml.outputLocation.set(rootBuildDir.file("reports/detekt/${project.name}-detekt.xml"))
        }
    }

    private fun Detekt.configureDetektClasspath() {
        try {
            // 使用 runtimeClasspath 替代 compileClasspath
            val runtimeClasspath = project.configurations.findByName("runtimeClasspath")
            if (runtimeClasspath?.isCanBeResolved == true) {
                classpath.setFrom(runtimeClasspath)
            }
        } catch (e: org.gradle.api.InvalidUserDataException) {
            // 处理Gradle配置相关的异常
            project.logger.warn("Gradle配置异常，无法设置 Detekt 类路径: ${e.message}")
        } catch (e: org.gradle.api.UnknownDomainObjectException) {
            // 处理未知配置对象异常
            project.logger.warn("未找到runtimeClasspath配置: ${e.message}")
        }
    }


    private fun Project.configureBaselineTasks() {
        tasks.withType<DetektCreateBaselineTask>().configureEach {
            description = "创建或更新Detekt baseline文件"
            group = VERIFICATION_GROUP

            // 设置JVM目标版本（直接赋值）
            jvmTarget = "17"

            // 设置baseline文件位置
            baseline.set(rootProject.file("gradle/build-logic/baseline.xml"))
        }
    }


    private fun Project.createConvenienceTasks() {
        tasks.register("detektAll") {
            description = "运行所有源集的Detekt检查"
            group = VERIFICATION_GROUP
            dependsOn(tasks.withType<Detekt>())

            doLast {
                val rootBuildDir = rootProject.layout.buildDirectory.get().asFile
                println("=== GymBro Detekt 静态分析完成 ===")
                println("📊 报告已生成到根项目 build 目录：")
                println("   HTML报告：${rootBuildDir}/reports/detekt/")
                println("   XML报告：${rootBuildDir}/reports/detekt/")
                println("🔍 查看具体模块报告：")
                println("   - ${rootBuildDir}/reports/detekt/${project.name}-detekt.html")
                println("   - ${rootBuildDir}/reports/detekt/${project.name}-detekt.xml")
                println("================================")
            }
        }

        // 为根项目创建聚合任务
        if (project == rootProject) {
            tasks.register("detektAllModules") {
                description = "运行所有模块的Detekt检查并聚合报告"
                group = VERIFICATION_GROUP

                doLast {
                    val rootBuildDir = rootProject.layout.buildDirectory.get().asFile
                    val detektReportsDir = File(rootBuildDir, "reports/detekt")

                    println("=== 🎯 GymBro 全项目 Detekt 分析完成 ===")
                    println("📊 所有模块报告已聚合到: ${detektReportsDir.absolutePath}")

                    if (detektReportsDir.exists()) {
                        val reportFiles = detektReportsDir.listFiles { file: File ->
                            file.name.endsWith(".html") || file.name.endsWith(".xml")
                        }

                        if (reportFiles != null && reportFiles.isNotEmpty()) {
                            println("🔍 可用报告文件:")
                            reportFiles.sortedBy { it.name }.forEach { file: File ->
                                println("   - ${file.name}")
                            }
                        }
                    }
                    println("🎯 主要质量报告位置: D:\\GymBro\\GymBro\\build\\reports\\detekt\\")
                    println("=========================================")
                }
            }
        }
    }


    private fun Project.configureFormatting() {
        gradle.taskGraph.whenReady {
            val isFormatting = gradle.startParameter.taskNames.any { name ->
                // 支持 "detektFormat", ":app:detektFormat" 等形式
                name.endsWith("detektFormat")
            }

            if (isFormatting) {
                tasks.withType<Detekt>().configureEach {
                    logger.info("为任务 ${this.path} 启用 detekt 自动格式化")
                    autoCorrect = true
                    ignoreFailures = true // 格式化不应使构建失败
                }
            }
        }
    }


    private fun Project.createValidationTasks() {
        tasks.register("gymbroValidation") {
            description = "验证GymBro项目约束：函数<${MAX_FUNCTION_LINES}行、文件<${MAX_FILE_LINES}行、timber log限制"
            group = VERIFICATION_GROUP

            doLast {
                println("=== GymBro 项目约束验证 ===")
                val hasViolations = validateProjectConstraints()

                if (hasViolations) {
                    throw org.gradle.api.GradleException("❌ GymBro项目约束验证失败！请修复上述问题。")
                } else {
                    println("✅ 所有GymBro项目约束验证通过")
                }
                println("================================")
            }
        }
    }

    private fun Project.validateProjectConstraints(): Boolean {
        var hasViolations = false
        val sourceFiles = fileTree("src") {
            include("**/*.kt")
            exclude("**/test/**", "**/androidTest/**", "**/build/**")
        }

        sourceFiles.forEach { file ->
            val lines = file.readLines()

            // 检查文件行数约束
            if (validateFileLineCount(file, lines)) {
                hasViolations = true
            }

            // 检查函数约束
            if (validateFunctionConstraints(file, lines)) {
                hasViolations = true
            }
        }

        return hasViolations
    }

    private fun Project.validateFileLineCount(file: java.io.File, lines: List<String>): Boolean {
        val lineCount = lines.size
        if (lineCount > MAX_FILE_LINES) {
            logger.error("❌ 文件行数超限: ${file.relativeTo(projectDir)} ($lineCount 行 > $MAX_FILE_LINES 行)")
            return true
        }
        return false
    }

    private fun Project.validateFunctionConstraints(file: java.io.File, lines: List<String>): Boolean {
        var hasViolations = false
        var currentFunctionLines = 0
        var currentFunctionTimberCount = 0
        var inFunction = false
        var bracketCount = 0

        lines.forEachIndexed { index, line ->
            val trimmedLine = line.trim()

            // 检测函数开始
            if (isFunctionStart(trimmedLine, lines, index)) {
                inFunction = true
                currentFunctionLines = 1
                currentFunctionTimberCount = 0
                bracketCount = line.count { it == '{' } - line.count { it == '}' }
            } else if (inFunction) {
                currentFunctionLines++
                bracketCount += line.count { it == '{' } - line.count { it == '}' }

                // 检测timber日志调用
                if (isTimberLogCall(trimmedLine)) {
                    currentFunctionTimberCount++
                }

                // 检测函数结束
                if (isFunctionEnd(bracketCount, trimmedLine)) {
                    inFunction = false

                    // 检查函数行数约束
                    if (currentFunctionLines > MAX_FUNCTION_LINES) {
                        val filePath = file.relativeTo(projectDir)
                        logger.error("❌ 函数行数超限: $filePath:${index + 1} ($currentFunctionLines 行 > $MAX_FUNCTION_LINES 行)")
                        hasViolations = true
                    }

                    // 检查timber log约束
                    if (currentFunctionTimberCount > MAX_TIMBER_LOGS_PER_FUNCTION) {
                        val filePath = file.relativeTo(projectDir)
                        logger.error("❌ Timber日志超限: $filePath:${index + 1} ($currentFunctionTimberCount 个 > $MAX_TIMBER_LOGS_PER_FUNCTION 个)")
                        hasViolations = true
                    }
                }
            }
        }

        return hasViolations
    }

}
