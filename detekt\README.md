# GymBro 自定义 Detekt 规则模块

## 📋 概述

这是 GymBro 项目的自定义 Detekt 规则模块，包含项目特定的代码质量检查规则。这些规则强制执行 GymBro 的架构原则、设计系统规范和编码标准。

## 🏗️ 规则分类

### 1. MVI 架构规则 (`mvi/`)
- **MviStateImmutability**: 确保 MVI State 类的不可变性
- **ImmutableStateClass**: 强制 State 类使用 @Immutable 注解
- **MviIntentNaming**: 验证 Intent 类的命名规范

### 2. 设计系统规则 (`design/`)
- **NoHardcodedColor**: 禁止硬编码颜色值，强制使用 Design Tokens
- **NoHardcodedDimension**: 禁止硬编码尺寸值，强制使用 Spacing Tokens
- **NoHardcodedDesignValues**: 通用设计值检查
- **UseWorkoutColors**: 检测硬编码颜色的使用，建议使用设计系统 tokens

### 3. 日志规则 (`logging/`)
- **MaxTimberLogsPerFile**: 限制每个文件的 Timber 日志数量
- **MaxTimberLogsPerFunction**: 限制每个函数的 Timber 日志数量
- **LoggingModuleRestriction**: 强制不同模块使用正确的日志方式

### 4. 文档规则 (`documentation/`)
- **KDocAutoFix**: 自动修复 KDoc 格式问题
- **KDocFormatter**: 智能格式化 KDoc 注释
- **KDocCompleteness**: 检查公共 API 的文档完整性

### 5. 质量规则 (`quality/`)
- **NoTodoOrFixme**: 警告 TODO、FIXME 等未完成标记

## 🚀 使用方法

### 1. 构建模块
```bash
./gradlew :detekt:build
```

### 2. 运行测试
```bash
./gradlew :detekt:test
```

### 3. 在项目中使用
在需要使用自定义规则的模块的 `build.gradle.kts` 中添加：

```kotlin
dependencies {
    detektPlugins(project(":detekt"))
}
```

### 4. 配置规则
在 detekt 配置文件中启用规则：

```yaml
gymbro-rules:
  active: true
  MviStateImmutability:
    active: true
  NoHardcodedColor:
    active: true
  MaxTimberLogsPerFile:
    active: true
    threshold: 5
  # ... 其他规则配置
```

## 📊 规则详情

### MVI 架构规则

#### MviStateImmutability
- **目的**: 确保 MVI State 类的不可变性
- **检查项**:
  - State 类必须是 data class
  - 所有属性必须是 val
  - 避免使用可变集合类型
  - 建议使用 @Immutable 注解

#### ImmutableStateClass
- **目的**: 强制 State 类使用 @Immutable 注解
- **检查项**: 所有 State 类必须有 @Immutable 注解

#### MviIntentNaming
- **目的**: 验证 Intent 类的命名规范
- **检查项**:
  - Intent 以动词开头 (Load, Update, Create 等)
  - Result Intent 以 "Result" 结尾
  - Internal Intent 以 "Internal" 开头

### 设计系统规则

#### NoHardcodedColor
- **目的**: 强制使用 Design System 颜色 Tokens
- **检查模式**:
  - `Color(0xFF123456)`
  - `Color.Red`
  - `"#FF0000".toColor()`
- **建议替换**: `Tokens.Color.primary`

#### NoHardcodedDimension
- **目的**: 强制使用 Design System 尺寸 Tokens
- **检查模式**:
  - `Modifier.padding(16.dp)`
  - `Modifier.size(24.dp)`
- **建议替换**: `Tokens.Spacing.medium`

#### UseWorkoutColors
- **目的**: 检测硬编码颜色的使用
- **检查模式**:
  - `Color.Red`, `Color.Blue` 等颜色常量
  - `Color(0xFF123456)` 等构造函数调用
- **建议**: 使用设计系统 tokens 而不是硬编码颜色值

### 日志规则

#### MaxTimberLogsPerFile
- **目的**: 防止过度使用日志
- **默认阈值**: 每个文件最多 5 个 Timber 调用
- **可配置**: `threshold` 参数

#### LoggingModuleRestriction
- **目的**: 强制不同模块使用正确的日志方式
- **规则**:
  - Domain 层使用 `log.*` (避免 Android 依赖)
  - 其他层使用 `Timber.*`

### 文档规则

#### KDocAutoFix
- **目的**: 自动修复 KDoc 格式问题
- **修复功能**:
  - 自动添加缺失的句号
  - 修正中英文标点符号混用
  - 检测并报告格式问题
- **示例**:
  ```kotlin
  // 问题：缺少句号
  /**
   * 这是一个函数
   */

  // 修复后：
  /**
   * 这是一个函数。
   */
  ```

#### KDocFormatter
- **目的**: 智能格式化 KDoc 注释
- **格式化功能**:
  - 统一标点符号使用
  - 格式化参数和返回值文档
  - 移除多余的空行
  - 确保文档结构一致性
- **示例**:
  ```kotlin
  // 格式化前：
  /**
   * 计算用户BMI
   * @param weight 体重
   * @param height 身高
   * @return BMI值
   */

  // 格式化后：
  /**
   * 计算用户BMI。
   *
   * @param weight 体重。
   * @param height 身高。
   * @return BMI值。
   */
  ```

#### KDocCompleteness
- **目的**: 检查公共 API 的文档完整性
- **检查项**:
  - 公共类、函数、属性必须有 KDoc
  - 函数参数必须有文档说明
  - 返回值必须有文档说明
  - 抛出的异常必须有文档说明
- **配置选项**:
  - `requireDocumentationForPublic`: 是否要求公共 API 有文档
  - `requireParameterDocumentation`: 是否要求参数文档
  - `requireReturnDocumentation`: 是否要求返回值文档

## 🔧 开发指南

### 添加新规则

1. 在相应的包中创建规则类
2. 继承 `Rule` 类
3. 实现 `issue` 属性和访问方法
4. 在 `GymBroRuleSetProvider` 中注册规则
5. 添加测试用例

### 规则实现示例

```kotlin
class MyCustomRule(config: Config = Config.empty) : Rule(config) {
    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "规则描述",
        Debt.FIVE_MINS
    )

    override fun visitCallExpression(expression: KtCallExpression) {
        // 实现检查逻辑
    }
}
```

## 📚 相关文档

- [Detekt 官方文档](https://detekt.dev/)
- [GymBro 架构规范](../gradle/build-logic/README-DETEKT.md)
- [自定义规则指南](../gradle/build-logic/README-CUSTOM-RULES.md)

## 🐛 故障排除

### 常见问题

1. **规则不生效**: 检查 META-INF/services 文件是否正确
2. **编译错误**: 确保 Detekt API 版本兼容
3. **配置错误**: 验证 detekt.yml 中的规则配置

### 调试技巧

- 使用 `./gradlew :detekt:test` 验证规则逻辑
- 检查 detekt 报告中的自定义规则结果
- 启用详细日志: `./gradlew detekt --info`

---

**版本**: 1.0.0
**兼容 Detekt**: 1.23.8
**维护者**: GymBro 开发团队
