package com.example.gymbro.features.profile.internal.presentation.workoutdays

import com.example.gymbro.core.logging.Logger
import javax.inject.Inject

/**
 * WorkoutDays功能的Reducer
 * 负责处理Intent并产生新的State和Effect
 *
 * 遵循MVI架构的纯函数原则，所有状态变更都是不可变的
 */
internal class WorkoutDaysReducer
    @Inject
    constructor(
        private val logger: Logger,
    ) {

        /**
         * Reducer结果数据类
         */
        data class ReduceResult(
            val newState: WorkoutDaysContract.State,
            val effects: List<WorkoutDaysContract.Effect> = emptyList(),
        )

        /**
         * 处理Intent并产生新的State和Effect
         *
         * @param intent 用户意图
         * @param currentState 当前状态
         * @return 新的状态和副作用列表
         */
        fun reduce(
            intent: WorkoutDaysContract.Intent,
            currentState: WorkoutDaysContract.State,
        ): ReduceResult {
            logger.d("WorkoutDaysReducer", "处理Intent: ${intent::class.simpleName}")

            return when (intent) {
                // === 数据加载相关 ===
                is WorkoutDaysContract.Intent.LoadWorkoutDays -> handleLoadWorkoutDays(currentState)
                is WorkoutDaysContract.Intent.RetryLoad -> handleRetryLoad(currentState)

                // === 训练日选择相关 ===
                is WorkoutDaysContract.Intent.ToggleWorkoutDay -> handleToggleWorkoutDay(intent, currentState)
                is WorkoutDaysContract.Intent.UpdateWorkoutDays -> handleUpdateWorkoutDays(intent, currentState)

                // === 保存相关 ===
                is WorkoutDaysContract.Intent.SaveWorkoutDays -> handleSaveWorkoutDays(currentState)
                is WorkoutDaysContract.Intent.ClearSaveSuccess -> handleClearSaveSuccess(currentState)

                // === 错误处理 ===
                is WorkoutDaysContract.Intent.ClearError -> handleClearError(currentState)

                // === 内部Intent处理 ===
                is WorkoutDaysContract.Intent.WorkoutDaysLoaded -> handleWorkoutDaysLoaded(intent, currentState)
                is WorkoutDaysContract.Intent.WorkoutDaysLoadError -> handleWorkoutDaysLoadError(
                    intent,
                    currentState,
                )

                is WorkoutDaysContract.Intent.WorkoutDaysSaveSuccess -> handleWorkoutDaysSaveSuccess(currentState)
                is WorkoutDaysContract.Intent.WorkoutDaysSaveError -> handleWorkoutDaysSaveError(
                    intent,
                    currentState,
                )
            }
        }

        // === 私有处理方法 ===

        private fun handleLoadWorkoutDays(currentState: WorkoutDaysContract.State): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(
                    isLoading = true,
                    error = null,
                ),
                effects = listOf(WorkoutDaysContract.Effect.LoadWorkoutDays),
            )
        }

        private fun handleRetryLoad(currentState: WorkoutDaysContract.State): ReduceResult {
            return handleLoadWorkoutDays(currentState)
        }

        private fun handleToggleWorkoutDay(
            intent: WorkoutDaysContract.Intent.ToggleWorkoutDay,
            currentState: WorkoutDaysContract.State,
        ): ReduceResult {
            val newSelectedDays = if (currentState.isWorkoutDaySelected(intent.weekDay)) {
                // 如果已选择，则取消选择（但至少保留一天）
                if (currentState.getSelectedDaysCount() > 1) {
                    currentState.selectedWorkoutDays - intent.weekDay
                } else {
                    // 如果只有一天，不允许取消
                    currentState.selectedWorkoutDays
                }
            } else {
                // 如果未选择，则添加选择
                currentState.selectedWorkoutDays + intent.weekDay
            }

            val hasChanges = newSelectedDays != currentState.selectedWorkoutDays
            val newState = currentState.copy(
                selectedWorkoutDays = newSelectedDays,
                hasUnsavedChanges = hasChanges,
                saveSuccess = false,
            )

            // 即时保存逻辑：如果有变更，立即触发保存
            val effects = if (hasChanges) {
                listOf(WorkoutDaysContract.Effect.SaveWorkoutDays(newSelectedDays))
            } else {
                emptyList()
            }

            return ReduceResult(newState = newState, effects = effects)
        }

        private fun handleUpdateWorkoutDays(
            intent: WorkoutDaysContract.Intent.UpdateWorkoutDays,
            currentState: WorkoutDaysContract.State,
        ): ReduceResult {
            val hasChanges = intent.workoutDays != currentState.selectedWorkoutDays
            val newState = currentState.copy(
                selectedWorkoutDays = intent.workoutDays,
                hasUnsavedChanges = hasChanges,
                saveSuccess = false,
            )

            // 即时保存逻辑
            val effects = if (hasChanges) {
                listOf(WorkoutDaysContract.Effect.SaveWorkoutDays(intent.workoutDays))
            } else {
                emptyList()
            }

            return ReduceResult(newState = newState, effects = effects)
        }

        private fun handleSaveWorkoutDays(currentState: WorkoutDaysContract.State): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(
                    isSaving = true,
                    error = null,
                ),
                effects = listOf(WorkoutDaysContract.Effect.SaveWorkoutDays(currentState.selectedWorkoutDays)),
            )
        }

        private fun handleClearSaveSuccess(currentState: WorkoutDaysContract.State): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(saveSuccess = false),
            )
        }

        private fun handleClearError(currentState: WorkoutDaysContract.State): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(error = null),
            )
        }

        private fun handleWorkoutDaysLoaded(
            intent: WorkoutDaysContract.Intent.WorkoutDaysLoaded,
            currentState: WorkoutDaysContract.State,
        ): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(
                    isLoading = false,
                    selectedWorkoutDays = intent.workoutDays,
                    hasUnsavedChanges = false,
                    error = null,
                ),
            )
        }

        private fun handleWorkoutDaysLoadError(
            intent: WorkoutDaysContract.Intent.WorkoutDaysLoadError,
            currentState: WorkoutDaysContract.State,
        ): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(
                    isLoading = false,
                    error = intent.error,
                ),
            )
        }

        private fun handleWorkoutDaysSaveSuccess(currentState: WorkoutDaysContract.State): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(
                    isSaving = false,
                    saveSuccess = true,
                    hasUnsavedChanges = false,
                    error = null,
                ),
            )
        }

        private fun handleWorkoutDaysSaveError(
            intent: WorkoutDaysContract.Intent.WorkoutDaysSaveError,
            currentState: WorkoutDaysContract.State,
        ): ReduceResult {
            return ReduceResult(
                newState = currentState.copy(
                    isSaving = false,
                    error = intent.error,
                ),
            )
        }
    }
