package com.example.gymbro.buildlogic.detekt.documentation

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.com.intellij.psi.PsiElement
import org.jetbrains.kotlin.kdoc.psi.api.KDoc
import org.jetbrains.kotlin.lexer.KtTokens
import org.jetbrains.kotlin.psi.KtClass
import org.jetbrains.kotlin.psi.KtNamedFunction
import org.jetbrains.kotlin.psi.KtParameter
import org.jetbrains.kotlin.psi.KtProperty

/**
 * GymBro 自定义规则：KDoc 完整性检查。
 *
 * 目的：确保公共 API 具有完整和高质量的文档。
 * 这有助于：
 * 1. 提高代码的可维护性
 * 2. 改善开发者体验
 * 3. 确保 API 文档的完整性
 * 4. 强制执行文档标准
 *
 * 检查项目：
 * - 公共类、函数、属性必须有 KDoc
 * - 函数参数必须有文档说明
 * - 返回值必须有文档说明
 * - 抛出的异常必须有文档说明
 */
class KDocCompleteness(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.Warning,
        "公共 API 缺少完整的 KDoc 文档。",
        Debt.TEN_MINS
    )

    private val requireDocumentationForPublic by config(true)
    private val requireParameterDocumentation by config(true)
    private val requireReturnDocumentation by config(true)

    override fun visitClass(klass: KtClass) {
        super.visitClass(klass)

        if (isPublic(klass) && requireDocumentationForPublic) {
            checkClassDocumentation(klass)
        }
    }

    override fun visitNamedFunction(function: KtNamedFunction) {
        super.visitNamedFunction(function)

        if (isPublic(function) && requireDocumentationForPublic) {
            checkFunctionDocumentation(function)
        }
    }

    override fun visitProperty(property: KtProperty) {
        super.visitProperty(property)

        if (isPublic(property) && requireDocumentationForPublic) {
            checkPropertyDocumentation(property)
        }
    }

    private fun checkClassDocumentation(klass: KtClass) {
        val kdoc = klass.docComment
        val className = klass.name ?: "未知类"

        if (kdoc == null) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(klass),
                    "公共类 '$className' 缺少 KDoc 文档。" +
                        "\n建议添加：\n/**\n * $className 的简要描述。\n *\n * 详细说明类的用途和功能。\n */"
                )
            )
        } else {
            validateKDocQuality(kdoc, klass as PsiElement, "类 '$className'")
        }
    }

    private fun checkFunctionDocumentation(function: KtNamedFunction) {
        val kdoc = function.docComment
        val functionName = function.name ?: "未知函数"

        if (kdoc == null) {
            val suggestion = generateFunctionKDocSuggestion(function)
            report(
                CodeSmell(
                    issue,
                    Entity.from(function),
                    "公共函数 '$functionName' 缺少 KDoc 文档。" +
                        "\n建议添加：\n$suggestion"
                )
            )
        } else {
            validateFunctionKDoc(kdoc, function)
        }
    }

    private fun checkPropertyDocumentation(property: KtProperty) {
        val kdoc = property.docComment
        val propertyName = property.name ?: "未知属性"

        if (kdoc == null) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(property),
                    "公共属性 '$propertyName' 缺少 KDoc 文档。" +
                        "\n建议添加：\n/**\n * $propertyName 的简要描述。\n */"
                )
            )
        } else {
            validateKDocQuality(kdoc, property as PsiElement, "属性 '$propertyName'")
        }
    }

    private fun validateFunctionKDoc(kdoc: KDoc, function: KtNamedFunction) {
        val kdocText = kdoc.text
        val functionName = function.name ?: "未知函数"
        val issues = mutableListOf<String>()

        // 检查参数文档
        if (requireParameterDocumentation) {
            val parameters = function.valueParameters
            val documentedParams = extractDocumentedParameters(kdocText)

            parameters.forEach { param ->
                val paramName = param.name
                if (paramName != null && !documentedParams.contains(paramName)) {
                    issues.add("缺少参数 '$paramName' 的文档")
                }
            }
        }

        // 检查返回值文档
        if (requireReturnDocumentation && function.hasBlockBody() && !function.typeReference?.text.equals("Unit")) {
            if (!kdocText.contains("@return")) {
                issues.add("缺少返回值文档")
            }
        }

        // 检查异常文档
        val throwsKeywords = listOf("throw", "throws", "exception")
        val hasThrowsInCode = function.bodyExpression?.text?.let { body ->
            throwsKeywords.any { keyword -> body.contains(keyword, ignoreCase = true) }
        } ?: false

        if (hasThrowsInCode && !kdocText.contains("@throws")) {
            issues.add("函数可能抛出异常但缺少 @throws 文档")
        }

        if (issues.isNotEmpty()) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(function),
                    "函数 '$functionName' 的 KDoc 不完整：${issues.joinToString(", ")}。"
                )
            )
        }
    }

    private fun validateKDocQuality(kdoc: KDoc, element: PsiElement, elementDescription: String) {
        val kdocText = kdoc.text
        val lines = kdocText.lines()

        // 检查是否只有空的 KDoc
        val contentLines = lines.drop(1).dropLast(1).map { it.trim().removePrefix("*").trim() }
        val hasContent = contentLines.any { it.isNotEmpty() }

        if (!hasContent) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(element),
                    "$elementDescription 的 KDoc 为空，请添加有意义的描述。"
                )
            )
        }
    }

    private fun generateFunctionKDocSuggestion(function: KtNamedFunction): String {
        val functionName = function.name ?: "函数"
        val params = function.valueParameters
        val hasReturnType = !function.typeReference?.text.equals("Unit")

        val kdocBuilder = StringBuilder()
        kdocBuilder.appendLine("/**")
        kdocBuilder.appendLine(" * $functionName 的简要描述。")
        kdocBuilder.appendLine(" *")
        kdocBuilder.appendLine(" * 详细说明函数的用途和行为。")

        if (params.isNotEmpty()) {
            kdocBuilder.appendLine(" *")
            params.forEach { param ->
                val paramName = param.name ?: "参数"
                kdocBuilder.appendLine(" * @param $paramName 参数描述")
            }
        }

        if (hasReturnType) {
            kdocBuilder.appendLine(" *")
            kdocBuilder.appendLine(" * @return 返回值描述")
        }

        kdocBuilder.append(" */")

        return kdocBuilder.toString()
    }

    private fun extractDocumentedParameters(kdocText: String): Set<String> {
        val paramRegex = Regex("@param\\s+(\\w+)")
        return paramRegex.findAll(kdocText)
            .map { it.groupValues[1] }
            .toSet()
    }

    private fun isPublic(element: Any): Boolean {
        // 简化的公共性检查，实际项目中可能需要更复杂的逻辑
        return when (element) {
            is KtClass -> !element.hasModifier(KtTokens.PRIVATE_KEYWORD) && !element.hasModifier(KtTokens.INTERNAL_KEYWORD)
            is KtNamedFunction -> !element.hasModifier(KtTokens.PRIVATE_KEYWORD) && !element.hasModifier(KtTokens.INTERNAL_KEYWORD)
            is KtProperty -> !element.hasModifier(KtTokens.PRIVATE_KEYWORD) && !element.hasModifier(KtTokens.INTERNAL_KEYWORD)
            else -> false
        }
    }
}
