package com.example.gymbro.data.ai.gateway

// 🧹 HARDCODE CLEANUP: 使用统一配置管理系统
import com.example.gymbro.core.config.AiConfig
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.common.CommonFeatureErrors
import com.example.gymbro.core.ui.text.UiText
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.datetime.Clock
import timber.log.Timber

/**
 * 配额熔断器
 *
 * 三层闸门系统的第三层，负责：
 * - Token使用配额管理
 * - 用户请求频率限制
 * - 熔断保护机制
 * - 成本预估与控制
 */
@Singleton
class QuotaCircuitBreaker
    @Inject
    constructor() {
        private val mutex = Mutex()

        // 用户配额状态
        private val userQuotaStatus = mutableMapOf<String, UserQuotaState>()

        // 系统级别配额
        private val systemQuotaStatus = SystemQuotaState()

        // 熔断器状态
        private val circuitBreakerStatus = mutableMapOf<String, CircuitBreakerState>()

        /**
         * 检查配额并进行成本预估
         */
        suspend fun checkQuotaAndEstimateCost(
            userId: String,
            requestType: AiRequestType,
            estimatedTokens: Int,
            providerName: String,
        ): ModernResult<QuotaDecision> {
            Timber.d(
                "QuotaCircuitBreaker: 检查配额 userId=$userId, tokens=$estimatedTokens, provider=$providerName",
            )

            return mutex.withLock {
                try {
                    // 1. 检查熔断器状态
                    val circuitCheck = checkCircuitBreaker(providerName)
                    if (!circuitCheck.passed) {
                        Timber.w("QuotaCircuitBreaker: 熔断器开启 provider=$providerName")
                        return@withLock ModernResult.Error(
                            CommonFeatureErrors.CoachError.processingFailed(
                                operationName = "QuotaCircuitBreaker.circuitOpen",
                                message = UiText.DynamicString("AI服务暂时不可用，请稍后重试"),
                                processType = "circuit_breaker",
                                reason = circuitCheck.reason,
                                metadataMap =
                                    mapOf(
                                        "provider_name" to providerName,
                                        "circuit_state" to circuitCheck.state,
                                        "circuit_open_time" to circuitCheck.openTime,
                                    ),
                            ),
                        )
                    }

                    // 2. 检查系统级配额
                    val systemCheck = checkSystemQuota(estimatedTokens)
                    if (!systemCheck.passed) {
                        Timber.w("QuotaCircuitBreaker: 系统配额不足")
                        return@withLock ModernResult.Error(
                            CommonFeatureErrors.CoachError.limitExceeded(
                                operationName = "QuotaCircuitBreaker.systemQuotaExceeded",
                                message = UiText.DynamicString("系统繁忙，请稍后重试"),
                                limitType = "system_tokens",
                                currentCount = systemQuotaStatus.usedTokens,
                                maxAllowed = systemQuotaStatus.maxTokens,
                                timeWindow = "daily",
                                metadataMap =
                                    mapOf(
                                        "requested_tokens" to estimatedTokens,
                                        "remaining_tokens" to (systemQuotaStatus.maxTokens - systemQuotaStatus.usedTokens),
                                    ),
                            ),
                        )
                    }

                    // 3. 检查用户配额
                    val userCheck = checkUserQuota(userId, estimatedTokens)
                    if (!userCheck.passed) {
                        Timber.w("QuotaCircuitBreaker: 用户配额不足 userId=$userId")
                        return@withLock ModernResult.Error(
                            CommonFeatureErrors.CoachError.limitExceeded(
                                operationName = "QuotaCircuitBreaker.userQuotaExceeded",
                                message = UiText.DynamicString("您的AI使用额度已用完，请明日再试"),
                                limitType = "user_tokens",
                                currentCount = userCheck.currentUsage,
                                maxAllowed = userCheck.maxAllowed,
                                timeWindow = "daily",
                                resetTime = userCheck.resetTime,
                                metadataMap =
                                    mapOf(
                                        "user_id" to userId,
                                        "requested_tokens" to estimatedTokens,
                                        "remaining_tokens" to userCheck.remainingTokens,
                                    ),
                            ),
                        )
                    }

                    // 4. 检查请求频率
                    val rateCheck = checkRequestRate(userId)
                    if (!rateCheck.passed) {
                        Timber.w("QuotaCircuitBreaker: 请求频率过高 userId=$userId")
                        return@withLock ModernResult.Error(
                            CommonFeatureErrors.CoachError.limitExceeded(
                                operationName = "QuotaCircuitBreaker.rateLimitExceeded",
                                message = UiText.DynamicString("请求过于频繁，请稍后再试"),
                                limitType = "request_rate",
                                currentCount = rateCheck.currentRequests,
                                maxAllowed = rateCheck.maxRequests,
                                timeWindow = "minute",
                                resetTime = rateCheck.resetTime.toString(),
                                metadataMap =
                                    mapOf(
                                        "user_id" to userId,
                                        "window_start" to rateCheck.windowStart.toString(),
                                    ),
                            ),
                        )
                    }

                    // 5. 计算成本预估
                    val cost = calculateDetailedCost(requestType, estimatedTokens, providerName)

                    Timber.d("QuotaCircuitBreaker: 配额检查通过，预估成本=${cost.totalCost}")
                    ModernResult.Success(
                        QuotaDecision(
                            approved = true,
                            estimatedCost = cost.totalCost,
                            estimatedTokens = estimatedTokens,
                            remainingUserQuota = userCheck.remainingTokens,
                            remainingSystemQuota = systemQuotaStatus.maxTokens - systemQuotaStatus.usedTokens,
                            costBreakdown = cost,
                            metadata =
                                mapOf(
                                    "provider_name" to providerName,
                                    "quota_check_time" to Clock.System.now().toEpochMilliseconds(),
                                ),
                        ),
                    )
                } catch (e: Exception) {
                    Timber.e(e, "QuotaCircuitBreaker: 配额检查异常")
                    ModernResult.Error(
                        CommonFeatureErrors.CoachError.processingFailed(
                            operationName = "QuotaCircuitBreaker.checkException",
                            message = UiText.DynamicString("配额检查服务异常"),
                            processType = "quota_check",
                            reason = "exception",
                            cause = e,
                            metadataMap =
                                mapOf(
                                    "exception_type" to e.javaClass.simpleName,
                                ),
                        ),
                    )
                }
            }
        }

        /**
         * 消费配额（请求成功后调用）
         */
        suspend fun consumeQuota(
            userId: String,
            actualTokens: Int,
            providerName: String,
        ): ModernResult<Unit> =
            mutex.withLock {
                try {
                    // 更新用户配额
                    val userState = userQuotaStatus.getOrPut(userId) { createUserQuotaState(userId) }
                    userState.usedTokens += actualTokens
                    userState.lastRequestTime = Clock.System.now().toEpochMilliseconds()

                    // 更新系统配额
                    systemQuotaStatus.usedTokens += actualTokens

                    // 记录成功请求，用于熔断器状态
                    recordSuccessfulRequest(providerName)

                    Timber.d("QuotaCircuitBreaker: 配额已消费 userId=$userId, tokens=$actualTokens")
                    ModernResult.Success(Unit)
                } catch (e: Exception) {
                    Timber.e(e, "QuotaCircuitBreaker: 消费配额异常")
                    ModernResult.Error(
                        CommonFeatureErrors.CoachError.processingFailed(
                            operationName = "QuotaCircuitBreaker.consumeException",
                            message = UiText.DynamicString("配额更新失败"),
                            processType = "quota_consume",
                            reason = "exception",
                            cause = e,
                        ),
                    )
                }
            }

        /**
         * 记录失败请求（用于熔断器）
         */
        suspend fun recordFailedRequest(
            providerName: String,
            error: Throwable,
        ) {
            mutex.withLock {
                val state =
                    circuitBreakerStatus.getOrPut(providerName) {
                        CircuitBreakerState(providerName)
                    }

                state.failureCount++
                state.lastFailureTime = Clock.System.now().toEpochMilliseconds()

                // 检查是否需要开启熔断器
                if (state.failureCount >= state.failureThreshold && state.state == CircuitState.CLOSED) {
                    state.state = CircuitState.OPEN
                    state.openTime = Clock.System.now().toEpochMilliseconds()
                    Timber.w(
                        "QuotaCircuitBreaker: 熔断器开启 provider=$providerName, failures=${state.failureCount}",
                    )
                }
            }
        }

        /**
         * 检查熔断器状态
         */
        private fun checkCircuitBreaker(providerName: String): CircuitCheck {
            val state =
                circuitBreakerStatus.getOrPut(providerName) {
                    CircuitBreakerState(providerName)
                }

            val now = Clock.System.now().toEpochMilliseconds()

            return when (state.state) {
                CircuitState.CLOSED -> CircuitCheck(true, "closed", CircuitState.CLOSED, 0)
                CircuitState.OPEN -> {
                    // 检查是否可以转为半开状态
                    if (now - state.openTime > state.timeoutDuration) {
                        state.state = CircuitState.HALF_OPEN
                        CircuitCheck(true, "half_open", CircuitState.HALF_OPEN, state.openTime)
                    } else {
                        CircuitCheck(false, "open", CircuitState.OPEN, state.openTime)
                    }
                }

                CircuitState.HALF_OPEN -> CircuitCheck(
                    true,
                    "half_open",
                    CircuitState.HALF_OPEN,
                    state.openTime,
                )
            }
        }

        /**
         * 检查系统配额
         */
        private fun checkSystemQuota(requestedTokens: Int): SystemQuotaCheck {
            val available = systemQuotaStatus.maxTokens - systemQuotaStatus.usedTokens
            return SystemQuotaCheck(
                passed = available >= requestedTokens,
                availableTokens = available,
                requestedTokens = requestedTokens,
            )
        }

        /**
         * 检查用户配额
         */
        private fun checkUserQuota(
            userId: String,
            requestedTokens: Int,
        ): UserQuotaCheck {
            val userState = userQuotaStatus.getOrPut(userId) { createUserQuotaState(userId) }

            // 检查是否需要重置日配额
            checkAndResetDailyQuota(userState)

            val available = userState.maxTokens - userState.usedTokens
            return UserQuotaCheck(
                passed = available >= requestedTokens,
                currentUsage = userState.usedTokens,
                maxAllowed = userState.maxTokens,
                remainingTokens = available,
                resetTime = getNextDayResetTime(),
            )
        }

        /**
         * 检查请求频率
         */
        private fun checkRequestRate(userId: String): RateCheck {
            val userState = userQuotaStatus.getOrPut(userId) { createUserQuotaState(userId) }
            val now = Clock.System.now().toEpochMilliseconds()
            val windowStart = now - 60_000 // 1分钟窗口

            // 清理过期的请求记录
            userState.requestTimes.removeAll { it < windowStart }

            val currentRequests = userState.requestTimes.size
            val maxRequests = 30 // 每分钟最多30次请求

            if (currentRequests < maxRequests) {
                userState.requestTimes.add(now)
            }

            return RateCheck(
                passed = currentRequests < maxRequests,
                currentRequests = currentRequests,
                maxRequests = maxRequests,
                windowStart = windowStart,
                resetTime = now + 60_000,
            )
        }

        private fun createUserQuotaState(userId: String): UserQuotaState =
            UserQuotaState(
                userId = userId,
                maxTokens = 10_000, // 每日10K tokens
                usedTokens = 0,
                lastResetTime = Clock.System.now().toEpochMilliseconds(),
                lastRequestTime = 0,
                requestTimes = mutableListOf(),
            )

        private fun checkAndResetDailyQuota(userState: UserQuotaState) {
            val now = Clock.System.now().toEpochMilliseconds()
            val dayInMillis = 24 * 60 * 60 * 1000

            if (now - userState.lastResetTime > dayInMillis) {
                userState.usedTokens = 0
                userState.lastResetTime = now
                userState.requestTimes.clear()
            }
        }

        private fun getNextDayResetTime(): String {
            val now = Clock.System.now().toEpochMilliseconds()
            val dayInMillis = 24 * 60 * 60 * 1000
            val nextReset = now + dayInMillis
            return "明日零点"
        }

        private fun calculateDetailedCost(
            requestType: AiRequestType,
            tokens: Int,
            providerName: String,
        ): CostBreakdown {
            val baseRate =
                when (requestType) {
                    AiRequestType.CHAT -> 0.002
                    AiRequestType.COMPLETION -> 0.001
                    AiRequestType.EMBEDDING -> 0.0001
                }

            val providerMultiplier =
                when (providerName) {
                    "openai" -> 1.0
                    "grok" -> 0.8
                    "proxy" -> 0.5
                    else -> 1.0
                }

            val baseCost = (tokens / 1000.0) * baseRate * providerMultiplier
            val platformFee = baseCost * 0.1 // 10% 平台费

            return CostBreakdown(
                baseCost = baseCost,
                platformFee = platformFee,
                totalCost = baseCost + platformFee,
                tokensUsed = tokens,
                ratePerThousandTokens = baseRate * providerMultiplier,
            )
        }

        private fun recordSuccessfulRequest(providerName: String) {
            val state =
                circuitBreakerStatus.getOrPut(providerName) {
                    CircuitBreakerState(providerName)
                }

            state.successCount++

            // 如果在半开状态且成功次数足够，关闭熔断器
            if (state.state == CircuitState.HALF_OPEN && state.successCount >= 3) {
                state.state = CircuitState.CLOSED
                state.failureCount = 0
                state.successCount = 0
            }
        }
    }

// 数据类定义
data class QuotaDecision(
    val approved: Boolean,
    val estimatedCost: Double,
    val estimatedTokens: Int,
    val remainingUserQuota: Int,
    val remainingSystemQuota: Int,
    val costBreakdown: CostBreakdown,
    val metadata: Map<String, Any> = emptyMap(),
)

data class CostBreakdown(
    val baseCost: Double,
    val platformFee: Double,
    val totalCost: Double,
    val tokensUsed: Int,
    val ratePerThousandTokens: Double,
)

data class UserQuotaState(
    val userId: String,
    val maxTokens: Int,
    var usedTokens: Int,
    var lastResetTime: Long,
    var lastRequestTime: Long,
    val requestTimes: MutableList<Long>,
)

data class SystemQuotaState(
    // 🧹 HARDCODE CLEANUP: 使用AiConfig统一管理Token限制
    val maxTokens: Int = AiConfig.Tokens.DAILY_TOKEN_LIMIT,
    var usedTokens: Int = 0,
)

data class CircuitBreakerState(
    val providerName: String,
    var state: CircuitState = CircuitState.CLOSED,
    var failureCount: Int = 0,
    var successCount: Int = 0,
    // 🧹 HARDCODE CLEANUP: 使用AiConfig统一管理熔断器配置
    val failureThreshold: Int = AiConfig.CircuitBreaker.FAILURE_THRESHOLD,
    val timeoutDuration: Long = AiConfig.CircuitBreaker.TIMEOUT_DURATION,
    var openTime: Long = 0,
    var lastFailureTime: Long = 0,
)

enum class CircuitState { CLOSED, OPEN, HALF_OPEN }

private data class CircuitCheck(
    val passed: Boolean,
    val reason: String,
    val state: CircuitState,
    val openTime: Long,
)

private data class SystemQuotaCheck(
    val passed: Boolean,
    val availableTokens: Int,
    val requestedTokens: Int,
)

private data class UserQuotaCheck(
    val passed: Boolean,
    val currentUsage: Int,
    val maxAllowed: Int,
    val remainingTokens: Int,
    val resetTime: String,
)

private data class RateCheck(
    val passed: Boolean,
    val currentRequests: Int,
    val maxRequests: Int,
    val windowStart: Long,
    val resetTime: Long,
)
