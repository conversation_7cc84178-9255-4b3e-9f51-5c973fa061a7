package com.example.gymbro.domain.workout.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ml.interfaces.VectorizedWorkoutState as CoreVectorizedWorkoutState
import com.example.gymbro.core.ml.interfaces.WorkoutStateVectorizer
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.json.VectorizedWorkoutState
import com.example.gymbro.domain.workout.model.timeline.WorkoutTimelineEntry
import com.example.gymbro.domain.workout.repository.SessionRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 训练状态向量化UseCase - Domain Layer
 *
 * 协调core-ml层的向量化器和data层的Timeline数据
 * 负责业务逻辑协调，不包含算法实现
 */
@Singleton
class VectorizeWorkoutStateUseCase
    @Inject
    constructor(
        private val workoutStateVectorizer: WorkoutStateVectorizer,
        private val sessionRepository: SessionRepository,
    ) {
        /**
         * 向量化当前训练会话状态
         *
         * @param sessionId 训练会话ID
         * @return 向量化的训练状态
         */
        suspend operator fun invoke(sessionId: String): ModernResult<VectorizedWorkoutState> {
            return try {
                if (sessionId.isBlank()) {
                    return ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "VectorizeWorkoutStateUseCase.invoke",
                            message = UiText.DynamicString("会话ID不能为空"),
                            inputType = "empty_session_id",
                            value = sessionId,
                        ),
                    )
                }

                // TODO: 需要适配新的SessionRepository接口
                // 暂时返回空结果
                val timelineResult = ModernResult.Success(null)
                val timelineEntry =
                    when (timelineResult) {
                        is ModernResult.Success -> timelineResult.data
                        is ModernResult.Error -> {
                            // Domain layer does not log - error handled by upper layers
                            return timelineResult.copy()
                        }

                        is ModernResult.Loading -> {
                            return ModernResult.Loading
                        }
                    }

                // 2. 构建训练状态描述
                val stateDescription =
                    if (timelineEntry != null) {
                        buildWorkoutStateDescription(timelineEntry)
                    } else {
                        "无训练会话进行中"
                    }

                // 3. 调用core-ml层向量化器
                val vectorizationResult = workoutStateVectorizer.vectorizeWorkoutState(stateDescription)
                when (vectorizationResult) {
                    is ModernResult.Success -> {
                        val coreVectorizedState = vectorizationResult.data

                        // 4. 转换为Domain层模型
                        val domainVectorizedState =
                            VectorizedWorkoutState(
                                sessionId = sessionId,
                                originalText = coreVectorizedState.originalText,
                                vector = coreVectorizedState.vector,
                                vectorDim = coreVectorizedState.vectorDim,
                                timestamp = coreVectorizedState.timestamp,
                                timelineEntryId = timelineEntry?.toString(), // TODO: 适配新的数据结构
                                exerciseId = null, // TODO: 适配新的数据结构
                                exerciseName = null, // TODO: 适配新的数据结构
                                progress = 0f, // TODO: 适配新的数据结构
                                metadata =
                                    coreVectorizedState.metadata +
                                        mapOf(
                                            "session_id" to sessionId,
                                            "has_timeline_data" to (timelineEntry != null),
                                        ),
                            )

                        ModernResult.Success(domainVectorizedState)
                    }

                    is ModernResult.Error -> {
                        // Domain layer does not log - error handled by upper layers
                        vectorizationResult.copy()
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Loading
                    }
                }
            } catch (e: Exception) {
                // Domain layer does not log - exception handled by upper layers
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "VectorizeWorkoutStateUseCase.invoke",
                        message = UiText.DynamicString("向量化训练状态失败"),
                        processType = "workout_state_vectorization",
                        reason = "usecase_exception",
                        cause = e,
                        metadataMap =
                            mapOf(
                                "session_id" to sessionId,
                            ),
                    ),
                )
            }
        }

        /**
         * 批量向量化训练状态历史
         *
         * @param userId 用户ID
         * @param limit 历史记录数量限制
         * @return 向量化的历史训练状态列表
         */
        suspend fun vectorizeHistoricalStates(
            userId: String,
            limit: Int = 10,
        ): ModernResult<List<VectorizedWorkoutState>> {
            return try {
                if (userId.isBlank()) {
                    return ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "VectorizeWorkoutStateUseCase.vectorizeHistoricalStates",
                            message = UiText.DynamicString("用户ID不能为空"),
                            inputType = "empty_user_id",
                            value = userId,
                        ),
                    )
                }

                // 这里可以扩展为从Timeline Repository获取历史数据
                // 目前先返回空列表，表示功能预留
                ModernResult.Success(emptyList())
            } catch (e: Exception) {
                // Domain layer does not log - exception handled by upper layers
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "VectorizeWorkoutStateUseCase.vectorizeHistoricalStates",
                        message = UiText.DynamicString("向量化历史状态失败"),
                        processType = "historical_state_vectorization",
                        reason = "historical_usecase_exception",
                        cause = e,
                        metadataMap =
                            mapOf(
                                "user_id" to userId,
                                "limit" to limit,
                            ),
                    ),
                )
            }
        }

        /**
         * 计算两个训练状态的相似度
         *
         * @param state1 第一个训练状态
         * @param state2 第二个训练状态
         * @return 相似度分数
         */
        fun calculateSimilarity(
            state1: VectorizedWorkoutState,
            state2: VectorizedWorkoutState,
        ): Float =
            try {
                // 转换为Core-ML层数据格式进行相似度计算
                val coreState1 =
                    CoreVectorizedWorkoutState(
                        originalText = state1.originalText,
                        vector = state1.vector,
                        vectorDim = state1.vectorDim,
                        timestamp = state1.timestamp,
                        metadata = state1.metadata,
                    )

                val coreState2 =
                    CoreVectorizedWorkoutState(
                        originalText = state2.originalText,
                        vector = state2.vector,
                        vectorDim = state2.vectorDim,
                        timestamp = state2.timestamp,
                        metadata = state2.metadata,
                    )

                val similarity = workoutStateVectorizer.calculateStateSimilarity(coreState1, coreState2)
                similarity
            } catch (e: Exception) {
                // Domain layer does not log - exception handled by upper layers
                0.0f
            }

        /**
         * 从Timeline条目构建训练状态描述
         */
        private fun buildWorkoutStateDescription(timelineEntry: WorkoutTimelineEntry): String =
            buildString {
                // 基础描述
                timelineEntry.exerciseName?.let { exerciseName ->
                    append("正在进行 $exerciseName")

                    // 组数信息
                    if (timelineEntry.completedSets > 0 && timelineEntry.totalSets > 0) {
                        append("，已完成 ${timelineEntry.completedSets}/${timelineEntry.totalSets} 组")
                    }

                    // 重量和次数信息
                    timelineEntry.currentWeight?.let { weight ->
                        append("，当前重量 ${weight}kg")
                    }
                    timelineEntry.currentReps?.let { reps ->
                        append("，$reps 次")
                    }

                    append("。")
                }

                // 整体进度信息
                if (timelineEntry.completedExercises > 0 && timelineEntry.totalExercises > 0) {
                    append("训练进度 ${timelineEntry.completedExercises}/${timelineEntry.totalExercises} 个动作")
                }

                // 时长信息
                if (timelineEntry.sessionDurationMin > 0) {
                    append("，已训练 ${timelineEntry.sessionDurationMin} 分钟")
                }

                // 休息信息
                timelineEntry.restTimeRemaining?.let { restTime ->
                    if (restTime > 0) {
                        append("，休息倒计时 $restTime 秒")
                    }
                }

                // 如果没有任何有效信息，提供默认描述
                if (isEmpty()) {
                    append("训练会话进行中，暂无详细状态信息")
                }
            }.trim()
    }
