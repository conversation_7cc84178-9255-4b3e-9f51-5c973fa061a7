package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.repository.PlanRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * Plan数据迁移UseCase
 *
 * ✅ Phase 5: 数据迁移和测试
 * - 将现有Plan的templateIds解析为templateVersionIds
 * - 保持向后兼容性
 * - 支持批量迁移
 */
@Singleton
class MigratePlanTemplateVersionsUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<Unit, MigratePlanTemplateVersionsUseCase.MigrationResult>(dispatcher, logger) {

        /**
         * 迁移结果
         */
        data class MigrationResult(
            val totalPlans: Int,
            val migratedPlans: Int,
            val skippedPlans: Int,
            val failedPlans: Int,
            val migrationDetails: List<PlanMigrationDetail>,
        ) {
            val migrationSuccessRate: Double = if (totalPlans > 0) migratedPlans.toDouble() / totalPlans else 0.0
            val hasFailures: Boolean = failedPlans > 0

            fun getSummaryMessage(): UiText {
                return UiText.DynamicString(
                    "迁移完成：总计 $totalPlans 个计划，成功 $migratedPlans 个，跳过 $skippedPlans 个，失败 $failedPlans 个",
                )
            }
        }

        /**
         * 单个计划迁移详情
         */
        data class PlanMigrationDetail(
            val planId: String,
            val planName: String,
            val status: MigrationStatus,
            val templateVersionsResolved: Int = 0,
            val errorMessage: String? = null,
        )

        /**
         * 迁移状态
         */
        enum class MigrationStatus {
            SUCCESS, // 迁移成功
            SKIPPED, // 跳过（已经适配或无需迁移）
            FAILED, // 迁移失败
        }

        /**
         * 执行Plan数据迁移
         */
        override suspend fun execute(parameters: Unit): ModernResult<MigrationResult> {
            logger.d("开始Plan数据迁移：templateIds -> templateVersionIds")

            // 1. 获取所有Plan
            val allPlans = planRepository.getAllPlans()
            logger.d("发现 ${allPlans.size} 个计划需要检查")

            if (allPlans.isEmpty()) {
                return ModernResult.Success(
                    MigrationResult(
                        totalPlans = 0,
                        migratedPlans = 0,
                        skippedPlans = 0,
                        failedPlans = 0,
                        migrationDetails = emptyList(),
                    ),
                )
            }

            // 2. 筛选需要迁移的Plan
            val plansNeedingMigration = allPlans.filter { plan ->
                plan.dailySchedule.values.any { dayPlan ->
                    !dayPlan.isAdaptedToTemplateVersion() && dayPlan.templateIds.isNotEmpty()
                }
            }

            logger.d("发现 ${plansNeedingMigration.size} 个计划需要迁移")

            // 3. 执行迁移
            val migrationDetails = mutableListOf<PlanMigrationDetail>()
            var migratedCount = 0
            var skippedCount = 0
            var failedCount = 0

            for (plan in allPlans) {
                try {
                    if (plansNeedingMigration.contains(plan)) {
                        // 需要迁移
                        logger.d("迁移计划: ${plan.id} - ${plan.name}")

                        val migrationResult = planRepository.resolvePlanTemplateVersions(plan)
                        when (migrationResult) {
                            is ModernResult.Success -> {
                                val migratedPlan = migrationResult.data

                                // 统计解析的TemplateVersion数量
                                val versionCount = migratedPlan.dailySchedule.values
                                    .sumOf { it.templateVersionIds.size }

                                migrationDetails.add(
                                    PlanMigrationDetail(
                                        planId = plan.id,
                                        planName = plan.name,
                                        status = MigrationStatus.SUCCESS,
                                        templateVersionsResolved = versionCount,
                                    ),
                                )
                                migratedCount++
                                logger.d("计划迁移成功: ${plan.id}, 解析了 $versionCount 个TemplateVersion")
                            }

                            is ModernResult.Error -> {
                                migrationDetails.add(
                                    PlanMigrationDetail(
                                        planId = plan.id,
                                        planName = plan.name,
                                        status = MigrationStatus.FAILED,
                                        errorMessage = migrationResult.error.message,
                                    ),
                                )
                                failedCount++
                                logger.e("计划迁移失败: ${plan.id}, 错误: ${migrationResult.error}")
                            }

                            is ModernResult.Loading -> {
                                migrationDetails.add(
                                    PlanMigrationDetail(
                                        planId = plan.id,
                                        planName = plan.name,
                                        status = MigrationStatus.FAILED,
                                        errorMessage = "迁移超时",
                                    ),
                                )
                                failedCount++
                                logger.e("计划迁移超时: ${plan.id}")
                            }
                        }
                    } else {
                        // 跳过（已适配或无需迁移）
                        migrationDetails.add(
                            PlanMigrationDetail(
                                planId = plan.id,
                                planName = plan.name,
                                status = MigrationStatus.SKIPPED,
                            ),
                        )
                        skippedCount++
                        logger.d("计划已适配，跳过: ${plan.id}")
                    }
                } catch (e: Exception) {
                    migrationDetails.add(
                        PlanMigrationDetail(
                            planId = plan.id,
                            planName = plan.name,
                            status = MigrationStatus.FAILED,
                            errorMessage = "异常: ${e.message}",
                        ),
                    )
                    failedCount++
                    logger.e("计划迁移异常: ${plan.id}", e)
                }
            }

            // 4. 构建迁移结果
            val migrationResult = MigrationResult(
                totalPlans = allPlans.size,
                migratedPlans = migratedCount,
                skippedPlans = skippedCount,
                failedPlans = failedCount,
                migrationDetails = migrationDetails,
            )

            logger.d("Plan数据迁移完成: ${migrationResult.getSummaryMessage()}")
            return ModernResult.Success(migrationResult)
        }
    }
