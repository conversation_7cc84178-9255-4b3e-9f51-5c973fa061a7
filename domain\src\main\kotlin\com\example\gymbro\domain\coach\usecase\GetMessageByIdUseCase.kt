package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.coach.repository.ChatRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

/**
 * 根据消息ID获取单个消息的用例
 *
 * 基于 history-todo-plan.md 的单个消息获取逻辑要求设计，实现：
 * - 通过消息ID准确获取CoachMessage
 * - 支持错误处理和重试机制
 * - 性能优化，避免N+1查询问题
 * - 完整的日志记录和调试支持
 *
 * 遵循Clean Architecture + MVI 2.0架构标准：
 * - UseCase位于domain层，不依赖外部框架
 * - 使用Repository接口，不直接依赖数据层实现
 * - 统一的ModernResult错误处理
 * - 结构化并发和协程调度器
 */
@Singleton
class GetMessageByIdUseCase
    @Inject
    constructor(
        private val chatRepository: ChatRepository,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * 参数数据类
         *
         * @param messageId 消息ID（字符串格式）
         */
        data class Params(
            val messageId: String,
        )

        /**
         * 执行获取单个消息的操作
         *
         * @param params 包含消息ID的参数
         * @return ModernResult包装的CoachMessage，如果消息不存在则返回null
         */
        suspend operator fun invoke(params: Params): ModernResult<CoachMessage?> =
            withContext(ioDispatcher) {
                try {
                    logger.d("GetMessageByIdUseCase", "开始获取消息: messageId=${params.messageId}")

                    // 验证参数
                    if (params.messageId.isBlank()) {
                        logger.w("GetMessageByIdUseCase", "消息ID为空")
                        return@withContext ModernResult.Success(null)
                    }

                    // 调用Repository获取消息
                    val result = chatRepository.getMessageById(params.messageId)

                    when (result) {
                        is ModernResult.Success -> {
                            val message = result.data
                            if (message != null) {
                                logger.d(
                                    "GetMessageByIdUseCase",
                                    "成功获取消息: messageId=${params.messageId}, type=${message::class.simpleName}",
                                )
                            } else {
                                logger.d("GetMessageByIdUseCase", "消息不存在: messageId=${params.messageId}")
                            }
                            result
                        }

                        is ModernResult.Error -> {
                            logger.e(
                                "GetMessageByIdUseCase",
                                "获取消息失败: messageId=${params.messageId}, error=${result.error}",
                            )
                            result
                        }

                        is ModernResult.Loading -> {
                            logger.d("GetMessageByIdUseCase", "获取消息中: messageId=${params.messageId}")
                            result
                        }
                    }
                } catch (e: Exception) {
                    logger.e("GetMessageByIdUseCase", "获取消息异常: messageId=${params.messageId}", e)
                    ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "getMessageById",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.Internal,
                            uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("获取消息失败"),
                            cause = e,
                        ),
                    )
                }
            }

        /**
         * 批量获取消息的便捷方法
         *
         * @param messageIds 消息ID列表
         * @return ModernResult包装的CoachMessage列表
         */
        suspend fun getBatch(messageIds: List<String>): ModernResult<List<CoachMessage>> =
            withContext(ioDispatcher) {
                try {
                    logger.d("GetMessageByIdUseCase", "开始批量获取消息: count=${messageIds.size}")

                    if (messageIds.isEmpty()) {
                        return@withContext ModernResult.Success(emptyList())
                    }

                    val messages = mutableListOf<CoachMessage>()
                    var errorCount = 0

                    // 逐个获取消息（可以考虑优化为批量查询）
                    for (messageId in messageIds) {
                        val result = invoke(Params(messageId))
                        when (result) {
                            is ModernResult.Success -> {
                                result.data?.let { messages.add(it) }
                            }

                            is ModernResult.Error -> {
                                errorCount++
                                logger.w("GetMessageByIdUseCase", "批量获取中单个消息失败: messageId=$messageId")
                            }

                            is ModernResult.Loading -> {
                                // 批量操作中不应该有Loading状态
                                logger.w("GetMessageByIdUseCase", "批量获取中遇到Loading状态: messageId=$messageId")
                            }
                        }
                    }

                    logger.d("GetMessageByIdUseCase", "批量获取完成: 成功=${messages.size}, 失败=$errorCount")

                    if (errorCount > 0 && messages.isEmpty()) {
                        // 全部失败
                        ModernResult.Error(
                            com.example.gymbro.core.error.types.ModernDataError(
                                operationName = "getBatchMessages",
                                errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.Internal,
                                uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("批量获取消息全部失败"),
                                cause = null,
                            ),
                        )
                    } else {
                        // 部分成功或全部成功
                        ModernResult.Success(messages)
                    }
                } catch (e: Exception) {
                    logger.e("GetMessageByIdUseCase", "批量获取消息异常: count=${messageIds.size}", e)
                    ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "getBatchMessages",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.Internal,
                            uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("批量获取消息失败"),
                            cause = e,
                        ),
                    )
                }
            }
    }
