package com.example.gymbro.domain.ai.trigger

import com.example.gymbro.core.ai.prompt.trigger.*
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.exercise.model.getDisplayName
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

/**
 * 智能触发引擎实现
 *
 * 实现自动/手动触发策略，提升AI Function Call的触发准确率
 * 基于用户输入智能判断是否需要触发Function Call
 *
 * @property exerciseRepository 动作库Repository（domain层接口）
 * @property ioDispatcher IO调度器
 * @property logger 日志记录器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
@Singleton
class SmartTriggerEngineImpl
    @Inject
    constructor(
        private val exerciseRepository: ExerciseRepository,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) : SmartTriggerEngine {

        companion object {
            // 动作搜索触发关键词
            private val EXERCISE_SEARCH_KEYWORDS = setOf(
                "动作", "练", "训练", "锻炼", "运动", "健身",
                "胸", "背", "腿", "肩", "手臂", "核心", "腹",
                "哑铃", "杠铃", "器械", "无器械", "自重",
                "初学者", "新手", "高级", "进阶",
                "推荐", "适合", "什么", "怎么", "如何",
            )

            // 训练会话触发关键词
            private val SESSION_KEYWORDS = setOf(
                "开始", "开始训练", "开始锻炼",
                "记录", "记录组数", "记录重量",
                "完成", "结束", "完成训练",
                "休息", "计时", "定时器",
            )

            // 模板相关关键词
            private val TEMPLATE_KEYWORDS = setOf(
                "模板", "计划", "方案", "课程",
                "生成", "创建", "制定", "设计",
                "周计划", "月计划", "训练计划",
            )

            // 动作ID模式
            private val EXERCISE_ID_PATTERN = """(off_|u_)[a-zA-Z0-9_-]+""".toRegex()

            // 否定词汇（降低触发概率）
            private val NEGATIVE_KEYWORDS = setOf(
                "不",
                "没有",
                "不要",
                "别",
                "停止",
                "取消",
            )
        }

        /**
         * 分析用户输入，判断是否需要触发Function Call
         */
        override suspend fun analyzeTriggerNeed(
            userInput: String,
            context: ConversationContext?,
        ): TriggerAnalysisResult = withContext(ioDispatcher) {
            logger.d("🧠 分析触发需求: '$userInput'")

            try {
                val cleanInput = userInput.trim().lowercase()

                // 1. 检测否定词汇
                val hasNegativeWords = NEGATIVE_KEYWORDS.any { cleanInput.contains(it) }
                if (hasNegativeWords) {
                    return@withContext TriggerAnalysisResult.NoTrigger("检测到否定词汇")
                }

                // 2. 检测动作ID
                val exerciseIdMatch = EXERCISE_ID_PATTERN.find(userInput)
                if (exerciseIdMatch != null) {
                    return@withContext TriggerAnalysisResult.DirectQuery(
                        functionName = "gymbro.exercise.get_detail",
                        arguments = mapOf("exercise_id" to exerciseIdMatch.value),
                        confidence = 0.95,
                        reason = "检测到动作ID: ${exerciseIdMatch.value}",
                    )
                }

                // 3. 检测动作搜索需求
                val exerciseSearchScore = calculateKeywordScore(cleanInput, EXERCISE_SEARCH_KEYWORDS)
                if (exerciseSearchScore > 0.3) {
                    val extractedQuery = extractSearchQuery(userInput)
                    return@withContext TriggerAnalysisResult.FunctionCall(
                        functionName = "gymbro.exercise.search",
                        arguments = mapOf("query" to extractedQuery),
                        confidence = exerciseSearchScore,
                        reason = "检测到动作搜索需求",
                    )
                }

                // 4. 检测训练会话需求
                val sessionScore = calculateKeywordScore(cleanInput, SESSION_KEYWORDS)
                if (sessionScore > 0.4) {
                    val sessionFunction = detectSessionFunction(cleanInput)
                    return@withContext TriggerAnalysisResult.FunctionCall(
                        functionName = sessionFunction.first,
                        arguments = sessionFunction.second,
                        confidence = sessionScore,
                        reason = "检测到训练会话需求",
                    )
                }

                // 5. 检测模板需求
                val templateScore = calculateKeywordScore(cleanInput, TEMPLATE_KEYWORDS)
                if (templateScore > 0.4) {
                    return@withContext TriggerAnalysisResult.FunctionCall(
                        functionName = "gymbro.template.search",
                        arguments = mapOf("query" to userInput),
                        confidence = templateScore,
                        reason = "检测到模板需求",
                    )
                }

                // 6. 检查是否需要Fallback RAG
                if (shouldTriggerFallbackRAG(cleanInput, context)) {
                    return@withContext TriggerAnalysisResult.FallbackRAG(
                        query = extractRAGQuery(userInput),
                        confidence = 0.6,
                        reason = "触发兜底RAG机制",
                    )
                }

                // 7. 无触发需求
                TriggerAnalysisResult.NoTrigger("未检测到明确的Function Call需求")
            } catch (e: Exception) {
                logger.e("触发分析异常", e)
                TriggerAnalysisResult.NoTrigger("分析过程发生异常: ${e.message}")
            }
        }

        /**
         * 执行Fallback RAG
         * 为缺乏动作数据的AI回答补充相关信息
         */
        override suspend fun executeFallbackRAG(
            query: String,
            maxResults: Int,
        ): ModernResult<List<AiExerciseInfo>> = safeCatch {
            withContext(ioDispatcher) {
                logger.d("🔄 执行Fallback RAG: '$query'")

                // 使用domain层ExerciseRepository进行搜索
                val searchResult = exerciseRepository.searchExercises(
                    query = query,
                    limit = maxResults,
                )

                when (searchResult) {
                    is ModernResult.Success -> {
                        logger.d("Fallback RAG成功: 找到${searchResult.data.size}条相关动作")
                        // 转换Exercise为AiExerciseInfo
                        val aiExerciseInfos = searchResult.data.map { exercise ->
                            AiExerciseInfo(
                                id = exercise.id,
                                name = exercise.name.toString(),
                                muscleGroup = exercise.muscleGroup.getDisplayName(),
                                equipment = exercise.equipment.firstOrNull()?.getDisplayName() ?: "无器械",
                                difficulty = exercise.difficultyLevel.toString(),
                            )
                        }
                        aiExerciseInfos
                    }

                    is ModernResult.Error -> {
                        logger.w("Fallback RAG失败: ${searchResult.error}")
                        emptyList() // 失败时返回空列表，不影响主流程
                    }

                    is ModernResult.Loading -> {
                        logger.w("Fallback RAG超时")
                        emptyList()
                    }
                }
            }
        }

        /**
         * 获取触发统计信息
         */
        override fun getTriggerStats(): TriggerStats {
            // 简化实现，实际应该维护统计数据
            return TriggerStats(
                totalAnalyses = 0,
                functionCallTriggers = 0,
                fallbackRAGTriggers = 0,
                noTriggers = 0,
                averageConfidence = 0.0,
            )
        }

        /**
         * 计算关键词匹配分数
         */
        private fun calculateKeywordScore(input: String, keywords: Set<String>): Double {
            val matchedKeywords = keywords.count { keyword ->
                input.contains(keyword)
            }

            return if (keywords.isNotEmpty()) {
                matchedKeywords.toDouble() / keywords.size
            } else {
                0.0
            }
        }

        /**
         * 提取搜索查询
         */
        private fun extractSearchQuery(input: String): String {
            // 简化实现：直接返回原输入
            // 实际可以做更复杂的NLP处理
            return input.take(50) // 限制长度
        }

        /**
         * 检测训练会话函数
         */
        private fun detectSessionFunction(input: String): Pair<String, Map<String, String>> {
            return when {
                input.contains("开始") -> {
                    "gymbro.session.start" to emptyMap()
                }

                input.contains("记录") -> {
                    "gymbro.session.log_set" to mapOf(
                        "exercise_id" to "placeholder",
                        "reps" to "1",
                    )
                }

                input.contains("完成") || input.contains("结束") -> {
                    "gymbro.session.complete" to emptyMap()
                }

                else -> {
                    "gymbro.session.start" to emptyMap()
                }
            }
        }

        /**
         * 判断是否需要触发Fallback RAG
         */
        private fun shouldTriggerFallbackRAG(
            input: String,
            context: ConversationContext?,
        ): Boolean {
            // 如果用户问题很短且没有明确意图，可能需要RAG补充
            if (input.length < 10) return false

            // 如果包含疑问词，可能需要更多信息
            val questionWords = setOf("什么", "怎么", "如何", "为什么", "哪个", "哪些")
            val hasQuestionWord = questionWords.any { input.contains(it) }

            // 如果上下文中缺乏动作相关信息，可能需要RAG
            val contextLacksExerciseInfo = context?.lastAIResponse?.let { response ->
                !response.contains("动作") && !response.contains("训练") && !response.contains("锻炼")
            } ?: true

            return hasQuestionWord && contextLacksExerciseInfo
        }

        /**
         * 提取RAG查询
         */
        private fun extractRAGQuery(input: String): String {
            // 提取关键词作为RAG查询
            val keywords = EXERCISE_SEARCH_KEYWORDS.filter { input.contains(it) }
            return if (keywords.isNotEmpty()) {
                keywords.joinToString(" ")
            } else {
                input.take(20) // 使用前20个字符
            }
        }
    }
