package com.example.gymbro.domain.profile.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.*

/**
 * 获取收藏项UseCase - 完整实现版本
 * 基于Clean Architecture原则，遵循GymBro项目UseCase标准
 *
 * 功能特性：
 * - 获取用户收藏的训练模板和训练计划
 * - 支持实时数据更新（Flow）
 * - 集成workout模块现有Repository
 * - 遵循ModernResult错误处理模式
 * - 支持并发数据获取，提升性能
 */
@OptIn(kotlinx.coroutines.ExperimentalCoroutinesApi::class)
@Singleton
class GetFavoriteItemsUseCase
    @Inject
    constructor(
        private val templateRepository: TemplateRepository,
        private val planRepository: PlanRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernFlowUseCase<Unit, GetFavoriteItemsUseCase.Result>(dispatcher, logger) {

        /**
         * 收藏项结果数据类
         * 包含收藏的模板和计划列表
         */
        data class Result(
            val favoriteTemplates: List<WorkoutTemplate> = emptyList(),
            val favoritePlans: List<WorkoutPlan> = emptyList(),
        ) {
            /**
             * 计算总收藏数量
             */
            val totalCount: Int
                get() = favoriteTemplates.size + favoritePlans.size

            /**
             * 检查是否为空
             */
            val isEmpty: Boolean
                get() = favoriteTemplates.isEmpty() && favoritePlans.isEmpty()
        }

        /**
         * 执行用例，获取收藏数据
         * 使用combine操作符并发获取模板和计划数据，提升性能
         */
        override fun createFlow(params: Unit): Flow<ModernResult<Result>> {
            return getCurrentUserIdUseCase().flatMapLatest { userIdResult ->
                when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (userId != null) {
                            combine(
                                templateRepository.getFavoriteTemplates(userId),
                                // TODO: 暂时使用空列表，等待PlanRepository添加收藏功能
                                kotlinx.coroutines.flow.flowOf(ModernResult.Success(emptyList<WorkoutPlan>())),
                            ) { templatesResult, plansResult ->
                                when {
                                    // 两个都成功时，合并结果
                                    templatesResult is ModernResult.Success && plansResult is ModernResult.Success -> {
                                        ModernResult.Success(
                                            Result(
                                                favoriteTemplates = templatesResult.data,
                                                favoritePlans = plansResult.data,
                                            ),
                                        )
                                    }

                                    // 任一失败时，返回错误（优先返回模板错误）
                                    templatesResult is ModernResult.Error -> templatesResult
                                    plansResult is ModernResult.Error -> plansResult

                                    // 任一加载中时，返回加载状态
                                    templatesResult is ModernResult.Loading || plansResult is ModernResult.Loading -> {
                                        ModernResult.Loading
                                    }

                                    // 其他情况，返回空结果
                                    else -> ModernResult.Success(Result())
                                }
                            }
                        } else {
                            kotlinx.coroutines.flow.flowOf(ModernResult.Success(Result()))
                        }
                    }

                    is ModernResult.Error -> kotlinx.coroutines.flow.flowOf(userIdResult)
                    is ModernResult.Loading -> kotlinx.coroutines.flow.flowOf(ModernResult.Loading)
                }
            }.flowOn(dispatcher)
        }

        /**
         * 便捷方法：仅获取收藏的训练模板
         */
        fun getFavoriteTemplatesOnly(): Flow<ModernResult<List<WorkoutTemplate>>> {
            return getCurrentUserIdUseCase().flatMapLatest { userIdResult ->
                when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (userId != null) {
                            templateRepository.getFavoriteTemplates(userId)
                        } else {
                            kotlinx.coroutines.flow.flowOf(ModernResult.Success(emptyList()))
                        }
                    }

                    is ModernResult.Error -> kotlinx.coroutines.flow.flowOf(userIdResult)
                    is ModernResult.Loading -> kotlinx.coroutines.flow.flowOf(ModernResult.Loading)
                }
            }.flowOn(dispatcher)
        }

        /**
         * 便捷方法：仅获取收藏的训练计划
         * TODO: 等待WorkoutPlanRepository添加收藏功能
         */
        fun getFavoritePlansOnly(): Flow<ModernResult<List<WorkoutPlan>>> {
            return kotlinx.coroutines.flow.flowOf(ModernResult.Success(emptyList<WorkoutPlan>()))
                .flowOn(dispatcher)
        }

        /**
         * 便捷方法：获取收藏数量统计
         */
        fun getFavoriteCount(): Flow<ModernResult<Int>> {
            return createFlow(Unit).map { result ->
                when (result) {
                    is ModernResult.Success -> ModernResult.Success(result.data.totalCount)
                    is ModernResult.Error -> result
                    is ModernResult.Loading -> ModernResult.Loading
                }
            }.flowOn(dispatcher)
        }

        /**
         * 便捷方法：检查特定模板是否被收藏
         */
        suspend fun isTemplateFavorite(templateId: String): ModernResult<Boolean> {
            return try {
                val templateResult = templateRepository.getTemplateById(templateId)
                when (templateResult) {
                    is ModernResult.Success -> {
                        val template = templateResult.data
                        // TODO: 检查模板的收藏状态，暂时返回false
                        ModernResult.Success(false)
                    }

                    is ModernResult.Error -> ModernResult.Error(templateResult.error)
                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                logger.e("检查模板收藏状态失败: $templateId", e)
                ModernResult.Error(
                    DataErrors.DataError.query(
                        operationName = "isTemplateFavorite",
                        message = UiText.DynamicString("检查模板收藏状态失败"),
                        entityType = "WorkoutTemplate",
                        entityId = templateId,
                    ),
                )
            }
        }

        /**
         * 便捷方法：检查特定计划是否被收藏
         */
        suspend fun isPlanFavorite(planId: String): ModernResult<Boolean> {
            return try {
                // TODO: 等待WorkoutPlanRepository添加收藏功能
                // 暂时返回false
                ModernResult.Success(false)
            } catch (e: Exception) {
                logger.e("检查计划收藏状态失败: $planId", e)
                ModernResult.Error(
                    DataErrors.DataError.query(
                        operationName = "isPlanFavorite",
                        message = UiText.DynamicString("检查计划收藏状态失败"),
                        entityType = "WorkoutPlan",
                        entityId = planId,
                    ),
                )
            }
        }
    }
