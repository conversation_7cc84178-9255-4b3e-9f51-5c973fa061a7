# =========================================================================================
# GymBro Detekt Configuration - v5.0 [SIMPLIFIED]
#
# 简化的 detekt 配置，专注于自定义规则和核心质量检查
# =========================================================================================

build:
  maxIssues: 50 # 允许一些问题，避免过于严格
  weights:
    complexity: 2
    performance: 2
    style: 1
    potential-bugs: 3

config:
  validation: true
  warningsAsErrors: false # 暂时设为 false，避免构建失败

# =========================================================================================
# 🔥 GymBro 自定义规则集 - 强制执行项目特定架构约束
# =========================================================================================
gymbro-rules:
  active: true
  # MVI Architecture Rules
  MviStateImmutability:
    active: true
  ImmutableStateClass:
    active: true
  MviIntentNaming:
    active: true
  # Design System Rules
  NoHardcodedColor:
    active: true
  NoHardcodedDimension:
    active: true
  NoHardcodedDesignValues:
    active: true
  UseWorkoutColors:
    active: true
  # Logging Rules
  MaxTimberLogsPerFile:
    active: true
    threshold: 5
  MaxTimberLogsPerFunction:
    active: true
    threshold: 1
  LoggingModuleRestriction:
    active: true
  # Documentation Rules
  KDocAutoFix:
    active: true
  KDocFormatter:
    active: true
  KDocCompleteness:
    active: true
    requireDocumentationForPublic: true
    requireParameterDocumentation: true
    requireReturnDocumentation: true
  # Quality Rules
  NoTodoOrFixme:
    active: true

# =========================================================================================
# 基本规则集 - 保持简单有效
# =========================================================================================
complexity:
  active: true
  LongMethod:
    active: true
    threshold: 80

style:
  active: true
  MaxLineLength:
    active: true
    maxLineLength: 120
  NewLineAtEndOfFile:
    active: true
