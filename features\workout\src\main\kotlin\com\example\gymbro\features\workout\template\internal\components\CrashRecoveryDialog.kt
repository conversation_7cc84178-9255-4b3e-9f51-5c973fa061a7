package com.example.gymbro.features.workout.template.internal.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.TemplateDraft
// 移除R资源导入，使用UiText.DynamicString
import com.example.gymbro.features.workout.template.cache.RecoveryResult
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import java.text.SimpleDateFormat
import java.util.*

/**
 * 崩溃恢复对话框 - P4阶段新增
 *
 * 🎯 功能:
 * - 检测未保存草稿并提供恢复选项
 * - 显示恢复项目的详细信息
 * - 支持批量恢复和选择性恢复
 * - 用户友好的恢复体验
 *
 * 🏗️ 架构原则:
 * - 使用 designSystem 主题令牌
 * - Clean Architecture + MVI 2.0模式
 * - 无障碍访问支持
 */
@Composable
fun CrashRecoveryDialog(
    recoveryItems: List<RecoveryItem>,
    onRecoverItem: (RecoveryItem) -> Unit,
    onRecoverAll: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        ),
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth(0.9f)
                .fillMaxHeight(0.8f),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.workoutColors.cardBackground,
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = Tokens.Elevation.Large,
            ),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(Tokens.Spacing.Large),
            ) {
                // 标题
                Text(
                    text = "崩溃恢复",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.workoutColors.accentSecondary,
                    fontWeight = FontWeight.Bold,
                )

                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

                // 描述
                Text(
                    text = "检测到未保存的模板数据，是否恢复？",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Tokens.Color.Gray600,
                )

                Spacer(modifier = Modifier.height(Tokens.Spacing.Large))

                // 恢复项目列表
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
                ) {
                    items(recoveryItems) { item ->
                        RecoveryItemCard(
                            item = item,
                            onRecover = { onRecoverItem(item) },
                        )
                    }
                }

                Spacer(modifier = Modifier.height(Tokens.Spacing.Large))

                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = MaterialTheme.workoutColors.accentSecondary,
                        ),
                        border = BorderStroke(
                            width = Tokens.Input.BorderWidth,
                            color = MaterialTheme.workoutColors.cardBorder,
                        ),
                    ) {
                        Text("跳过")
                    }

                    // 全部恢复按钮
                    Button(
                        onClick = onRecoverAll,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.workoutColors.accentPrimary,
                            contentColor = Tokens.Color.Gray950,
                        ),
                    ) {
                        Text("全部恢复")
                    }
                }
            }
        }
    }
}

/**
 * 恢复项目卡片
 */
@Composable
private fun RecoveryItemCard(
    item: RecoveryItem,
    onRecover: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.workoutColors.cardBackground,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = Tokens.Elevation.Small,
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                ) {
                    // 名称
                    Text(
                        text = item.name,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.workoutColors.accentSecondary,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )

                    Spacer(modifier = Modifier.height(Tokens.Spacing.Tiny))

                    // 类型和时间
                    Text(
                        text = "${item.type} • ${formatTime(item.lastModified)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = Tokens.Color.Gray600,
                    )

                    // 描述（如果有）
                    if (item.description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(Tokens.Spacing.Tiny))
                        Text(
                            text = item.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = Tokens.Color.Gray600,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                        )
                    }
                }

                Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                // 恢复按钮
                Button(
                    onClick = onRecover,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.workoutColors.accentPrimary,
                        contentColor = Tokens.Color.Gray950,
                    ),
                    contentPadding = PaddingValues(
                        horizontal = Tokens.Spacing.Medium,
                        vertical = Tokens.Spacing.Small,
                    ),
                ) {
                    Text(
                        text = "恢复",
                        style = MaterialTheme.typography.labelMedium,
                    )
                }
            }

            // 崩溃恢复标识
            if (item.isCrashRecovery) {
                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = "崩溃恢复",
                        tint = Tokens.Color.Warning,
                        modifier = Modifier.size(Tokens.Icon.Small),
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Tiny))
                    Text(
                        text = "恢复后原数据将被覆盖",
                        style = MaterialTheme.typography.labelSmall,
                        color = Tokens.Color.Warning,
                    )
                }
            }
        }
    }
}

/**
 * 恢复项目数据类
 */
data class RecoveryItem(
    val id: String,
    val name: String,
    val description: String,
    val type: String, // "模板" 或 "草稿"
    val lastModified: Long,
    val isCrashRecovery: Boolean,
    val template: WorkoutTemplateDto? = null,
    val draft: TemplateDraft? = null,
)

/**
 * 从 RecoveryResult 创建 RecoveryItem
 */
fun RecoveryResult.toRecoveryItem(): RecoveryItem? {
    return when (this) {
        is RecoveryResult.Found -> RecoveryItem(
            id = template.id,
            name = template.name,
            description = template.description,
            type = "模板",
            lastModified = template.updatedAt,
            isCrashRecovery = isCrashRecovery,
            template = template,
        )

        is RecoveryResult.FoundDraft -> RecoveryItem(
            id = draft.id,
            name = draft.name,
            description = draft.description ?: "",
            type = "草稿",
            lastModified = draft.updatedAt.toEpochMilliseconds(),
            isCrashRecovery = isCrashRecovery,
            draft = draft,
        )

        is RecoveryResult.NotFound,
        is RecoveryResult.Error,
        -> null
    }
}

/**
 * 格式化时间显示
 */
private fun formatTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp

    return when {
        diff < 60 * 1000 -> "刚刚"
        diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
        diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
        else -> {
            val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
            formatter.format(Date(timestamp))
        }
    }
}
