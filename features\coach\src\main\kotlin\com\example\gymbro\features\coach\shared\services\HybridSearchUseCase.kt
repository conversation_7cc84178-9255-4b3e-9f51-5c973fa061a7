package com.example.gymbro.features.coach.shared.services

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.coach.dao.ChatSearchDao
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.data.local.model.ChatSearchResult
import com.example.gymbro.domain.coach.model.CoachMessage
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import timber.log.Timber

/**
 * 混合搜索UseCase - FTS5全文搜索 + BGE向量搜索的融合实现
 *
 * 基于架构清理要求，统一向量搜索实现：
 * - 使用VectorSearchService进行真正的向量相似度计算
 * - 并行执行FTS5关键词搜索和BGE向量搜索
 * - 智能融合算法，结合关键词匹配和语义相似度
 * - 性能优化，使用协程并发和线程池调度
 * - 降级策略，向量搜索失败时自动降级到关键词搜索
 * - 会话内搜索支持，提供精确的上下文搜索
 *
 * 搜索融合策略：
 * - 关键词权重：0.4 (精确匹配优先)
 * - 语义权重：0.6 (语义理解增强)
 * - 分数归一化：统一不同搜索引擎的分数范围
 * - 结果去重：基于消息ID进行智能去重
 *
 * @since Coach模块MVI架构重构 + RAG架构清理
 */
@Singleton
class HybridSearchUseCase
    @Inject
    constructor(
        private val chatSearchDao: ChatSearchDao,
        private val vectorSearchService: VectorSearchService,
        private val ioDispatcher: CoroutineDispatcher,
        private val defaultDispatcher: CoroutineDispatcher,
    ) {
        companion object {
            // 搜索参数配置
            private const val DEFAULT_LIMIT = 20
            private const val FTS_LIMIT = 30 // FTS5搜索更多结果用于融合
            private const val VECTOR_LIMIT = 30 // 向量搜索更多结果用于融合

            // 融合权重配置
            private const val KEYWORD_WEIGHT = 0.4f // 关键词搜索权重
            private const val SEMANTIC_WEIGHT = 0.6f // 语义搜索权重
        }

        /**
         * 全局混合搜索
         *
         * @param query 搜索查询
         * @param limit 结果数量限制
         * @return 搜索结果Flow
         */
        suspend fun search(
            query: String,
            limit: Int = DEFAULT_LIMIT,
        ): Flow<ModernResult<List<CoachMessage>>> =
            flow {
                emit(ModernResult.Loading)

                try {
                    if (query.isBlank()) {
                        emit(ModernResult.Success(emptyList()))
                        return@flow
                    }

                    Timber.d("🔍 开始混合搜索: query='$query', limit=$limit")
                    val startTime = System.currentTimeMillis()

                    val results = performHybridSearch(query, limit)

                    val duration = System.currentTimeMillis() - startTime
                    Timber.d("✅ 混合搜索完成: 找到${results.size}条结果, 耗时${duration}ms")

                    emit(ModernResult.Success(results))
                } catch (e: Exception) {
                    Timber.e(e, "❌ 混合搜索失败: query='$query'")
                    emit(
                        ModernResult.error(
                            e.toModernDataError(
                                operationName = "hybridSearch",
                                uiMessage = UiText.DynamicString("搜索失败"),
                            ),
                        ),
                    )
                }
            }.flowOn(defaultDispatcher) // 🔥 切换到Default线程执行计算密集型操作

        /**
         * 会话内混合搜索
         *
         * @param sessionId 会话ID
         * @param query 搜索查询
         * @param limit 结果数量限制
         * @return 搜索结果Flow
         */
        suspend fun searchInSession(
            sessionId: String,
            query: String,
            limit: Int = DEFAULT_LIMIT,
        ): Flow<ModernResult<List<CoachMessage>>> =
            flow {
                emit(ModernResult.Loading)

                try {
                    if (query.isBlank()) {
                        emit(ModernResult.Success(emptyList()))
                        return@flow
                    }

                    Timber.d("🔍 开始会话内混合搜索: sessionId='$sessionId', query='$query'")
                    val startTime = System.currentTimeMillis()

                    val results = performHybridSearchInSession(sessionId, query, limit)

                    val duration = System.currentTimeMillis() - startTime
                    Timber.d("✅ 会话内混合搜索完成: 找到${results.size}条结果, 耗时${duration}ms")

                    emit(ModernResult.Success(results))
                } catch (e: Exception) {
                    Timber.e(e, "❌ 会话内混合搜索失败: sessionId='$sessionId', query='$query'")
                    emit(
                        ModernResult.error(
                            e.toModernDataError(
                                operationName = "hybridSearchInSession",
                                uiMessage = UiText.DynamicString("会话内搜索失败"),
                            ),
                        ),
                    )
                }
            }.flowOn(defaultDispatcher) // 🔥 切换到Default线程执行计算密集型操作

        /**
         * 执行混合搜索的核心逻辑
         */
        private suspend fun performHybridSearch(
            query: String,
            limit: Int,
        ): List<CoachMessage> =
            coroutineScope {
                // 并行执行FTS5和向量搜索
                val ftsDeferred =
                    async(ioDispatcher) {
                        Timber.d("🔤 执行FTS5关键词搜索")
                        chatSearchDao.ftsSearch(query, FTS_LIMIT)
                    }

                val vectorDeferred =
                    async(defaultDispatcher) {
                        Timber.d("🧠 执行BGE向量搜索")
                        try {
                            // 基于架构清理要求，使用统一的VectorSearchService
                            vectorSearchService.performVectorSearch(query, VECTOR_LIMIT)
                        } catch (e: Exception) {
                            Timber.w(e, "向量搜索失败，降级到仅关键词搜索")
                            emptyList<ChatSearchResult>()
                        }
                    }

                // 等待搜索结果
                val ftsResults = ftsDeferred.await()
                val vectorResults = vectorDeferred.await()

                Timber.d("📊 搜索结果统计: FTS=${ftsResults.size}, Vector=${vectorResults.size}")

                // 融合搜索结果
                val fusedResults = fuseSearchResults(ftsResults, vectorResults, limit)

                // 转换为CoachMessage
                convertToCoachMessages(fusedResults)
            }

        /**
         * 执行会话内混合搜索
         */
        private suspend fun performHybridSearchInSession(
            sessionId: String,
            query: String,
            limit: Int,
        ): List<CoachMessage> =
            coroutineScope {
                // 🔥 修复：直接使用DAO的会话内搜索方法，避免全局搜索后过滤的性能问题
                val ftsDeferred =
                    async(ioDispatcher) {
                        Timber.d("🔤 执行会话内FTS5关键词搜索")
                        chatSearchDao.ftsSearchInSession(sessionId, query, FTS_LIMIT)
                    }

                val vectorDeferred =
                    async(defaultDispatcher) {
                        Timber.d("🧠 执行会话内BGE向量搜索")
                        try {
                            // 基于架构清理要求，使用统一的VectorSearchService
                            vectorSearchService.performVectorSearch(query, VECTOR_LIMIT, sessionId)
                        } catch (e: Exception) {
                            Timber.w(e, "会话内向量搜索失败，降级到仅关键词搜索")
                            emptyList<ChatSearchResult>()
                        }
                    }

                // 等待搜索结果
                val ftsResults = ftsDeferred.await()
                val vectorResults = vectorDeferred.await()

                Timber.d("📊 会话内搜索结果统计: FTS=${ftsResults.size}, Vector=${vectorResults.size}")

                // 融合搜索结果
                val fusedResults = fuseSearchResults(ftsResults, vectorResults, limit)

                // 转换为CoachMessage
                convertToCoachMessages(fusedResults)
            }

        /**
         * 融合FTS5和向量搜索结果
         */
        private fun fuseSearchResults(
            ftsResults: List<ChatSearchResult>,
            vectorResults: List<ChatSearchResult>,
            limit: Int,
        ): List<ChatSearchResult> {
            // 创建ID到结果的映射
            val ftsMap = ftsResults.associateBy { it.id }
            val vectorMap = vectorResults.associateBy { it.id }

            // 获取所有唯一ID
            val allIds = (ftsMap.keys + vectorMap.keys).toSet()

            // 计算融合分数
            val scoredResults =
                allIds.mapNotNull { id ->
                    val ftsResult = ftsMap[id]
                    val vectorResult = vectorMap[id]

                    // 至少要有一个搜索结果
                    val baseResult = ftsResult ?: vectorResult ?: return@mapNotNull null

                    // 计算融合分数
                    val ftsScore = ftsResult?.let { normalizeScore(it.score, isVector = false) } ?: 0f
                    val vectorScore = vectorResult?.let { normalizeScore(it.score, isVector = true) } ?: 0f

                    val fusedScore = KEYWORD_WEIGHT * ftsScore + SEMANTIC_WEIGHT * vectorScore

                    baseResult.copy(score = fusedScore)
                }

            // 按融合分数排序并限制结果数量
            return scoredResults
                .sortedByDescending { it.score }
                .take(limit)
        }

        /**
         * 归一化搜索分数
         *
         * 基于架构清理要求，更新向量搜索分数处理方式
         */
        private fun normalizeScore(
            score: Float,
            isVector: Boolean,
        ): Float =
            if (isVector) {
                // 向量搜索：余弦相似度 [0, 1]（VectorSearchService已经过滤了负值）
                // 直接使用，无需转换
                score
            } else {
                // FTS5搜索：BM25分数归一化
                // BM25分数通常在0-10范围内，归一化到[0, 1]
                minOf(score / 10f, 1f)
            }

        /**
         * 转换搜索结果为CoachMessage
         */
        private fun convertToCoachMessages(results: List<ChatSearchResult>): List<CoachMessage> =
            results.map { result ->
                when (result.role) {
                    ChatRaw.ROLE_USER ->
                        CoachMessage.UserMessage(
                            id = result.id.toString(),
                            content = result.content,
                            timestamp = result.timestamp,
                        )

                    ChatRaw.ROLE_ASSISTANT ->
                        CoachMessage.AiMessage(
                            id = result.id.toString(),
                            content = result.content,
                            timestamp = result.timestamp,
                        )

                    else -> throw IllegalArgumentException("Unknown message role: ${result.role}")
                }
            }

        // 🔥 FloatArray转ByteArray工具方法已移至 VectorUtils
    }
