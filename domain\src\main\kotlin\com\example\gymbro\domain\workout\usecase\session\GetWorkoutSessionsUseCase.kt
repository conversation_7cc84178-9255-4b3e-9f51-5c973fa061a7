package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow

/**
 * 获取训练会话列表用例
 *
 * 此用例从仓库获取当前用户的所有训练会话，并返回包含会话列表的Flow
 */
@Singleton
class GetWorkoutSessionsUseCase
    @Inject
    constructor(
        private val sessionRepository: SessionRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernFlowUseCase<Unit, List<WorkoutSession>>(dispatcher, logger) {
        /**
         * 执行用例，获取训练会话列表
         *
         * @param parameters 无需参数，使用Unit
         * @return 包含训练会话列表的Flow
         */
        override fun createFlow(parameters: Unit): Flow<ModernResult<List<WorkoutSession>>> =
            flow {
                val userIdResult = getCurrentUserIdUseCase().first()

                when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (userId == null) {
                            emit(
                                ModernResult.Error(
                                    BusinessErrors.BusinessError.rule(
                                        operationName = "getWorkoutSessions",
                                        message = UiText.DynamicString("User not logged in"),
                                        recoverable = true,
                                    ),
                                ),
                            )
                            return@flow
                        }

                        emitAll(sessionRepository.getUserSessions(userId))
                    }

                    is ModernResult.Error -> {
                        emit(userIdResult)
                    }

                    is ModernResult.Loading -> {
                        emit(ModernResult.Loading)
                    }
                }
            }

        /**
         * 无参数调用方法
         *
         * @return 包含训练会话列表的Flow
         */
        operator fun invoke(): Flow<ModernResult<List<WorkoutSession>>> = createFlow(Unit)
    }
