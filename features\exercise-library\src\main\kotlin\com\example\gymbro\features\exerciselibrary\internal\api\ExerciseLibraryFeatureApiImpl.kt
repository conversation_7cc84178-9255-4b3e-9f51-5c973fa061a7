package com.example.gymbro.features.exerciselibrary.internal.api

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.usecase.*
import com.example.gymbro.features.exerciselibrary.api.ExerciseLibraryFeatureApi
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

/**
 * ExerciseLibraryFeatureApi 的默认实现。
 *
 * 此实现负责：
 * - 与 domain 层的 UseCase 进行交互
 * - 将内部的业务逻辑封装成对外的 API 接口
 * - 处理错误转换和异常处理
 *
 * 注入依赖：
 * - 通过构造函数注入需要的 UseCase
 * - 使用 @Singleton 确保全局唯一实例
 */
@Singleton
class ExerciseLibraryFeatureApiImpl
    @Inject
    constructor(
        private val getExerciseDetailsUseCase: GetExerciseDetailsUseCase,
        private val searchExercisesUseCase: SearchExercisesUseCase,
        private val getAllExercisesUseCase: GetAllExercisesUseCase,
        private val createCustomExerciseUseCase: CreateCustomExerciseUseCase,
        private val updateCustomExerciseUseCase: UpdateCustomExerciseUseCase,
        private val deleteCustomExerciseUseCase: DeleteCustomExerciseUseCase,
        private val getUserCustomExercisesUseCase: GetUserCustomExercisesUseCase,
        private val checkExerciseNameExistsUseCase: CheckExerciseNameExistsUseCase,
    ) : ExerciseLibraryFeatureApi {
        override suspend fun getExerciseDetails(
            exerciseId: String,
        ): ModernResult<Exercise> = getExerciseDetailsUseCase(exerciseId)

        override fun searchExercises(query: String): Flow<List<Exercise>> =
            searchExercisesUseCase(query).map { result ->
                when (result) {
                    is ModernResult.Success -> result.data
                    is ModernResult.Error -> emptyList()
                    is ModernResult.Loading -> emptyList()
                }
            }

        override fun getExercisesByMuscleGroup(muscleGroup: String): Flow<List<Exercise>> {
            // TODO: 实现根据肌肉群筛选动作的逻辑，当前返回空列表
            return flowOf(emptyList<Exercise>())
        }

        override fun getExercisesByEquipment(equipment: String): Flow<List<Exercise>> {
            // TODO: 实现根据器械筛选动作的逻辑，当前返回空列表
            return flowOf(emptyList<Exercise>())
        }

        override fun getAllExercises(): Flow<List<Exercise>> =
            getAllExercisesUseCase().map { result ->
                when (result) {
                    is ModernResult.Success -> result.data
                    is ModernResult.Error -> emptyList()
                    is ModernResult.Loading -> emptyList()
                }
            }

        override suspend fun createCustomExercise(exercise: Exercise): ModernResult<Exercise> =
            createCustomExerciseUseCase(exercise)

        override suspend fun updateCustomExercise(exercise: Exercise): ModernResult<Exercise> =
            updateCustomExerciseUseCase(exercise)

        override suspend fun deleteCustomExercise(exerciseId: String, userId: String): ModernResult<Unit> =
            deleteCustomExerciseUseCase(exerciseId, userId)

        override fun getUserCustomExercises(userId: String): Flow<List<Exercise>> =
            getUserCustomExercisesUseCase(userId).map { result ->
                when (result) {
                    is ModernResult.Success -> result.data
                    is ModernResult.Error -> emptyList()
                    is ModernResult.Loading -> emptyList()
                }
            }

        override suspend fun isExerciseNameExists(name: String, userId: String): ModernResult<Boolean> =
            checkExerciseNameExistsUseCase(name, userId)
    }
