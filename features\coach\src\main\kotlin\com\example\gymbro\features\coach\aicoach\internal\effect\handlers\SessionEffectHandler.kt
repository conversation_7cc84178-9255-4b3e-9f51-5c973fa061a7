package com.example.gymbro.features.coach.aicoach.internal.effect.handlers

// HistoryMapper已被HistoryActor替代，移除引用
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.autosave.ChatHistoryAutoSaveService
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.usecase.ChatSessionManagementUseCase
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.AiCoachMapper
import javax.inject.Inject
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * SessionEffectHandler - 处理会话管理相关的Effect
 *
 * 负责：
 * 1. 会话创建和加载
 * 2. 会话历史管理
 * 3. 消息保存
 * 4. 自动保存功能
 */
internal class SessionEffectHandler
    @Inject
    constructor(
        private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
        private val chatHistoryAutoSaveService: ChatHistoryAutoSaveService,
        // historyMapper已被HistoryActor替代，移除依赖

        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) {
        private lateinit var handlerScope: CoroutineScope
        private lateinit var sendIntent: (AiCoachContract.Intent) -> Unit
        private lateinit var getCurrentState: () -> AiCoachContract.State
        // 🔥 清理：移除老的 AutoSave 会话跟踪，新系统自动管理

        fun initialize(
            scope: CoroutineScope,
            intentSender: (AiCoachContract.Intent) -> Unit,
            stateProvider: () -> AiCoachContract.State,
        ) {
            this.handlerScope = scope
            this.sendIntent = intentSender
            this.getCurrentState = stateProvider
        }

        // 🔥 删除重复的函数，使用下面统一的 handleCreateNewSession() 函数

        /**
         * 🔥 彻底清理：禁用自动会话加载
         *
         * 不再自动创建会话，只响应用户明确操作
         */
        fun handleLoadInitialSession() {
            Timber.d("🔍 [SessionEffectHandler] 跳过自动会话加载，等待用户操作")
            // 不执行任何自动会话创建逻辑
        }

        /**
         * 🔥 彻底清理：禁用同步会话加载
         *
         * 不再自动创建会话，只响应用户明确操作
         */
        suspend fun handleLoadInitialSessionSync() {
            Timber.d("🔍 [SessionEffectHandler-Sync] 跳过自动会话加载，等待用户操作")
            // 不执行任何自动会话创建逻辑
        }

        /**
         * 🔥 【废弃】此方法已废弃，请使用AiCoachSessionHandler.handleCreateNewSession()
         *
         * 保留此方法仅为了向后兼容，实际应该使用AiCoachSessionHandler中的实现
         */
        @Deprecated(
            "使用AiCoachSessionHandler.handleCreateNewSession()替代",
            ReplaceWith("aiCoachSessionHandler.handleCreateNewSession()"),
        )
        fun handleCreateNewSession() {
            Timber
                .tag("HISTORY-FIX")
                .w("⚠️ [废弃警告] SessionEffectHandler.handleCreateNewSession()已废弃，应使用AiCoachSessionHandler")
            handlerScope.launch {
                try {
                    Timber.tag("HISTORY-FIX").d("📝 [Phase1] SessionEffectHandler开始创建新会话 - 已废弃")
                    Timber.d("📝 创建新会话")

                    val result =
                        chatSessionManagementUseCase.createSession(
                            userId = "default_user",
                            title = "新的对话", // 🔥 修复：与ConversationPagingSource中的检查条件保持一致
                        )

                    Timber
                        .tag("HISTORY-FIX")
                        .d("📝 [Phase1] 会话创建UseCase调用完成，结果类型: ${result::class.simpleName}")

                    when (result) {
                        is ModernResult.Success -> {
                            val newSession = result.data
                            val newSessionId = newSession.id
                            Timber
                                .tag("HISTORY-FIX")
                                .d("✅ [Phase1] 会话创建成功: sessionId=$newSessionId, title=${newSession.title}")
                            Timber.d("✅ 会话创建成功: $newSessionId")

                            // 🔥 清理：移除老的 AutoSave 会话管理，新系统在消息保存时自动处理
                            Timber.d("✅ 新会话创建完成，双层数据持久化系统将在消息保存时自动启动")

                            Timber.tag("HISTORY-FIX").d("✅ [Phase1] 发送SessionCreatedResult Intent")
                            sendIntent(AiCoachContract.Intent.SessionCreatedResult(newSession))
                        }

                        is ModernResult.Error -> {
                            Timber.tag("HISTORY-FIX").e("❌ [Phase1] 创建会话失败: ${result.error}")
                            Timber.e("❌ 创建会话失败: ${result.error}")
                            Timber.tag("HISTORY-FIX").d("❌ [Phase1] 发送OperationFailed Intent")
                            sendIntent(
                                AiCoachContract.Intent.OperationFailed(
                                    AiCoachContract.ErrorCode.UNKNOWN_ERROR,
                                ),
                            )
                        }

                        is ModernResult.Loading -> {
                            Timber.tag("HISTORY-FIX").d("⏳ [Phase1] 正在创建会话...")
                            Timber.d("⏳ 正在创建会话...")
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "❌ 创建会话异常")
                    sendIntent(
                        AiCoachContract.Intent.OperationFailed(AiCoachContract.ErrorCode.UNKNOWN_ERROR),
                    )
                }
            }
        }

        fun handleLoadSessionHistory() {
            Timber.d("📚 加载所有会话")

            chatSessionManagementUseCase
                .getUserSessions("default_user")
                .flowOn(ioDispatcher)
                .onEach { result: ModernResult<List<ChatSession>> ->
                    when (result) {
                        is ModernResult.Success -> {
                            Timber.d("✅ 成功加载${result.data.size}个会话")
                            sendIntent(
                                AiCoachContract.Intent.SessionsLoadedResult(result.data.toImmutableList()),
                            )
                        }

                        is ModernResult.Error -> {
                            Timber.e("❌ 加载会话失败: ${result.error}")
                            sendIntent(
                                AiCoachContract.Intent.OperationFailed(
                                    AiCoachContract.ErrorCode.UNKNOWN_ERROR,
                                ),
                            )
                        }

                        is ModernResult.Loading -> {
                            Timber.d("⏳ 正在加载会话...")
                        }
                    }
                }.catch { error ->
                    Timber.e(error, "❌ 加载会话时发生异常")
                    sendIntent(
                        AiCoachContract.Intent.OperationFailed(AiCoachContract.ErrorCode.UNKNOWN_ERROR),
                    )
                }.launchIn(handlerScope)
        }

        fun handleSwitchSession(effect: AiCoachContract.Effect.SwitchSession) {
            handlerScope.launch {
                try {
                    val newSessionId = effect.sessionId
                    Timber.tag("HISTORY-FIX").d("🔄 [Phase1] SessionEffectHandler开始切换会话: $newSessionId")
                    Timber.d("🔄 切换会话: $newSessionId")

                    // 🔥 清理：移除老的 AutoSave 会话管理，新系统自动处理

                    // 2. 加载会话信息 from Room (Source of Truth)
                    Timber.tag("HISTORY-FIX").d("🔄 [Phase1] 调用loadSession UseCase")
                    val sessionResult = chatSessionManagementUseCase.loadSession(newSessionId)
                    Timber
                        .tag("HISTORY-FIX")
                        .d("🔄 [Phase1] loadSession结果类型: ${sessionResult::class.simpleName}")
                    when (sessionResult) {
                        is ModernResult.Success -> {
                            val roomSession = sessionResult.data
                            // 注意：loadSession在会话不存在时会返回Error，所以这里data不会是null
                            Timber.d(
                                "✅ 成功加载会话 from Room: ${roomSession.id}, Room messages: ${roomSession.messages.size}",
                            )

                            // 🔥 清理：移除老的缓存恢复逻辑，新系统自动同步
                            // 直接使用 ROOM 数据库中的消息作为权威数据源
                            val finalSession = roomSession

                            // 5. 转换为UI模型 and 发送完整的会话和消息数据
                            val messageUiList =
                                AiCoachMapper
                                    .convertCoachMessagesToMessageUi(
                                        finalSession.messages,
                                    ).toImmutableList()
                            sendIntent(
                                AiCoachContract.Intent.SessionWithMessagesLoaded(
                                    session = finalSession, // Send the session with reconciled messages
                                    messages = messageUiList,
                                ),
                            )

                            // 🔥 清理：移除老的 AutoSave 重新初始化，新系统自动处理

                            // 生成会话标题（如果需要）
                            if (finalSession.title == "新对话" && finalSession.messages.isNotEmpty()) {
                                Timber.d("🏷️ 触发会话标题生成")
                                // TODO: 添加生成标题的Intent和Effect
                            }

                            // BGE嵌入处理（自动向量化历史消息）
                            if (finalSession.messages.isNotEmpty()) {
                                Timber.d("🧠 触发BGE嵌入处理: ${finalSession.messages.size}条消息")
                                // 这里BGE处理会在后台自动进行
                                Timber.d("🧠 会话加载完成，BGE嵌入将在后台自动处理")
                            }
                        }

                        is ModernResult.Error -> {
                            Timber.e("❌ 加载会话失败: ${sessionResult.error}")
                            sendIntent(
                                AiCoachContract.Intent.OperationFailed(
                                    AiCoachContract.ErrorCode.UNKNOWN_ERROR,
                                ),
                            )
                        }

                        is ModernResult.Loading -> {
                            Timber.d("⏳ 正在加载会话...")
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "❌ 会话切换异常: ${effect.sessionId}")
                    sendIntent(
                        AiCoachContract.Intent.OperationFailed(AiCoachContract.ErrorCode.UNKNOWN_ERROR),
                    )
                }
            }
        }

        fun handleSaveAiMessage(effect: AiCoachContract.Effect.SaveAiMessage) {
            handlerScope.launch {
                try {
                    // 🔥 【最终富文本调试】详细记录SaveAiMessage处理过程
                    Timber.tag("FINAL-DEBUG").d("🎯 [SaveAiMessage] 开始处理SaveAiMessage")
                    Timber.tag(
                        "FINAL-DEBUG",
                    ).d("🎯 [SaveAiMessage] sessionId=${effect.sessionId}, messageId=${effect.messageId}") // 🔥 【ID统一】
                    Timber.tag(
                        "FINAL-DEBUG",
                    ).d(
                        "🎯 [SaveAiMessage] content长度=${effect.content.length}, 内容预览='${
                            effect.content.take(
                                100,
                            )
                        }...'",
                    )
                    Timber.tag(
                        "FINAL-DEBUG",
                    ).d("🎯 [SaveAiMessage] finalMarkdown字段='${effect.finalMarkdown?.take(100)}...'")

                    Timber.d("💾 保存AI消息: sessionId=${effect.sessionId}, messageId=${effect.messageId}") // 🔥 【ID统一】

                    // 🔥 验证会话存在性
                    if (!validateSessionExists(effect.sessionId)) {
                        Timber.e("❌ 会话不存在，无法保存AI消息: sessionId=${effect.sessionId}")
                        return@launch
                    }

                    // 🔥 修复：正确提取finalMarkdown内容
                    val finalMarkdown = extractFinalMarkdown(effect.content)

                    // 🔥 【最终富文本调试】记录final内容提取结果
                    Timber.tag(
                        "FINAL-DEBUG",
                    ).d(
                        "🎯 [SaveAiMessage] 提取的finalMarkdown: 长度=${finalMarkdown?.length}, 内容预览='${
                            finalMarkdown?.take(
                                100,
                            )
                        }...'",
                    )

                    val cleanContent =
                        if (finalMarkdown != null) {
                            // 如果有final内容，使用final内容作为主要内容
                            finalMarkdown
                        } else {
                            // 🔥 紧急修复：如果没有final内容，使用原始内容，避免过度清理
                            effect.content
                        }

                    // 🔥 【最终富文本调试】记录最终使用的内容
                    Timber.tag(
                        "FINAL-DEBUG",
                    ).d(
                        "🎯 [SaveAiMessage] 最终使用的cleanContent: 长度=${cleanContent.length}, 内容预览='${
                            cleanContent.take(
                                100,
                            )
                        }...'",
                    )

                    val aiMessage =
                        com.example.gymbro.domain.coach.model.CoachMessage.AiMessage(
                            id = effect.messageId, // 🔥 【ID统一】使用messageId
                            content = cleanContent,
                            timestamp =
                                kotlinx.datetime.Clock.System
                                    .now()
                                    .toEpochMilliseconds(),
                            // 🔥 修复：正确设置finalMarkdown字段
                            finalMarkdown = finalMarkdown,
                            functionCall = effect.lastFunctionCall,
                            mcp = effect.lastMcp,
                            summary = cleanContent.take(120), // 使用清理后的内容生成摘要
                            rawTokens = effect.tokensSnapshot.takeIf { it.isNotBlank() },
                        )

                    // 保存到数据库
                    val result = chatSessionManagementUseCase.saveAiMessage(effect.sessionId, aiMessage)
                    when (result) {
                        is ModernResult.Success<*> -> {
                            Timber.d("✅ AI消息保存成功: ${effect.messageId}") // 🔥 【ID统一】

                            // 🔥 紧急修复：多重状态重置，确保发送按钮可用
                            Timber.d("🔄 发送状态重置Intent")
                            sendIntent(AiCoachContract.Intent.ResetStreamingState)

                            // 🔥 【架构清理】UpdateStreamingMessage Intent 已移除
                            // 流式状态重置已通过 ResetStreamingState 完成

                            // 如果是会话的第一条AI回复，触发标题生成
                            if (effect.inReplyToMessageId.isNotEmpty()) {
                                Timber.d("🏷️ 触发会话标题生成")
                                // TODO: 实现标题生成逻辑
                            }
                        }

                        is ModernResult.Error -> {
                            Timber.e("❌ AI消息保存失败: ${result.error}")
                            // 不发送错误Intent，避免影响用户体验
                        }

                        is ModernResult.Loading -> {
                            Timber.d("⏳ 正在保存AI消息...")
                        }

                        else -> {
                            Timber.d("⚠️ 未知保存结果状态")
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "❌ 保存AI消息异常: ${effect.messageId}") // 🔥 【ID统一】
                }
            }
        }

        fun handleSaveUserMessage(effect: AiCoachContract.Effect.SaveUserMessage) {
            handlerScope.launch {
                try {
                    Timber.d(
                        "🚀 [EFFECT-DEBUG] handleSaveUserMessage 开始: sessionId=${effect.sessionId}, messageId=${effect.messageId}", // 🔥 【ID统一】
                    )
                    Timber.d(
                        "🚀 [EFFECT-DEBUG] 用户消息内容: ${
                            effect.content.take(
                                100,
                            )
                        }${if (effect.content.length > 100) "..." else ""}",
                    )

                    // 🔥 修复：移除会话存在性验证，让Repository层处理会话创建
                    // ChatSessionRepositoryImpl.addMessage() 已经有完整的会话不存在处理逻辑
                    // 移除这个验证可以避免竞态条件，让消息保存和会话创建正常工作
                    Timber.d("🔄 [EFFECT-DEBUG] 跳过会话验证，直接保存消息（Repository层会处理会话创建）")
                    Timber.d("✅ [EFFECT-DEBUG] 会话存在性验证通过: sessionId=${effect.sessionId}")

                    // 创建用户消息对象
                    val userMessage =
                        com.example.gymbro.domain.coach.model.CoachMessage.UserMessage(
                            id = effect.messageId, // 🔥 【ID统一】使用messageId
                            content = effect.content,
                            timestamp =
                                kotlinx.datetime.Clock.System
                                    .now()
                                    .toEpochMilliseconds(),
                        )
                    Timber.d(
                        "💾 [EFFECT-DEBUG] 用户消息对象创建完成: id=${userMessage.id}, timestamp=${userMessage.timestamp}",
                    )

                    // 保存到数据库
                    Timber.d("🔄 [EFFECT-DEBUG] 开始调用 chatSessionManagementUseCase.saveUserMessage")
                    val result = chatSessionManagementUseCase.saveUserMessage(effect.sessionId, userMessage)
                    Timber.d("🔄 [EFFECT-DEBUG] saveUserMessage 调用完成，结果类型: ${result::class.simpleName}")

                    when (result) {
                        is ModernResult.Success<*> -> {
                            Timber.d("✅ [EFFECT-DEBUG] 用户消息保存成功: ${effect.messageId}")
                        }

                        is ModernResult.Error -> {
                            Timber.e("❌ [EFFECT-DEBUG] 用户消息保存失败: ${result.error}")
                            Timber.e("❌ [EFFECT-DEBUG] 错误详情: ${result.error.message}")
                            // 不发送错误Intent，避免影响用户体验
                        }

                        is ModernResult.Loading -> {
                            Timber.d("⏳ [EFFECT-DEBUG] 正在保存用户消息...")
                        }

                        else -> {
                            Timber.w("⚠️ [EFFECT-DEBUG] 未知保存结果状态: $result")
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "❌ 保存用户消息异常: ${effect.messageId}")
                }
            }
        }

        /**
         * 验证会话是否存在
         *
         * @param sessionId 会话ID
         * @return 如果会话存在返回true，否则返回false
         */
        private suspend fun validateSessionExists(sessionId: String): Boolean {
            try {
                // 特殊处理"default_session"，这是一个无效的会话ID
                if (sessionId == "default_session") {
                    Timber.w("⚠️ 检测到无效的default_session会话ID")
                    return false
                }

                // 从UseCase获取会话
                val sessionResult = chatSessionManagementUseCase.loadSessionInfo(sessionId)
                return when (sessionResult) {
                    is ModernResult.Success<ChatSession> -> {
                        val exists = sessionResult.data != null
                        if (!exists) {
                            Timber.w("⚠️ 会话不存在: sessionId=$sessionId")
                        }
                        exists
                    }

                    is ModernResult.Error -> {
                        Timber.e("❌ 验证会话存在性失败: ${sessionResult.error}")
                        false
                    }

                    is ModernResult.Loading -> {
                        Timber.w("⚠️ 会话加载中，无法确定存在性: sessionId=$sessionId")
                        false
                    }

                    else -> {
                        Timber.w("⚠️ 未知会话状态，无法确定存在性: sessionId=$sessionId")
                        false
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "❌ 验证会话存在性异常: sessionId=$sessionId")
                return false
            }
        }

        /**
         * 🔥 【消息迁移修复】处理消息迁移到新会话
         *
         * 将临时会话（如"default_session"）的消息迁移到真实会话中
         */
        fun handleMigrateMessagesToSession(effect: AiCoachContract.Effect.MigrateMessagesToSession) {
            handlerScope.launch {
                try {
                    Timber.d(
                        "🔄 [消息迁移] 开始迁移消息: ${effect.fromSessionId} -> ${effect.toSessionId}, 消息数=${effect.messages.size}",
                    )

                    // 遍历所有消息，将它们保存到新会话中
                    effect.messages.forEach { messageUi ->
                        try {
                            if (messageUi.isFromUser) {
                                // 迁移用户消息
                                val userMessage =
                                    com.example.gymbro.domain.coach.model.CoachMessage.UserMessage(
                                        id = messageUi.id,
                                        content = messageUi.content,
                                        timestamp = messageUi.timestamp.toEpochMilliseconds(),
                                    )

                                val result =
                                    chatSessionManagementUseCase.saveUserMessage(
                                        effect.toSessionId,
                                        userMessage,
                                    )
                                when (result) {
                                    is ModernResult.Success -> {
                                        Timber.d("✅ [消息迁移] 用户消息迁移成功: ${messageUi.id}")
                                    }

                                    is ModernResult.Error -> {
                                        Timber.e("❌ [消息迁移] 用户消息迁移失败: ${messageUi.id}, error=${result.error}")
                                    }

                                    is ModernResult.Loading -> {
                                        Timber.d("⏳ [消息迁移] 用户消息迁移中: ${messageUi.id}")
                                    }
                                }
                            } else {
                                // 迁移AI消息
                                val aiMessage =
                                    com.example.gymbro.domain.coach.model.CoachMessage.AiMessage(
                                        id = messageUi.id,
                                        content = messageUi.content,
                                        timestamp = messageUi.timestamp.toEpochMilliseconds(),
                                        finalMarkdown = messageUi.finalMarkdown,
                                        // 注意：CoachMessage.AiMessage 没有 thinkingNodes 字段
                                    )

                                val result =
                                    chatSessionManagementUseCase.saveAiMessage(effect.toSessionId, aiMessage)
                                when (result) {
                                    is ModernResult.Success -> {
                                        Timber.d("✅ [消息迁移] AI消息迁移成功: ${messageUi.id}")
                                    }

                                    is ModernResult.Error -> {
                                        Timber.e("❌ [消息迁移] AI消息迁移失败: ${messageUi.id}, error=${result.error}")
                                    }

                                    is ModernResult.Loading -> {
                                        Timber.d("⏳ [消息迁移] AI消息迁移中: ${messageUi.id}")
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Timber.e(e, "❌ [消息迁移] 迁移单条消息异常: ${messageUi.id}")
                        }
                    }

                    Timber.d("✅ [消息迁移] 消息迁移完成: ${effect.fromSessionId} -> ${effect.toSessionId}")

                    // 🔥 【关键修复】消息迁移完成后，延迟触发历史消息流设置
                    // 确保数据库写入完成后再设置分页流，避免加载空数据
                    kotlinx.coroutines.delay(500) // 给数据库写入500ms时间

                    Timber.d("🔄 [消息迁移] 迁移完成，触发历史消息流刷新: ${effect.toSessionId}")
                    sendIntent(
                        AiCoachContract.Intent.RefreshHistoryFlow(effect.toSessionId),
                    )
                } catch (e: Exception) {
                    Timber.e(e, "❌ [消息迁移] 消息迁移异常: ${effect.fromSessionId} -> ${effect.toSessionId}")
                }
            }
        }

        companion object {
            /**
             * 从AI响应内容中提取<final>标签内容
             */
            private fun extractFinalMarkdown(content: String): String? {
                val finalRegex = Regex("<final>(.*?)</final>", RegexOption.DOT_MATCHES_ALL)
                val finalMatch = finalRegex.find(content)
                return finalMatch?.groupValues?.get(1)?.trim()
            }

            /**
             * 清理思考标签，保留可显示的内容
             */
            private fun cleanThinkingTags(content: String): String =
                content
                    // 移除<think>标签及其内容
                    .replace(Regex("<think>.*?</think>", RegexOption.DOT_MATCHES_ALL), "")
                    // 移除<thinking>标签及其内容
                    .replace(Regex("<thinking>.*?</thinking>", RegexOption.DOT_MATCHES_ALL), "")
                    // 移除<phase>标签及其内容
                    .replace(Regex("<phase[^>]*>.*?</phase>", RegexOption.DOT_MATCHES_ALL), "")
                    // 移除<final>标签但保留内容（这部分内容会被单独提取为finalMarkdown）
                    .replace(Regex("</?final>"), "")
                    .trim()
        }
    }
