// 简化的 detekt 自定义规则模块配置
// 避免使用项目约定插件以防止冲突

plugins {
    `java-library`
    kotlin("jvm") version "2.0.21"
}

dependencies {
    // Detekt API dependencies for custom rules
    implementation("io.gitlab.arturbosch.detekt:detekt-api:1.23.8")
    implementation("io.gitlab.arturbosch.detekt:detekt-psi-utils:1.23.8")

    // Test dependencies for rule testing
    testImplementation("io.gitlab.arturbosch.detekt:detekt-test:1.23.8")
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.jetbrains.kotlin:kotlin-test:2.0.21")
}

// Java 兼容性配置
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

// Kotlin 编译选项
tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions {
        jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
    }
}
