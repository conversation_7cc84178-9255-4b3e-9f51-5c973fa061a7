package com.example.gymbro.buildlogic.detekt.mvi

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.*

/**
 * GymBro 自定义规则：确保 MVI State 类的不可变性。
 *
 * 目的：强制执行 MVI 架构中 State 类的不可变性原则。
 * 这有助于：
 * 1. 确保状态的可预测性
 * 2. 避免意外的状态修改
 * 3. 提高 Compose 重组性能
 * 4. 遵循 MVI 架构最佳实践
 *
 * 检查规则：
 * 1. State 类必须是 data class
 * 2. 所有属性必须是 val（不可变）
 * 3. 属性类型应该是不可变的
 * 4. 建议使用 @Immutable 注解
 */
class MviStateImmutability(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "MVI State 类必须是不可变的，所有属性都应该是 val。",
        Debt.TEN_MINS
    )

    private val mutableCollectionTypes = setOf(
        "MutableList", "MutableSet", "MutableMap",
        "ArrayList", "HashSet", "HashMap", "LinkedHashMap",
        "mutableListOf", "mutableSetOf", "mutableMapOf"
    )

    override fun visitClass(klass: KtClass) {
        super.visitClass(klass)

        val className = klass.name ?: return

        // 检查是否是 State 类（通过命名约定）
        if (isStateClass(className)) {
            checkStateClassStructure(klass)
        }
    }

    private fun isStateClass(className: String): Boolean {
        return className.endsWith("State") ||
            className.contains("State") ||
            className.endsWith("UiState")
    }

    private fun checkStateClassStructure(klass: KtClass) {
        val className = klass.name!!

        // 检查是否是 data class
        if (!klass.isData()) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(klass),
                    "State 类 '$className' 应该是 data class。" +
                        "\n建议：将 'class $className' 改为 'data class $className'"
                )
            )
        }

        // 检查 @Immutable 注解
        if (!hasImmutableAnnotation(klass)) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(klass),
                    "State 类 '$className' 建议添加 @Immutable 注解以优化 Compose 性能。" +
                        "\n建议：在类声明前添加 @Immutable"
                )
            )
        }

        // 检查所有属性
        checkStateProperties(klass)
    }

    private fun checkStateProperties(klass: KtClass) {
        val className = klass.name!!
        val primaryConstructor = klass.primaryConstructor

        primaryConstructor?.valueParameters?.forEach { parameter ->
            checkStateProperty(parameter, className)
        }

        // 检查类体中的属性
        klass.getProperties().forEach { property ->
            checkClassProperty(property, className)
        }
    }

    private fun checkStateProperty(parameter: KtParameter, className: String) {
        val propertyName = parameter.name ?: return

        // 检查是否是 val
        if (parameter.isMutable) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(parameter),
                    "State 类 '$className' 的属性 '$propertyName' 必须是 val（不可变）。" +
                        "\n建议：将 'var $propertyName' 改为 'val $propertyName'"
                )
            )
        }

        // 检查属性类型是否可变
        val typeReference = parameter.typeReference
        if (typeReference != null) {
            checkPropertyType(parameter, propertyName, typeReference, className)
        }
    }

    private fun checkClassProperty(property: KtProperty, className: String) {
        val propertyName = property.name ?: return

        // 检查是否是 val
        if (property.isVar) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(property),
                    "State 类 '$className' 的属性 '$propertyName' 必须是 val（不可变）。" +
                        "\n建议：将 'var $propertyName' 改为 'val $propertyName'"
                )
            )
        }

        // 检查属性类型是否可变
        val typeReference = property.typeReference
        if (typeReference != null) {
            checkPropertyType(property, propertyName, typeReference, className)
        }
    }

    private fun checkPropertyType(
        element: KtElement,
        propertyName: String,
        typeReference: KtTypeReference,
        className: String
    ) {
        val typeText = typeReference.text

        // 检查是否使用了可变集合类型
        if (mutableCollectionTypes.any { mutableType -> typeText.contains(mutableType) }) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(element),
                    "State 类 '$className' 的属性 '$propertyName' 使用了可变类型 '$typeText'。" +
                        "\n建议的不可变替换：" +
                        "\n- MutableList -> List" +
                        "\n- MutableSet -> Set" +
                        "\n- MutableMap -> Map" +
                        "\n- ArrayList -> List" +
                        "\n- HashSet -> Set" +
                        "\n- HashMap -> Map" +
                        "\n\n或者使用 kotlinx.collections.immutable 中的不可变集合。"
                )
            )
        }
    }

    private fun hasImmutableAnnotation(klass: KtClass): Boolean {
        return klass.annotationEntries.any { annotation ->
            val annotationName = annotation.shortName?.asString()
            annotationName == "Immutable" || annotationName == "Stable"
        }
    }
}
