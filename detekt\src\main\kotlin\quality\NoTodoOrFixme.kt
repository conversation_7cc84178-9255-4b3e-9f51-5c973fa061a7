package com.example.gymbro.buildlogic.detekt.quality

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.KtFile
import org.jetbrains.kotlin.psi.KtNamedFunction
import org.jetbrains.kotlin.psi.KtProperty

/**
 * 检查 TODO 和 FIXME 注释。
 *
 * 规则：代码中出现 TODO、FIXME、HACK、XXX 等注释时发出警告。
 * 这些注释表明代码未完成或存在问题，应该及时处理。
 */
class NoTodoOrFixme(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.Warning,
        "代码中存在 TODO、FIXME 等未完成标记，建议及时处理。",
        Debt.FIVE_MINS
    )

    private val keywords by config(listOf("TODO", "FIXME", "HACK", "XXX")) {
        "禁止的关键词列表"
    }

    override fun visitKtFile(file: KtFile) {
        super.visitKtFile(file)

        // 检查文件内容中的注释
        val fileContent = file.text
        val lines = fileContent.lines()

        lines.forEachIndexed { index, line ->
            val trimmedLine = line.trim()
            if (trimmedLine.startsWith("//") || trimmedLine.startsWith("/*") || trimmedLine.contains("*/")) {
                val commentText = trimmedLine.uppercase()

                for (keyword in keywords) {
                    if (commentText.contains(keyword)) {
                        report(
                            CodeSmell(
                                issue,
                                Entity.from(file, index + 1),
                                "发现未完成标记 '$keyword'，建议及时处理。" +
                                    "\n注释内容：${trimmedLine}" +
                                    "\n建议：完成相关工作后移除此标记，或转换为具体的 issue 跟踪。"
                            )
                        )
                        break // 避免同一注释多次报告
                    }
                }
            }
        }
    }

    override fun visitProperty(property: KtProperty) {
        super.visitProperty(property)

        // 检查属性名中是否包含这些关键词
        val propertyName = property.name?.uppercase() ?: return

        for (keyword in keywords) {
            if (propertyName.contains(keyword)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(property),
                        "属性名 '${property.name}' 包含未完成标记 '$keyword'，建议使用更描述性的名称。"
                    )
                )
                break
            }
        }
    }

    override fun visitNamedFunction(function: KtNamedFunction) {
        super.visitNamedFunction(function)

        // 检查函数名中是否包含这些关键词
        val functionName = function.name?.uppercase() ?: return

        for (keyword in keywords) {
            if (functionName.contains(keyword)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(function),
                        "函数名 '${function.name}' 包含未完成标记 '$keyword'，建议使用更描述性的名称。"
                    )
                )
                break
            }
        }
    }
}
