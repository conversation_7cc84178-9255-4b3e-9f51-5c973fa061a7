package com.example.gymbro.data.repository.auth

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.error.types.auth.AuthErrors
import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.util.DateTimeConverters
import com.example.gymbro.data.local.dao.user.UserDao
import com.example.gymbro.data.local.entity.user.UserCacheEntity
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.User
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.model.user.WorkoutDay
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender
import com.example.gymbro.domain.profile.model.user.enums.UserType
import com.example.gymbro.domain.profile.model.user.enums.WeightUnit
import com.example.gymbro.domain.profile.model.user.state.UserTypeWithNetworkStatus
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.*
import timber.log.Timber

/**
 * 用户管理功能仓库
 *
 * 负责处理用户信息获取、用户类型判断、用户资料管理等功能
 */
@Singleton
class UserManagementRepository
    @Inject
    constructor(
        private val userDao: UserDao,
        private val networkMonitor: NetworkMonitor,
    ) {
        /**
         * 获取当前用户
         */
        fun getCurrentUser(): Flow<ModernResult<AuthUser?>> =
            userDao
                .getCurrentUser()
                .map { userEntity ->
                    Timber.d("获取当前用户，实体: %s", userEntity?.userId)
                    when {
                        userEntity == null -> {
                            // 无用户信息
                            ModernResult.Success(null)
                        }

                        else -> {
                            try {
                                // 直接从userEntity创建AuthUser，不使用mapper
                                val authUser =
                                    AuthUser(
                                        uid = userEntity.userId ?: "",
                                        displayName = userEntity.displayName ?: userEntity.username,
                                        email = userEntity.email,
                                        phoneNumber = userEntity.phoneNumber,
                                        isAnonymous = userEntity.isAnonymous,
                                    )
                                ModernResult.Success(authUser)
                            } catch (e: Exception) {
                                Timber.e(e, "用户信息映射失败")
                                ModernResult.Error(
                                    AuthErrors.AuthError.unknown(
                                        operationName = "getCurrentUser.mapping",
                                        message = UiText.DynamicString("用户信息映射失败"),
                                        cause = e,
                                        metadataMap =
                                            mapOf(
                                                StandardKeys.EXCEPTION.key to e,
                                                StandardKeys.OPERATION_TYPE.key to "user_mapping",
                                            ),
                                    ),
                                )
                            }
                        }
                    }
                }.catch { e ->
                    Timber.e(e, "获取当前用户失败")
                    emit(
                        ModernResult.Error(
                            AuthErrors.AuthError.unknown(
                                operationName = "getCurrentUser",
                                message = UiText.DynamicString("获取当前用户失败"),
                                cause = e,
                                metadataMap =
                                    mapOf(
                                        StandardKeys.EXCEPTION.key to e,
                                        StandardKeys.OPERATION_TYPE.key to "current_user",
                                    ),
                            ),
                        ),
                    )
                }

        /**
         * 获取当前用户ID
         */
        fun getCurrentUserId(): Flow<ModernResult<String?>> =
            flow {
                Timber.d("获取当前用户ID")
                when (val userResult = getCurrentUser().first()) {
                    is ModernResult.Success -> emit(ModernResult.Success(userResult.data?.uid))
                    is ModernResult.Error -> emit(ModernResult.Error(userResult.error))
                    is ModernResult.Loading -> emit(ModernResult.Loading)
                }
            }

        /**
         * 检查是否为匿名用户
         */
        suspend fun isAnonymousUser(): ModernResult<Boolean> {
            Timber.d("检查是否为匿名用户")
            return try {
                val userEntity = userDao.getCurrentUser().first()
                when {
                    userEntity == null -> {
                        // 无用户信息，不是匿名用户（实际是未登录）
                        ModernResult.Success(false)
                    }

                    userEntity.isAnonymous -> {
                        // 通过字段判断是匿名用户
                        ModernResult.Success(true)
                    }

                    else -> {
                        // 正常注册用户
                        ModernResult.Success(false)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "检查匿名用户状态失败")
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "isAnonymousUser",
                        message = UiText.DynamicString("检查匿名用户状态失败"),
                        cause = e,
                        metadataMap =
                            mapOf(
                                StandardKeys.EXCEPTION.key to e,
                                StandardKeys.OPERATION_TYPE.key to "auth_anonymous",
                            ),
                    ),
                )
            }
        }

        /**
         * 获取用户类型
         */
        fun getUserType(): Flow<ModernResult<UserType>> =
            userDao
                .getCurrentUser()
                .map { userEntity ->
                    Timber.d("获取用户类型，实体: %s", userEntity?.userId)
                    when {
                        userEntity == null -> {
                            // 无用户信息，未登录用户
                            ModernResult.Success(UserType.ANONYMOUS)
                        }

                        userEntity.isAnonymous -> {
                            // 通过字段判断是匿名用户
                            ModernResult.Success(UserType.ANONYMOUS)
                        }

                        else -> {
                            try {
                                // 检查订阅状态来确定用户类型
                                // 注：这里简化处理，实际应该查询订阅信息
                                // TODO: 集成订阅仓库来获取准确的订阅状态

                                val isAdmin = userEntity.email?.contains("admin") == true
                                val isCoach = userEntity.userType?.equals("COACH", ignoreCase = true) == true

                                val userType =
                                    when {
                                        userEntity.userType?.equals(
                                            "SUBSCRIBED",
                                            ignoreCase = true,
                                        ) == true -> UserType.SUBSCRIBED

                                        !userEntity.email.isNullOrBlank() -> UserType.REGISTERED
                                        else -> UserType.ANONYMOUS
                                    }

                                ModernResult.Success(userType)
                            } catch (e: Exception) {
                                Timber.e(e, "用户类型判断失败")
                                ModernResult.Error(
                                    AuthErrors.AuthError.unknown(
                                        operationName = "getUserType.mapping",
                                        message = UiText.DynamicString("用户类型判断失败"),
                                        cause = e,
                                        metadataMap =
                                            mapOf(
                                                StandardKeys.EXCEPTION.key to e,
                                                StandardKeys.OPERATION_TYPE.key to "user_type",
                                            ),
                                    ),
                                )
                            }
                        }
                    }
                }.catch { e ->
                    Timber.e(e, "获取用户类型失败")
                    emit(
                        ModernResult.Error(
                            AuthErrors.AuthError.unknown(
                                operationName = "getUserType",
                                message = UiText.DynamicString("获取用户类型失败"),
                                cause = e,
                                metadataMap =
                                    mapOf(
                                        StandardKeys.EXCEPTION.key to e,
                                        StandardKeys.OPERATION_TYPE.key to "user_type",
                                    ),
                            ),
                        ),
                    )
                }

        /**
         * 获取用户类型 + 网络状态
         */
        fun getUserTypeWithNetworkStatus(): Flow<ModernResult<UserTypeWithNetworkStatus>> =
            flow {
                Timber.d("获取用户类型和网络状态")
                try {
                    // 获取用户类型
                    getUserType().collect { userTypeResult ->
                        when (userTypeResult) {
                            is ModernResult.Success -> {
                                try {
                                    // 直接获取当前网络状态 - 简化架构，移除Repository层
                                    val isOnline = networkMonitor.isOnline
                                    val userTypeWithNetworkStatus =
                                        UserTypeWithNetworkStatus(
                                            userType = userTypeResult.data,
                                            isOnline = isOnline,
                                        )
                                    emit(ModernResult.Success(userTypeWithNetworkStatus))
                                } catch (e: Exception) {
                                    Timber.e(e, "获取网络状态失败")
                                    // 网络状态获取失败时，假设离线状态
                                    val userTypeWithNetworkStatus =
                                        UserTypeWithNetworkStatus(
                                            userType = userTypeResult.data,
                                            isOnline = false,
                                        )
                                    emit(ModernResult.Success(userTypeWithNetworkStatus))
                                }
                            }

                            is ModernResult.Error -> {
                                emit(ModernResult.Error(userTypeResult.error))
                            }

                            is ModernResult.Loading -> {
                                emit(ModernResult.Loading)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "获取用户类型和网络状态失败")
                    emit(
                        ModernResult.Error(
                            AuthErrors.AuthError.unknown(
                                operationName = "getUserTypeWithNetworkStatus",
                                message = UiText.DynamicString("获取用户类型和网络状态失败"),
                                cause = e,
                                metadataMap =
                                    mapOf(
                                        StandardKeys.EXCEPTION.key to e,
                                        StandardKeys.OPERATION_TYPE.key to "user_type_network",
                                    ),
                            ),
                        ),
                    )
                }
            }

        /**
         * 获取用户资料
         */
        suspend fun getUserProfile(userId: String): ModernResult<UserProfile> {
            Timber.d("获取用户资料: %s", userId)
            return try {
                // 通过userDao获取用户实体
                val userEntity = userDao.getUser(userId).first()

                if (userEntity == null) {
                    ModernResult.Error(
                        AuthErrors.AuthError.unknown(
                            operationName = "getUserProfile",
                            message = UiText.DynamicString("用户不存在"),
                            metadataMap =
                                mapOf(
                                    StandardKeys.USER_ID.key to userId,
                                    StandardKeys.OPERATION_TYPE.key to "user_profile",
                                ),
                        ),
                    )
                } else {
                    // 将UserCacheEntity转换为UserProfile
                    val userProfile = mapUserCacheEntityToUserProfile(userEntity)
                    ModernResult.Success(userProfile)
                }
            } catch (e: Exception) {
                Timber.e(e, "获取用户资料失败: %s", userId)
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "getUserProfile",
                        message = UiText.DynamicString("获取用户资料失败"),
                        cause = e,
                        metadataMap =
                            mapOf(
                                StandardKeys.EXCEPTION.key to e,
                                StandardKeys.USER_ID.key to userId,
                                StandardKeys.OPERATION_TYPE.key to "user_profile",
                            ),
                    ),
                )
            }
        }

        /**
         * 根据ID获取用户
         */
        suspend fun getUserById(userId: String): ModernResult<User> =
            try {
                val entity = userDao.getUser(userId).first()
                if (entity != null) {
                    // 暂时返回错误，因为User映射需要重新设计
                    // TODO: 实现直接的User映射逻辑
                    ModernResult.Error(
                        AuthErrors.AuthError.unknown(
                            operationName = "getUserById.notImplemented",
                            message = UiText.DynamicString("用户映射功能暂未实现"),
                            metadataMap = mapOf(StandardKeys.USER_ID.key to userId),
                        ),
                    )
                } else {
                    ModernResult.Error(
                        AuthErrors.AuthError.unknown(
                            operationName = "getUserById.notFound",
                            message = UiText.DynamicString("用户不存在"),
                            metadataMap = mapOf(StandardKeys.USER_ID.key to userId),
                        ),
                    )
                }
            } catch (e: Exception) {
                Timber.e(e, "获取用户失败")
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "getUserById.exception",
                        message = UiText.DynamicString("获取用户失败"),
                        cause = e,
                        metadataMap =
                            mapOf(
                                StandardKeys.USER_ID.key to userId,
                                StandardKeys.EXCEPTION.key to e,
                                StandardKeys.OPERATION_TYPE.key to "user_profile",
                            ),
                    ),
                )
            }

        /**
         * 将UserCacheEntity转换为UserProfile领域模型
         */
        private fun mapUserCacheEntityToUserProfile(entity: UserCacheEntity): UserProfile {
            // 简化解析，暂时使用默认值
            // TODO: 实现直接的JSON解析逻辑
            val fitnessGoals = emptyList<com.example.gymbro.domain.profile.model.user.FitnessGoal>()

            // 暂时使用默认训练日
            val workoutDays = emptyList<WorkoutDay>()

            // 暂时使用空的社交链接
            val socialLinks = emptyMap<String, String>()

            // 转换性别枚举
            val gender =
                entity.gender?.let { genderStr ->
                    try {
                        Gender.valueOf(genderStr.uppercase())
                    } catch (e: IllegalArgumentException) {
                        Gender.OTHER
                    }
                } ?: Gender.OTHER

            // 转换体重单位枚举
            val weightUnit =
                entity.weightUnit?.let { unitStr ->
                    try {
                        WeightUnit.valueOf(unitStr.uppercase())
                    } catch (e: IllegalArgumentException) {
                        WeightUnit.KG
                    }
                } ?: WeightUnit.KG

            // 转换健身水平枚举
            val fitnessLevel =
                entity.fitnessLevel?.let { levelInt ->
                    FitnessLevel.entries.getOrNull(levelInt) ?: FitnessLevel.BEGINNER
                } ?: FitnessLevel.BEGINNER

            // 转换时间戳为LocalDateTime
            val joinDate =
                entity.createdAt?.let { timestamp ->
                    DateTimeConverters.longToLocalDateTime(timestamp)
                }

            val lastActive =
                entity.lastLoginAt?.let { timestamp ->
                    DateTimeConverters.longToLocalDateTime(timestamp)
                }

            return UserProfile(
                userId = entity.userId,
                username = entity.username,
                displayName = entity.displayName,
                email = entity.email,
                phoneNumber = entity.phoneNumber,
                avatarUrl = entity.photoUrl ?: entity.avatar,
                bio = entity.bio,
                gender = gender,
                weight = entity.weight,
                weightUnit = weightUnit,
                fitnessLevel = fitnessLevel,
                fitnessGoals = fitnessGoals,
                joinDate = joinDate,
                lastActive = lastActive,
                workoutDays = workoutDays,
                allowPartnerMatching = entity.allowPartnerMatching,
                // TODO: 在重制workout模块后重新添加totalWorkoutCount
                // totalWorkoutCount = entity.totalWorkoutCount,
                weeklyActiveMinutes = entity.weeklyActiveMinutes,
                likesReceived = entity.likesReceived,
                isSubscribed = entity.subscriptionPlan != UserCacheEntity.SUBSCRIPTION_PLAN_NONE,
                isAnonymous = entity.isAnonymous,
                socialLinks = socialLinks,
                isMpOwner =
                    entity.userType?.equals("COACH", ignoreCase = true) == true ||
                        entity.userType?.equals("ADMIN", ignoreCase = true) == true,
            )
        }
    }
