package com.example.gymbro.features.auth.ui.viewmodel

//  // 使用designSystem资源
// 🧹 MVI COMPLIANCE: 添加@Immutable注解支持
import androidx.compose.runtime.Immutable
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.content.ContentDisplayProvider
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.util.ValidationUtils
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.auth.usecase.*
import com.example.gymbro.features.auth.shared.utils.AuthPerformanceConfig
import com.example.gymbro.features.auth.shared.utils.applyAuthStateOptimizations
import com.example.gymbro.features.auth.shared.utils.applyAuthUiOptimizations
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 重构后的AuthViewModel - 轻量级版本
 * 主要功能已拆分到专门的ViewModel：
 * - AuthLoginViewModel: 登录逻辑
 * - AuthRegisterViewModel: 注册逻辑
 * - AuthUiStateManager: UI状态管理
 *
 * 保留此ViewModel用于向后兼容和整体状态协调
 */
@HiltViewModel
class AuthViewModel
    @Inject
    constructor(
        private val loginWithEmailUseCase: LoginWithEmailUseCase,
        private val loginWithGoogleUseCase: LoginWithGoogleUseCase,
        private val loginAnonymouslyUseCase: LoginAnonymouslyUseCase,
        private val registerWithEmailUseCase: RegisterWithEmailUseCase,
        private val observeCurrentUserUseCase: ObserveCurrentUserUseCase,
        private val upgradeAnonymousAccountUseCase: UpgradeAnonymousAccountUseCase,
        private val authRepository: AuthRepository,
        private val contentDisplayProvider: ContentDisplayProvider,
        private val modernErrorHandler: ModernErrorHandler,
    ) : ViewModel() {
        // 🧹 MVI COMPLIANCE: 添加@Immutable注解确保Compose重组性能
        @Immutable
        data class AuthUiState(
            val isLoading: Boolean = false,
            val navigationTarget: String? = null,
            val error: UiText? = null,
            val successMessage: UiText? = null,
            val isRegisterMode: Boolean = false,
            val email: String = "",
            val password: String = "",
            val confirmPassword: String = "", // 新增确认密码字段
            val determinedRegion: String? = null,
            val showMoreOptionsSheet: Boolean = false,
            val showEmailLoginCard: Boolean = false,
            val isNetworkAvailable: Boolean = false,
            val isAnonymousUser: Boolean = false,
            val anonymousAuthState: AnonymousAuthState = AnonymousAuthState.Unknown,
        )

        private val _uiState = MutableStateFlow(AuthUiState())

        // 🎯 性能优化：保持StateFlow类型，在使用时应用优化
        val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()

        // 🎯 性能优化：输入防抖处理
        private val emailInputFlow = MutableSharedFlow<String>(extraBufferCapacity = 1)
        private val passwordInputFlow = MutableSharedFlow<String>(extraBufferCapacity = 1)

        enum class AnonymousAuthState {
            Unknown,
            Unauthenticated,
            AuthenticatingAnonymously,
            AnonymousAuthenticated,
            FullyAuthenticated,
            AuthenticationError,
        }

        init {
            // 🎯 性能优化：延迟初始化，避免启动时阻塞主线程
            viewModelScope.launch {
                delay(AuthPerformanceConfig.ViewModel.DELAYED_INIT_MS)
                initializeRegionConfig()
                observeAuthState()

                // 🎯 性能优化：初始化输入防抖处理
                initializeInputDebouncing()
            }
        }

        /**
         * 初始化地区配置
         */
        private fun initializeRegionConfig() {
            viewModelScope.launch {
                try {
                    val appConfig = contentDisplayProvider.getAppDisplayConfig()
                    _uiState.update {
                        it.copy(
                            determinedRegion = appConfig.regionCode,
                            isNetworkAvailable = true,
                        )
                    }
                    Timber.d("AuthViewModel initialized with region: ${appConfig.regionCode}")

                    // 检查Firebase可用性（CN区禁用Firebase）
                    if (appConfig.regionCode == "CN") {
                        Timber.d("CN region detected, Firebase features disabled")
                        // CN区可以设置特殊状态或禁用某些功能
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Failed to initialize region config")
                    _uiState.update { it.copy(determinedRegion = "INTERNATIONAL") }
                }
            }
        }

        /**
         * 观察认证状态 - 应用性能优化
         */
        private fun observeAuthState() {
            viewModelScope.launch {
                // 🎯 性能优化：为用户状态观察添加优化，减少频繁更新
                observeCurrentUserUseCase()
                    .applyAuthStateOptimizations()
                    .collect { userResult ->
                        when (userResult) {
                            is ModernResult.Success -> {
                                val user = userResult.data
                                _uiState.update {
                                    it.copy(
                                        isAnonymousUser = user?.isAnonymous == true,
                                        anonymousAuthState =
                                            when {
                                                user == null -> AnonymousAuthState.Unauthenticated
                                                user.isAnonymous -> AnonymousAuthState.AnonymousAuthenticated
                                                else -> AnonymousAuthState.FullyAuthenticated
                                            },
                                    )
                                }
                            }

                            is ModernResult.Error -> {
                                Timber.e("观察用户状态失败: ${userResult.error.message}")
                                _uiState.update {
                                    it.copy(anonymousAuthState = AnonymousAuthState.AuthenticationError)
                                }
                            }

                            is ModernResult.Loading -> {
                                // 保持当前状态
                            }
                        }
                    }
            }
        }

        private fun processAndSetAuthState(
            result: ModernResult<*>,
            successLogMessage: String,
        ) {
            when (result) {
                is ModernResult.Success -> {
                    // 移除自动设置navigationTarget，让UI层控制导航
                    _uiState.update { it.copy(isLoading = false) }
                    Timber.d(successLogMessage)
                }

                is ModernResult.Error -> {
                    // handleError is called, which updates the error message in uiState.
                    // We also need to ensure isLoading is false.
                    _uiState.update { it.copy(isLoading = false) } // Explicitly set isLoading to false
                    handleError(result.error) // handleError will set the .error field
                }

                is ModernResult.Loading -> {
                    _uiState.update { it.copy(isLoading = true) }
                    Timber.d("Authentication operation: Loading...")
                }
            }
        }

        /**
         * 🎯 性能优化：初始化输入防抖处理
         * 避免每次输入都触发状态更新，减少重组频率
         */
        @OptIn(FlowPreview::class)
        private fun initializeInputDebouncing() {
            // Email输入防抖
            viewModelScope.launch {
                emailInputFlow
                    .debounce(AuthPerformanceConfig.ViewModel.STATE_UPDATE_DEBOUNCE_MS)
                    .distinctUntilChanged()
                    .applyAuthUiOptimizations()
                    .collect { email ->
                        _uiState.update {
                            it.copy(email = email, error = null)
                        }
                    }
            }

            // Password输入防抖
            viewModelScope.launch {
                passwordInputFlow
                    .debounce(AuthPerformanceConfig.ViewModel.STATE_UPDATE_DEBOUNCE_MS)
                    .distinctUntilChanged()
                    .applyAuthUiOptimizations()
                    .collect { password ->
                        _uiState.update {
                            it.copy(password = password, error = null)
                        }
                    }
            }
        }

        fun onEmailChange(value: String) {
            // 🎯 性能优化：使用防抖Flow而不是直接更新状态
            emailInputFlow.tryEmit(value)
        }

        fun onPasswordChange(value: String) {
            // 🎯 性能优化：使用防抖Flow而不是直接更新状态
            passwordInputFlow.tryEmit(value)
        }

        fun onConfirmPasswordChange(value: String) {
            _uiState.update {
                it.copy(
                    confirmPassword = value,
                    error = null,
                )
            }
        }

        fun toggleRegisterMode() {
            _uiState.update {
                it.copy(
                    isRegisterMode = !_uiState.value.isRegisterMode,
                    error = null,
                )
            }
        }

        fun onEmailAuthClick() {
            val emailValue = _uiState.value.email
            val passwordValue = _uiState.value.password
            val confirmPasswordValue = _uiState.value.confirmPassword

            // 验证输入
            if (!ValidationUtils.isValidEmail(emailValue)) {
                _uiState.update { it.copy(error = UiText.DynamicString("请输入有效的邮箱地址")) }
                return
            }

            if (passwordValue.length < 6) {
                _uiState.update { it.copy(error = UiText.DynamicString("密码长度至少为6位")) }
                return
            }

            if (_uiState.value.isRegisterMode && passwordValue != confirmPasswordValue) {
                _uiState.update { it.copy(error = UiText.DynamicString("密码确认不匹配")) }
                return
            }

            _uiState.update { it.copy(isLoading = true, error = null) }

            viewModelScope.launch {
                try {
                    val result =
                        if (_uiState.value.isRegisterMode) {
                            // 注册 - 使用细粒度的RegisterWithEmailUseCase
                            registerWithEmailUseCase(
                                RegisterWithEmailUseCase.Params(
                                    email = emailValue,
                                    password = passwordValue,
                                ),
                            )
                        } else {
                            // 登录 - 使用细粒度的LoginWithEmailUseCase
                            loginWithEmailUseCase(
                                LoginWithEmailUseCase.Params(
                                    email = emailValue,
                                    password = passwordValue,
                                ),
                            )
                        }

                    processAndSetAuthState(
                        result,
                        if (_uiState.value.isRegisterMode) "注册成功" else "登录成功",
                    )
                } catch (e: Exception) {
                    Timber.e(e, "邮箱认证失败")
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            error = UiText.DynamicString("认证失败，请重试"),
                        )
                    }
                }
            }
        }

        fun onPerformLogin(provider: String) {
            _uiState.update { it.copy(isLoading = true, error = null) }

            viewModelScope.launch {
                try {
                    val result =
                        when (provider) {
                            "google" -> {
                                Timber.d("开始Google登录")
                                // Google登录需要ID Token，这里暂时模拟
                                loginWithGoogleUseCase(
                                    LoginWithGoogleUseCase.Params(idToken = "mock_google_token"),
                                )
                            }

                            "anonymous" -> {
                                Timber.d("开始匿名登录")
                                loginAnonymouslyUseCase()
                            }

                            else -> {
                                throw IllegalArgumentException("未知的登录提供商: $provider")
                            }
                        }

                    processAndSetAuthState(result, "${provider}登录成功")
                } catch (e: Exception) {
                    Timber.e(e, "${provider}登录失败")
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            error = UiText.DynamicString("${provider}登录失败，请重试"),
                        )
                    }
                }
            }
        }

        private fun handleError(error: ModernDataError) {
            val errorMessage: UiText = modernErrorHandler.getUiMessage(error)
            _uiState.update { currentState ->
                currentState.copy(
                    error = errorMessage,
                    isLoading = false,
                )
            }
            Timber.e("Error handled: %s", error.message)
        }

        fun resetNavigation() {
            _uiState.update { it.copy(navigationTarget = null) }
        }

        fun clearError() {
            _uiState.update { it.copy(error = null) }
        }
    }
