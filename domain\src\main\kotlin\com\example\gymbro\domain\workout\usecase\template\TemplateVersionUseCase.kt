package com.example.gymbro.domain.workout.usecase.template

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.template.TemplateVersion
import com.example.gymbro.domain.workout.repository.TemplateRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow

/**
 * 模板版本管理UseCase - 版本控制功能
 *
 * Phase 0: UseCase架构重构 - 聚合设计
 * 管理模板版本控制相关功能
 *
 * 功能覆盖:
 * - 创建版本快照 (发布版本)
 * - 获取版本历史
 * - 版本恢复
 * - 获取最新版本
 */
@Singleton
class TemplateVersionUseCase
    @Inject
    constructor(
        private val repository: TemplateRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * 创建模板版本快照
         * 通常在"发布版本"时调用
         */
        inner class CreateVersion : ModernUseCase<CreateVersionParams, TemplateVersion>(dispatcher, logger) {
            override suspend fun execute(params: CreateVersionParams): ModernResult<TemplateVersion> {
                logger.d("创建模板版本: ${params.templateId}, 描述: ${params.description}")

                if (params.templateId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "createVersion",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID不能为空"),
                        ),
                    )
                }

                // 检查模板是否存在
                val templateResult = repository.getTemplateById(params.templateId)
                if (templateResult is ModernResult.Error) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "createVersion",
                            errorType = GlobalErrorType.Business.NotFound,
                            uiMessage = UiText.DynamicString("模板不存在"),
                        ),
                    )
                }

                val template = (templateResult as ModernResult.Success).data
                    ?: return ModernResult.Error(
                        ModernDataError(
                            operationName = "createVersion",
                            errorType = GlobalErrorType.Business.NotFound,
                            uiMessage = UiText.DynamicString("模板不存在"),
                        ),
                    )

                return when (val result = repository.createVersion(params.templateId, params.description)) {
                    is ModernResult.Success -> {
                        logger.d("成功创建版本: ${result.data.versionNumber}")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.e("创建版本失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "createVersion",
                                errorType = GlobalErrorType.System.General,
                                uiMessage = UiText.DynamicString("创建版本失败"),
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 获取模板版本历史
         */
        inner class GetVersionHistory : ModernFlowUseCase<String, List<TemplateVersion>>(dispatcher, logger) {
            override fun createFlow(templateId: String): Flow<ModernResult<List<TemplateVersion>>> {
                logger.d("获取版本历史: $templateId")

                if (templateId.isBlank()) {
                    return kotlinx.coroutines.flow.flow {
                        emit(
                            ModernResult.Error(
                                ModernDataError(
                                    operationName = "getVersionHistory",
                                    errorType = GlobalErrorType.Validation.InvalidInput,
                                    uiMessage = UiText.DynamicString("模板ID不能为空"),
                                ),
                            ),
                        )
                    }
                }

                return kotlinx.coroutines.flow.flow {
                    try {
                        repository.getVersionHistory(templateId).collect { result ->
                            when (result) {
                                is ModernResult.Success<*> -> {
                                    val data = result.data
                                    if (data is List<*> && data.all { it is TemplateVersion }) {
                                        @Suppress("UNCHECKED_CAST")
                                        emit(ModernResult.Success(data as List<TemplateVersion>))
                                    } else {
                                        emit(
                                            ModernResult.Error(
                                                ModernDataError(
                                                    operationName = "getVersionHistory",
                                                    errorType = GlobalErrorType.System.DataCorruption,
                                                    uiMessage = UiText.DynamicString("版本历史数据格式错误"),
                                                ),
                                            ),
                                        )
                                    }
                                }

                                is ModernResult.Error -> emit(result)
                                is ModernResult.Loading -> emit(ModernResult.Loading)
                            }
                        }
                    } catch (throwable: Throwable) {
                        logger.e(throwable, "获取版本历史发生异常")
                        emit(
                            ModernResult.Error(
                                ModernDataError(
                                    operationName = "getVersionHistory",
                                    errorType = GlobalErrorType.Data.General,
                                    uiMessage = UiText.DynamicString("获取版本历史失败"),
                                ),
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 获取最新版本
         * Plan和Session创建时使用
         */
        inner class GetLatestVersion : ModernUseCase<String, TemplateVersion?>(dispatcher, logger) {
            override suspend fun execute(templateId: String): ModernResult<TemplateVersion?> {
                logger.d("获取最新版本: $templateId")

                if (templateId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "getLatestVersion",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID不能为空"),
                        ),
                    )
                }

                return when (val result = repository.getLatestVersion(templateId)) {
                    is ModernResult.Success -> {
                        val versionData = result.data
                        if (versionData != null) {
                            logger.d("成功获取最新版本: v${versionData.versionNumber}")
                        } else {
                            logger.w("模板暂无版本: $templateId")
                        }
                        result
                    }

                    is ModernResult.Error -> {
                        logger.e("获取最新版本失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "getLatestVersion",
                                errorType = GlobalErrorType.System.General,
                                uiMessage = UiText.DynamicString("获取最新版本失败"),
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 恢复版本
         * 将指定版本的内容恢复到当前模板
         */
        inner class RestoreVersion : ModernUseCase<RestoreVersionParams, Unit>(dispatcher, logger) {
            override suspend fun execute(params: RestoreVersionParams): ModernResult<Unit> {
                logger.d("恢复版本: ${params.templateId} -> ${params.versionId}")

                if (params.templateId.isBlank() || params.versionId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "restoreVersion",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID和版本ID不能为空"),
                        ),
                    )
                }

                return when (val result = repository.restoreFromVersion(params.templateId, params.versionId)) {
                    is ModernResult.Success -> {
                        logger.d("成功恢复版本")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.e("恢复版本失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "restoreVersion",
                                errorType = GlobalErrorType.System.General,
                                uiMessage = UiText.DynamicString("恢复版本失败"),
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 获取模板最新版本（支持自动创建）
         * 整合原有的GetTemplateLatestVersionUseCase功能
         * Plan和Session创建时需要的核心逻辑
         */
        inner class GetLatestVersionWithAutoCreate : ModernUseCase<GetLatestVersionWithAutoCreateParams, TemplateVersion>(
            dispatcher,
            logger,
        ) {
            override suspend fun execute(
                params: GetLatestVersionWithAutoCreateParams,
            ): ModernResult<TemplateVersion> {
                logger.d("获取最新版本（自动创建）: ${params.templateId}")

                if (params.templateId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "getLatestVersionWithAutoCreate",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("模板ID不能为空"),
                        ),
                    )
                }

                // 获取最新版本
                return when (val latestResult = repository.getLatestVersion(params.templateId)) {
                    is ModernResult.Success -> {
                        // 如果有版本，直接返回
                        latestResult.data?.let { existingVersion ->
                            logger.d("获取到现有版本: v${existingVersion.versionNumber}")
                            ModernResult.Success(existingVersion)
                        } ?: run {
                            // 没有版本，判断是否需要自动创建
                            if (params.autoCreateIfNotExist) {
                                logger.d("模板无版本，自动创建初始版本")
                                // 自动创建第一个版本
                                repository.createVersion(params.templateId, "Initial version")
                            } else {
                                ModernResult.Error(
                                    ModernDataError(
                                        operationName = "getLatestVersionWithAutoCreate",
                                        errorType = GlobalErrorType.Data.NotFound,
                                        uiMessage = UiText.DynamicString("模板尚未发布任何版本"),
                                    ),
                                )
                            }
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("获取最新版本失败: ${latestResult.error}")
                        latestResult
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "getLatestVersionWithAutoCreate",
                                errorType = GlobalErrorType.System.Internal,
                                uiMessage = UiText.DynamicString("获取最新版本失败"),
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 批量获取模板最新版本
         * 整合原有的batchGetLatestVersions功能
         */
        inner class BatchGetLatestVersions : ModernUseCase<BatchGetLatestVersionsParams, Map<String, TemplateVersion>>(
            dispatcher,
            logger,
        ) {
            override suspend fun execute(
                params: BatchGetLatestVersionsParams,
            ): ModernResult<Map<String, TemplateVersion>> {
                logger.d("批量获取最新版本: ${params.templateIds.size}个模板")

                if (params.templateIds.isEmpty()) {
                    return ModernResult.Success(emptyMap())
                }

                val versionMap = mutableMapOf<String, TemplateVersion>()
                val failures = mutableListOf<String>()

                for (templateId in params.templateIds) {
                    val singleParams = GetLatestVersionWithAutoCreateParams(
                        templateId = templateId,
                        autoCreateIfNotExist = params.autoCreateIfNotExist,
                    )

                    when (val result = GetLatestVersionWithAutoCreate().invoke(singleParams)) {
                        is ModernResult.Success -> {
                            versionMap[templateId] = result.data
                        }

                        is ModernResult.Error -> {
                            failures.add("$templateId: ${result.error.message}")
                        }

                        is ModernResult.Loading -> {
                            failures.add("$templateId: 获取版本超时")
                        }
                    }
                }

                return if (failures.isNotEmpty()) {
                    logger.e("批量获取版本部分失败: ${failures.joinToString(", ")}")
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "batchGetLatestVersions",
                            errorType = GlobalErrorType.Data.General,
                            uiMessage = UiText.DynamicString("部分模板获取版本失败: ${failures.joinToString(", ")}"),
                        ),
                    )
                } else {
                    logger.d("成功批量获取${versionMap.size}个版本")
                    ModernResult.Success(versionMap)
                }
            }
        }

        // ==================== 参数类 ====================

        /**
         * 创建版本参数
         */
        data class CreateVersionParams(
            val templateId: String,
            val description: String? = null,
        )

        /**
         * 恢复版本参数
         */
        data class RestoreVersionParams(
            val templateId: String,
            val versionId: String,
        )

        /**
         * 获取最新版本（自动创建）参数
         */
        data class GetLatestVersionWithAutoCreateParams(
            val templateId: String,
            val autoCreateIfNotExist: Boolean = true,
        )

        /**
         * 批量获取最新版本参数
         */
        data class BatchGetLatestVersionsParams(
            val templateIds: List<String>,
            val autoCreateIfNotExist: Boolean = true,
        )

        // ==================== UseCase实例 ====================
        val createVersion = CreateVersion()
        val getVersionHistory = GetVersionHistory()
        val getLatestVersion = GetLatestVersion()
        val restoreVersion = RestoreVersion()
        val getLatestVersionWithAutoCreate = GetLatestVersionWithAutoCreate()
        val batchGetLatestVersions = BatchGetLatestVersions()
    }
