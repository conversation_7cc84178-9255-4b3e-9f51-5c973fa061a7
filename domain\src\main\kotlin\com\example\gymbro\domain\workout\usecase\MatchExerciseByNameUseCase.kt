package com.example.gymbro.domain.workout.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.exercise.model.integration.ExerciseMatch
import com.example.gymbro.domain.exercise.model.integration.MatchReason
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 动作名称匹配UseCase
 * 按照训练模板.md v1.0设计规范实现
 *
 * 将AI生成的自然语言动作名称（如"Bench Press", "卧推"）
 * 精确匹配到系统标准动作库中的动作ID
 */
@Singleton
class MatchExerciseByNameUseCase
    @Inject
    constructor(
        private val exerciseRepository: ExerciseRepository,
    ) {

        /**
         * 根据动作名称匹配系统动作
         *
         * 匹配策略：
         * 1. 精确匹配：直接匹配动作名称
         * 2. 模糊匹配：去除空格、大小写不敏感
         * 3. 同义词匹配：内置常见动作同义词表
         * 4. 多语言匹配：支持中英文名称匹配
         *
         * @param exerciseName AI提供的动作名称
         * @return 匹配结果，null表示未找到匹配
         */
        suspend operator fun invoke(exerciseName: String): ExerciseMatch? {
            if (exerciseName.isBlank()) return null

            // 1. 精确匹配 - 使用搜索方法进行精确匹配
            val searchResult = exerciseRepository.searchExercises(exerciseName)
            var exactMatch: ExerciseMatch? = null
            searchResult.collect { result ->
                if (result is ModernResult.Success && exactMatch == null) {
                    val exercises = result.data
                    // 查找完全匹配的动作
                    val foundExercise = exercises.find { exercise ->
                        normalizeExerciseName(exercise.name.toString()) == normalizeExerciseName(exerciseName)
                    }
                    if (foundExercise != null) {
                        exactMatch = ExerciseMatch(
                            exercise = foundExercise,
                            confidence = 1.0f,
                            matchReason = MatchReason.EXACT_NAME,
                        )
                    }
                }
            }

            if (exactMatch != null) {
                return exactMatch
            }

            // 2. 同义词匹配（优先级高于模糊搜索）
            val synonymMatch = findBySynonym(exerciseName)
            if (synonymMatch != null) {
                return synonymMatch
            }

            // 3. 模糊搜索（使用精确匹配方法）
            val fuzzySearchResult = exerciseRepository.findByExactName(exerciseName)
            var fuzzyMatch: ExerciseMatch? = null

            when (fuzzySearchResult) {
                is ModernResult.Success -> {
                    val bestMatch = fuzzySearchResult.data
                    if (bestMatch != null) {
                        val similarity = calculateSimilarity(exerciseName, bestMatch.name.toString())
                        if (similarity >= 0.7f) { // 降低阈值以提高匹配率
                            fuzzyMatch = ExerciseMatch(
                                exercise = bestMatch,
                                confidence = similarity,
                                matchReason = MatchReason.SEMANTIC,
                            )
                        }
                    }
                }

                else -> {
                    /* 搜索失败，保持 fuzzyMatch 为 null */
                }
            }

            return fuzzyMatch
        }

        /**
         * 计算字符串相似度
         * 简单的Levenshtein距离算法
         */
        private fun calculateSimilarity(str1: String, str2: String): Float {
            val s1 = normalizeExerciseName(str1)
            val s2 = normalizeExerciseName(str2)

            if (s1 == s2) return 1.0f
            if (s1.isEmpty() || s2.isEmpty()) return 0.0f

            val maxLength = maxOf(s1.length, s2.length)
            val distance = levenshteinDistance(s1, s2)
            return 1.0f - distance.toFloat() / maxLength
        }

        /**
         * 计算Levenshtein距离
         */
        private fun levenshteinDistance(s1: String, s2: String): Int {
            val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }

            for (i in 0..s1.length) dp[i][0] = i
            for (j in 0..s2.length) dp[0][j] = j

            for (i in 1..s1.length) {
                for (j in 1..s2.length) {
                    val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                    dp[i][j] = minOf(
                        dp[i - 1][j] + 1, // deletion
                        dp[i][j - 1] + 1, // insertion
                        dp[i - 1][j - 1] + cost, // substitution
                    )
                }
            }

            return dp[s1.length][s2.length]
        }

        /**
         * 标准化动作名称
         * - 转换为小写
         * - 移除多余空格
         * - 移除特殊符号
         */
        private fun normalizeExerciseName(name: String): String {
            return name.trim()
                .lowercase()
                .replace(Regex("\\s+"), " ")
                .replace(Regex("[^a-z0-9\\s\\u4e00-\\u9fff]"), "")
        }

        /**
         * 同义词匹配
         * 内置常见动作的中英文同义词表
         */
        private suspend fun findBySynonym(exerciseName: String): ExerciseMatch? {
            val synonymMap = mapOf(
                // 卧推系列
                "bench press" to listOf("卧推", "平板卧推", "barbell bench press"),
                "incline bench press" to listOf("上斜卧推", "斜板卧推"),
                "decline bench press" to listOf("下斜卧推"),

                // 深蹲系列
                "squat" to listOf("深蹲", "下蹲", "barbell squat"),
                "front squat" to listOf("前深蹲", "前蹲"),
                "goblet squat" to listOf("高脚杯深蹲"),

                // 硬拉系列
                "deadlift" to listOf("硬拉", "硬举", "barbell deadlift"),
                "romanian deadlift" to listOf("罗马尼亚硬拉", "直腿硬拉"),
                "sumo deadlift" to listOf("相扑硬拉"),

                // 引体向上系列
                "pull up" to listOf("引体向上", "正手引体向上"),
                "chin up" to listOf("反手引体向上", "underhand pull up"),

                // 推举系列
                "overhead press" to listOf("过头推举", "肩推", "军事推举"),
                "shoulder press" to listOf("肩部推举"),

                // 划船系列
                "barbell row" to listOf("杠铃划船", "俯身划船"),
                "dumbbell row" to listOf("哑铃划船"),

                // 其他常见动作
                "dips" to listOf("双杠臂屈伸", "撑体", "parallel bar dips"),
                "push up" to listOf("俯卧撑", "push-up"),
                "plank" to listOf("平板支撑", "板式支撑"),
            )

            val normalizedInput = normalizeExerciseName(exerciseName)

            for ((standardName, synonyms) in synonymMap) {
                val allVariants = synonyms + standardName
                if (allVariants.any { normalizeExerciseName(it) == normalizedInput }) {
                    // 通过标准名称查找动作
                    val searchResult = exerciseRepository.findByExactName(standardName)
                    when (searchResult) {
                        is ModernResult.Success -> {
                            val exactMatch = searchResult.data
                            if (exactMatch != null && normalizeExerciseName(exactMatch.name.toString()) == normalizeExerciseName(
                                    standardName,
                                )
                            ) {
                                return ExerciseMatch(
                                    exercise = exactMatch,
                                    confidence = 0.9f,
                                    matchReason = MatchReason.ALIAS,
                                )
                            }
                        }

                        else -> {
                            /* 搜索失败，继续下一个同义词 */
                        }
                    }
                }
            }

            return null
        }
    }
