package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.service.coach.ChatSummaryService
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * 生成并保存聊天概要用例
 *
 * 在AI回复完成后自动调用，为对话生成简洁概要并保存到数据库
 */
@Singleton
class GenerateAndSaveSummaryUseCase
    @Inject
    constructor(
        private val chatRepository: ChatRepository,
        private val chatSummaryService: ChatSummaryService,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<GenerateAndSaveSummaryUseCase.Params, Unit>(dispatcher, logger) {
        /**
         * 参数数据类
         */
        data class Params(
            val sessionId: String,
        )

        /**
         * 执行概要生成和保存
         */
        override suspend fun execute(parameters: Params): ModernResult<Unit> {
            logger.d("开始为会话生成概要: sessionId=${parameters.sessionId}")

            try {
                // 1. 获取会话信息
                val sessionResult = chatRepository.getSession(parameters.sessionId)
                when (sessionResult) {
                    is ModernResult.Success -> {
                        val session = sessionResult.data
                        if (session == null) {
                            logger.w("会话不存在: sessionId=${parameters.sessionId}")
                            return ModernResult.Success(Unit) // 会话不存在，静默返回
                        }

                        // 2. 检查是否需要生成概要
                        if (!chatSummaryService.needsSummary(session)) {
                            logger.d("会话无需生成概要: sessionId=${parameters.sessionId}")
                            return ModernResult.Success(Unit)
                        }

                        // 3. 生成概要
                        val summaryResult = chatSummaryService.generateSummary(session)
                        when (summaryResult) {
                            is ModernResult.Success -> {
                                val summary = summaryResult.data
                                logger.d("概要生成成功: sessionId=${parameters.sessionId}, summary=$summary")

                                // 4. 保存概要到数据库
                                val updateResult =
                                    chatRepository.updateSessionSummary(
                                        parameters.sessionId,
                                        summary,
                                    )
                                when (updateResult) {
                                    is ModernResult.Success -> {
                                        logger.d("概要保存成功: sessionId=${parameters.sessionId}")
                                        return ModernResult.Success(Unit)
                                    }

                                    is ModernResult.Error -> {
                                        logger.e(
                                            "概要保存失败: sessionId=${parameters.sessionId}, error=${updateResult.error}",
                                        )
                                        return updateResult
                                    }

                                    is ModernResult.Loading -> {
                                        // 不应该发生
                                        return ModernResult.Success(Unit)
                                    }
                                }
                            }

                            is ModernResult.Error -> {
                                logger.e(
                                    "概要生成失败: sessionId=${parameters.sessionId}, error=${summaryResult.error}",
                                )
                                // 概要生成失败不应该影响主流程，静默返回成功
                                return ModernResult.Success(Unit)
                            }

                            is ModernResult.Loading -> {
                                // 不应该发生
                                return ModernResult.Success(Unit)
                            }
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("获取会话失败: sessionId=${parameters.sessionId}, error=${sessionResult.error}")
                        // 获取会话失败不应该影响主流程，静默返回成功
                        return ModernResult.Success(Unit)
                    }

                    is ModernResult.Loading -> {
                        // 不应该发生
                        return ModernResult.Success(Unit)
                    }
                }
            } catch (e: Exception) {
                logger.e("生成概要过程中发生异常: sessionId=${parameters.sessionId}", e)
                // 异常不应该影响主流程，静默返回成功
                return ModernResult.Success(Unit)
            }
        }
    }
