package com.example.gymbro.features.workout.stats

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.workout.model.stats.TimeRange
import com.example.gymbro.features.workout.stats.internal.effect.StatsEffectHandler
import com.example.gymbro.features.workout.stats.internal.reducer.StatsReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

/**
 * Stats ViewModel - 统计页面的MVI 2.0视图模型
 *
 * 继承自BaseMviViewModel，实现标准的MVI 2.0架构模式。
 * 负责协调Reducer和EffectHandler，管理Stats页面的状态和副作用。
 *
 * 核心职责：
 * - 继承BaseMviViewModel的MVI 2.0能力
 * - 初始化EffectHandler和数据流
 * - 提供便利的公开方法供UI调用
 * - 管理ViewModel生命周期
 * - 处理错误和日志记录
 *
 * 设计特点：
 * - 遵循MVI 2.0标准模式
 * - 自动处理状态管理和副作用
 * - 支持状态恢复和持久化
 * - 提供丰富的调试和监控能力
 *
 * @param statsReducer Stats状态变换器
 * @param statsEffectHandler Stats副作用处理器
 * @param logger 日志记录器
 */
@HiltViewModel
internal class StatsViewModel
    @Inject
    constructor(
        private val statsReducer: StatsReducer,
        private val statsEffectHandler: StatsEffectHandler,
        private val logger: Logger,
    ) : BaseMviViewModel<StatsContract.Intent, StatsContract.State, StatsContract.Effect>(
        initialState = StatsContract.State(),
    ) {

        override val reducer: Reducer<StatsContract.Intent, StatsContract.State, StatsContract.Effect> = statsReducer

        init {
            // 初始化EffectHandler
            initializeEffectHandler()

            // 自动加载本周统计数据
            dispatch(StatsContract.Intent.LoadStats(TimeRange.WEEK))

            // 预加载常用时间范围数据
            preloadCommonRanges()

            logger.d(TAG, "StatsViewModel initialized with state: ${state.value}")
        }

        /**
         * 初始化EffectHandler并建立数据流连接
         */
        override fun initializeEffectHandler() {
            // 初始化EffectHandler
            statsEffectHandler.initialize(
                scope = handlerScope,
                intentSender = ::dispatch,
            )

            // 订阅Effect流并处理
            effect
                .onEach { currentEffect ->
                    logger.d(TAG, "Processing effect: $currentEffect")

                    statsEffectHandler.handle(
                        effect = currentEffect,
                        state = state.value,
                        scope = handlerScope,
                        dispatch = ::dispatch,
                    )
                }
                .launchIn(viewModelScope)
        }

        // ==================== 公开方法 - 供UI层调用 ====================

        /**
         * 加载指定时间范围的统计数据
         */
        fun loadStats(
            timeRange: TimeRange,
            forceRefresh: Boolean = false,
        ) {
            logger.d(TAG, "loadStats called: timeRange=$timeRange, forceRefresh=$forceRefresh")
            dispatch(
                StatsContract.Intent.LoadStats(
                    timeRange = timeRange,
                    forceRefresh = forceRefresh,
                ),
            )
        }

        /**
         * 刷新当前统计数据
         */
        fun refreshStats() {
            logger.d(TAG, "refreshStats called")
            dispatch(StatsContract.Intent.RefreshStats)
        }

        /**
         * 切换时间范围
         */
        fun selectTimeRange(timeRange: TimeRange) {
            logger.d(TAG, "selectTimeRange called: $timeRange")
            dispatch(StatsContract.Intent.SelectTimeRange(timeRange))
        }

        /**
         * 设置自定义日期范围
         */
        fun setCustomDateRange(
            startDate: kotlinx.datetime.LocalDate,
            endDate: kotlinx.datetime.LocalDate,
        ) {
            logger.d(TAG, "setCustomDateRange called: $startDate to $endDate")
            dispatch(
                StatsContract.Intent.SetCustomDateRange(
                    startDate = startDate,
                    endDate = endDate,
                ),
            )
        }

        /**
         * 切换Tab
         */
        fun selectTab(tab: StatsContract.StatsTab) {
            logger.d(TAG, "selectTab called: $tab")
            dispatch(StatsContract.Intent.SelectTab(tab))
        }

        /**
         * 显示统计详情
         */
        fun showStatsDetail(detailType: StatsContract.DetailType) {
            logger.d(TAG, "showStatsDetail called: $detailType")
            dispatch(StatsContract.Intent.ShowStatsDetail(detailType))
        }

        /**
         * 隐藏统计详情
         */
        fun hideStatsDetail() {
            logger.d(TAG, "hideStatsDetail called")
            dispatch(StatsContract.Intent.HideStatsDetail)
        }

        /**
         * 切换图表类型
         */
        fun changeChartType(chartType: StatsContract.ChartType) {
            logger.d(TAG, "changeChartType called: $chartType")
            dispatch(StatsContract.Intent.ChangeChartType(chartType))
        }

        /**
         * 切换对比显示
         */
        fun toggleComparison() {
            logger.d(TAG, "toggleComparison called")
            dispatch(StatsContract.Intent.ToggleComparison)
        }

        /**
         * 切换图表动画
         */
        fun toggleChartAnimation() {
            logger.d(TAG, "toggleChartAnimation called")
            dispatch(StatsContract.Intent.ToggleChartAnimation)
        }

        /**
         * 更改排序选项
         */
        fun changeSortOption(sortBy: StatsContract.SortOption) {
            logger.d(TAG, "changeSortOption called: $sortBy")
            dispatch(StatsContract.Intent.ChangeSortOption(sortBy))
        }

        /**
         * 更改强度筛选
         */
        fun changeIntensityFilter(filter: StatsContract.IntensityFilter) {
            logger.d(TAG, "changeIntensityFilter called: $filter")
            dispatch(StatsContract.Intent.ChangeIntensityFilter(filter))
        }

        /**
         * 切换高质量训练筛选
         */
        fun toggleHighQualityFilter() {
            logger.d(TAG, "toggleHighQualityFilter called")
            dispatch(StatsContract.Intent.ToggleHighQualityFilter)
        }

        /**
         * 清除所有筛选条件
         */
        fun clearAllFilters() {
            logger.d(TAG, "clearAllFilters called")
            dispatch(StatsContract.Intent.ClearAllFilters)
        }

        /**
         * 显示日期选择器
         */
        fun showDatePicker() {
            logger.d(TAG, "showDatePicker called")
            dispatch(StatsContract.Intent.ShowDatePicker)
        }

        /**
         * 隐藏日期选择器
         */
        fun hideDatePicker() {
            logger.d(TAG, "hideDatePicker called")
            dispatch(StatsContract.Intent.HideDatePicker)
        }

        /**
         * 显示日统计详情
         */
        fun showDailyStatsDetail(dailyStats: com.example.gymbro.domain.workout.model.stats.DailyStats) {
            logger.d(TAG, "showDailyStatsDetail called: ${dailyStats.date}")
            dispatch(StatsContract.Intent.ShowDailyStatsDetail(dailyStats))
        }

        /**
         * 导航到Session详情
         */
        fun navigateToSessionDetail(sessionId: String) {
            logger.d(TAG, "navigateToSessionDetail called: $sessionId")
            dispatch(StatsContract.Intent.NavigateToSessionDetail(sessionId))
        }

        /**
         * 导航到Exercise统计
         */
        fun navigateToExerciseStats(exerciseId: String) {
            logger.d(TAG, "navigateToExerciseStats called: $exerciseId")
            dispatch(StatsContract.Intent.NavigateToExerciseStats(exerciseId))
        }

        /**
         * 导航返回
         */
        fun navigateBack() {
            logger.d(TAG, "navigateBack called")
            dispatch(StatsContract.Intent.NavigateBack)
        }

        /**
         * 清除错误状态
         */
        fun clearError() {
            logger.d(TAG, "clearError called")
            dispatch(StatsContract.Intent.ClearError)
        }

        /**
         * 重新计算统计数据
         */
        fun recalculateStats(
            startDate: kotlinx.datetime.LocalDate,
            endDate: kotlinx.datetime.LocalDate,
        ) {
            logger.d(TAG, "recalculateStats called: $startDate to $endDate")
            dispatch(
                StatsContract.Intent.RecalculateStats(
                    startDate = startDate,
                    endDate = endDate,
                ),
            )
        }

        /**
         * 导出统计数据
         */
        fun exportStats() {
            logger.d(TAG, "exportStats called")
            dispatch(StatsContract.Intent.ExportStats)
        }

        /**
         * 显示更多选项
         */
        fun showMoreOptions() {
            logger.d(TAG, "showMoreOptions called")
            dispatch(StatsContract.Intent.ShowMoreOptions)
        }

        /**
         * 导航到开始训练
         */
        fun navigateToStartWorkout() {
            logger.d(TAG, "navigateToStartWorkout called")
            dispatch(StatsContract.Intent.NavigateToStartWorkout)
        }

        /**
         * 导入统计数据
         */
        fun importStats() {
            logger.d(TAG, "importStats called")
            dispatch(StatsContract.Intent.ImportStats)
        }

        // ==================== AI分析相关方法 ====================

        /**
         * 请求AI分析
         *
         * 🔥 【硬指标】每个session完成ID只发送一次，通过状态管理防重复
         */
        fun requestAiAnalysis(
            dailyStats: List<com.example.gymbro.domain.workout.model.stats.DailyStats>,
            timeRange: TimeRange,
            model: String = "gpt-4",
        ) {
            logger.d(TAG, "requestAiAnalysis called: 数据天数=${dailyStats.size}, 模型=$model")

            // 🔥 【硬指标】检查是否已经在分析中，防止重复请求
            if (state.value.isAnalyzing) {
                logger.w(TAG, "AI分析已在进行中，忽略重复请求")
                return
            }

            dispatch(
                StatsContract.Intent.RequestAiAnalysis(
                    dailyStats = dailyStats,
                    timeRange = timeRange,
                    model = model,
                ),
            )
        }

        /**
         * 清理AI分析状态
         */
        fun clearAiAnalysis() {
            logger.d(TAG, "clearAiAnalysis called")
            dispatch(StatsContract.Intent.ClearAiAnalysis)
        }

        /**
         * 检查是否正在进行AI分析
         */
        fun isAnalyzing(): Boolean = state.value.isAnalyzing

        /**
         * 获取当前分析ID
         */
        fun getCurrentAnalysisId(): String? = state.value.analysisId

        /**
         * 检查是否有AI分析错误
         */
        fun hasAnalysisError(): Boolean = state.value.analysisError != null

        /**
         * 获取AI分析错误信息
         */
        fun getAnalysisError(): com.example.gymbro.core.ui.text.UiText? = state.value.analysisError

        // ==================== 便利方法 ====================

        /**
         * 检查当前是否正在加载数据
         */
        fun isLoading(): Boolean = state.value.isLoading

        /**
         * 检查当前是否正在刷新数据
         */
        fun isRefreshing(): Boolean = state.value.isRefreshing

        /**
         * 检查是否有错误状态
         */
        fun hasError(): Boolean = state.value.error != null

        /**
         * 获取当前错误信息
         */
        fun getCurrentError(): com.example.gymbro.core.ui.text.UiText? = state.value.error

        /**
         * 检查是否有统计数据
         */
        fun hasStatsData(): Boolean = state.value.stats.totalSessions > 0

        /**
         * 检查数据是否新鲜（5分钟内更新）
         */
        fun isDataFresh(): Boolean = state.value.isDataFresh

        /**
         * 获取当前时间范围显示标题
         */
        fun getCurrentRangeTitle(): String = state.value.currentRangeTitle

        /**
         * 检查是否可以显示对比数据
         */
        fun canShowComparison(): Boolean = state.value.canShowComparison

        /**
         * 检查是否可以显示图表
         */
        fun canShowCharts(): Boolean = state.value.canShowCharts

        // ==================== 私有方法 ====================

        /**
         * 预加载常用时间范围数据
         */
        private fun preloadCommonRanges() {
            viewModelScope.launch {
                // 预加载本月和本年数据
                listOf(TimeRange.MONTH, TimeRange.YEAR).forEach { range ->
                    dispatch(
                        StatsContract.Intent.LoadStats(
                            timeRange = range,
                            forceRefresh = false,
                        ),
                    )
                }
            }
        }

        // 移除了不存在的方法重写

        companion object {
            private const val TAG = "StatsViewModel"
        }
    }
