package com.example.gymbro.buildlogic.detekt.documentation

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.com.intellij.psi.PsiElement
import org.jetbrains.kotlin.kdoc.psi.api.KDoc
import org.jetbrains.kotlin.psi.KtClass
import org.jetbrains.kotlin.psi.KtNamedFunction
import org.jetbrains.kotlin.psi.KtProperty

/**
 * GymBro 自定义规则：KDoc 格式化器。
 *
 * 目的：自动格式化和修复 KDoc 注释，确保符合项目标准。
 * 这个规则提供了比标准 detekt 规则更智能的修复建议。
 *
 * 修复功能：
 * 1. 自动添加缺失的句号
 * 2. 修正中英文标点符号混用
 * 3. 格式化参数和返回值文档
 * 4. 移除多余的空行和空格
 * 5. 确保文档结构的一致性
 */
class KDocFormatter(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.Style,
        "KDoc 格式需要改进，提供自动修复建议。",
        Debt.FIVE_MINS
    )

    private val endPunctuationRegex = Regex("[.!?。！？]$")
    private val chinesePunctuationMap = mapOf(
        "。" to ".",
        "！" to "!",
        "？" to "?",
        "，" to ",",
        "；" to ";",
        "：" to ":"
    )

    override fun visitClass(klass: KtClass) {
        super.visitClass(klass)
        klass.docComment?.let { kdoc ->
            processKDoc(kdoc, klass, "类")
        }
    }

    override fun visitNamedFunction(function: KtNamedFunction) {
        super.visitNamedFunction(function)
        function.docComment?.let { kdoc ->
            processKDoc(kdoc, function, "函数")
        }
    }

    override fun visitProperty(property: KtProperty) {
        super.visitProperty(property)
        property.docComment?.let { kdoc ->
            processKDoc(kdoc, property, "属性")
        }
    }

    private fun processKDoc(kdoc: KDoc, element: PsiElement, elementType: String) {
        val originalText = kdoc.text
        val formattedText = formatKDoc(originalText)

        if (originalText != formattedText) {
            val elementName = when (element) {
                is KtClass -> element.name ?: "未知类"
                is KtNamedFunction -> element.name ?: "未知函数"
                is KtProperty -> element.name ?: "未知属性"
                else -> "未知元素"
            }

            report(
                CodeSmell(
                    issue,
                    Entity.from(element),
                    "KDoc 格式需要改进 ($elementType: $elementName)。" +
                        "\n\n原始格式：\n$originalText" +
                        "\n\n建议格式：\n$formattedText" +
                        "\n\n修复说明：${getFixDescription(originalText, formattedText)}"
                )
            )
        }
    }

    private fun formatKDoc(kdocText: String): String {
        val lines = kdocText.lines().toMutableList()

        if (lines.size < 3) return kdocText // 至少需要 /** 内容 */

        // 处理每一行
        for (i in 1 until lines.size - 1) { // 跳过 /** 和 */
            val originalLine = lines[i]
            val trimmedContent = originalLine.trim().removePrefix("*").trim()

            if (trimmedContent.isNotEmpty()) {
                val formattedContent = formatKDocLine(trimmedContent)
                lines[i] = " * $formattedContent"
            }
        }

        // 移除多余的空行
        val cleanedLines = removeExcessiveEmptyLines(lines)

        return cleanedLines.joinToString("\n")
    }

    private fun formatKDocLine(content: String): String {
        var formatted = content

        // 修正中英文标点符号混用
        chinesePunctuationMap.forEach { (chinese, english) ->
            formatted = formatted.replace(chinese, english)
        }

        // 确保第一句以正确的标点符号结尾
        if (!formatted.startsWith("@") && !endPunctuationRegex.containsMatchIn(formatted)) {
            // 如果不是标签行且没有结尾标点，添加句号
            formatted = "$formatted."
        }

        // 格式化参数文档
        if (formatted.startsWith("@param")) {
            formatted = formatParamDoc(formatted)
        }

        // 格式化返回值文档
        if (formatted.startsWith("@return")) {
            formatted = formatReturnDoc(formatted)
        }

        return formatted
    }

    private fun formatParamDoc(paramLine: String): String {
        val parts = paramLine.split("\\s+".toRegex(), 3)
        if (parts.size >= 3) {
            val tag = parts[0] // @param
            val paramName = parts[1]
            val description = parts[2]

            // 确保描述以正确的标点符号结尾
            val formattedDescription = if (!endPunctuationRegex.containsMatchIn(description)) {
                "$description."
            } else {
                description
            }

            return "$tag $paramName $formattedDescription"
        }
        return paramLine
    }

    private fun formatReturnDoc(returnLine: String): String {
        val description = returnLine.removePrefix("@return").trim()
        if (description.isNotEmpty() && !endPunctuationRegex.containsMatchIn(description)) {
            return "@return $description."
        }
        return returnLine
    }

    private fun removeExcessiveEmptyLines(lines: MutableList<String>): List<String> {
        val result = mutableListOf<String>()
        var lastWasEmpty = false

        for (line in lines) {
            val isEmptyContentLine = line.trim() == "*" || line.trim().isEmpty()

            if (isEmptyContentLine) {
                if (!lastWasEmpty) {
                    result.add(" *")
                    lastWasEmpty = true
                }
            } else {
                result.add(line)
                lastWasEmpty = false
            }
        }

        return result
    }

    private fun getFixDescription(original: String, formatted: String): String {
        val fixes = mutableListOf<String>()

        if (original.contains("。") && formatted.contains(".")) {
            fixes.add("中文句号替换为英文句号")
        }

        if (!endPunctuationRegex.containsMatchIn(original.lines().getOrNull(1)?.removePrefix("*")?.trim() ?: "")) {
            fixes.add("添加缺失的句号")
        }

        val originalEmptyLines = original.lines().count { it.trim() == "*" }
        val formattedEmptyLines = formatted.lines().count { it.trim() == "*" }
        if (originalEmptyLines > formattedEmptyLines) {
            fixes.add("移除多余的空行")
        }

        return if (fixes.isNotEmpty()) {
            fixes.joinToString(", ")
        } else {
            "格式优化"
        }
    }
}
