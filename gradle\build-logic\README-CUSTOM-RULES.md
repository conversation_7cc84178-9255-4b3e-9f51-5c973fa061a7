# GymBro 自定义 Detekt 规则指南

## 📋 概述

GymBro 项目包含一套自定义的 Detekt 规则，用于强制执行项目特定的架构原则和编码规范。这些规则目前暂时禁用，但可以通过以下步骤启用。

## 🏗️ 自定义规则列表

### 1. MVI 架构规则
- **MviStateImmutability**: 确保 MVI State 类是不可变的
- 检查 State 类的所有属性都是 `val`
- 验证 State 类使用 `@Immutable` 注解

### 2. 设计系统规则
- **NoHardcodedColor**: 禁止硬编码颜色值
- **NoHardcodedDimension**: 禁止硬编码尺寸值
- 强制使用 `designSystem` 模块的 `Tokens` 系统

### 3. 日志规则
- **MaxTimberLogsPerFile**: 限制每个文件的 Timber 日志数量
- 默认阈值：每个文件最多 5 个日志调用

## 🚫 当前状态

自定义规则目前在 `gradle/build-logic/detekt.yml` 中被注释掉：

```yaml
# =========================================================================================
# GymBro 自定义规则集 - 项目特定的架构约束 (暂时禁用)
# 说明: 自定义规则需要特殊的插件注册机制，目前暂时禁用
# =========================================================================================
# gymbro-rules:
#   active: true
#   MaxTimberLogsPerFile:
#     active: true
#     threshold: 5
#   NoHardcodedDimension:
#     active: true
#   NoHardcodedColor:
#     active: true
#   MviStateImmutability:
#     active: true
```

## ✅ 启用自定义规则的步骤

### 方法 1: 独立插件模块（推荐）

1. **创建独立的 detekt 插件模块**：
   ```
   detekt-rules/
   ├── build.gradle.kts
   └── src/main/kotlin/
       └── com/example/gymbro/detekt/
           ├── GymBroRuleSetProvider.kt
           ├── MviStateImmutability.kt
           ├── NoHardcodedColor.kt
           ├── NoHardcodedDimension.kt
           └── MaxTimberLogsPerFile.kt
   ```

2. **配置 build.gradle.kts**：
   ```kotlin
   plugins {
       kotlin("jvm")
   }
   
   dependencies {
       implementation("io.gitlab.arturbosch.detekt:detekt-api:1.23.8")
       implementation("io.gitlab.arturbosch.detekt:detekt-psi-utils:1.23.8")
   }
   ```

3. **在项目中添加依赖**：
   ```kotlin
   dependencies {
       detektPlugins(project(":detekt-rules"))
   }
   ```

### 方法 2: 使用现有 build-logic

1. **修改 DetektConventionPlugin.kt**：
   ```kotlin
   private fun Project.applyDetektPlugin() {
       pluginManager.apply("io.gitlab.arturbosch.detekt")
       
       // 添加自定义规则依赖
       dependencies.add("detektPlugins", files("gradle/build-logic/build/libs/build-logic.jar"))
   }
   ```

2. **启用配置文件中的规则**：
   取消注释 `gradle/build-logic/detekt.yml` 中的 `gymbro-rules` 部分

## 🔧 验证自定义规则

启用后，运行以下命令验证：

```bash
# 运行 detekt 检查
./gradlew detekt

# 检查报告中是否包含自定义规则的检查结果
# 查看 build/reports/detekt/app-detekt.html
```

## 📊 自定义规则实现

所有自定义规则的实现位于：
- `gradle/build-logic/src/main/kotlin/com/example/gymbro/buildlogic/detekt-rules/`

### 规则注册
- `GymBroRuleSetProvider.kt`: 规则集提供者
- `META-INF/services/io.gitlab.arturbosch.detekt.api.RuleSetProvider`: 服务注册文件

## 🐛 故障排除

### 常见问题

1. **"Property 'gymbro-rules' is misspelled or does not exist"**
   - 确保自定义规则正确编译到类路径中
   - 检查 `GymBroRuleSetProvider` 的 `ruleSetId` 与配置文件匹配

2. **自定义规则不生效**
   - 验证 `META-INF/services` 文件存在且内容正确
   - 确保 detektPlugins 依赖正确配置

3. **编译错误**
   - 检查 Kotlin 和 Detekt 版本兼容性
   - 确保所有必需的 PSI 依赖可用

## 📝 注意事项

- 自定义规则需要与 Detekt 版本兼容
- 当前使用 Detekt 1.23.8，确保自定义规则与此版本兼容
- 启用自定义规则前，建议先在小范围内测试

## 🔗 相关文档

- [Detekt 自定义规则官方文档](https://detekt.dev/docs/introduction/custom-rules)
- [GymBro 架构规范](../README-DETEKT.md)
- [代码质量标准](../../docs/code-quality.md)
