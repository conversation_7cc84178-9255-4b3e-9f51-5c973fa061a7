package com.example.gymbro.domain.profile.usecase

// StringResource已移除，使用UiText.DynamicString替代
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.user.service.UserDataService
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first

/**
 * 更新用户资料的用例
 *
 * 重构后使用 UserDataService 作为统一的用户数据管理接口。
 * 该用例封装了更新当前用户资料的业务逻辑，执行用户资料验证并处理可能的错误。
 *
 * 核心变更：
 * - 使用 UserDataService 接口替代 UserAggregateRepository
 * - 遵循 Clean Architecture 依赖倒置原则
 * - 确保数据的唯一真实来源（SSOT）
 *
 * @property userDataService 用户数据服务接口
 * @property authRepository 认证仓库
 * @property dispatcher 协程调度器
 * @property logger 日志记录器
 */
@Singleton
class UpdateUserProfileUseCase
    @Inject
    constructor(
        private val userDataService: UserDataService,
        private val authRepository: AuthRepository,
        @IoDispatcher dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<UserProfile, Unit>(dispatcher, logger) {
        /**
         * 执行更新用户资料的业务逻辑
         *
         * @param parameters 要更新的用户资料对象
         * @return 成功或失败的结果
         */
        override suspend fun execute(parameters: UserProfile): ModernResult<Unit> {
            val validationResult = validateUserProfile(parameters)
            if (validationResult != null) {
                return validationResult
            }

            val currentUserIdResult = authRepository.getCurrentUserId().first()

            return when (currentUserIdResult) {
                is ModernResult.Error -> {
                    ModernResult.Error(currentUserIdResult.error)
                }

                is ModernResult.Success -> {
                    val userIdValue = currentUserIdResult.data
                    if (userIdValue.isNullOrBlank()) {
                        ModernResult.Error(
                            BusinessErrors.BusinessError.rule(
                                operationName = "updateUserProfile",
                                message = UiText.DynamicString("用户未认证"),
                                metadataMap =
                                    mapOf(
                                        StandardKeys.ERROR_SUBTYPE.key to "UNAUTHORIZED",
                                        StandardKeys.ERROR_CATEGORY.key to ErrorCategory.AUTH.name,
                                        StandardKeys.ERROR_SEVERITY.key to ErrorSeverity.ERROR.name,
                                    ),
                            ),
                        )
                    } else {
                        val profileToSave = parameters.copy(userId = userIdValue)
                        // 🔥 关键修复：使用 UserDataService 进行统一的用户数据管理
                        userDataService.updateUserProfile(profileToSave)
                    }
                }

                is ModernResult.Loading -> {
                    ModernResult.Error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "updateUserProfile",
                            message = UiText.DynamicString("用户状态不可用"),
                            recoverable = true,
                        ),
                    )
                }
            }
        }

        /**
         * 验证用户资料字段
         *
         * @param profile 要验证的用户资料
         * @return 如果验证失败，返回错误结果，否则返回null
         */
        private fun validateUserProfile(profile: UserProfile): ModernResult<Unit>? {
            // 验证显示名称长度
            if (profile.displayName?.isBlank() == true) {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "displayName",
                        operationName = "validateUserProfile",
                    ),
                )
            }

            if (profile.displayName?.length ?: 0 < 2) {
                return ModernResult.Error(
                    DataErrors.Validation.rangeError(
                        field = "displayName",
                        min = 2,
                        operationName = "validateUserProfile",
                    ),
                )
            }

            if (profile.displayName?.length ?: 0 > 50) {
                return ModernResult.Error(
                    DataErrors.Validation.rangeError(
                        field = "displayName",
                        max = 50,
                        operationName = "validateUserProfile",
                    ),
                )
            }

            // 这里可以添加更多验证，如验证生日范围、身高体重范围等

            if (profile.userId.isBlank()) {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "userId",
                        operationName = "UpdateUserProfileUseCase",
                    ),
                )
            }

            return null
        }
    }
