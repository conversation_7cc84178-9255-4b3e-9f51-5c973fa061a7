package com.example.gymbro.data.shared.sync

import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * 同步冲突解决器
 * 处理本地数据和远程数据之间的同步冲突
 */
@Singleton
class SyncConflictResolver
    @Inject
    constructor() {
        /**
         * 冲突解决策略类型
         */
        enum class ConflictResolutionStrategy {
            /**
             * 以本地数据为准
             */
            PREFER_LOCAL,

            /**
             * 以远程数据为准
             */
            PREFER_REMOTE,

            /**
             * 使用最近修改的数据
             */
            PREFER_MOST_RECENT,

            /**
             * 合并数据
             */
            MERGE,
        }

        /**
         * 冲突解决结果
         */
        sealed class ConflictResolutionResult<T> {
            /**
             * 冲突已解决
             * @param resolvedData 解决后的数据
             * @param strategy 使用的解决策略
             */
            data class Resolved<T>(
                val resolvedData: T,
                val strategy: ConflictResolutionStrategy,
            ) : ConflictResolutionResult<T>()

            /**
             * 冲突解决失败
             * @param error 错误信息
             */
            data class Failed<T>(
                val error: String,
            ) : ConflictResolutionResult<T>()

            /**
             * 无冲突
             * @param data 数据
             */
            data class NoConflict<T>(
                val data: T,
            ) : ConflictResolutionResult<T>()
        }

        /**
         * 解决数据同步冲突
         * @param localData 本地数据
         * @param remoteData 远程数据
         * @param strategy 冲突解决策略
         * @param merger 数据合并函数，用于MERGE策略
         * @param getLastModified 获取数据最后修改时间的函数，用于PREFER_MOST_RECENT策略
         * @return 冲突解决结果
         */
        fun <T> resolveConflict(
            localData: T,
            remoteData: T,
            strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.PREFER_MOST_RECENT,
            merger: (T, T) -> T = { _, remote -> remote }, // 默认合并函数使用远程数据
            getLastModified: (T) -> Long = { 0 }, // 默认获取修改时间函数返回0
        ): ConflictResolutionResult<T> {
            return try {
                // 检查是否存在冲突（如果数据相同，则无冲突）
                if (localData == remoteData) {
                    return ConflictResolutionResult.NoConflict(localData)
                }

                // 根据策略解决冲突
                val resolvedData =
                    when (strategy) {
                        ConflictResolutionStrategy.PREFER_LOCAL -> localData
                        ConflictResolutionStrategy.PREFER_REMOTE -> remoteData
                        ConflictResolutionStrategy.PREFER_MOST_RECENT -> {
                            val localModified = getLastModified(localData)
                            val remoteModified = getLastModified(remoteData)

                            if (localModified >= remoteModified) localData else remoteData
                        }

                        ConflictResolutionStrategy.MERGE -> merger(localData, remoteData)
                    }

                ConflictResolutionResult.Resolved(resolvedData, strategy)
            } catch (e: Exception) {
                Timber.e(e, "解决数据冲突失败: %s", e.message)
                ConflictResolutionResult.Failed("解决数据冲突失败: ${e.message}")
            }
        }

        /**
         * 检测数据冲突
         * @param localLastModified 本地数据最后修改时间
         * @param remoteLastModified 远程数据最后修改时间
         * @param localLastSynced 本地数据最后同步时间
         * @return 是否存在冲突
         */
        fun hasConflict(
            localLastModified: Long,
            remoteLastModified: Long,
            localLastSynced: Long,
        ): Boolean {
            // 如果本地数据上次同步后又被修改，且远程数据也被修改过
            // 则存在冲突
            return localLastModified > localLastSynced && remoteLastModified > localLastSynced
        }

        /**
         * 获取默认冲突解决策略
         * 可根据数据类型、用户设置等返回不同策略
         * @param dataType 数据类型
         * @return 冲突解决策略
         */
        fun getDefaultStrategyForType(dataType: String): ConflictResolutionStrategy =
            when (dataType) {
                "user_profile" -> ConflictResolutionStrategy.PREFER_MOST_RECENT
                "workout_session" -> ConflictResolutionStrategy.MERGE
                "exercise_set" -> ConflictResolutionStrategy.PREFER_LOCAL
                "payment" -> ConflictResolutionStrategy.PREFER_REMOTE // 支付信息通常以远程为准
                else -> ConflictResolutionStrategy.PREFER_MOST_RECENT
            }
    }
