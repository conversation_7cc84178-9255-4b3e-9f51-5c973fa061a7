package com.example.gymbro.data.autosave.service

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.autosave.PlanAutoSaveService
import com.example.gymbro.domain.workout.model.WorkoutPlanDraft
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.shared.models.workout.WorkoutPlan
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json

/**
 * Plan自动保存服务实现
 *
 * 基于DataStore的Plan数据自动保存功能
 * 支持WorkoutPlan（即时保存）、WorkoutPlanDraft（定时保存）
 * 严格遵循1秒最低间隔硬性指标
 */
@Singleton
class PlanAutoSaveServiceImpl
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
        private val dataStore: DataStore<Preferences>,
        private val planRepository: PlanRepository,
        private val json: Json,
        private val logger: Logger,
    ) : PlanAutoSaveService {

        private val activeSessions = ConcurrentHashMap<String, PlanAutoSaveSession>()

        companion object {
            const val TRAINING_PLAN_CACHE_PREFIX = "workout_plan_cache_"
            const val WORKOUT_PLAN_DRAFT_CACHE_PREFIX = "workout_plan_draft_cache_"

            // 严格遵循1秒最低间隔硬性指标
            const val IMMEDIATE_SAVE_DELAY_MS = 1000L // 即时保存的防抖延迟（1秒）
            const val INTERVAL_SAVE_DELAY_MS = 3000L // 定时保存间隔（3秒）
        }

        // ==================== WorkoutPlan 自动保存 ====================

        override suspend fun createWorkoutPlanAutoSave(planId: String, scope: CoroutineScope): ModernResult<String> {
            return createSession(
                dataType = PlanDataType.TRAINING_PLAN,
                dataId = planId,
                scope = scope,
                saveStrategy = PlanSaveStrategy.IMMEDIATE,
            )
        }

        override suspend fun startWorkoutPlanAutoSave(
            sessionId: String,
            planId: String,
            initialPlan: WorkoutPlan?,
        ): ModernResult<Unit> {
            return startSession(sessionId, initialPlan)
        }

        override suspend fun updateWorkoutPlan(sessionId: String, plan: WorkoutPlan): ModernResult<Unit> {
            return updateSessionData(sessionId, plan)
        }

        // ==================== WorkoutPlanDraft 自动保存 ====================

        override suspend fun createWorkoutPlanDraftAutoSave(draftId: String, scope: CoroutineScope): ModernResult<String> {
            return createSession(
                dataType = PlanDataType.WORKOUT_PLAN_DRAFT,
                dataId = draftId,
                scope = scope,
                saveStrategy = PlanSaveStrategy.INTERVAL,
            )
        }

        override suspend fun startWorkoutPlanDraftAutoSave(
            sessionId: String,
            draftId: String,
            initialDraft: WorkoutPlanDraft?,
        ): ModernResult<Unit> {
            return startSession(sessionId, initialDraft)
        }

        override suspend fun updateWorkoutPlanDraft(sessionId: String, draft: WorkoutPlanDraft): ModernResult<Unit> {
            return updateSessionData(sessionId, draft)
        }

        // ==================== 通用操作 ====================

        override suspend fun saveNow(sessionId: String): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "PlanAutoSaveServiceImpl.saveNow",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                session.saveNow()
                logger.d("PlanAutoSaveServiceImpl", "立即保存完成: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "立即保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.saveNow",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun pauseAutoSave(sessionId: String): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                session?.pause()
                logger.d("PlanAutoSaveServiceImpl", "自动保存已暂停: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "暂停自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.pauseAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun resumeAutoSave(sessionId: String): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                session?.resume()
                logger.d("PlanAutoSaveServiceImpl", "自动保存已恢复: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "恢复自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.resumeAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun stopAutoSave(sessionId: String): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                if (session != null) {
                    session.stop()
                    activeSessions.remove(sessionId)
                }
                logger.d("PlanAutoSaveServiceImpl", "自动保存已停止: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "停止自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.stopAutoSave",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        override suspend fun restoreWorkoutPlanFromCache(sessionId: String): ModernResult<WorkoutPlan?> {
            return restoreFromCache(sessionId)
        }

        override suspend fun restoreWorkoutPlanDraftFromCache(sessionId: String): ModernResult<WorkoutPlanDraft?> {
            return restoreFromCache(sessionId)
        }

        override suspend fun discardCache(sessionId: String): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                session?.discardCache()
                logger.d("PlanAutoSaveServiceImpl", "缓存数据已丢弃: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "丢弃缓存数据失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.discardCache",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        // ==================== 内部方法 ====================

        private suspend fun createSession(
            dataType: PlanDataType,
            dataId: String,
            scope: CoroutineScope,
            saveStrategy: PlanSaveStrategy,
        ): ModernResult<String> {
            return try {
                val sessionId = "${dataType.name.lowercase()}_session_${dataId}_${System.currentTimeMillis()}"
                logger.d("PlanAutoSaveServiceImpl", "创建${dataType.name}自动保存会话: $sessionId")

                val session = PlanAutoSaveSession(
                    sessionId = sessionId,
                    dataType = dataType,
                    dataId = dataId,
                    saveStrategy = saveStrategy,
                    scope = scope,
                    dataStore = dataStore,
                    planRepository = planRepository,
                    json = json,
                    logger = logger,
                )

                activeSessions[sessionId] = session

                logger.d("PlanAutoSaveServiceImpl", "${dataType.name}自动保存会话创建成功: $sessionId")
                ModernResult.Success(sessionId)
            } catch (e: Exception) {
                logger.e(e, "创建${dataType.name}自动保存会话失败")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.createSession",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        private suspend fun <T : Any> startSession(sessionId: String, initialData: T?): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    logger.e("PlanAutoSaveServiceImpl", "会话不存在: $sessionId")
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "PlanAutoSaveServiceImpl.startSession",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                session.start(initialData)
                logger.d("PlanAutoSaveServiceImpl", "自动保存启动成功: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "启动自动保存失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.startSession",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        private suspend fun <T : Any> updateSessionData(sessionId: String, data: T): ModernResult<Unit> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    logger.w("PlanAutoSaveServiceImpl", "会话不存在，无法更新数据: $sessionId")
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "PlanAutoSaveServiceImpl.updateSessionData",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                session.updateData(data)
                logger.d("PlanAutoSaveServiceImpl", "数据已更新: $sessionId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "更新数据失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.updateSessionData",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }

        private suspend inline fun <reified T : Any> restoreFromCache(sessionId: String): ModernResult<T?> {
            return try {
                val session = activeSessions[sessionId]
                if (session == null) {
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "PlanAutoSaveServiceImpl.restoreFromCache",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Business.NotFound,
                            category = com.example.gymbro.core.error.types.ErrorCategory.BUSINESS,
                        ),
                    )
                }

                val cachedData = session.restoreFromCache<T>()
                logger.d("PlanAutoSaveServiceImpl", "缓存数据恢复: $sessionId")
                ModernResult.Success(cachedData)
            } catch (e: Exception) {
                logger.e(e, "恢复缓存数据失败: $sessionId")
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PlanAutoSaveServiceImpl.restoreFromCache",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                        category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
                        cause = e,
                    ),
                )
            }
        }
    }

/**
 * Plan数据类型枚举
 */
private enum class PlanDataType {
    TRAINING_PLAN,
    WORKOUT_PLAN_DRAFT,
}

/**
 * Plan保存策略枚举
 */
private enum class PlanSaveStrategy {
    IMMEDIATE, // 即时保存（1秒防抖）
    INTERVAL, // 定时保存（3秒间隔）
}

/**
 * Plan自动保存会话
 *
 * 管理单个Plan数据的自动保存生命周期
 * 支持多种数据类型和保存策略
 * 严格遵循1秒最低间隔硬性指标
 */
private class PlanAutoSaveSession(
    val sessionId: String,
    private val dataType: PlanDataType,
    private val dataId: String,
    private val saveStrategy: PlanSaveStrategy,
    private val scope: CoroutineScope,
    private val dataStore: DataStore<Preferences>,
    private val planRepository: PlanRepository,
    private val json: Json,
    private val logger: Logger,
) {
    private var currentData: Any? = null
    private var isActive = false
    private var isPaused = false
    private var saveJob: kotlinx.coroutines.Job? = null

    private val cacheKey = when (dataType) {
        PlanDataType.TRAINING_PLAN -> "${PlanAutoSaveServiceImpl.TRAINING_PLAN_CACHE_PREFIX}$dataId"
        PlanDataType.WORKOUT_PLAN_DRAFT -> "${PlanAutoSaveServiceImpl.WORKOUT_PLAN_DRAFT_CACHE_PREFIX}$dataId"
    }

    fun start(initialData: Any?) {
        if (isActive) {
            logger.w("PlanAutoSaveSession", "会话已启动: $sessionId")
            return
        }

        currentData = initialData
        isActive = true
        isPaused = false

        logger.d("PlanAutoSaveSession", "${dataType.name}自动保存会话启动: $sessionId")
    }

    fun updateData(data: Any) {
        if (!isActive || isPaused) {
            logger.d("PlanAutoSaveSession", "会话未激活或已暂停，忽略更新: $sessionId")
            return
        }

        currentData = data

        // 取消之前的保存任务
        saveJob?.cancel()

        // 根据策略启动保存任务，严格遵循1秒最低间隔硬性指标
        val delayMs = when (saveStrategy) {
            PlanSaveStrategy.IMMEDIATE -> PlanAutoSaveServiceImpl.IMMEDIATE_SAVE_DELAY_MS // 1000ms
            PlanSaveStrategy.INTERVAL -> PlanAutoSaveServiceImpl.INTERVAL_SAVE_DELAY_MS // 3000ms
        }

        saveJob = scope.launch {
            delay(delayMs)
            performSave()
        }

        logger.d("PlanAutoSaveSession", "${dataType.name}数据已更新，将在${delayMs}ms后保存: $sessionId")
    }

    suspend fun saveNow() {
        if (!isActive) {
            logger.w("PlanAutoSaveSession", "会话未激活，无法立即保存: $sessionId")
            return
        }

        saveJob?.cancel()
        performSave()
    }

    fun pause() {
        isPaused = true
        saveJob?.cancel()
        logger.d("PlanAutoSaveSession", "${dataType.name}自动保存已暂停: $sessionId")
    }

    fun resume() {
        isPaused = false
        logger.d("PlanAutoSaveSession", "${dataType.name}自动保存已恢复: $sessionId")
    }

    fun stop() {
        isActive = false
        isPaused = false
        saveJob?.cancel()
        saveJob = null
        currentData = null
        logger.d("PlanAutoSaveSession", "${dataType.name}自动保存会话已停止: $sessionId")
    }

    suspend inline fun <reified T : Any> restoreFromCache(): T? {
        return try {
            val prefKey = stringPreferencesKey(cacheKey)
            val jsonString = dataStore.data.first()[prefKey]

            if (jsonString != null) {
                val data = json.decodeFromString<T>(jsonString)
                logger.d("PlanAutoSaveSession", "从缓存恢复${dataType.name}数据: $sessionId")
                data
            } else {
                logger.d("PlanAutoSaveSession", "未找到缓存的${dataType.name}数据: $sessionId")
                null
            }
        } catch (e: Exception) {
            logger.e(e, "恢复${dataType.name}缓存数据失败: $sessionId")
            null
        }
    }

    suspend fun discardCache() {
        try {
            val prefKey = stringPreferencesKey(cacheKey)
            dataStore.edit { preferences ->
                preferences.remove(prefKey)
            }
            logger.d("PlanAutoSaveSession", "${dataType.name}缓存数据已丢弃: $sessionId")
        } catch (e: Exception) {
            logger.e(e, "丢弃${dataType.name}缓存数据失败: $sessionId")
        }
    }

    private suspend fun performSave() {
        val data = currentData
        if (data == null) {
            logger.w("PlanAutoSaveSession", "没有${dataType.name}数据可保存: $sessionId")
            return
        }

        try {
            logger.d("PlanAutoSaveSession", "开始保存${dataType.name}数据: $sessionId")

            // 1. 保存到缓存
            val jsonString = when (data) {
                is WorkoutPlan -> json.encodeToString(data)
                is WorkoutPlanDraft -> json.encodeToString(data)
                else -> {
                    logger.e("PlanAutoSaveSession", "不支持的数据类型: ${data::class.simpleName}")
                    return
                }
            }

            val prefKey = stringPreferencesKey(cacheKey)
            dataStore.edit { preferences ->
                preferences[prefKey] = jsonString
            }

            // 2. 保存到Repository（持久化）
            val result = when (dataType) {
                PlanDataType.TRAINING_PLAN -> {
                    planRepository.savePlan(data as WorkoutPlan)
                }

                PlanDataType.WORKOUT_PLAN_DRAFT -> {
                    // WorkoutPlanDraft 暂时只保存到缓存，等待后续实现Repository方法
                    logger.d("PlanAutoSaveSession", "WorkoutPlanDraft暂时只保存到缓存")
                    ModernResult.Success(Unit)
                }
            }

            when (result) {
                is ModernResult.Success -> {
                    logger.d("PlanAutoSaveSession", "${dataType.name}数据保存成功: $sessionId")
                }

                is ModernResult.Error -> {
                    logger.e("PlanAutoSaveSession", "${dataType.name}数据保存失败", result.error.cause)
                }

                is ModernResult.Loading -> {
                    logger.d("PlanAutoSaveSession", "${dataType.name}数据保存中...")
                }
            }
        } catch (e: Exception) {
            logger.e(e, "保存${dataType.name}数据异常: $sessionId")
        }
    }
}
