package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.ai.config.PromptConfig
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.profile.repository.user.UserAggregateRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.shared.models.ai.RelevantAiContext
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext

/**
 * 准备AI上下文用例
 * Task2-CoachContext数据中心集成：RAG检索编排核心UseCase
 *
 * 负责向量化用户查询，并行检索相关知识和历史记录，聚合为RelevantAiContext
 * 实现RAG架构中的检索增强生成逻辑
 *
 * 核心功能：
 * 1. 向量化用户查询
 * 2. 并行检索相关训练模板（Top-K）
 * 3. 获取最近对话历史（Last-N）
 * 4. 获取用户资料摘要
 * 5. 聚合为统一的RelevantAiContext
 *
 * @property embeddingEngine 向量化引擎
 * @property chatRepository 聊天仓库，提供历史记录和向量搜索
 * @property templateRepository 训练模板仓库，提供模板向量搜索
 * @property userAggregateRepository 用户聚合仓库，提供资料摘要
 * @property getCurrentUserIdUseCase 获取当前用户ID用例
 * @property promptConfig Prompt配置，定义检索参数
 * @property ioDispatcher IO调度器
 */
class PrepareAiContextUseCase
    @Inject
    constructor(
        private val embeddingEngine: EmbeddingEngine,
        private val chatRepository: ChatRepository,
        private val templateRepository: TemplateRepository,
        private val userAggregateRepository: UserAggregateRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val promptConfig: PromptConfig,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) {

        /**
         * 执行AI上下文准备
         *
         * @param query 用户查询文本
         * @param sessionId 会话ID
         * @param contextK 相关模板检索数量（Top-K）
         * @param historyN 最近历史记录数量（Last-N）
         * @return 聚合的相关AI上下文
         */
        suspend operator fun invoke(
            query: String,
            sessionId: String,
            contextK: Int = 5,
            historyN: Int = 10,
        ): ModernResult<RelevantAiContext> = withContext(ioDispatcher) {
            try {
                // 验证输入参数
                if (query.isBlank() || sessionId.isBlank()) {
                    return@withContext ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "PrepareAiContextUseCase.invoke",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Validation.InvalidInput,
                            category = com.example.gymbro.core.error.types.ErrorCategory.VALIDATION,
                            uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("输入参数无效"),
                            severity = com.example.gymbro.core.error.types.ErrorSeverity.ERROR,
                        ),
                    )
                }

                val startTime = System.currentTimeMillis()

                // 1. 向量化用户查询
                val queryVector = embeddingEngine.embed(query)

                // 2. 并行检索相关数据
                val context = coroutineScope {
                    // 并行启动所有检索任务
                    val templatesDeferred = async {
                        // TODO: 实现向量搜索，暂时使用文本搜索
                        templateRepository.searchTemplates(query).first()
                    }

                    val historyDeferred = async {
                        chatRepository.getRecentHistory(
                            sessionId = sessionId,
                            n = historyN,
                        )
                    }

                    val profileDeferred = async {
                        // 🔥 修复: Coach模块负责从Profile获取原始数据
                        val currentUserIdResult = getCurrentUserIdUseCase().first()

                        // 🔥 [诊断1] 检查用户ID获取
                        println("🔥 [诊断1] getCurrentUserIdUseCase结果: $currentUserIdResult")
                        if (currentUserIdResult is ModernResult.Success) {
                            println("🔥 [诊断1] 获取到的userId: ${currentUserIdResult.data}")
                        } else {
                            println("🔥 [诊断1] ❌ 获取用户ID失败: $currentUserIdResult")
                        }

                        when (currentUserIdResult) {
                            is ModernResult.Success -> {
                                val userId = currentUserIdResult.data
                                if (userId != null) {
                                    // 获取原始Profile数据
                                    val profileResult = userAggregateRepository.getUserProfile(userId)
                                    when (profileResult) {
                                        is ModernResult.Success -> {
                                            val profile = profileResult.data
                                            if (profile != null) {
                                                // 返回UserProfile对象而不是摘要字符串
                                                ModernResult.Success(profile)
                                            } else {
                                                ModernResult.Success(null)
                                            }
                                        }

                                        is ModernResult.Error -> profileResult
                                        is ModernResult.Loading -> ModernResult.Success(null)
                                    }
                                } else {
                                    ModernResult.Error(
                                        com.example.gymbro.core.error.types.ModernDataError(
                                            operationName = "PrepareAiContextUseCase.getProfile",
                                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Auth.Unauthorized,
                                            category = com.example.gymbro.core.error.types.ErrorCategory.AUTH,
                                            uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString(
                                                "用户未登录",
                                            ),
                                            severity = com.example.gymbro.core.error.types.ErrorSeverity.ERROR,
                                        ),
                                    )
                                }
                            }

                            is ModernResult.Error -> currentUserIdResult
                            is ModernResult.Loading -> ModernResult.Error(
                                com.example.gymbro.core.error.types.ModernDataError(
                                    operationName = "PrepareAiContextUseCase.getCurrentUserId",
                                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.Internal,
                                    category = com.example.gymbro.core.error.types.ErrorCategory.SYSTEM,
                                    uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("获取用户ID中"),
                                    severity = com.example.gymbro.core.error.types.ErrorSeverity.ERROR,
                                ),
                            )
                        }
                    }

                    // 等待所有任务完成并处理结果
                    val templatesResult = templatesDeferred.await()
                    val historyResult = historyDeferred.await()
                    val profileResult = profileDeferred.await()

                    // 处理检索结果
                    val relevantTemplates = when (templatesResult) {
                        is ModernResult.Success -> templatesResult.data
                        is ModernResult.Error -> emptyList()
                        is ModernResult.Loading -> emptyList()
                    }

                    val recentHistory = when (historyResult) {
                        is ModernResult.Success -> historyResult.data
                        is ModernResult.Error -> emptyList()
                        is ModernResult.Loading -> emptyList()
                    }

                    // 🔥 增强调试：详细检查profileResult
                    println("🔥 PrepareAiContextUseCase: profileResult类型 = ${profileResult::class.simpleName}")
                    val userProfileData = when (profileResult) {
                        is ModernResult.Success -> {
                            println("🔥 profileResult.Success: data = ${profileResult.data}")
                            profileResult.data
                        }

                        is ModernResult.Error -> {
                            println("🔥 profileResult.Error: ${profileResult.error}")
                            println("🔥 错误详情: ${profileResult.error.message}")
                            println("🔥 错误原因: ${profileResult.error.cause}")
                            null
                        }

                        is ModernResult.Loading -> {
                            println("🔥 profileResult.Loading")
                            null
                        }
                    }

                    // 🔥 门槛1：构建用户档案 - 永远返回有效的默认基础信息
                    val userProfile = com.example.gymbro.shared.models.ai.AiUserProfile(
                        gender = userProfileData?.gender?.getDisplayName() ?: "未知性别",
                        age = userProfileData?.let { profile ->
                            // 暂时使用默认年龄，因为没有出生日期信息
                            25 // 默认年龄
                        } ?: 25, // 🔥 改进：使用合理的默认年龄而不是0
                        height = userProfileData?.height?.toInt() ?: 170, // 🔥 改进：使用合理的默认身高
                        weight = userProfileData?.weight?.toInt() ?: 65, // 🔥 改进：使用合理的默认体重
                        experience = userProfileData?.fitnessLevel?.getDisplayName() ?: "健身新手",
                        bodyFatPercentage = null, // 暂时没有体脂率数据
                    )

                    // 🔥 [诊断3] 检查数据转换
                    println("🔥 [诊断3] 构建的AiUserProfile:")
                    println("🔥 [诊断3] - gender: ${userProfile.gender}")
                    println("🔥 [诊断3] - age: ${userProfile.age}")
                    println("🔥 [诊断3] - height: ${userProfile.height}")
                    println("🔥 [诊断3] - weight: ${userProfile.weight}")
                    println("🔥 [诊断3] - experience: ${userProfile.experience}")
                    println("🔥 [诊断3] - bodyFatPercentage: ${userProfile.bodyFatPercentage}")
                    if (userProfileData == null) {
                        println("🔥 [诊断3] ⚠️ 使用了默认值，因为userProfileData为null")
                    } else {
                        println("🔥 [诊断3] ✅ 使用了真实的用户数据")
                    }

                    // 🔥 增强调试：详细检查用户资料数据获取情况
                    println("🔥 PrepareAiContextUseCase: 用户资料数据获取结果")
                    println("🔥 userProfileData: $userProfileData")
                    if (userProfileData != null) {
                        println("🔥 用户数据详情:")
                        println("  - userId: ${userProfileData.userId}")
                        println("  - displayName: ${userProfileData.displayName}")
                        println("  - email: ${userProfileData.email}")
                        println("  - gender: ${userProfileData.gender}")
                        println("  - height: ${userProfileData.height}")
                        println("  - weight: ${userProfileData.weight}")
                        println("  - fitnessLevel: ${userProfileData.fitnessLevel}")
                        println("  - fitnessGoals: ${userProfileData.fitnessGoals}")
                    } else {
                        println("🔥 ⚠️ 用户资料数据为空！这将导致prompt中缺少个人信息")
                    }
                    println("🔥 构建的AiUserProfile: $userProfile")

                    // 转换训练模板
                    val convertedTemplates = relevantTemplates.map { template ->
                        com.example.gymbro.shared.models.ai.WorkoutTemplate(
                            id = template.id,
                            name = template.name,
                            description = template.description ?: "",
                            targetMuscles = template.targetMuscleGroups ?: emptyList(),
                            difficultyLevel = template.difficulty?.toString() ?: "未知",
                            exercises = emptyList(), // TODO: 转换exercises
                            similarity = 1.0f,
                        )
                    }

                    // 转换对话历史
                    val convertedHistory = recentHistory.map { message ->
                        when (message) {
                            is com.example.gymbro.domain.coach.model.CoachMessage.UserMessage -> {
                                com.example.gymbro.shared.models.ai.ConversationMessage(
                                    sender = "用户",
                                    content = message.content,
                                    timestamp = message.timestamp,
                                )
                            }

                            is com.example.gymbro.domain.coach.model.CoachMessage.AiMessage -> {
                                com.example.gymbro.shared.models.ai.ConversationMessage(
                                    sender = "教练",
                                    content = message.content,
                                    timestamp = message.timestamp,
                                )
                            }
                        }
                    }

                    // 构建用户目标 - 修复：使用实际的健身目标数据
                    val userGoals = userProfileData?.fitnessGoals?.map { goal ->
                        com.example.gymbro.shared.models.ai.FitnessGoal(
                            type = goal.name,
                            description = goal.getDisplayName(),
                            targetDate = null, // 暂时没有目标日期数据
                        )
                    } ?: emptyList()

                    // 构建用户限制 - 从训练日推导可用时间限制
                    val userLimitations = mutableListOf<String>().apply {
                        userProfileData?.let { profile ->
                            if (profile.workoutDays.isEmpty()) {
                                add("未设置训练日")
                            } else if (profile.workoutDays.size < 3) {
                                add("训练频率较低（每周${profile.workoutDays.size}天）")
                            }

                            if (profile.height == null || profile.weight == null) {
                                add("缺少身体数据")
                            }

                            if (profile.fitnessGoals.isEmpty()) {
                                add("未设置明确的健身目标")
                            }
                        }
                    }

                    // 构建相关AI上下文
                    RelevantAiContext(
                        userProfile = userProfile,
                        relevantTemplates = convertedTemplates,
                        recentHistory = convertedHistory,
                        relevantHistory = emptyList(),
                        userGoals = userGoals,
                        userLimitations = userLimitations,
                    )
                }

                ModernResult.Success(context)
            } catch (e: Exception) {
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "PrepareAiContextUseCase.invoke",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.Internal,
                        category = com.example.gymbro.core.error.types.ErrorCategory.SYSTEM,
                        uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("准备AI上下文失败"),
                        severity = com.example.gymbro.core.error.types.ErrorSeverity.ERROR,
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 🔥 Coach模块内部方法: 生成Profile摘要用于AI上下文
         * 这是从Profile模块转移过来的职责，确保RAG逻辑集中在Coach模块
         */
        private fun generateProfileSummaryForAI(
            profile: com.example.gymbro.domain.profile.model.user.UserProfile,
        ): String {
            return buildString {
                // 基本信息
                profile.displayName?.let { append("用户昵称: $it\n") }

                // 身体信息
                append("性别: ${profile.gender.getDisplayName()}\n")
                profile.height?.let { append("身高: ${it.toInt()}cm\n") }
                profile.weight?.let { append("体重: ${it}kg\n") }

                // 健身信息
                append("健身水平: ${profile.fitnessLevel.getDisplayName()}\n")

                if (profile.fitnessGoals.isNotEmpty()) {
                    append("训练目标: ${profile.fitnessGoals.joinToString("、") { it.getDisplayName() }}\n")
                }

                if (profile.workoutDays.isNotEmpty()) {
                    append("训练日: ${profile.workoutDays.joinToString("、") { it.getDisplayName() }}\n")
                }

                // 其他偏好
                if (profile.allowPartnerMatching) {
                    append("允许健身伙伴匹配\n")
                }

                profile.bio?.takeIf { it.isNotBlank() }?.let {
                    append("个人简介: $it")
                }
            }.trim()
        }

        /**
         * 获取上下文准备的性能统计
         * 用于监控和优化
         */
        suspend fun getPerformanceStats(): ContextPreparationStats {
            // TODO: 实现性能统计收集
            return ContextPreparationStats(
                averageRetrievalTimeMs = 0L,
                templateHitRate = 0.0,
                historyAvailabilityRate = 0.0,
                profileCompletionRate = 0.0,
            )
        }
    }

// 🔥 Coach模块内部扩展函数: Profile显示名称生成（从Profile模块转移）
private fun com.example.gymbro.domain.profile.model.user.enums.Gender.getDisplayName(): String =
    when (this) {
        com.example.gymbro.domain.profile.model.user.enums.Gender.MALE -> "男性"
        com.example.gymbro.domain.profile.model.user.enums.Gender.FEMALE -> "女性"
        com.example.gymbro.domain.profile.model.user.enums.Gender.OTHER -> "其他"
        com.example.gymbro.domain.profile.model.user.enums.Gender.PREFER_NOT_TO_SAY -> "不愿透露"
        com.example.gymbro.domain.profile.model.user.enums.Gender.UNSPECIFIED -> "未设置"
    }

private fun com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.getDisplayName(): String =
    when (this) {
        com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.BEGINNER -> "初学者"
        com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.INTERMEDIATE -> "中级"
        com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.ADVANCED -> "高级"
        com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.EXPERT -> "专家"
        com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.PRO -> "专业"
        com.example.gymbro.domain.profile.model.user.enums.FitnessLevel.UNSPECIFIED -> "未设置"
    }

private fun com.example.gymbro.domain.profile.model.user.WorkoutDay.getDisplayName(): String =
    when (this) {
        com.example.gymbro.domain.profile.model.user.WorkoutDay.MONDAY -> "周一"
        com.example.gymbro.domain.profile.model.user.WorkoutDay.TUESDAY -> "周二"
        com.example.gymbro.domain.profile.model.user.WorkoutDay.WEDNESDAY -> "周三"
        com.example.gymbro.domain.profile.model.user.WorkoutDay.THURSDAY -> "周四"
        com.example.gymbro.domain.profile.model.user.WorkoutDay.FRIDAY -> "周五"
        com.example.gymbro.domain.profile.model.user.WorkoutDay.SATURDAY -> "周六"
        com.example.gymbro.domain.profile.model.user.WorkoutDay.SUNDAY -> "周日"
    }

/**
 * 上下文准备性能统计
 * 用于监控PrepareAiContextUseCase的性能表现
 */
data class ContextPreparationStats(
    val averageRetrievalTimeMs: Long,
    val templateHitRate: Double,
    val historyAvailabilityRate: Double,
    val profileCompletionRate: Double,
) {
    val isPerformant: Boolean
        get() = averageRetrievalTimeMs < 200L

    val hasGoodQuality: Boolean
        get() = templateHitRate > 0.5 && historyAvailabilityRate > 0.7
}
