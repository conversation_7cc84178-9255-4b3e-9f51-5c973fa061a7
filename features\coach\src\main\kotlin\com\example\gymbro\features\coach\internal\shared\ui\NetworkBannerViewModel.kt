package com.example.gymbro.features.coach.internal.shared.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.network.monitor.NetworkEvent
import com.example.gymbro.core.network.monitor.NetworkWatchdog
import com.example.gymbro.features.coach.shared.utils.applyNetworkOptimizations
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 网络Banner ViewModel - D2阶段
 *
 * 管理网络状态Banner的显示逻辑，遵循MVI架构模式
 * 订阅NetworkWatchdog事件流，控制Banner的显示/隐藏
 */
@HiltViewModel
class NetworkBannerViewModel
    @Inject
    constructor(
        private val networkWatchdog: NetworkWatchdog,
    ) : ViewModel() {

        // UI状态
        private val _uiState = MutableStateFlow(NetworkBannerUiState())
        val uiState: StateFlow<NetworkBannerUiState> = _uiState.asStateFlow()

        init {
            Timber.d("🎯 NetworkBannerViewModel初始化")
            // 🎯 不再重复启动NetworkWatchdog，由AiCoachViewModel统一管理
            observeNetworkEvents()
        }

        /**
         * 观察网络事件 - 应用全局性能优化配置
         */
        private fun observeNetworkEvents() {
            viewModelScope.launch {
                networkWatchdog.networkEvents
                    .filterNotNull()
                    .applyNetworkOptimizations() // 🎯 使用全局网络优化配置
                    .collect { event ->
                        handleNetworkEvent(event)
                    }
            }
        }

        /**
         * 处理网络事件
         */
        private fun handleNetworkEvent(event: NetworkEvent) {
            Timber.d("🎯 处理网络事件: ${event::class.simpleName}")

            when (event) {
                is NetworkEvent.Connected -> {
                    // 网络连接成功，隐藏Banner
                    _uiState.value = _uiState.value.copy(
                        isVisible = false,
                        bannerType = NetworkBannerType.SUCCESS,
                        message = "网络连接已恢复",
                    )

                    // 短暂显示成功消息后自动隐藏
                    viewModelScope.launch {
                        delay(2000) // 2秒后隐藏
                        _uiState.value = _uiState.value.copy(isVisible = false)
                    }
                }

                is NetworkEvent.Losing -> {
                    // 网络不稳定，显示警告Banner
                    _uiState.value = _uiState.value.copy(
                        isVisible = true,
                        bannerType = NetworkBannerType.WARNING,
                        message = "网络连接不稳定，功能将自动降级",
                        canDismiss = true, // 允许手动关闭
                        isDismissed = false,
                    )
                }

                is NetworkEvent.Lost -> {
                    // 网络连接丢失，显示错误Banner
                    val message = when (event.reason) {
                        com.example.gymbro.core.network.monitor.NetworkLossReason.AIRPLANE_MODE -> "飞行模式已开启"
                        com.example.gymbro.core.network.monitor.NetworkLossReason.NO_SIGNAL -> "无网络信号"
                        com.example.gymbro.core.network.monitor.NetworkLossReason.WIFI_DISCONNECTED -> "WiFi连接已断开"
                        com.example.gymbro.core.network.monitor.NetworkLossReason.CELLULAR_UNAVAILABLE -> "移动网络不可用"
                        else -> "网络连接已断开"
                    }

                    _uiState.value = _uiState.value.copy(
                        isVisible = true,
                        bannerType = NetworkBannerType.ERROR,
                        message = message,
                        canDismiss = false, // 网络断开时不允许关闭
                        isDismissed = false,
                    )
                }

                is NetworkEvent.Restored -> {
                    // 网络恢复，显示成功Banner
                    _uiState.value = _uiState.value.copy(
                        isVisible = true,
                        bannerType = NetworkBannerType.SUCCESS,
                        message = "网络连接已恢复",
                        canDismiss = true,
                        isDismissed = false,
                    )

                    // 3秒后自动隐藏
                    viewModelScope.launch {
                        delay(3000)
                        _uiState.value = _uiState.value.copy(isVisible = false)
                    }
                }

                is NetworkEvent.Bandwidth -> {
                    // 带宽变化事件 - 按照websock+Okhttp.md核心实现方案
                    if (event.isLowBandwidth()) {
                        // 低带宽警告 (<300kbps)
                        _uiState.value = _uiState.value.copy(
                            isVisible = true,
                            bannerType = NetworkBannerType.WARNING,
                            message = "当前网络较差(${event.kbps}kbps)，将自动降级",
                            canDismiss = true,
                            isDismissed = false,
                        )

                        Timber.w("📊 低带宽警告: ${event.kbps}kbps < 300kbps")
                    } else {
                        // 带宽恢复正常，如果当前显示的是低带宽警告则隐藏
                        val currentState = _uiState.value
                        if (currentState.isVisible &&
                            currentState.bannerType == NetworkBannerType.WARNING &&
                            currentState.message.contains("网络较差")
                        ) {
                            _uiState.value = currentState.copy(isVisible = false)
                            Timber.d("📊 带宽恢复正常: ${event.kbps}kbps，隐藏低带宽警告")
                        }
                    }
                }
            }
        }

        /**
         * 用户手动关闭Banner
         */
        fun dismissBanner() {
            val currentState = _uiState.value
            if (currentState.canDismiss) {
                Timber.d("🎯 用户手动关闭Banner")
                _uiState.value = currentState.copy(
                    isVisible = false,
                    isDismissed = true,
                )
            }
        }

        /**
         * 应用进入前台时刷新网络状态
         * 🔥 【架构修正】只触发状态刷新，不管理网络监控生命周期
         */
        fun onAppResumed() {
            Timber.d("🎯 应用恢复，刷新网络状态")
            // 🔥 移除resumeWatching()，网络监控由应用层管理
            networkWatchdog.refreshNetworkState()
        }

        /**
         * 应用进入后台时的处理
         * 🔥 【架构修正】移除网络监控暂停，由应用层统一管理
         */
        fun onAppPaused() {
            Timber.d("🎯 应用暂停")
            // 🔥 移除pauseWatching()，网络监控由应用层管理
            // networkWatchdog.pauseWatching() - 已移除
        }

        override fun onCleared() {
            super.onCleared()
            Timber.d("🎯 NetworkBannerViewModel清理")
            // 🔥 【架构修正】移除网络监控停止，网络层由应用层统一管理
            // networkWatchdog.stopWatching() - 已移除，网络监控由AppStartupManager管理
        }
    }

/**
 * 网络Banner UI状态
 */
data class NetworkBannerUiState(
    val isVisible: Boolean = false,
    val bannerType: NetworkBannerType = NetworkBannerType.INFO,
    val message: String = "",
    val canDismiss: Boolean = true,
    val isDismissed: Boolean = false,
)

/**
 * 网络Banner类型
 */
enum class NetworkBannerType {
    SUCCESS, // 成功/恢复
    WARNING, // 警告/不稳定
    ERROR, // 错误/断开
    INFO, // 信息
}
