package com.example.gymbro.domain.usecase.workout.feedback

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.model.ai.*
import com.example.gymbro.domain.profile.repository.user.UserAggregateRepository
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.session.WorkoutSession
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.SessionRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

/**
 * 获取训练上下文UseCase
 * 负责收集用户的训练历史、偏好和当前状态信息，为AI提供上下文
 */
@Singleton
class GetWorkoutContextUseCase
    @Inject
    constructor(
        private val sessionRepository: SessionRepository,
        private val userAggregateRepository: UserAggregateRepository,
        private val planRepository: PlanRepository,
        private val logger: Logger,
    ) {
        // 缓存机制
        private val contextCache = mutableMapOf<String, Pair<WorkoutContext, Instant>>()
        private val cacheExpirationMinutes = 15

        /**
         * 获取用户的训练上下文信息
         * @param userId 用户ID
         * @return 训练上下文信息或错误信息
         */
        suspend operator fun invoke(userId: String): ModernResult<WorkoutContext> {
            return try {
                logger.d("开始获取训练上下文: userId=$userId")

                // 验证用户ID
                if (userId.isBlank()) {
                    logger.w("用户ID为空")
                    return ModernResult.error(
                        DataErrors.Validation.required(
                            field = "userId",
                            message = UiText.DynamicString("用户ID不能为空"),
                        ),
                    )
                }

                // 检查缓存
                getCachedContext(userId)?.let { cachedContext ->
                    logger.d("返回缓存的训练上下文: userId=$userId")
                    return ModernResult.success(cachedContext)
                }

                // 并行获取各种上下文信息
                val recentWorkouts = getRecentWorkouts(userId)
                val userPreferences = getUserPreferences(userId)
                val performanceMetrics = getPerformanceMetrics(userId)
                val currentProgram = getCurrentProgram(userId)

                // 构建训练上下文
                val workoutContext =
                    WorkoutContext(
                        userId = userId,
                        recentWorkouts =
                            when (recentWorkouts) {
                                is ModernResult.Success -> recentWorkouts.data
                                is ModernResult.Error -> emptyList()
                                is ModernResult.Loading -> emptyList()
                            },
                        favoriteExercises =
                            when (userPreferences) {
                                is ModernResult.Success -> userPreferences.data.favoriteExercises
                                is ModernResult.Error -> emptyList()
                                is ModernResult.Loading -> emptyList()
                            },
                        preferredMuscleGroups =
                            when (userPreferences) {
                                is ModernResult.Success -> userPreferences.data.preferredMuscleGroups
                                is ModernResult.Error -> emptyList()
                                is ModernResult.Loading -> emptyList()
                            },
                        fitnessGoals =
                            when (userPreferences) {
                                is ModernResult.Success -> userPreferences.data.fitnessGoals
                                is ModernResult.Error -> emptyList()
                                is ModernResult.Loading -> emptyList()
                            },
                        availableEquipment =
                            when (userPreferences) {
                                is ModernResult.Success -> userPreferences.data.availableEquipment
                                is ModernResult.Error -> emptyList()
                                is ModernResult.Loading -> emptyList()
                            },
                        workoutExperience =
                            when (userPreferences) {
                                is ModernResult.Success -> userPreferences.data.workoutExperience
                                is ModernResult.Error ->
                                    WorkoutExperience(
                                        level = ExperienceLevel.BEGINNER,
                                        yearsWorkout = 0,
                                        displayName = "初学者",
                                    )

                                is ModernResult.Loading ->
                                    WorkoutExperience(
                                        level = ExperienceLevel.BEGINNER,
                                        yearsWorkout = 0,
                                        displayName = "初学者",
                                    )
                            },
                        currentProgram =
                            when (currentProgram) {
                                is ModernResult.Success -> currentProgram.data
                                is ModernResult.Error -> null
                                is ModernResult.Loading -> null
                            },
                        physicalLimitations =
                            when (userPreferences) {
                                is ModernResult.Success -> userPreferences.data.physicalLimitations
                                is ModernResult.Error -> emptyList()
                                is ModernResult.Loading -> emptyList()
                            },
                        timeConstraints =
                            when (userPreferences) {
                                is ModernResult.Success -> userPreferences.data.timeConstraints
                                is ModernResult.Error ->
                                    TimeConstraints(
                                        maxSessionDuration = 60,
                                        preferredDays = listOf("周一", "周三", "周五"),
                                        availableTimeSlots = emptyList(),
                                    )

                                is ModernResult.Loading ->
                                    TimeConstraints(
                                        maxSessionDuration = 60,
                                        preferredDays = listOf("周一", "周三", "周五"),
                                        availableTimeSlots = emptyList(),
                                    )
                            },
                        performanceMetrics =
                            when (performanceMetrics) {
                                is ModernResult.Success -> performanceMetrics.data
                                is ModernResult.Error ->
                                    PerformanceMetrics(
                                        averageRpe = 7.0f,
                                        consistencyScore = 0.8f,
                                        strengthGains = mapOf("深蹲" to 10.0f, "卧推" to 8.0f),
                                        volumeProgression = 15.0f,
                                    )

                                is ModernResult.Loading ->
                                    PerformanceMetrics(
                                        averageRpe = 7.0f,
                                        consistencyScore = 0.8f,
                                        strengthGains = mapOf("深蹲" to 10.0f, "卧推" to 8.0f),
                                        volumeProgression = 15.0f,
                                    )
                            },
                    )

                val recentWorkoutsCount =
                    when (recentWorkouts) {
                        is ModernResult.Success -> recentWorkouts.data.size
                        is ModernResult.Error -> 0
                        is ModernResult.Loading -> 0
                    }

                // 缓存结果
                cacheContext(userId, workoutContext)

                logger.d("训练上下文获取成功: userId=$userId, recentWorkouts=$recentWorkoutsCount")
                ModernResult.success(workoutContext)
            } catch (e: Exception) {
                logger.e(e, "获取训练上下文时发生异常: userId=$userId")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "getWorkoutContext",
                        message = UiText.DynamicString("获取训练上下文时发生异常: ${e.message}"),
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }
        }

        /**
         * 获取用户最近的训练记录
         */
        private suspend fun getRecentWorkouts(userId: String): ModernResult<List<RecentWorkout>> =
            try {
                logger.d("获取最近训练记录: userId=$userId")

                // TODO: 需要适配新的SessionRepository接口
                // 暂时返回空列表
                ModernResult.success(emptyList<RecentWorkout>())
            /*
            when (val sessionsResult = sessionRepository.observeSessionHistory(userId).first()) {
                is ModernResult.Success -> {
                    val sessions = sessionsResult.data

                    // 过滤最近30天的训练记录
                    val thirtyDaysAgo =
                        Clock.System.now().minus(
                            30,
                            DateTimeUnit.DAY,
                            TimeZone.currentSystemDefault(),
                        )
                    val recentSessions =
                        sessions.filter { session ->
                            (session.startTime ?: 0L) >= thirtyDaysAgo.toEpochMilliseconds()
                        }

                    val recentWorkouts =
                        recentSessions.map { session ->
                            RecentWorkout(
                                templateName = session.name ?: "自由训练",
                                completedAt =
                                    Instant.fromEpochMilliseconds(
                                        session.endTime ?: session.startTime ?: 0L,
                                    ),
                                completedRate = calculateCompletionRate(session),
                                averageRpe = calculateAverageRpe(session),
                                durationMinutes = calculateDurationMinutes(session),
                            )
                        }
                    ModernResult.success(recentWorkouts)
                }
                is ModernResult.Error -> ModernResult.Error(sessionsResult.error)
                is ModernResult.Loading -> ModernResult.Loading
            }
             */
            } catch (e: Exception) {
                logger.e(e, "获取最近训练记录失败: userId=$userId")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "getRecentWorkouts",
                        message = UiText.DynamicString("获取最近训练记录失败: ${e.message}"),
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }

        /**
         * 获取用户偏好设置
         */
        private suspend fun getUserPreferences(userId: String): ModernResult<UserPreferences> =
            try {
                logger.d("获取用户偏好设置: userId=$userId")

                // TODO: 实现从UserAggregateRepository获取用户偏好
                // val profile = userAggregateRepository.getUserProfile(userId)

                // 暂时返回默认偏好设置
                val defaultPreferences =
                    UserPreferences(
                        favoriteExercises = listOf("深蹲", "卧推", "硬拉", "引体向上"),
                        preferredMuscleGroups = listOf("胸部", "背部", "腿部"),
                        fitnessGoals = listOf("增肌", "力量提升"),
                        availableEquipment = listOf("杠铃", "哑铃", "器械"),
                        workoutExperience =
                            WorkoutExperience(
                                level = ExperienceLevel.INTERMEDIATE,
                                yearsWorkout = 2,
                                displayName = "中级",
                            ),
                        physicalLimitations = emptyList(),
                        timeConstraints =
                            TimeConstraints(
                                maxSessionDuration = 60,
                                preferredDays = listOf("周一", "周三", "周五"),
                                availableTimeSlots = listOf("morning", "evening"),
                            ),
                    )

                ModernResult.success(defaultPreferences)
            } catch (e: Exception) {
                logger.e(e, "获取用户偏好设置失败: userId=$userId")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "getUserPreferences",
                        message = UiText.DynamicString("获取用户偏好设置失败: ${e.message}"),
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }

        /**
         * 获取用户表现指标
         */
        private suspend fun getPerformanceMetrics(userId: String): ModernResult<PerformanceMetrics> =
            try {
                logger.d("获取用户表现指标: userId=$userId")

                // TODO: 实现从WorkoutRepository计算表现指标

                // 暂时返回默认指标
                val defaultMetrics =
                    PerformanceMetrics(
                        averageRpe = 7.2f,
                        consistencyScore = 0.85f,
                        strengthGains =
                            mapOf(
                                "深蹲" to 15.5f,
                                "卧推" to 12.0f,
                                "硬拉" to 18.0f,
                            ),
                        volumeProgression = 20.0f,
                    )

                ModernResult.success(defaultMetrics)
            } catch (e: Exception) {
                logger.e(e, "获取用户表现指标失败: userId=$userId")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "getPerformanceMetrics",
                        message = UiText.DynamicString("获取用户表现指标失败: ${e.message}"),
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }

        /**
         * 获取用户当前训练计划
         */
        private suspend fun getCurrentProgram(userId: String): ModernResult<CurrentProgram?> =
            try {
                logger.d("获取当前训练计划: userId=$userId")

                // TODO: 需要适配新的PlanRepository接口
                // 暂时返回null
                ModernResult.success(null)
            /*
            when (val planResult = planRepository.getActivePlan().first()) {
                is ModernResult.Success -> {
                    val plan = planResult.data
                    if (plan != null) {
                        val currentProgram =
                            CurrentProgram(
                                name = plan.name.toString(),
                                startDate = Instant.fromEpochMilliseconds(plan.createdAt ?: 0L),
                                durationWeeks = calculateDurationWeeks(plan),
                                currentWeek = calculateCurrentWeek(plan),
                            )
                        ModernResult.success(currentProgram)
                    } else {
                        ModernResult.success(null)
                    }
                }
                is ModernResult.Error -> ModernResult.Error(planResult.error)
                is ModernResult.Loading -> ModernResult.Loading
            }
             */
            } catch (e: Exception) {
                logger.e(e, "获取当前训练计划失败: userId=$userId")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "getCurrentProgram",
                        message = UiText.DynamicString("获取当前训练计划失败: ${e.message}"),
                        metadataMap = mapOf("userId" to userId),
                    ),
                )
            }

        /**
         * 缓存相关方法
         */
        private fun getCachedContext(userId: String): WorkoutContext? {
            val cached = contextCache[userId]
            return if (cached != null) {
                val (context, timestamp) = cached
                val now = Clock.System.now()
                val duration = now.minus(timestamp)
                if (duration.inWholeMinutes < cacheExpirationMinutes) {
                    logger.d("使用缓存的训练上下文: userId=$userId")
                    context
                } else {
                    contextCache.remove(userId)
                    null
                }
            } else {
                null
            }
        }

        private fun cacheContext(
            userId: String,
            context: WorkoutContext,
        ) {
            contextCache[userId] = context to Clock.System.now()
            logger.d("缓存训练上下文: userId=$userId")
        }

        /**
         * 计算方法
         */
        private fun calculateCompletionRate(
            session: WorkoutSession,
        ): Float {
            // 简化实现：基于训练组完成情况计算
            return 0.85f // 默认完成率
        }

        private fun calculateAverageRpe(session: WorkoutSession): Float {
            // 简化实现：计算平均RPE
            return 7.5f // 默认RPE
        }

        private fun calculateDurationMinutes(session: WorkoutSession): Int {
            // 计算训练时长
            val endTimeMs = session.endTime ?: System.currentTimeMillis()
            val startTimeMs = session.startTime ?: 0L
            val durationMs = endTimeMs - startTimeMs
            return (durationMs / (1000 * 60)).toInt() // 转换为分钟
        }

        private fun calculateCurrentWeek(plan: WorkoutPlan): Int {
            // 简化实现：计算当前周数
            return 1 // 默认第1周
        }

        private fun calculateDurationWeeks(plan: WorkoutPlan): Int {
            // 基于总天数计算周数
            return (plan.totalDays + 6) / 7 // 向上取整
        }

        private fun calculatePlanCompletionRate(
            plan: WorkoutPlan,
            userId: String,
        ): Float {
            // 简化实现：计算计划完成率
            return 0.75f // 默认完成率
        }

        /**
         * 用户偏好设置数据类
         */
        private data class UserPreferences(
            val favoriteExercises: List<String>,
            val preferredMuscleGroups: List<String>,
            val fitnessGoals: List<String>,
            val availableEquipment: List<String>,
            val workoutExperience: WorkoutExperience,
            val physicalLimitations: List<String>,
            val timeConstraints: TimeConstraints,
        )
    }
