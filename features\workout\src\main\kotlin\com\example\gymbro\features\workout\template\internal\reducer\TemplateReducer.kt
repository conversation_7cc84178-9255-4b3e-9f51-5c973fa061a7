package com.example.gymbro.features.workout.template.internal.reducer

import com.example.gymbro.features.workout.template.TemplateContract
import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * 模板Reducer - 主协调器
 *
 * 🎯 功能：
 * - 作为Intent分发的主入口
 * - 协调业务逻辑处理器和UI处理器
 * - 维护ReduceResult数据结构
 * - 统一日志和错误处理
 */
@Singleton
internal class TemplateReducer
    @Inject
    constructor(
        private val businessHandlers: TemplateReducerBusinessHandlers,
        private val uiHandlers: TemplateReducerUIHandlers,
    ) {

        /**
         * 状态缩减结果
         */
        data class ReduceResult<S, E>(
            val newState: S,
            val effect: E? = null,
        ) {
            companion object {
                fun <S, E> stateOnly(newState: S): ReduceResult<S, E> = ReduceResult(newState)
                fun <S, E> stateAndEffect(
                    newState: S,
                    effect: E,
                ): ReduceResult<S, E> = ReduceResult(newState, effect)
            }
        }

        /**
         * 主要的状态缩减函数 - Intent分发器
         */
        internal fun reduce(
            intent: TemplateContract.Intent,
            state: TemplateContract.State,
        ): ReduceResult<TemplateContract.State, TemplateContract.Effect> {
            Timber.d("Reducing intent: ${intent::class.simpleName}")

            return when (intent) {
                // === 模板管理 (Business) ===
                is TemplateContract.Intent.LoadTemplates -> businessHandlers.handleLoadTemplates(intent, state)
                is TemplateContract.Intent.RefreshTemplates -> businessHandlers.handleRefreshTemplates(
                    intent,
                    state,
                )

                is TemplateContract.Intent.CreateTemplate -> businessHandlers.handleCreateTemplate(intent, state)
                is TemplateContract.Intent.EditTemplate -> businessHandlers.handleEditTemplate(intent, state)
                // 🔥 修复：移除主界面的保存逻辑，避免与 TemplateEditScreen 冲突
                // is TemplateContract.Intent.UpdateTemplate -> businessHandlers.handleUpdateTemplate(intent, state) // 已移除
                // is TemplateContract.Intent.SaveTemplate -> businessHandlers.handleSaveTemplate(intent, state) // 已移除
                is TemplateContract.Intent.DeleteTemplate -> businessHandlers.handleDeleteTemplate(intent, state)
                is TemplateContract.Intent.DuplicateTemplate -> businessHandlers.handleDuplicateTemplate(
                    intent,
                    state,
                )

                // === 草稿管理 (Business) ===
                is TemplateContract.Intent.LoadDrafts -> businessHandlers.handleLoadDrafts(intent, state)
                is TemplateContract.Intent.RefreshDrafts -> businessHandlers.handleRefreshDrafts(intent, state)
                is TemplateContract.Intent.CreateNewDraft -> businessHandlers.handleCreateNewDraft(intent, state)
                is TemplateContract.Intent.EditDraft -> businessHandlers.handleEditDraft(intent, state)
                is TemplateContract.Intent.SaveDraft -> businessHandlers.handleSaveDraft(intent, state)
                is TemplateContract.Intent.DeleteDraft -> businessHandlers.handleDeleteDraft(intent, state)
                is TemplateContract.Intent.PromoteDraft -> businessHandlers.handlePromoteDraft(intent, state)

                // === 动作管理 (Business) ===
                is TemplateContract.Intent.ShowExerciseSelector -> businessHandlers.handleShowExerciseSelector(
                    intent,
                    state,
                )

                is TemplateContract.Intent.HideExerciseSelector -> businessHandlers.handleHideExerciseSelector(
                    intent,
                    state,
                )

                is TemplateContract.Intent.SetExerciseSelectorMode -> businessHandlers.handleSetExerciseSelectorMode(
                    intent,
                    state,
                )

                is TemplateContract.Intent.AddExercise -> businessHandlers.handleAddExercise(intent, state)
                is TemplateContract.Intent.AddExercises -> businessHandlers.handleAddExercises(intent, state)
                is TemplateContract.Intent.RemoveExercise -> businessHandlers.handleRemoveExercise(intent, state)
                is TemplateContract.Intent.UpdateExercise -> businessHandlers.handleUpdateExercise(intent, state)
                is TemplateContract.Intent.ReorderExercises -> businessHandlers.handleReorderExercises(
                    intent,
                    state,
                )

                // === 拖拽排序操作 (Business) ===
                is TemplateContract.Intent.StartDragTemplate -> businessHandlers.handleStartDragTemplate(
                    intent,
                    state,
                )

                is TemplateContract.Intent.UpdateDragTemplate -> businessHandlers.handleUpdateDragTemplate(
                    intent,
                    state,
                )

                is TemplateContract.Intent.EndDragTemplate -> businessHandlers.handleEndDragTemplate(
                    intent,
                    state,
                )

                is TemplateContract.Intent.CancelDragTemplate -> businessHandlers.handleCancelDragTemplate(
                    intent,
                    state,
                )

                is TemplateContract.Intent.StartDragDraft -> businessHandlers.handleStartDragDraft(intent, state)
                is TemplateContract.Intent.UpdateDragDraft -> businessHandlers.handleUpdateDragDraft(
                    intent,
                    state,
                )

                is TemplateContract.Intent.EndDragDraft -> businessHandlers.handleEndDragDraft(intent, state)
                is TemplateContract.Intent.CancelDragDraft -> businessHandlers.handleCancelDragDraft(
                    intent,
                    state,
                )

                // === 一键置顶操作 (Business) ===
                is TemplateContract.Intent.MoveTemplateToTop -> businessHandlers.handleMoveTemplateToTop(
                    intent,
                    state,
                )

                is TemplateContract.Intent.MoveDraftToTop -> businessHandlers.handleMoveDraftToTop(intent, state)

                // === 搜索和筛选 (Business) ===
                is TemplateContract.Intent.SearchTemplates -> businessHandlers.handleSearchTemplates(
                    intent,
                    state,
                )

                is TemplateContract.Intent.FilterByCategory -> businessHandlers.handleFilterByCategory(
                    intent,
                    state,
                )

                is TemplateContract.Intent.ClearFilters -> businessHandlers.handleClearFilters(intent, state)
                is TemplateContract.Intent.ToggleSearch -> businessHandlers.handleToggleSearch(intent, state)
                is TemplateContract.Intent.ShowSearch -> businessHandlers.handleShowSearch(intent, state)
                is TemplateContract.Intent.HideSearch -> businessHandlers.handleHideSearch(intent, state)

                // === 缓存管理 (Business) ===
                is TemplateContract.Intent.SaveToCache -> businessHandlers.handleSaveToCache(intent, state)
                is TemplateContract.Intent.RestoreFromCache -> businessHandlers.handleRestoreFromCache(
                    intent,
                    state,
                )

                is TemplateContract.Intent.ClearCache -> businessHandlers.handleClearCache(intent, state)
                is TemplateContract.Intent.EnableAutoSave -> businessHandlers.handleEnableAutoSave(intent, state)
                is TemplateContract.Intent.DisableAutoSave -> businessHandlers.handleDisableAutoSave(
                    intent,
                    state,
                )

                is TemplateContract.Intent.ShowCacheRestoreDialog -> businessHandlers.handleShowCacheRestoreDialog(
                    intent,
                    state,
                )

                is TemplateContract.Intent.HideCacheRestoreDialog -> businessHandlers.handleHideCacheRestoreDialog(
                    intent,
                    state,
                )

                is TemplateContract.Intent.RestoreSpecificCache -> businessHandlers.handleRestoreSpecificCache(
                    intent,
                    state,
                )

                is TemplateContract.Intent.AutoSaveStateChanged -> ReduceResult.stateOnly(
                    state.copy(autoSaveState = intent.state),
                )

                // === 手势操作 (UI) ===
                is TemplateContract.Intent.StartSwipe -> uiHandlers.handleStartSwipe(intent, state)
                is TemplateContract.Intent.UpdateSwipe -> uiHandlers.handleUpdateSwipe(intent, state)
                is TemplateContract.Intent.CompleteSwipe -> {
                    // 特殊处理：完成滑动需要同时处理UI状态和业务逻辑
                    val uiResult = uiHandlers.handleCompleteSwipe(intent, state)
                    // 然后调用业务逻辑删除练习
                    businessHandlers.handleRemoveExercise(
                        TemplateContract.Intent.RemoveExercise(intent.exerciseId),
                        uiResult.newState,
                    )
                }

                is TemplateContract.Intent.CancelSwipe -> uiHandlers.handleCancelSwipe(intent, state)

                // === 错误处理 (UI) ===
                is TemplateContract.Intent.ClearError -> uiHandlers.handleClearError(intent, state)
                is TemplateContract.Intent.ClearSaveError -> uiHandlers.handleClearSaveError(intent, state)
                is TemplateContract.Intent.ClearNetworkError -> uiHandlers.handleClearNetworkError(intent, state)
                is TemplateContract.Intent.RetryLastOperation -> uiHandlers.handleRetryLastOperation(
                    intent,
                    state,
                )

                is TemplateContract.Intent.HandleError -> uiHandlers.handleHandleError(intent, state)

                // === 对话框管理 (UI) ===
                is TemplateContract.Intent.ShowDeleteDialog -> uiHandlers.handleShowDeleteDialog(intent, state)
                is TemplateContract.Intent.HideDeleteDialog -> uiHandlers.handleHideDeleteDialog(intent, state)
                is TemplateContract.Intent.ConfirmDelete -> uiHandlers.handleConfirmDelete(intent, state)
                is TemplateContract.Intent.ShowSaveConfirmDialog -> uiHandlers.handleShowSaveConfirmDialog(
                    intent,
                    state,
                )

                is TemplateContract.Intent.HideSaveConfirmDialog -> uiHandlers.handleHideSaveConfirmDialog(
                    intent,
                    state,
                )

                is TemplateContract.Intent.ConfirmSave -> uiHandlers.handleConfirmSave(intent, state)
                is TemplateContract.Intent.DiscardChanges -> uiHandlers.handleDiscardChanges(intent, state)

                // === Tab切换 (UI) ===
                is TemplateContract.Intent.SwitchTab -> uiHandlers.handleSwitchTab(intent, state)

                // === 导航 (UI) ===
                is TemplateContract.Intent.NavigateToTemplateDetail -> uiHandlers.handleNavigateToTemplateDetail(
                    intent,
                    state,
                )

                is TemplateContract.Intent.NavigateToEditTemplate -> uiHandlers.handleNavigateToEditTemplate(
                    intent,
                    state,
                )

                is TemplateContract.Intent.NavigateToNewTemplate -> uiHandlers.handleNavigateToNewTemplate(
                    intent,
                    state,
                )

                is TemplateContract.Intent.NavigateToDraftEditor -> uiHandlers.handleNavigateToDraftEditor(
                    intent,
                    state,
                )

                is TemplateContract.Intent.NavigateToCreateDraft -> uiHandlers.handleNavigateToCreateDraft(
                    intent,
                    state,
                )

                is TemplateContract.Intent.NavigateBack -> uiHandlers.handleNavigateBack(intent, state)
                is TemplateContract.Intent.ResetNavigationState -> uiHandlers.handleResetNavigationState(
                    intent,
                    state,
                )

                // === 内部状态更新 (UI) ===
                is TemplateContract.Intent.TemplatesLoaded -> uiHandlers.handleTemplatesLoaded(intent, state)
                is TemplateContract.Intent.DraftsLoaded -> uiHandlers.handleDraftsLoaded(intent, state)
                is TemplateContract.Intent.TemplateSaved -> uiHandlers.handleTemplateSaved(intent, state)
                is TemplateContract.Intent.TemplateDeleted -> uiHandlers.handleTemplateDeleted(intent, state)
                is TemplateContract.Intent.CacheRestored -> uiHandlers.handleCacheRestored(intent, state)
                is TemplateContract.Intent.SaveError -> uiHandlers.handleSaveError(intent, state)
                is TemplateContract.Intent.LoadError -> uiHandlers.handleLoadError(intent, state)
                is TemplateContract.Intent.NetworkError -> uiHandlers.handleNetworkError(intent, state)
                is TemplateContract.Intent.SaveSuccess -> uiHandlers.handleSaveSuccess(intent, state)
                is TemplateContract.Intent.DeleteSuccess -> uiHandlers.handleDeleteSuccess(intent, state)

                // === 兼容性Intent (UI) ===
                is TemplateContract.Intent.LoadTemplate -> uiHandlers.handleLoadTemplate(intent, state)
                is TemplateContract.Intent.StartWorkout -> uiHandlers.handleStartWorkout(intent, state)
                is TemplateContract.Intent.SelectTemplate -> uiHandlers.handleSelectTemplate(intent, state)
                is TemplateContract.Intent.ToggleTemplateFavorite -> uiHandlers.handleToggleTemplateFavorite(
                    intent,
                    state,
                )

                is TemplateContract.Intent.StartWorkoutFromTemplate -> uiHandlers.handleStartWorkoutFromTemplate(
                    intent,
                    state,
                )

                is TemplateContract.Intent.CreateNewTemplate -> uiHandlers.handleCreateNewTemplate(intent, state)
                is TemplateContract.Intent.ShowDeleteDraftDialog -> uiHandlers.handleShowDeleteDraftDialog(
                    intent,
                    state,
                )

                is TemplateContract.Intent.OnTemplatesLoaded -> uiHandlers.handleOnTemplatesLoaded(intent, state)

                // === 未处理的Intent ===
                else -> {
                    Timber.w("Unhandled intent: ${intent::class.simpleName}")
                    ReduceResult.stateOnly(state)
                }
            }
        }
    }
