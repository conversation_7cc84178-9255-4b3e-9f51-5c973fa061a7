package com.example.gymbro.navigation

import androidx.navigation.NavController
import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * 跨模块导航器实现
 * 实现Coach与Workout模块之间的具体导航逻辑
 */
@Singleton
class CrossModuleNavigatorImpl
    @Inject
    constructor() : CrossModuleNavigator {

        private var navController: NavController? = null

        override fun setNavController(navController: NavController?) {
            this.navController = navController
            Timber.d("CrossModuleNavigator: NavController set")
        }

        // ==================== Coach → Workout 导航实现 ====================

        override fun navigateToTemplateEditor(templateId: String) {
            val route = CrossModuleRoutes.buildTemplateEditorRoute(templateId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to template editor with ID: $templateId")
        }

        override fun navigateToPlanDraftEditor(draftId: String) {
            val route = CrossModuleRoutes.buildPlanDraftEditorRoute(draftId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to plan draft editor with ID: $draftId")
        }

        override fun navigateToPlanApplyWizard(draftId: String) {
            val route = CrossModuleRoutes.buildPlanApplyWizardRoute(draftId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to plan apply wizard with ID: $draftId")
        }

        override fun navigateToTemplateDetails(templateId: String) {
            val route = CrossModuleRoutes.buildTemplateDetailsRoute(templateId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to template details with ID: $templateId")
        }

        override fun navigateToPlanDetails(planId: String) {
            val route = CrossModuleRoutes.buildPlanDetailsRoute(planId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to plan details with ID: $planId")
        }

        // ==================== 新增：精确Workout导航实现 ====================

        override fun navigateToSessionStart(sessionId: String) {
            val route = CrossModuleRoutes.buildSessionStartRoute(sessionId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to session start with ID: $sessionId")
        }

        override fun navigateToNewSession() {
            val route = CrossModuleRoutes.buildNewSessionRoute()
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to new session")
        }

        override fun navigateToCreateTemplate() {
            val route = CrossModuleRoutes.buildCreateTemplateRoute()
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to create template")
        }

        override fun navigateToCreatePlan() {
            val route = CrossModuleRoutes.buildCreatePlanRoute()
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to create plan")
        }

        override fun navigateToExerciseLibrary() {
            val route = CrossModuleRoutes.buildExerciseLibraryRoute()
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to exercise library")
        }

        override fun navigateToWorkoutMain() {
            val route = CrossModuleRoutes.buildWorkoutMainRoute()
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to workout main")
        }

        // ==================== Workout → Coach 导航实现 ====================

        override fun navigateToAiCoachWithContext(prompt: String, context: String) {
            val route = CrossModuleRoutes.buildAiCoachWithPromptRoute(prompt, context)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to AI coach with prompt: $prompt")
        }

        override fun navigateToAiTemplateAdjust(templateId: String) {
            val route = CrossModuleRoutes.buildAiTemplateAdjustRoute(templateId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to AI template adjust with ID: $templateId")
        }

        override fun navigateToAiPlanOptimize(planId: String) {
            val route = CrossModuleRoutes.buildAiPlanOptimizeRoute(planId)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to AI plan optimize with ID: $planId")
        }

        override fun navigateToAiWorkoutAdvice(sessionId: String, feedback: String) {
            val route = CrossModuleRoutes.buildAiWorkoutAdviceRoute(sessionId, feedback)
            navigateToRoute(route)
            Timber.d("CrossModuleNavigator: Navigating to AI workout advice with session ID: $sessionId")
        }

        // ==================== 通用导航实现 ====================

        override fun navigateBack(): Boolean {
            return try {
                val result = navController?.popBackStack() ?: false
                Timber.d("CrossModuleNavigator: Navigate back result: $result")
                result
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error navigating back")
                false
            }
        }

        override fun navigateToRoute(route: String, clearBackStack: Boolean) {
            try {
                navController?.navigate(route) {
                    if (clearBackStack) {
                        popUpTo(0) { inclusive = true }
                    }
                }
                Timber.d("CrossModuleNavigator: Navigated to route: $route")
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error navigating to route: $route")
            }
        }

        override fun handleDeepLink(deepLink: String): Boolean {
            return try {
                when {
                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_TEMPLATE_DRAFT) -> {
                        val draftId = extractDraftIdFromDeepLink(deepLink)
                        if (draftId != null) {
                            navigateToTemplateEditor(draftId)
                            true
                        } else {
                            false
                        }
                    }

                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_PLAN_DRAFT) -> {
                        val draftId = extractDraftIdFromDeepLink(deepLink)
                        if (draftId != null) {
                            navigateToPlanDraftEditor(draftId)
                            true
                        } else {
                            false
                        }
                    }

                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_AI_COACH) -> {
                        val prompt = extractPromptFromDeepLink(deepLink)
                        if (prompt != null) {
                            navigateToAiCoachWithContext(prompt)
                            true
                        } else {
                            false
                        }
                    }

                    // 🔥 新增：处理训练模块深度链接
                    deepLink.startsWith("gymbro://workout/") -> {
                        handleWorkoutDeepLink(deepLink)
                    }

                    // 🔥 新增：处理动作库深度链接
                    deepLink.startsWith("gymbro://exercise/") -> {
                        handleExerciseDeepLink(deepLink)
                    }

                    // 🔥 新增：处理新的深度链接格式
                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_WORKOUT_MAIN) -> {
                        navigateToWorkoutMain()
                        true
                    }

                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_CREATE_TEMPLATE) -> {
                        navigateToCreateTemplate()
                        true
                    }

                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_CREATE_PLAN) -> {
                        navigateToCreatePlan()
                        true
                    }

                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_NEW_SESSION) -> {
                        navigateToNewSession()
                        true
                    }

                    deepLink.startsWith(CrossModuleRoutes.DEEP_LINK_EXERCISE_LIBRARY) -> {
                        navigateToExerciseLibrary()
                        true
                    }

                    // 带参数的深度链接
                    deepLink.contains("/template/edit/") -> {
                        val templateId = extractIdFromPath(deepLink, "template/edit")
                        if (templateId != null) {
                            navigateToTemplateEditor(templateId)
                            true
                        } else {
                            false
                        }
                    }

                    deepLink.contains("/plan/details/") -> {
                        val planId = extractIdFromPath(deepLink, "plan/details")
                        if (planId != null) {
                            navigateToPlanDetails(planId)
                            true
                        } else {
                            false
                        }
                    }

                    deepLink.contains("/session/") && !deepLink.contains("/session/new") -> {
                        val sessionId = extractIdFromPath(deepLink, "session")
                        if (sessionId != null) {
                            navigateToSessionStart(sessionId)
                            true
                        } else {
                            false
                        }
                    }

                    else -> {
                        Timber.w("CrossModuleNavigator: Unhandled deep link: $deepLink")
                        false
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error handling deep link: $deepLink")
                false
            }
        }

        // ==================== 导航状态查询实现 ====================

        override fun isCurrentRoute(route: String): Boolean {
            return try {
                val currentDestination = navController?.currentDestination
                val currentRoute = currentDestination?.route
                currentRoute == route
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error checking current route")
                false
            }
        }

        override fun getCurrentRoute(): String? {
            return try {
                navController?.currentDestination?.route
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error getting current route")
                null
            }
        }

        override fun canNavigateBack(): Boolean {
            return try {
                navController?.previousBackStackEntry != null
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error checking if can navigate back")
                false
            }
        }

        // ==================== 私有辅助方法 ====================

        /**
         * 从深度链接中提取草稿ID
         * @param deepLink 深度链接URL
         * @return 草稿ID或null
         */
        private fun extractDraftIdFromDeepLink(deepLink: String): String? {
            return try {
                val uri = android.net.Uri.parse(deepLink)
                uri.lastPathSegment
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error extracting draft ID from deep link")
                null
            }
        }

        /**
         * 从深度链接中提取提示词
         * @param deepLink 深度链接URL
         * @return 提示词或null
         */
        private fun extractPromptFromDeepLink(deepLink: String): String? {
            return try {
                val uri = android.net.Uri.parse(deepLink)
                uri.getQueryParameter("prompt")?.let {
                    java.net.URLDecoder.decode(it, "UTF-8")
                }
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error extracting prompt from deep link")
                null
            }
        }

        /**
         * 从深度链接路径中提取ID
         * @param deepLink 深度链接URL
         * @param pathPattern 路径模式（如 "template/edit", "session"）
         * @return 提取的ID或null
         */
        private fun extractIdFromPath(deepLink: String, pathPattern: String): String? {
            return try {
                val uri = android.net.Uri.parse(deepLink)
                val pathSegments = uri.pathSegments
                val patternSegments = pathPattern.split("/")

                // 查找模式在路径中的位置
                for (i in 0..pathSegments.size - patternSegments.size) {
                    var match = true
                    for (j in patternSegments.indices) {
                        if (pathSegments[i + j] != patternSegments[j]) {
                            match = false
                            break
                        }
                    }
                    if (match) {
                        // 返回模式后面的下一个段作为ID
                        val idIndex = i + patternSegments.size
                        return if (idIndex < pathSegments.size) {
                            pathSegments[idIndex]
                        } else {
                            null
                        }
                    }
                }
                null
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error extracting ID from path: $pathPattern")
                null
            }
        }

        /**
         * 🔥 处理训练模块深度链接 - 增强版支持参数
         * @param deepLink 深度链接URL
         * @return 是否成功处理
         */
        private fun handleWorkoutDeepLink(deepLink: String): Boolean {
            return try {
                val uri = android.net.Uri.parse(deepLink)
                val pathSegments = uri.pathSegments

                when {
                    // 训练主页
                    pathSegments.contains("main") -> {
                        navigateToWorkoutMain()
                        true
                    }

                    // 模板相关
                    pathSegments.contains("template") -> {
                        when {
                            pathSegments.contains("new") -> {
                                navigateToCreateTemplate()
                                true
                            }

                            pathSegments.contains("edit") && pathSegments.size >= 4 -> {
                                val templateId = pathSegments[3] // workout/template/edit/{templateId}
                                navigateToTemplateEditor(templateId)
                                true
                            }

                            else -> {
                                navigateToCreateTemplate()
                                true
                            }
                        }
                    }

                    // 计划相关
                    pathSegments.contains("plan") -> {
                        when {
                            pathSegments.contains("new") -> {
                                navigateToCreatePlan()
                                true
                            }

                            pathSegments.contains("details") && pathSegments.size >= 4 -> {
                                val planId = pathSegments[3] // workout/plan/details/{planId}
                                navigateToPlanDetails(planId)
                                true
                            }

                            else -> {
                                navigateToCreatePlan()
                                true
                            }
                        }
                    }

                    // 训练会话相关
                    pathSegments.contains("session") -> {
                        when {
                            pathSegments.contains("new") -> {
                                navigateToNewSession()
                                true
                            }

                            pathSegments.size >= 3 -> {
                                val sessionId = pathSegments[2] // workout/session/{sessionId}
                                navigateToSessionStart(sessionId)
                                true
                            }

                            else -> {
                                navigateToNewSession()
                                true
                            }
                        }
                    }

                    else -> {
                        // 默认导航到训练主页
                        navigateToWorkoutMain()
                        true
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error handling workout deep link: $deepLink")
                false
            }
        }

        /**
         * 🔥 处理动作库深度链接
         * @param deepLink 深度链接URL
         * @return 是否成功处理
         */
        private fun handleExerciseDeepLink(deepLink: String): Boolean {
            return try {
                val uri = android.net.Uri.parse(deepLink)
                val pathSegments = uri.pathSegments

                when {
                    pathSegments.contains("library") -> {
                        navigateToRoute("exercise-library")
                        true
                    }

                    else -> {
                        // 默认导航到动作库主页
                        navigateToRoute("exercise-library")
                        true
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "CrossModuleNavigator: Error handling exercise deep link: $deepLink")
                false
            }
        }
    }
