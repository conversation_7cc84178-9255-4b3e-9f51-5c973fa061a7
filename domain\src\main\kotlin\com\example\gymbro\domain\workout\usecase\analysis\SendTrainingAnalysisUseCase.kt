package com.example.gymbro.domain.workout.usecase.analysis

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.ai.prompt.builder.LayeredPromptBuilder
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.util.Constants
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.workout.model.stats.DailyStats
import com.example.gymbro.domain.workout.repository.AnalysisStreamRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.datetime.LocalDate
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * 简化的时间范围数据类
 */
@Serializable
data class SimpleTimeRange(
    val startDate: LocalDate,
    val endDate: LocalDate,
)

/**
 * 发送训练分析数据到AI的UseCase
 *
 * 🔥 【事件总线架构】简化版本：
 * 1. 构建Prompt消息
 * 2. 调用StreamRepository获取流式响应
 * 3. Token自动通过事件总线路由到ThinkingBox
 * 4. 返回分析ID给UI用于订阅事件总线
 */
@Singleton
class SendTrainingAnalysisUseCase
    @Inject
    constructor(
        private val analysisStreamRepository: AnalysisStreamRepository,
        private val layeredPromptBuilder: LayeredPromptBuilder,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernFlowUseCase<SendTrainingAnalysisUseCase.Params, String>(dispatcher, logger) {

        data class Params(
            val userId: String,
            val timeRange: SimpleTimeRange,
            val dailyStats: List<DailyStats>,
            val model: String, // 由调用方指定使用的AI模型
        )

        override fun createFlow(parameters: Params): Flow<ModernResult<String>> = flow {
            try {
                logger.d("开始训练分析: userId=${parameters.userId}, 数据天数=${parameters.dailyStats.size}")

                // 1. 生成分析ID用于事件总线路由
                val analysisId = Constants.MessageId.generate()

                // 2. 构建分析专用的消息列表
                val messages = buildAnalysisMessages(parameters)

                // 3. 🔥 【事件总线架构】调用Repository，Token会自动发布到事件总线
                logger.d(
                    "🚀 开始调用 analysisStreamRepository.getStreamingResponseWithMessageId() - analysisId=$analysisId",
                )
                logger.d("📝 消息列表: ${messages.size}条消息，模型=${parameters.model}")

                // 调用新的事件总线方法，Token会自动路由到ThinkingBox
                analysisStreamRepository.getStreamingResponseWithMessageId(analysisId, messages, parameters.model)

                // 🔥 【事件总线架构】返回分析ID给UI，UI可以通过事件总线订阅Token
                emit(ModernResult.Success(analysisId))
                logger.d("✅ 分析ID已返回给UI: $analysisId")

                logger.d("训练分析启动完成: analysisId=$analysisId")
            } catch (e: Exception) {
                logger.e(e, "训练分析失败")
                emit(
                    ModernResult.error(
                        com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                            operationName = "sendTrainingAnalysis",
                            message = com.example.gymbro.core.ui.text.UiText.DynamicString(
                                "训练分析失败: ${e.message}",
                            ),
                            metadataMap = mapOf("userId" to parameters.userId),
                        ),
                    ),
                )
            }
        }

        /**
         * 构建分析专用的消息列表
         * 参考coach模块的prompt构建模式
         */
        private fun buildAnalysisMessages(params: Params): List<CoreChatMessage> {
            val statsJson = Json.encodeToString(params.dailyStats)

            return listOf(
                CoreChatMessage(
                    role = "system",
                    content = buildSystemPrompt(),
                ),
                CoreChatMessage(
                    role = "user",
                    content = buildUserPrompt(params, statsJson),
                ),
            )
        }

        /**
         * 系统Prompt - 定义AI分析师角色
         */
        private fun buildSystemPrompt(): String {
            return """
        你是一个专业的健身数据分析师。
        分析用户的训练数据，提供简洁实用的洞察和建议。

        重点关注：
        1. 训练容量趋势（总重量、组数变化）
        2. 训练频率和一致性
        3. 强度分布（基于RPE）
        4. 恢复模式和建议

        回答要简洁明了，提供可操作的建议。
                """.trimIndent()
        }

        /**
         * 用户Prompt - 包含具体的训练数据
         */
        private fun buildUserPrompt(params: Params, statsJson: String): String {
            return """
        请分析我的训练数据：

        **分析时间范围**: ${params.timeRange.startDate} 至 ${params.timeRange.endDate}
        **数据天数**: ${params.dailyStats.size}天

        **训练统计数据**:
        ```json
        $statsJson
        ```

        请提供：
        1. 总体训练表现评估
        2. 发现的问题或优势
        3. 具体改进建议
                """.trimIndent()
        }
    }
