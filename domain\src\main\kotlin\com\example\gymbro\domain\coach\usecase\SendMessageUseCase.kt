package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.ai.prompt.builder.ConversationTurn
import com.example.gymbro.core.ai.prompt.builder.LayeredPromptBuilder
import com.example.gymbro.core.ai.prompt.memory.MemoryContext
import com.example.gymbro.core.ai.prompt.memory.MemoryContextBuilder
import com.example.gymbro.core.ai.prompt.memory.MemoryContextType
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ml.service.BgeEmbeddingService
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.exercise.repository.HybridSearchRepository
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.user.repository.UserDataProvider
import com.example.gymbro.domain.workout.model.json.RawWorkoutState
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

/**
 * SendMessageUseCase - 单一职责的消息发送用例
 *
 * 🎯 核心职责：
 * - 构建完整的AI请求prompt（通过LayeredPromptBuilder）
 * - 发送消息到Core-Network
 * - 立即返回操作结果，不处理AI响应
 *
 * 🔥 架构原则：
 * - 职责分离：只负责发送，不负责接收
 * - Plan B架构：AI响应通过DirectOutputChannel自动路由到ThinkingBox
 * - 保持完整的prompt构建流程：系统提示词、历史对话、Memory集成
 *
 * @param aiCoachUseCase AI教练用例
 * @param chatSessionManagementUseCase 聊天会话管理用例
 * @param bgeEmbeddingService BGE向量化服务
 * @param hybridSearchRepository 混合搜索仓库
 * @param initializeBgeEngineUseCase 初始化BGE引擎用例
 * @param layeredPromptBuilder LayeredPromptBuilder
 * @param getCurrentUserIdUseCase 获取当前用户ID用例
 * @param userDataProvider 用户数据提供者
 * @param memoryContextBuilder Memory上下文构建器
 * @param dispatcher IO调度器
 * @param logger 日志记录器
 */
@Singleton
class SendMessageUseCase
    @Inject
    constructor(
        private val aiCoachUseCase: AiCoachUseCase,
        private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
        private val bgeEmbeddingService: BgeEmbeddingService,
        private val hybridSearchRepository: HybridSearchRepository,
        private val initializeBgeEngineUseCase: InitializeBgeEngineUseCase,
        private val layeredPromptBuilder: LayeredPromptBuilder,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val userDataProvider: UserDataProvider,
        private val memoryContextBuilder: MemoryContextBuilder,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<SendMessageUseCase.Params, Unit>(dispatcher, logger) {

        /**
         * 参数数据类
         *
         * @property sessionId 会话ID
         * @property userMessageContent 用户消息内容
         * @property workoutContext 训练上下文（可选）
         * @property messageCountInSession 当前会话的消息数
         * @property currentModel 当前选择的模型名称（用于DeepSeek专用prompt切换）
         * @property forceOmitSystemPrompt 强制省略系统提示词
         */
        data class Params(
            val sessionId: String,
            val userMessageContent: String,
            val workoutContext: WorkoutContext? = null,
            val messageCountInSession: Int = 0,
            val currentModel: String? = null,
            val forceOmitSystemPrompt: Boolean = false,
        )

        /**
         * 训练上下文数据
         * 用于增强AI响应的上下文信息
         */
        data class WorkoutContext(
            val currentExerciseId: String?,
            val completedExercises: Int,
            val totalExercises: Int,
            val currentWeight: Float?,
            val currentReps: Int?,
            val sessionDurationMin: Int,
            val exerciseDescription: String? = null,
        ) {
            fun toRawWorkoutState(sessionId: String): RawWorkoutState {
                return RawWorkoutState(
                    sessionId = sessionId,
                    description = exerciseDescription ?: "",
                    exerciseId = currentExerciseId,
                    completedExercises = completedExercises,
                    totalExercises = totalExercises,
                    currentWeight = currentWeight,
                    currentReps = currentReps,
                    sessionDurationMin = sessionDurationMin,
                )
            }
        }

        override suspend fun execute(parameters: Params): ModernResult<Unit> {
            return try {
                logger.d("🚀 [SendMessageUseCase] 开始发送消息: sessionId=${parameters.sessionId}")

                // 1. 获取当前用户ID
                val currentUserId = try {
                    val userIdResult = getCurrentUserIdUseCase().first()
                    when (userIdResult) {
                        is ModernResult.Success -> {
                            userIdResult.data ?: "user_default"
                        }

                        is ModernResult.Error -> {
                            logger.w("获取用户ID失败，使用默认值: ${userIdResult.error}")
                            "user_default"
                        }

                        is ModernResult.Loading -> "user_default"
                    }
                } catch (e: Exception) {
                    logger.w(e, "获取用户ID异常，使用默认值")
                    "user_default"
                }

                // 2. 构建对话历史
                val conversationHistory = buildConversationHistory(parameters.sessionId)
                logger.d("📚 构建对话历史: ${conversationHistory.size}轮对话")

                // 3. 构建Memory上下文
                val memoryContext = buildMemoryContext(
                    userId = currentUserId,
                    userInput = parameters.userMessageContent,
                    history = conversationHistory,
                    workoutContext = parameters.workoutContext,
                    sessionId = parameters.sessionId,
                )

                // 4. 获取系统层配置
                val systemLayer = layeredPromptBuilder.getCurrentSystemLayer()

                // 5. 使用LayeredPromptBuilder构建完整消息
                val messages = layeredPromptBuilder.buildChatMessagesWithMemory(
                    systemLayer = systemLayer,
                    userInput = parameters.userMessageContent,
                    history = conversationHistory,
                    memoryContext = memoryContext,
                    forceOmitSystemPrompt = parameters.forceOmitSystemPrompt,
                    model = parameters.currentModel,
                )

                logger.d(
                    "🎯 Prompt构建完成: 用户ID=$currentUserId, 历史轮次=${conversationHistory.size}, 消息总数=${messages.size}, 省略系统提示词=${parameters.forceOmitSystemPrompt}",
                )

                // 6. 🔥 【关键修改】发送消息但不等待响应，让DirectOutputChannel处理响应路由
                logger.d("📤 [SendMessageUseCase] 发送消息到AI服务，不等待响应")

                // 🔥 【Plan B架构】启动流处理但立即返回，让Core-Network → DirectOutputChannel → ThinkingBox处理响应
                val flow = aiCoachUseCase.streamMessage(parameters.sessionId, messages)

                // 在后台协程中启动流处理，但不等待完成
                val backgroundScope = CoroutineScope(dispatcher + SupervisorJob())
                backgroundScope.launch {
                    try {
                        flow.collect { token ->
                            // 🔥 【Plan B架构】不处理token，让DirectOutputChannel自动路由到ThinkingBox
                            // 这里只是确保流被正确启动和处理
                            logger.v("📥 [SendMessageUseCase] Token已发送到DirectOutputChannel: ${token.take(50)}...")
                        }
                        logger.d("✅ [SendMessageUseCase] 消息流处理完成")
                    } catch (e: Exception) {
                        logger.e(e, "❌ [SendMessageUseCase] 消息流处理失败")
                    }
                }

                logger.d("✅ [SendMessageUseCase] 消息发送请求已提交，响应将通过DirectOutputChannel路由到ThinkingBox")

                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, "发送消息失败")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "sendMessage",
                        message = UiText.DynamicString("发送消息时发生未知错误: ${e.message}"),
                        metadataMap = mapOf("sessionId" to parameters.sessionId),
                    ),
                )
            }
        }

        /**
         * 构建对话历史
         */
        private suspend fun buildConversationHistory(
            sessionId: String,
            maxHistoryCount: Int = 8,
        ): List<ConversationTurn> {
            return try {
                val sessionResult = chatSessionManagementUseCase.loadSession(sessionId, includeMessages = true)

                when (sessionResult) {
                    is ModernResult.Success -> {
                        val session = sessionResult.data
                        val messageList = session.messages
                        logger.d("📚 获取到${messageList.size}条历史消息")

                        // 将消息转换为对话轮次
                        val turns = mutableListOf<ConversationTurn>()
                        var i = 0
                        while (i < messageList.size - 1 && turns.size < maxHistoryCount) {
                            val userMsg = messageList[i]
                            val aiMsg = messageList.getOrNull(i + 1)

                            // 检查消息类型并提取内容
                            val userContent = when (userMsg) {
                                is com.example.gymbro.domain.coach.model.CoachMessage.UserMessage -> userMsg.content
                                else -> null
                            }
                            val aiContent = when (aiMsg) {
                                is com.example.gymbro.domain.coach.model.CoachMessage.AiMessage -> aiMsg.content
                                else -> null
                            }

                            if (userContent != null && aiContent != null) {
                                turns.add(
                                    ConversationTurn(
                                        user = userContent,
                                        assistant = aiContent,
                                    ),
                                )
                                i += 2
                            } else {
                                i += 1
                            }
                        }

                        logger.d("📚 构建了${turns.size}轮对话历史")
                        turns
                    }

                    else -> {
                        logger.w("获取历史消息失败，使用空历史")
                        emptyList()
                    }
                }
            } catch (e: Exception) {
                logger.w(e, "构建对话历史失败")
                emptyList()
            }
        }

        /**
         * 构建Memory上下文
         */
        private suspend fun buildMemoryContext(
            userId: String,
            userInput: String,
            history: List<ConversationTurn>,
            workoutContext: WorkoutContext?,
            sessionId: String,
        ): MemoryContext? {
            return try {
                // 判断上下文类型
                val contextType = when {
                    workoutContext != null -> MemoryContextType.TRAINING
                    history.isNotEmpty() -> MemoryContextType.CONVERSATION
                    else -> MemoryContextType.GENERAL
                }

                // 构建metadata
                val metadata = buildMap {
                    put("session_id", sessionId)
                    put("has_workout_context", workoutContext != null)
                    put("history_turns", history.size)

                    workoutContext?.let { context ->
                        put("current_exercise_id", context.currentExerciseId ?: "")
                        put("completed_exercises", context.completedExercises)
                        put("total_exercises", context.totalExercises)
                        put("session_duration_min", context.sessionDurationMin)
                    }
                }

                memoryContextBuilder.buildContext(
                    userId = userId,
                    userInput = userInput,
                    history = history,
                )
            } catch (e: Exception) {
                logger.w(e, "构建Memory上下文失败")
                null
            }
        }
    }
