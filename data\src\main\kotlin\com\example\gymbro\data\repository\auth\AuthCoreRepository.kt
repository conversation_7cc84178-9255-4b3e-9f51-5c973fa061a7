package com.example.gymbro.data.repository.auth

import android.app.Activity
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.error.types.auth.AuthErrors
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.dao.auth.TokenDao
import com.example.gymbro.data.local.dao.user.UserDao
import com.example.gymbro.data.mapper.auth.toEntity
import com.example.gymbro.data.model.auth.TokenDto as DataToken
import com.example.gymbro.data.remote.AuthApi
import com.example.gymbro.data.remote.firebase.auth.FirebaseAuthService
import com.example.gymbro.data.remote.firebase.auth.PhoneVerificationCallback
import com.example.gymbro.data.remote.firebase.auth.PhoneVerificationContext
import com.example.gymbro.data.remote.firebase.auth.PhoneVerificationService
import com.example.gymbro.data.remote.firebase.datasource.FirebaseUserDataSource
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.model.auth.BeginSignInResult
import com.example.gymbro.domain.profile.model.user.User
import com.google.firebase.FirebaseException
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthProvider
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.tasks.await
import timber.log.Timber

/**
 * 核心认证功能仓库
 *
 * 负责处理用户登录、注册、登出等核心认证操作
 */
@Singleton
class AuthCoreRepository
    @Inject
    constructor(
        private val authApi: AuthApi,
        private val firebaseAuthService: FirebaseAuthService,
        private val phoneVerificationService: PhoneVerificationService,
        private val tokenDao: TokenDao,
        private val userDao: UserDao,
        private val firebaseUserDataSource: FirebaseUserDataSource,
    ) {
        companion object {
            private const val PHONE_NUMBER_KEY = "phone"
        }

        /**
         * 使用邮箱密码登录
         */
        suspend fun loginWithEmail(
            email: String,
            password: String,
        ): ModernResult<DataToken> {
            Timber.d("用户邮箱登录开始")
            return safeCatch {
                val request =
                    mapOf(
                        "email" to email,
                        "password" to password,
                    )
                val resp = authApi.login(request)
                if (resp.isSuccessful && resp.body() != null) {
                    val tokenDto = resp.body()!! // Remove .data access

                    // 保存Token到本地
                    tokenDao.insertToken(tokenDto.toEntity())

                    // 注意：用户数据现在由 UserDataCenter 统一管理
                    // AuthRepositoryImpl 会在登录成功后调用 syncAuthDataToUserDataCenter()

                    tokenDto
                } else if (resp.code() == 401) {
                    throw BusinessErrors.BusinessError.rule(
                        operationName = "login.invalidCredentials",
                        message = UiText.DynamicString("邮箱或密码错误"),
                        metadataMap = mapOf("errorType" to "invalidCredentials"),
                    )
                } else {
                    val msg = resp.errorBody()?.string() ?: "登录失败"
                    throw BusinessErrors.BusinessError.rule(
                        operationName = "login.exception",
                        message = UiText.DynamicString("登录失败: $msg"),
                        metadataMap = mapOf("errorType" to "unknown"),
                    )
                }
            }
        }

        /**
         * 使用手机号登录
         */
        suspend fun loginWithPhone(
            phoneNumber: String,
            code: String,
            verificationId: String,
        ): ModernResult<AuthUser> {
            Timber.d("使用电话号码登录: %s", phoneNumber)

            return try {
                // 1. 验证输入参数
                if (phoneNumber.isBlank() || code.isBlank() || verificationId.isBlank()) {
                    return ModernResult.Error(
                        DataErrors.Validation.required(
                            field = "phoneNumber",
                            operationName = "loginWithPhone.validation",
                            message = UiText.DynamicString("手机号、验证码和验证ID不能为空"),
                            metadataMap =
                                mapOf(
                                    "phoneNumber" to phoneNumber,
                                    StandardKeys.OPERATION_TYPE.key to "phone_login_validation",
                                ),
                        ),
                    )
                }

                // 2. 使用Firebase Phone Auth验证
                val firebaseResult = phoneVerificationService.verifyPhoneCode(verificationId, code)

                when (firebaseResult) {
                    is ModernResult.Success -> {
                        val credential = firebaseResult.data // credential is PhoneAuthCredential

                        // 使用凭据登录
                        val signInResult = firebaseAuthService.loginWithPhone(credential)
                        if (signInResult is ModernResult.Error) {
                            Timber.e(signInResult.error, "Firebase手机凭证登录失败")
                            return ModernResult.Error(
                                BusinessErrors.BusinessError.rule(
                                    operationName = "loginWithPhone.signInWithCredential",
                                    message = UiText.DynamicString("Firebase登录失败"),
                                    metadataMap =
                                        mapOf(
                                            "errorType" to "unknown",
                                            "cause" to signInResult.error.toString(),
                                        ),
                                ),
                            )
                        }

                        // 获取当前 FirebaseUser
                        val currentFirebaseUserResult = firebaseAuthService.getCurrentUser()
                        when (currentFirebaseUserResult) {
                            is ModernResult.Success -> {
                                val currentFirebaseUser = currentFirebaseUserResult.data
                                if (currentFirebaseUser == null) {
                                    Timber.e("获取当前 FirebaseUser 失败")
                                    return ModernResult.Error(
                                        BusinessErrors.BusinessError.rule(
                                            operationName = "loginWithPhone.getCurrentFirebaseUser",
                                            message = UiText.DynamicString("无法获取用户信息"),
                                            metadataMap = mapOf("errorType" to "unknown"),
                                        ),
                                    )
                                }

                                // 3. 获取ID Token
                                val idTokenResult = firebaseAuthService.getIdToken(false)

                                when (idTokenResult) {
                                    is ModernResult.Success -> {
                                        val idToken = idTokenResult.data

                                        // 4. 调用后端API验证并获取JWT
                                        val loginResult =
                                            authApi.loginWithPhone(
                                                mapOf(
                                                    PHONE_NUMBER_KEY to phoneNumber,
                                                    "firebaseIdToken" to idToken,
                                                ),
                                            )

                                        if (loginResult.isSuccessful && loginResult.body() != null) {
                                            val tokenDto = loginResult.body()!! // Remove .data access

                                            // 5. 保存Token到本地
                                            tokenDao.insertToken(tokenDto.toEntity())

                                            // 注意：用户数据现在由 UserDataCenter 统一管理
                                            // AuthRepositoryImpl 会在登录成功后调用 syncAuthDataToUserDataCenter()

                                            // 6. 创建AuthUser 使用 currentFirebaseUser
                                            val authUser =
                                                AuthUser(
                                                    uid = tokenDto.userId ?: currentFirebaseUser.uid,
                                                    displayName = currentFirebaseUser.displayName ?: "",
                                                    email = currentFirebaseUser.email,
                                                    phoneNumber = phoneNumber, // phoneNumber来自方法参数
                                                    isAnonymous = currentFirebaseUser.isAnonymous,
                                                    photoUrl = currentFirebaseUser.photoUrl?.toString(),
                                                )

                                            Timber.d("手机号登录成功: userId=%s", authUser.uid)
                                            ModernResult.Success(authUser)
                                        } else {
                                            ModernResult.Error(
                                                BusinessErrors.BusinessError.rule(
                                                    operationName = "loginWithPhone.apiError",
                                                    message =
                                                        UiText.DynamicString(
                                                            "手机号登录失败: ${loginResult.errorBody()?.string()}",
                                                        ),
                                                    metadataMap = mapOf("errorType" to "unknown"),
                                                ),
                                            )
                                        }
                                    }

                                    is ModernResult.Error -> {
                                        Timber.e("获取Firebase ID Token失败")
                                        ModernResult.Error(
                                            BusinessErrors.BusinessError.rule(
                                                operationName = "loginWithPhone.getIdToken",
                                                message = UiText.DynamicString("获取认证令牌失败"),
                                                metadataMap =
                                                    mapOf(
                                                        "phoneNumber" to phoneNumber,
                                                        StandardKeys.OPERATION_TYPE.key to "get_id_token",
                                                        "errorType" to "unknown",
                                                        "cause" to idTokenResult.error.toString(),
                                                    ),
                                            ),
                                        )
                                    }

                                    is ModernResult.Loading -> ModernResult.Loading
                                }
                            }

                            is ModernResult.Error -> {
                                Timber.e("获取当前 FirebaseUser 失败")
                                ModernResult.Error(
                                    AuthErrors.AuthError.unknown(
                                        operationName = "loginWithPhone.getCurrentFirebaseUser",
                                        message = UiText.DynamicString("无法获取用户信息"),
                                        cause = currentFirebaseUserResult.error,
                                    ),
                                )
                            }

                            is ModernResult.Loading -> ModernResult.Loading
                        }
                    }

                    is ModernResult.Error -> {
                        Timber.e("手机验证码验证失败")
                        ModernResult.Error(
                            AuthErrors.AuthError.unknown(
                                operationName = "loginWithPhone.verifyCode",
                                message = UiText.DynamicString("验证码错误或已过期"),
                                cause = firebaseResult.error,
                                metadataMap =
                                    mapOf(
                                        "phoneNumber" to phoneNumber,
                                        StandardKeys.OPERATION_TYPE.key to "verify_code",
                                    ),
                            ),
                        )
                    }

                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                Timber.e(e, "手机号登录失败")
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "loginWithPhone.exception",
                        message = UiText.DynamicString("手机号登录失败: ${e.message}"),
                        cause = e,
                        metadataMap =
                            mapOf(
                                StandardKeys.EXCEPTION.key to e,
                                "phoneNumber" to phoneNumber,
                                StandardKeys.OPERATION_TYPE.key to "phone_login",
                            ),
                    ),
                )
            }
        }

        /**
         * 使用Google账号登录
         */
        suspend fun loginWithGoogle(idToken: String): ModernResult<DataToken> {
            Timber.d("使用Google账号登录")
            if (idToken.isBlank()) {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "idToken",
                        operationName = "loginWithGoogle.validation",
                        message = UiText.DynamicString("ID Token不能为空"),
                    ),
                )
            }
            return safeCatch {
                val request = mapOf("idToken" to idToken)
                val resp = authApi.loginWithGoogle(request)
                if (resp.isSuccessful && resp.body() != null) {
                    val tokenDto = resp.body()!! // Remove .data access

                    // 保存Token到本地
                    tokenDao.insertToken(tokenDto.toEntity())

                    // 注意：用户数据现在由 UserDataCenter 统一管理
                    // AuthRepositoryImpl 会在登录成功后调用 syncAuthDataToUserDataCenter()

                    tokenDto
                } else {
                    val msg = resp.errorBody()?.string() ?: "Google登录失败"
                    throw AuthErrors.AuthError.unknown(
                        operationName = "loginWithGoogle.apiError",
                        message = UiText.DynamicString(msg),
                    )
                }
            }
        }

        /**
         * 匿名登录
         */
        suspend fun loginAnonymously(): ModernResult<AuthUser> =
            safeCatch {
                // 简化：直接创建匿名 token 并保存
                val anonymousDto =
                    DataToken(
                        accessToken = "anonymous_token_${java.util.UUID.randomUUID()}",
                        refreshToken = "anonymous_refresh_${java.util.UUID.randomUUID()}",
                        tokenType = "Bearer",
                        expiresIn = 3600,
                        issuedAt = System.currentTimeMillis(),
                        userId =
                            java.util.UUID
                                .randomUUID()
                                .toString(),
                        // ✅ 标准UUID格式
                        scope = "anonymous",
                    )
                tokenDao.insertToken(anonymousDto.toEntity())

                // 注意：用户数据现在由 UserDataCenter 统一管理
                // AuthRepositoryImpl 会在登录成功后调用 syncAuthDataToUserDataCenter()

                AuthUser(
                    uid = anonymousDto.userId ?: "",
                    // 使用null，让UI层根据需要显示本地化的"游客"文本
                    isAnonymous = true,
                )
            }

        /**
         * 用户注册
         */
        suspend fun register(
            email: String,
            password: String,
            username: String,
        ): ModernResult<DataToken> {
            Timber.d("注册用户: %s", email)
            return safeCatch {
                val request =
                    mapOf(
                        "email" to email,
                        "password" to password,
                        "username" to username,
                    )
                val resp = authApi.register(request)
                if (resp.isSuccessful && resp.body() != null) {
                    val tokenDto = resp.body()!! // Remove .data access

                    // 保存Token到本地
                    tokenDao.insertToken(tokenDto.toEntity())

                    // 注意：用户数据现在由 UserDataCenter 统一管理
                    // AuthRepositoryImpl 会在登录成功后调用 syncAuthDataToUserDataCenter()

                    // ✅ P1修复：注册成功后创建Firestore用户文档
                    try {
                        val userId = tokenDto.userId ?: ""
                        val newUser =
                            User(
                                userId = userId,
                                email = email,
                                displayName = username,
                                username = username,
                                createdAt = System.currentTimeMillis(),
                                lastLoginAt = System.currentTimeMillis(),
                                isAnonymous = false,
                            )

                        when (val createResult = firebaseUserDataSource.createOrUpdateUserProfile(newUser)) {
                            is ModernResult.Success -> {
                                Timber.d("Firestore用户文档创建成功: userId=$userId")
                            }

                            is ModernResult.Error -> {
                                Timber.e(createResult.error.cause, "Firestore用户文档创建失败: userId=$userId")
                                // 不抛出异常，避免影响注册流程，但记录错误
                            }

                            is ModernResult.Loading -> {
                                Timber.w("Firestore用户文档创建超时: userId=$userId")
                            }
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "创建Firestore用户文档时发生异常")
                        // 不抛出异常，避免影响注册流程
                    }

                    tokenDto
                } else {
                    val msg = resp.errorBody()?.string() ?: "注册失败"
                    throw AuthErrors.AuthError.unknown(
                        operationName = "register.exception",
                        message = UiText.DynamicString("注册失败: $msg"),
                    )
                }
            }
        }

        /**
         * 用户登出
         */
        suspend fun logout(): ModernResult<Unit> =
            safeCatch {
                val tokenEntity = tokenDao.getToken().first()
                tokenEntity?.let {
                    try {
                        authApi.logout(mapOf("accessToken" to it.accessToken))
                    } catch (e: Exception) {
                        Timber.w(e, "远程注销失败，但本地仍会清除token")
                    }
                    tokenDao.deleteToken()
                }
                // 修复 signOut 方法调用
                when (val signOutResult = firebaseAuthService.logout()) {
                    is ModernResult.Success -> Unit
                    is ModernResult.Error -> {
                        Timber.w(signOutResult.error, "Firebase 登出失败，但本地token已清除")
                    }

                    is ModernResult.Loading -> Unit
                }
            }

        /**
         * 开始 Google 登录流程
         *
         * 注意：此方法返回的是用于启动Google登录的配置信息
         * 实际的Google登录流程需要在UI层处理，因为需要Activity上下文
         */
        fun beginSignInWithGoogle(): ModernResult<BeginSignInResult> {
            Timber.d("开始Google登录流程")
            return try {
                // 返回Google登录配置信息，实际登录在UI层处理
                // 这里提供必要的配置信息供UI层使用
                val beginSignInResult =
                    BeginSignInResult(
                        pendingIntent = "google_sign_in_intent", // UI层需要使用GoogleSignInClient获取实际Intent
                        clientId = "configured_in_google_sign_in_module", // 实际配置在GoogleSignInModule中
                    )

                Timber.d("Google登录流程初始化成功")
                ModernResult.Success(beginSignInResult)
            } catch (e: Exception) {
                Timber.e(e, "Google登录流程初始化失败")
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "beginSignInWithGoogle",
                        message = UiText.DynamicString("Google登录初始化失败"),
                        cause = e,
                        metadataMap =
                            mapOf(
                                StandardKeys.EXCEPTION.key to e,
                                StandardKeys.OPERATION_TYPE.key to "google_sign_in_init",
                                StandardKeys.AUTH_PROVIDER.key to "google",
                            ),
                    ),
                )
            }
        }

        /**
         * 开始电话号码验证流程
         */
        suspend fun beginPhoneNumberVerification(phoneNumber: String): ModernResult<BeginSignInResult> {
            Timber.d("开始电话号码验证: $phoneNumber")
            return try {
                // 发送验证码并获取verificationId
                val context = SimplePhoneVerificationContext(null)
                val callback =
                    SimplePhoneVerificationCallback(
                        onVerified = {},
                        onFailed = { e -> Timber.e(e, "Phone verification failed") },
                    )
                val verificationIdResult =
                    phoneVerificationService.sendVerificationCode(
                        phoneNumber,
                        context,
                        callback,
                    )

                when (verificationIdResult) {
                    is ModernResult.Success -> {
                        // 创建BeginSignInResult，使用verificationId作为pendingIntent
                        val beginSignInResult =
                            BeginSignInResult(
                                pendingIntent = "phone_verification_$phoneNumber",
                                clientId = "phone_verification_$phoneNumber",
                            )
                        ModernResult.Success(beginSignInResult)
                    }

                    is ModernResult.Error -> {
                        Timber.e(verificationIdResult.error, "电话号码验证开始失败")
                        ModernResult.Error(verificationIdResult.error)
                    }

                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                Timber.e(e, "电话号码验证开始时发生异常")
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "beginPhoneNumberVerification.exception",
                        message = UiText.DynamicString("电话号码验证失败"),
                        cause = e,
                        metadataMap = mapOf("phoneNumber" to phoneNumber),
                    ),
                )
            }
        }

        /**
         * 获取当前认证的 Firebase 用户
         */
        suspend fun getCurrentFirebaseUser(): ModernResult<FirebaseUser?> = firebaseAuthService.getCurrentUser()

        /**
         * 清除当前认证用户的 Token
         */
        suspend fun clearCurrentToken(): ModernResult<Unit> =
            try {
                tokenDao.deleteToken()
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "clearCurrentToken.dbError",
                        message = UiText.DynamicString("清除本地Token失败"),
                        cause = e,
                    ),
                )
            }

        // ✅ 移除重复的observeUser方法，使用AuthStateRepository.observeAuthState()代替

        suspend fun deleteAccount(): ModernResult<String> {
            Timber.d("删除账号")
            return try {
                // 先登出
                val logoutResult = logout()
                if (logoutResult is ModernResult.Error) {
                    Timber.e(logoutResult.error, "删除账号前登出失败")
                }

                // 清除本地Token
                clearCurrentToken()

                // Firebase用户删除
                val currentUserResult = firebaseAuthService.getCurrentUser()
                when (currentUserResult) {
                    is ModernResult.Success -> {
                        val currentUser = currentUserResult.data
                        currentUser?.delete()?.await()
                    }

                    is ModernResult.Error -> {
                        Timber.e("获取当前用户失败: ${currentUserResult.error}")
                    }

                    is ModernResult.Loading -> {
                        // 处理加载状态
                    }
                }

                Timber.d("账号删除成功")
                ModernResult.Success("账号已成功删除")
            } catch (e: Exception) {
                Timber.e(e, "删除账号过程中发生异常")
                ModernResult.Error(
                    AuthErrors.AuthError.unknown(
                        operationName = "deleteAccount.exception",
                        message = UiText.DynamicString("删除账号失败: ${e.message}"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 观察认证状态 - 修复方法实现
         */
        fun observeAuthState(): Flow<AuthUser?> =
            firebaseAuthService.observeAuthState().map { firebaseUser ->
                firebaseUser?.let {
                    AuthUser(
                        uid = it.uid,
                        displayName = it.displayName,
                        email = it.email,
                        phoneNumber = it.phoneNumber,
                        isAnonymous = it.isAnonymous,
                        photoUrl = it.photoUrl?.toString(),
                    )
                }
            }

        private fun sessionExpired(
            message: String = "会话已过期",
            operation: String = "AuthCoreRepository.sessionExpired",
            metadata: Map<String, Any>? = null,
        ): ModernDataError =
            AuthErrors.AuthError.tokenExpired(
                operationName = operation,
                message = UiText.DynamicString(message),
                metadataMap = metadata ?: emptyMap(),
            )

        // 添加PhoneVerificationContext和PhoneVerificationCallback的简单实现
        private class SimplePhoneVerificationContext(
            private val activity: Activity?,
        ) : PhoneVerificationContext {
            override fun getContext(): Any = activity ?: "context_placeholder"
        }

        private class SimplePhoneVerificationCallback(
            private val onVerified: (PhoneAuthCredential) -> Unit,
            private val onFailed: (FirebaseException) -> Unit,
        ) : PhoneVerificationCallback {
            override fun onVerificationCompleted(credential: PhoneAuthCredential) {
                Timber.d("Phone verification completed.")
                onVerified(credential)
            }

            override fun onVerificationFailed(exception: Exception) {
                Timber.e(exception, "Phone verification failed.")
                if (exception is FirebaseException) {
                    onFailed(exception)
                } else {
                    onFailed(FirebaseException("Unknown verification error: ${exception.message}"))
                }
            }

            override fun onCodeSent(
                verificationId: String,
                resendToken: Any?,
            ) {
                Timber.d("Phone verification code sent: $verificationId")
                val token = resendToken as? PhoneAuthProvider.ForceResendingToken
                // 接口接受Any?，所以可以直接传递转换后的token（可能为null）
                onCodeSent(verificationId, token)
            }

            override fun onVerificationTimeout() {
                Timber.d("Phone verification timeout")
            }
        }

        // 注意：saveUserDataAfterLogin 方法已移除
        // 用户数据现在由 UserDataCenter 统一管理，通过 AuthRepositoryImpl.syncAuthDataToUserDataCenter() 调用
    }
