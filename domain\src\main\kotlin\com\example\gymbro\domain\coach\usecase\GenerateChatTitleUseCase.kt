package com.example.gymbro.domain.coach.usecase

// 移除Timber依赖 - domain层应保持纯净
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.title.TitleGenerationException
import com.example.gymbro.domain.title.TitleGenerator
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 聊天标题生成UseCase
 *
 * 简化策略：
 * 1. 消息≤15字 → 直接云端AI生成 (DeepSeek)
 * 2. 消息>15字 → 截取前15字+"..."
 */
class GenerateChatTitleUseCase
    @Inject
    constructor(
        private val titleGenerator: TitleGenerator,
        private val chatRepository: ChatRepository,
    ) {

        /**
         * 为聊天会话生成标题
         *
         * @param sessionId 会话ID
         * @param firstMessage 首条消息内容
         */
        suspend operator fun invoke(
            sessionId: String,
            firstMessage: String,
        ): ModernResult<TitleGenerationInfo> = withContext(Dispatchers.Default) {
            try {
                val startTime = System.currentTimeMillis()

                // 使用标题生成器生成标题
                val title = titleGenerator.generateTitle(firstMessage)
                val duration = System.currentTimeMillis() - startTime

                // 更新会话标题
                val updateResult = chatRepository.updateSession(
                    sessionId = sessionId,
                    title = title,
                )

                when (updateResult) {
                    is ModernResult.Success -> {
                        ModernResult.Success(
                            TitleGenerationInfo(
                                sessionId = sessionId,
                                title = title,
                                source = determineSource(firstMessage),
                                confidence = 0.8f,
                                generationTimeMs = duration,
                            ),
                        )
                    }

                    is ModernResult.Error -> {
                        // 即使更新失败也返回生成的标题
                        ModernResult.Success(
                            TitleGenerationInfo(
                                sessionId = sessionId,
                                title = title,
                                source = determineSource(firstMessage),
                                confidence = 0.8f,
                                generationTimeMs = duration,
                                updateFailed = true,
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // Loading状态不应该在这里出现
                        ModernResult.Error(
                            FeatureErrors.CoachError.processingFailed(
                                operationName = "GenerateChatTitleUseCase.invoke",
                                message = UiText.DynamicString("会话更新状态异常"),
                                processType = "title_generation",
                                reason = "unexpected_loading_state",
                            ),
                        )
                    }
                }
            } catch (e: TitleGenerationException) {
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "GenerateChatTitleUseCase.invoke",
                        message = UiText.DynamicString("聊天标题生成失败: ${e.message}"),
                        processType = "title_generation",
                        reason = "title_generation_failed",
                        cause = e,
                        metadataMap = mapOf(
                            "session_id" to sessionId,
                            "message_length" to firstMessage.length,
                        ),
                    ),
                )
            } catch (e: Exception) {
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "GenerateChatTitleUseCase.invoke",
                        message = UiText.DynamicString("聊天标题生成失败"),
                        processType = "title_generation",
                        reason = "title_generation_failed",
                        cause = e,
                        metadataMap = mapOf(
                            "session_id" to sessionId,
                            "message_length" to firstMessage.length,
                        ),
                    ),
                )
            }
        }

        /**
         * 确定标题来源
         */
        private fun determineSource(message: String): String {
            return if (message.length <= 15) "DeepSeek AI" else "文本截取"
        }

        /**
         * 批量生成标题（用于历史会话补充标题）
         */
        suspend fun generateBatchTitles(
            sessions: List<SessionToTitle>,
        ): ModernResult<List<TitleGenerationInfo>> = withContext(Dispatchers.Default) {
            try {
                val results = mutableListOf<TitleGenerationInfo>()

                for (session in sessions) {
                    val result = invoke(
                        sessionId = session.sessionId,
                        firstMessage = session.firstMessage,
                    )

                    when (result) {
                        is ModernResult.Success -> {
                            results.add(result.data)
                        }

                        is ModernResult.Error -> {
                            // 继续处理其他会话，不中断批量操作
                        }

                        is ModernResult.Loading -> {
                            // Loading状态不应该在这里出现
                        }
                    }

                    // 适当延迟，避免过度消耗资源
                    kotlinx.coroutines.delay(100)
                }

                ModernResult.Success(results)
            } catch (e: Exception) {
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "GenerateChatTitleUseCase.generateBatchTitles",
                        message = UiText.DynamicString("批量标题生成失败"),
                        processType = "batch_title_generation",
                        reason = "batch_generation_failed",
                        cause = e,
                        metadataMap = mapOf(
                            "session_count" to sessions.size,
                        ),
                    ),
                )
            }
        }

        /**
         * 标题生成信息
         */
        data class TitleGenerationInfo(
            val sessionId: String,
            val title: String,
            val source: String,
            val confidence: Float,
            val generationTimeMs: Long,
            val updateFailed: Boolean = false,
        )

        /**
         * 待生成标题的会话信息
         */
        data class SessionToTitle(
            val sessionId: String,
            val firstMessage: String,
        )
    }
