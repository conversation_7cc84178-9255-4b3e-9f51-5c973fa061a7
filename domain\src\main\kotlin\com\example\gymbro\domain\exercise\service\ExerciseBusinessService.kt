package com.example.gymbro.domain.exercise.service

import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.model.ExerciseCategory
import com.example.gymbro.shared.models.exercise.Equipment
import com.example.gymbro.shared.models.exercise.MuscleGroup
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Exercise 业务逻辑服务
 * 集中管理 Exercise 相关的复杂业务逻辑和计算
 */
@Singleton
class ExerciseBusinessService
    @Inject
    constructor() {

        /**
         * 计算动作的综合难度评分
         * 考虑因素：肌群数量、器械复杂度、技术要求等
         *
         * @param exercise 动作对象
         * @return 难度评分 (1-10)
         */
        fun calculateDifficultyScore(exercise: Exercise): Int {
            var score = exercise.difficultyLevel // 基础难度

            // 肌群复杂度加分
            val muscleComplexity = when {
                exercise.exerciseMuscles.size >= 4 -> 2
                exercise.exerciseMuscles.size >= 2 -> 1
                else -> 0
            }

            // 器械复杂度加分
            val equipmentComplexity = when {
                exercise.exerciseEquipments.any { it.isFreeWeight() } -> 2
                exercise.exerciseEquipments.any { it.isMachine() } -> 1
                exercise.equipment.contains(Equipment.NONE) -> 0
                else -> 1
            }

            // 动作类型难度
            val categoryDifficulty = when (exercise.getExerciseCategory()) {
                ExerciseCategory.FULL_BODY -> 2
                ExerciseCategory.CORE -> 1
                ExerciseCategory.UPPER_BODY, ExerciseCategory.LOWER_BODY -> 1
                else -> 0
            }

            score += muscleComplexity + equipmentComplexity + categoryDifficulty

            return score.coerceIn(1, 10)
        }

        /**
         * 计算动作的卡路里消耗预估
         * 基于肌群参与度、动作强度等因素
         *
         * @param exercise 动作对象
         * @param durationMinutes 持续时间（分钟）
         * @param bodyWeightKg 体重（公斤）
         * @return 预估卡路里消耗
         */
        fun calculateCalorieBurn(
            exercise: Exercise,
            durationMinutes: Int,
            bodyWeightKg: Float = 70f,
        ): Int {
            // 基础代谢当量 (METs)
            val baseMet = when (exercise.getExerciseCategory()) {
                ExerciseCategory.CARDIO -> 8.0
                ExerciseCategory.FULL_BODY -> 6.5
                ExerciseCategory.UPPER_BODY, ExerciseCategory.LOWER_BODY -> 5.0
                ExerciseCategory.CORE -> 4.0
                else -> 3.5
            }

            // 强度修正
            val intensityMultiplier = when (exercise.difficultyLevel) {
                in 1..3 -> 0.8
                in 4..6 -> 1.0
                in 7..8 -> 1.2
                else -> 1.4
            }

            // 肌群参与度修正
            val muscleParticipation = exercise.exerciseMuscles.size.coerceAtLeast(1)
            val muscleMultiplier = 1.0 + (muscleParticipation - 1) * 0.1

            val adjustedMet = baseMet * intensityMultiplier * muscleMultiplier

            // 卡路里计算公式: METs × 体重(kg) × 时间(hours)
            return (adjustedMet * bodyWeightKg * (durationMinutes / 60.0)).toInt()
        }

        /**
         * 分析动作的肌群分布
         *
         * @param exercise 动作对象
         * @return 肌群分布分析结果
         */
        fun analyzeMuscleDistribution(exercise: Exercise): MuscleDistributionAnalysis {
            val muscles = exercise.exerciseMuscles

            val primaryMuscles = muscles.filter { it.isPrimary }
            val secondaryMuscles = muscles.filter { !it.isPrimary }

            val upperBodyMuscles = muscles.filter { muscle ->
                muscle.muscleGroup in listOf(
                    MuscleGroup.CHEST, MuscleGroup.BACK, MuscleGroup.SHOULDERS,
                    MuscleGroup.BICEPS, MuscleGroup.TRICEPS, MuscleGroup.FOREARMS,
                    MuscleGroup.TRAPEZIUS,
                )
            }

            val lowerBodyMuscles = muscles.filter { muscle ->
                muscle.muscleGroup in listOf(
                    MuscleGroup.QUADRICEPS, MuscleGroup.HAMSTRINGS,
                    MuscleGroup.GLUTES, MuscleGroup.CALVES,
                )
            }

            val coreMuscles = muscles.filter { muscle ->
                muscle.muscleGroup in listOf(
                    MuscleGroup.ABS, MuscleGroup.CORE, MuscleGroup.OBLIQUES,
                    MuscleGroup.LOWER_BACK,
                )
            }

            return MuscleDistributionAnalysis(
                totalMuscles = muscles.size,
                primaryCount = primaryMuscles.size,
                secondaryCount = secondaryMuscles.size,
                upperBodyCount = upperBodyMuscles.size,
                lowerBodyCount = lowerBodyMuscles.size,
                coreCount = coreMuscles.size,
                averageActivation = muscles.map { it.activationLevel }.average(),
                dominantRegion = when {
                    upperBodyMuscles.size > lowerBodyMuscles.size && upperBodyMuscles.size > coreMuscles.size -> "上肢"
                    lowerBodyMuscles.size > upperBodyMuscles.size && lowerBodyMuscles.size > coreMuscles.size -> "下肢"
                    coreMuscles.size >= upperBodyMuscles.size && coreMuscles.size >= lowerBodyMuscles.size -> "核心"
                    else -> "均衡"
                },
            )
        }

        /**
         * 生成动作的个性化建议
         *
         * @param exercise 动作对象
         * @param userLevel 用户水平 (1-10)
         * @param availableEquipment 可用器械列表
         * @return 个性化建议
         */
        fun generatePersonalizedRecommendations(
            exercise: Exercise,
            userLevel: Int,
            availableEquipment: List<Equipment>,
        ): PersonalizedRecommendations {
            val recommendations = mutableListOf<String>()
            val modifications = mutableListOf<String>()
            val alternatives = mutableListOf<String>()

            // 难度调整建议
            val exerciseDifficulty = calculateDifficultyScore(exercise)
            when {
                exerciseDifficulty > userLevel + 2 -> {
                    recommendations.add("该动作对您可能偏难，建议先练习基础版本")
                    modifications.add("减少组数和次数")
                    modifications.add("使用辅助器械")
                }

                exerciseDifficulty < userLevel - 2 -> {
                    recommendations.add("您可以尝试更有挑战性的变式")
                    modifications.add("增加重量或阻力")
                    modifications.add("增加组数和次数")
                }

                else -> {
                    recommendations.add("该动作难度适合您的水平")
                }
            }

            // 器械替代建议
            val requiredEquipment = exercise.getRequiredEquipments()
            val missingEquipment = requiredEquipment.filter { it !in availableEquipment }

            if (missingEquipment.isNotEmpty()) {
                missingEquipment.forEach { missing ->
                    val alternativeEquipment = exercise.exerciseEquipments
                        .find { it.equipment == missing }
                        ?.alternatives
                        ?.filter { it in availableEquipment }

                    if (!alternativeEquipment.isNullOrEmpty()) {
                        alternatives.add(
                            "${missing.name} 可用 ${alternativeEquipment.joinToString("或") { it.name }} 替代",
                        )
                    } else {
                        alternatives.add("缺少 ${missing.name}，建议寻找类似功能的器械")
                    }
                }
            }

            // 安全提醒
            if (exerciseDifficulty >= 7) {
                recommendations.add("高难度动作，建议在专业指导下进行")
            }

            if (exercise.exerciseMuscles.any { it.muscleGroup == MuscleGroup.LOWER_BACK }) {
                recommendations.add("涉及下背部，请特别注意动作姿势")
            }

            return PersonalizedRecommendations(
                userLevel = userLevel,
                exerciseDifficulty = exerciseDifficulty,
                recommendations = recommendations,
                modifications = modifications,
                alternatives = alternatives,
            )
        }

        /**
         * 创建动作进阶路径
         *
         * @param baseExercise 基础动作
         * @return 进阶路径建议
         */
        fun createProgressionPath(baseExercise: Exercise): List<ProgressionStep> {
            val steps = mutableListOf<ProgressionStep>()

            // 基础阶段
            steps.add(
                ProgressionStep(
                    level = 1,
                    name = "基础版本",
                    description = "掌握基本动作模式",
                    sets = 2,
                    reps = 8,
                    modifications = listOf("减少负重", "降低动作幅度", "增加休息时间"),
                ),
            )

            // 进阶阶段
            steps.add(
                ProgressionStep(
                    level = 2,
                    name = "标准版本",
                    description = "标准动作执行",
                    sets = baseExercise.defaultSets,
                    reps = baseExercise.defaultReps,
                    modifications = listOf("标准动作范围", "控制动作节奏"),
                ),
            )

            // 高级阶段
            steps.add(
                ProgressionStep(
                    level = 3,
                    name = "强化版本",
                    description = "增加难度和强度",
                    sets = baseExercise.defaultSets + 1,
                    reps = baseExercise.defaultReps + 5,
                    modifications = listOf("增加负重", "加快动作节奏", "增加动作幅度"),
                ),
            )

            // 专家阶段
            if (baseExercise.difficultyLevel >= 6) {
                steps.add(
                    ProgressionStep(
                        level = 4,
                        name = "专家版本",
                        description = "最高难度挑战",
                        sets = baseExercise.defaultSets + 2,
                        reps = baseExercise.defaultReps + 8,
                        modifications = listOf("最大负重", "爆发力执行", "复合动作组合"),
                    ),
                )
            }

            return steps
        }
    }

/**
 * 肌群分布分析结果
 */
data class MuscleDistributionAnalysis(
    val totalMuscles: Int,
    val primaryCount: Int,
    val secondaryCount: Int,
    val upperBodyCount: Int,
    val lowerBodyCount: Int,
    val coreCount: Int,
    val averageActivation: Double,
    val dominantRegion: String,
)

/**
 * 个性化建议
 */
data class PersonalizedRecommendations(
    val userLevel: Int,
    val exerciseDifficulty: Int,
    val recommendations: List<String>,
    val modifications: List<String>,
    val alternatives: List<String>,
)

/**
 * 进阶路径步骤
 */
data class ProgressionStep(
    val level: Int,
    val name: String,
    val description: String,
    val sets: Int,
    val reps: Int,
    val modifications: List<String>,
)
