package com.example.gymbro.domain.coach.executor.modules

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.executor.FunctionCall
import com.example.gymbro.domain.coach.executor.FunctionResult
import com.example.gymbro.domain.workout.model.WorkoutAction
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.usecase.plan.CreatePlanWithTemplateVersionsUseCase
import com.example.gymbro.shared.models.workout.DayPlan
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json

/**
 * 训练计划域模块
 *
 * 负责处理训练计划相关的Function Call请求
 * 参照Exercise和Template模块的简单设计模式
 *
 * 核心功能：
 * 1. 计划搜索：支持关键词搜索和筛选
 * 2. 计划详情：根据ID获取完整计划信息
 * 3. 计划创建：用户自定义计划的创建和更新
 * 4. 空白生成：生成空白计划模板
 *
 * 设计原则：
 * - 保持简单，避免过度设计
 * - 直接处理参数，不使用复杂映射器
 * - 返回简单JSON格式，便于AI解析
 * - 严格的参数验证和错误处理
 *
 * @property ioDispatcher IO调度器
 * @property logger 日志记录器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Plan层Function Call简化设计)
 */
@Singleton
class PlanFunctionModule
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val createPlanWithTemplateVersionsUseCase: CreatePlanWithTemplateVersionsUseCase, // ✅ Phase 4新增
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * 处理训练计划域的Function Call
         *
         * @param functionCall Function Call请求
         * @param onActionTrigger UI动作触发回调
         * @return 函数执行结果
         */
        suspend fun handle(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)? = null,
        ): ModernResult<FunctionResult> = safeCatch {
            logger.d("📅 处理训练计划函数: ${functionCall.name}")

            val result = when (functionCall.name) {
                "gymbro.plan.search" -> handleSearch(functionCall.arguments)
                "gymbro.plan.get_detail" -> handleGetDetail(functionCall.arguments)
                "gymbro.plan.upsert" -> handleUpsert(functionCall.arguments, onActionTrigger)
                "gymbro.plan.generate_blank" -> handleGenerateBlank(functionCall.arguments, onActionTrigger)
                "gymbro.plan.generate_calendar_json" -> handleGenerateCalendarJson(functionCall.arguments)
                else -> ModernResult.Success(
                    FunctionResult(
                        success = false,
                        error = "未知的训练计划函数: ${functionCall.name}",
                    ),
                )
            }

            when (result) {
                is ModernResult.Success -> result.data
                is ModernResult.Error -> FunctionResult(
                    success = false,
                    error = "执行失败: ${result.error}",
                )

                is ModernResult.Loading -> FunctionResult(
                    success = false,
                    error = "执行超时，请稍后重试",
                )
            }
        }

        /**
         * 处理计划搜索
         */
        private suspend fun handleSearch(
            arguments: Map<String, String>,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val query = arguments["query"]
                if (query.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "搜索关键词不能为空",
                    )
                }

                val topK = arguments["top_k"]?.toIntOrNull() ?: 5
                val planType = arguments["plan_type"]
                val difficulty = arguments["difficulty"]

                logger.d("计划搜索: query='$query', topK=$topK, planType='$planType', difficulty='$difficulty'")

                try {
                    // 获取当前用户ID
                    val userIdResult = getCurrentUserIdUseCase().first()
                    if (userIdResult !is ModernResult.Success || userIdResult.data.isNullOrBlank()) {
                        return@withContext FunctionResult(
                            success = false,
                            error = "用户未登录，无法搜索计划",
                        )
                    }
                    val userId = userIdResult.data!!

                    // 执行搜索
                    val plansFlow = planRepository.searchPlans(userId, query)
                    val plans = plansFlow.first()

                    // 构建搜索结果JSON
                    val searchResults = plans.take(topK).map { plan ->
                        mapOf(
                            "id" to plan.id,
                            "name" to plan.name,
                            "description" to plan.description,
                            "planType" to plan.planType.name,
                            "difficulty" to when (plan.difficultyLevel) {
                                1 -> "BEGINNER"
                                2 -> "NOVICE"
                                3 -> "INTERMEDIATE"
                                4 -> "ADVANCED"
                                5 -> "EXPERT"
                                else -> "BEGINNER"
                            },
                            "totalDays" to plan.totalDays,
                            "workoutDays" to plan.getTotalWorkoutDays(),
                            "targetGoal" to (plan.targetGoal ?: ""),
                            "tags" to plan.tags,
                            "estimatedWeeklyDuration" to (plan.estimatedDuration ?: 0),
                        )
                    }

                    val responseJson = Json.encodeToString(searchResults)

                    return@withContext FunctionResult(
                        success = true,
                        data = responseJson,
                        metadata = mapOf(
                            "result_count" to searchResults.size.toString(),
                            "query" to query,
                            "search_type" to "plan_search",
                            "user_id" to userId,
                        ),
                    )
                } catch (e: Exception) {
                    logger.e("计划搜索失败", e)
                    return@withContext FunctionResult(
                        success = false,
                        error = "搜索失败: ${e.message}",
                    )
                }
            }
        }

        /**
         * 处理计划详情获取
         */
        private suspend fun handleGetDetail(
            arguments: Map<String, String>,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val planId = arguments["plan_id"]
                if (planId.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "计划ID不能为空",
                    )
                }

                logger.d("获取计划详情: planId='$planId'")

                try {
                    // 获取计划详情
                    val planResult = planRepository.getPlan(planId)
                    when (planResult) {
                        is ModernResult.Success -> {
                            val plan = planResult.data

                            // 构建详情JSON
                            val planDetail = mapOf(
                                "id" to plan.id,
                                "name" to plan.name,
                                "description" to plan.description,
                                "planType" to plan.planType.name,
                                "difficulty" to when (plan.difficultyLevel) {
                                    1 -> "BEGINNER"
                                    2 -> "NOVICE"
                                    3 -> "INTERMEDIATE"
                                    4 -> "ADVANCED"
                                    5 -> "EXPERT"
                                    else -> "BEGINNER"
                                },
                                "totalDays" to plan.totalDays,
                                "workoutDays" to plan.getTotalWorkoutDays(),
                                "restDays" to plan.getTotalRestDays(),
                                "estimatedWeeklyDuration" to (plan.estimatedDuration ?: 0),
                                "targetMuscleGroups" to listOf(plan.targetGoal ?: ""),
                                "tags" to plan.tags,
                                "isActive" to plan.isActive,
                                "createdAt" to plan.createdAt,
                                "updatedAt" to plan.updatedAt,
                                "dailySchedule" to plan.dailySchedule.map { (dayNumber, dayPlan) ->
                                    mapOf(
                                        "dayNumber" to dayNumber,
                                        "templateIds" to dayPlan.templateIds,
                                        "isRestDay" to dayPlan.isRestDay,
                                        "notes" to dayPlan.dayNotes,
                                    )
                                },
                            )

                            val responseJson = Json.encodeToString(planDetail)

                            return@withContext FunctionResult(
                                success = true,
                                data = responseJson,
                                metadata = mapOf("plan_id" to planId),
                            )
                        }

                        is ModernResult.Error -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "获取计划详情失败: ${planResult.error.uiMessage}",
                            )
                        }

                        is ModernResult.Loading -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "获取计划详情超时，请稍后重试",
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.e("获取计划详情失败", e)
                    return@withContext FunctionResult(
                        success = false,
                        error = "获取详情失败: ${e.message}",
                    )
                }
            }
        }

        /**
         * 处理计划创建/更新
         */
        private suspend fun handleUpsert(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val name = arguments["name"]
                if (name.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "计划名称不能为空",
                    )
                }

                logger.d("创建计划: name='$name'")

                try {
                    // 获取当前用户ID
                    val userIdResult = getCurrentUserIdUseCase().first()
                    if (userIdResult !is ModernResult.Success || userIdResult.data.isNullOrBlank()) {
                        return@withContext FunctionResult(
                            success = false,
                            error = "用户未登录，无法创建计划",
                        )
                    }
                    val userId = userIdResult.data!!

                    // 解析其他参数
                    val description = arguments["description"] ?: "AI生成的训练计划"
                    val planType = arguments["plan_type"]?.let {
                        try {
                            com.example.gymbro.shared.models.workout.PlanType.valueOf(it.uppercase())
                        } catch (e: Exception) {
                            com.example.gymbro.shared.models.workout.PlanType.CUSTOM
                        }
                    } ?: com.example.gymbro.shared.models.workout.PlanType.CUSTOM

                    // ✅ Phase 4: 解析训练模板安排（支持TemplateVersion自动解析）
                    val templateScheduleRaw = arguments["template_schedule"]
                    val dailySchedule = if (!templateScheduleRaw.isNullOrBlank()) {
                        try {
                            val scheduleList = Json.decodeFromString<List<Map<String, Any>>>(templateScheduleRaw)
                            scheduleList.mapIndexed { index, dayData ->
                                val dayNumber = (dayData["dayNumber"] as? Number)?.toInt() ?: (index + 1)
                                val templateId = dayData["template_id"] as? String
                                val notes = dayData["notes"] as? String
                                val isRestDay = dayData["isRestDay"] as? Boolean ?: false

                                dayNumber to if (isRestDay || templateId.isNullOrBlank()) {
                                    DayPlan(
                                        dayNumber = dayNumber,
                                        isRestDay = true,
                                        dayNotes = notes ?: "休息日",
                                    )
                                } else {
                                    // ✅ 使用templateIds，让Repository自动解析为templateVersionIds
                                    DayPlan.fromTemplateIds(
                                        dayNumber = dayNumber,
                                        templateIds = listOf(templateId),
                                        notes = notes,
                                    )
                                }
                            }.toMap()
                        } catch (e: Exception) {
                            logger.w("解析template_schedule失败，使用空计划: ${e.message}")
                            emptyMap()
                        }
                    } else {
                        emptyMap()
                    }

                    // ✅ Phase 4: 使用新的UseCase，支持TemplateVersion自动解析
                    val createPlanParams = CreatePlanWithTemplateVersionsUseCase.Params(
                        name = name,
                        description = description,
                        userId = userId,
                        dailySchedule = dailySchedule,
                    )

                    // 使用新UseCase创建并解析TemplateVersion
                    val saveResult = createPlanWithTemplateVersionsUseCase(createPlanParams)
                    when (saveResult) {
                        is ModernResult.Success -> {
                            val createdPlan = saveResult.data

                            // 触发UI动作
                            onActionTrigger?.invoke(WorkoutAction.CreateBlankPlan)

                            val responseData = mapOf(
                                "id" to createdPlan.id,
                                "name" to createdPlan.name,
                                "operation" to "created",
                                "message" to "计划创建成功",
                                "template_versions_resolved" to dailySchedule.values.any { it.isAdaptedToTemplateVersion() },
                                "total_workout_days" to createdPlan.getTotalWorkoutDays(),
                            )

                            val responseJson = Json.encodeToString(responseData)

                            return@withContext FunctionResult(
                                success = true,
                                data = responseJson,
                                actionTriggered = "CreateBlankPlan",
                                metadata = mapOf(
                                    "plan_id" to createdPlan.id,
                                    "name" to createdPlan.name,
                                    "template_versions_count" to createdPlan.dailySchedule.values
                                        .sumOf { it.templateVersionIds.size }.toString(),
                                ),
                                executionPath = "plan_upsert_success",
                                resourceId = createdPlan.id, // 🔥 新增：返回创建的计划ID
                                resourceType = "plan", // 🔥 新增：资源类型
                            )
                        }

                        is ModernResult.Error -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "保存计划失败: ${saveResult.error.uiMessage}",
                            )
                        }

                        is ModernResult.Loading -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "保存计划超时，请稍后重试",
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.e("创建计划失败", e)
                    return@withContext FunctionResult(
                        success = false,
                        error = "创建失败: ${e.message}",
                    )
                }
            }
        }

        /**
         * 处理空白计划生成
         */
        private suspend fun handleGenerateBlank(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val name = arguments["name"] ?: "新建训练计划"

                logger.d("生成空白计划: name='$name'")

                try {
                    // 获取当前用户ID
                    val userIdResult = getCurrentUserIdUseCase().first()
                    if (userIdResult !is ModernResult.Success || userIdResult.data.isNullOrBlank()) {
                        return@withContext FunctionResult(
                            success = false,
                            error = "用户未登录，无法生成计划",
                        )
                    }
                    val userId = userIdResult.data!!

                    // 解析参数
                    val startDate = arguments["start_date"] ?: "2024-01-01"
                    val generateCalendarJson = arguments["generate_calendar_json"]?.toBoolean() ?: false

                    // 创建空白计划
                    val planId = "plan_blank_${System.currentTimeMillis()}"
                    val blankPlan = com.example.gymbro.shared.models.workout.WorkoutPlan(
                        id = planId,
                        name = name,
                        description = "空白训练计划，可自定义内容",
                        userId = userId,
                        targetGoal = "自定义训练",
                        difficultyLevel = 1, // BEGINNER
                        estimatedDuration = 0,
                        planType = com.example.gymbro.shared.models.workout.PlanType.CUSTOM,
                        dailySchedule = emptyMap(),
                        totalDays = 7,
                        tags = listOf("空白计划"),
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis(),
                        isActive = false,
                    )

                    // 保存计划
                    val saveResult = planRepository.savePlan(blankPlan)
                    when (saveResult) {
                        is ModernResult.Success -> {
                            // 触发UI动作
                            onActionTrigger?.invoke(WorkoutAction.CreateBlankPlan)

                            val responseData = mutableMapOf(
                                "id" to planId,
                                "name" to name,
                                "operation" to "generated_blank",
                                "message" to "空白计划生成成功",
                            )

                            // 如果需要生成calendar.json
                            if (generateCalendarJson) {
                                val calendarResult = planRepository.generatePlanCalendarJson(planId, startDate)
                                when (calendarResult) {
                                    is ModernResult.Success -> {
                                        responseData["calendar_json"] = Json.encodeToString(calendarResult.data)
                                    }

                                    is ModernResult.Error -> {
                                        logger.w("生成calendar.json失败: ${calendarResult.error.uiMessage}")
                                    }

                                    is ModernResult.Loading -> {
                                        logger.w("生成calendar.json超时")
                                    }
                                }
                            }

                            val responseJson = Json.encodeToString(responseData)

                            return@withContext FunctionResult(
                                success = true,
                                data = responseJson,
                                actionTriggered = "CreateBlankPlan",
                                metadata = mapOf("plan_id" to planId, "name" to name, "start_date" to startDate),
                            )
                        }

                        is ModernResult.Error -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "保存空白计划失败: ${saveResult.error.uiMessage}",
                            )
                        }

                        is ModernResult.Loading -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "保存空白计划超时，请稍后重试",
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.e("生成空白计划失败", e)
                    return@withContext FunctionResult(
                        success = false,
                        error = "生成失败: ${e.message}",
                    )
                }
            }
        }

        /**
         * 处理calendar.json生成
         */
        private suspend fun handleGenerateCalendarJson(
            arguments: Map<String, String>,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val planId = arguments["plan_id"]
                if (planId.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "计划ID不能为空",
                    )
                }

                val startDate = arguments["start_date"] ?: "2024-01-01"

                logger.d("生成calendar.json: planId='$planId', startDate='$startDate'")

                try {
                    // 生成calendar.json数据
                    val calendarResult = planRepository.generatePlanCalendarJson(planId, startDate)
                    when (calendarResult) {
                        is ModernResult.Success -> {
                            val calendarData = calendarResult.data
                            val responseJson = Json.encodeToString(calendarData)

                            return@withContext FunctionResult(
                                success = true,
                                data = responseJson,
                                metadata = mapOf(
                                    "plan_id" to planId,
                                    "start_date" to startDate,
                                    "calendar_entries_count" to calendarData.calendarEntries.size.toString(),
                                    "format" to "calendar_json",
                                ),
                            )
                        }

                        is ModernResult.Error -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "生成calendar.json失败: ${calendarResult.error.uiMessage}",
                            )
                        }

                        is ModernResult.Loading -> {
                            return@withContext FunctionResult(
                                success = false,
                                error = "生成calendar.json超时，请稍后重试",
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.e("生成calendar.json失败", e)
                    return@withContext FunctionResult(
                        success = false,
                        error = "生成失败: ${e.message}",
                    )
                }
            }
        }
    }
