package com.example.gymbro.core.util.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.recovery.RecoveryStrategy
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 基础ViewModel抽象类
 *
 * 为所有ViewModel提供标准化的功能，包括错误处理、状态管理和日志记录。
 * 实现了RecoveryAwareViewModel接口，支持错误恢复策略。
 *
 * @param S UI状态类型，必须实现BaseUiState接口
 * @property errorHandler 错误处理器，用于处理和格式化错误消息
 */
abstract class BaseViewModel<S : BaseUiState>(
    override val errorHandler: ModernErrorHandler,
) : ViewModel(),
    RecoveryAwareViewModel {
        // 保存当前操作名称，用于日志记录
        private var currentOperation: String? = null

        /**
         * 更新当前UI状态
         *
         * @param update 状态更新函数
         * @param source 状态更新来源，用于日志记录
         */
        protected fun updateState(
            update: (S) -> S,
            source: String,
        ) {
            val oldState = uiState.value
            _uiState.update(update)
            val newState = uiState.value

            // 使用ViewModelLogging记录状态更新
            ViewModelLogging.logStateUpdate(
                this::class.java.simpleName,
                oldState,
                newState,
                source,
            )
        }

        /**
         * 处理异步操作结果
         *
         * @param result 操作结果
         * @param operationName 操作名称
         * @param handleSuccess 处理成功结果的函数
         * @param handleError 处理错误的函数，可选
         * @param handleLoading 处理加载状态的函数，可选
         * @param attemptRecovery 是否尝试应用恢复策略，默认为true
         */
        protected fun <T> handleResult(
            result: ModernResult<T>,
            operationName: String,
            handleSuccess: (T) -> Unit,
            handleError: (ModernDataError) -> Unit = { error ->
                // 默认错误处理：更新UI状态中的错误信息
                updateState(
                    { state ->
                        createErrorState(state, error)
                    },
                    "handleResult.error",
                )
            },
            handleLoading: () -> Unit = {
                // 默认加载处理：更新UI状态为加载中
                updateState(
                    { state ->
                        createLoadingState(state)
                    },
                    "handleResult.loading",
                )
            },
            attemptRecovery: Boolean = true,
        ) {
            // 记录操作结果
            ViewModelLogging.logOperationResult(
                this::class.java.simpleName,
                operationName,
                result,
            )

            when (result) {
                is ModernResult.Success -> {
                    handleSuccess(result.data)
                }

                is ModernResult.Error -> {
                    if (attemptRecovery) {
                        // 尝试应用恢复策略
                        val strategy = provideRecoveryStrategy<T>(result.error)
                        if (strategy != null) {
                            applyRecoveryStrategy(
                                error = result.error,
                                strategy = strategy,
                                operationName = operationName,
                                onSuccess = handleSuccess,
                                onFailure = handleError,
                            )
                            return
                        }
                    }
                    handleError(result.error)
                }

                is ModernResult.Loading -> {
                    handleLoading()
                }
            }
        }

        /**
         * 应用恢复策略
         *
         * @param error 需要恢复的错误
         * @param strategy 要应用的恢复策略
         * @param operationName 操作名称
         * @param onSuccess 恢复成功的回调函数
         * @param onFailure 恢复失败的回调函数
         */
        protected fun <T> applyRecoveryStrategy(
            error: ModernDataError,
            strategy: RecoveryStrategy<T>,
            operationName: String,
            onSuccess: (T) -> Unit,
            onFailure: (ModernDataError) -> Unit,
        ) {
            // 记录恢复尝试
            ViewModelLogging.logRecoveryAttempt(
                this::class.java.simpleName,
                operationName,
                error,
            )

            // 使用viewModelScope启动协程
            viewModelScope.launch {
                try {
                    // 执行恢复策略
                    val recoveryResult = strategy.execute()

                    if (recoveryResult != null) {
                        // 恢复成功
                        ViewModelLogging.logRecoveryResult(
                            this@BaseViewModel::class.java.simpleName,
                            operationName,
                            true,
                        )
                        onSuccess(recoveryResult)
                    } else {
                        // 恢复失败
                        ViewModelLogging.logRecoveryResult(
                            this@BaseViewModel::class.java.simpleName,
                            operationName,
                            false,
                            error,
                        )
                        onFailure(error)
                    }
                } catch (e: Exception) {
                    // 恢复过程中发生异常
                    Timber.e(e, "[$operationName] 执行恢复策略时发生异常: ${e.message}")
                    onFailure(error)
                }
            }
        }

        /**
         * 创建包含错误信息的状态
         *
         * @param currentState 当前状态
         * @param error 错误对象
         * @return 更新后的状态
         */
        protected open fun createErrorState(
            currentState: S,
            error: ModernDataError,
        ): S {
            // 默认实现简单地设置错误相关字段
            // 子类可以覆盖此方法以提供更专业的错误状态
            val errorMessage = errorHandler.getUiMessage(error)
            val isRecoverable = error.recoverable

            return when (currentState) {
                is CommonUiState ->
                    @Suppress("UNCHECKED_CAST")
                    SimpleUiState(
                        isLoading = false,
                        error = errorMessage,
                        errorSeverity = error.severity,
                        isErrorRecoverable = isRecoverable,
                        rawError = error,
                        errorType = error.errorType,
                    ) as S

                else -> {
                    // 如果不是CommonUiState的子类，通过反射设置字段
                    // 这里简化处理，实际中应该使用专门的错误状态创建逻辑
                    Timber.w("无法为类型 ${currentState::class.java.simpleName} 创建错误状态，返回当前状态")
                    currentState
                }
            }
        }

        /**
         * 创建加载中状态
         *
         * @param currentState 当前状态
         * @return 更新后的状态
         */
        protected open fun createLoadingState(currentState: S): S {
            // 默认实现简单地设置加载标志
            // 子类可以覆盖此方法以提供更专业的加载状态
            return when (currentState) {
                is CommonUiState ->
                    @Suppress("UNCHECKED_CAST")
                    SimpleUiState(
                        isLoading = true,
                        error = null,
                        errorSeverity = null,
                        isErrorRecoverable = false,
                        rawError = null,
                        errorType = null,
                    ) as S

                else -> {
                    Timber.w("无法为类型 ${currentState::class.java.simpleName} 创建加载状态，返回当前状态")
                    currentState
                }
            }
        }

        /**
         * 执行异步操作并处理结果
         *
         * @param operationName 操作名称，用于日志记录
         * @param params 操作参数，用于日志记录
         * @param execution 执行异步操作的函数
         * @param handleSuccess 处理成功结果的函数
         * @param handleError 处理错误的函数，可选
         * @param handleLoading 处理加载状态的函数，可选
         */
        protected fun <T> executeOperation(
            operationName: String,
            params: Map<String, Any?> = emptyMap(),
            execution: suspend () -> ModernResult<T>,
            handleSuccess: (T) -> Unit,
            handleError: (ModernDataError) -> Unit = { error ->
                updateState(
                    { state -> createErrorState(state, error) },
                    "executeOperation.error",
                )
            },
            handleLoading: () -> Unit = {
                updateState(
                    { state -> createLoadingState(state) },
                    "executeOperation.loading",
                )
            },
        ) {
            // 记录操作开始
            ViewModelLogging.logOperationStart(
                this::class.java.simpleName,
                operationName,
                params,
            )

            // 保存当前操作名称
            currentOperation = operationName

            // 设置加载状态
            handleLoading()

            // 启动协程执行操作
            viewModelScope.launch {
                try {
                    val result = execution()
                    handleResult(
                        result = result,
                        operationName = operationName,
                        handleSuccess = handleSuccess,
                        handleError = handleError,
                        handleLoading = handleLoading,
                    )
                } catch (e: Exception) {
                    // 捕获执行过程中的异常
                    Timber.e(e, "[$operationName] 执行过程中发生异常: ${e.message}")

                    // 创建一个未知错误并处理
                    val unknownError =
                        ModernDataError(
                            operationName = operationName,
                            errorType = GlobalErrorType.Unknown,
                            category = ErrorCategory.UNKNOWN,
                            uiMessage = UiText.DynamicString("执行操作时发生未知错误"),
                            cause = e,
                            metadataMap = mapOf("operation" to operationName),
                        )
                    handleError(unknownError)
                } finally {
                    // 清除当前操作
                    currentOperation = null
                }
            }
        }

        /**
         * 记录用户交互
         *
         * @param action 用户操作名称
         * @param params 操作参数
         */
        protected fun logUserAction(
            action: String,
            params: Map<String, Any?> = emptyMap(),
        ) {
            ViewModelLogging.logUserAction(
                this::class.java.simpleName,
                action,
                params,
            )
        }

        /**
         * UI状态流，子类需要实现
         */
        abstract val uiState: StateFlow<S>

        /**
         * 内部可变状态流，子类需要实现
         */
        protected abstract val _uiState: MutableStateFlow<S>
    }

/**
 * 为BaseUiState提供创建错误状态的扩展函数
 */
fun <S : BaseUiState> S.createErrorState(
    error: ModernDataError,
    errorHandler: ModernErrorHandler,
): S {
    val errorMessage = errorHandler.getUiMessage(error)
    val isRecoverable = error.recoverable

    return when (this) {
        is CommonUiState ->
            @Suppress("UNCHECKED_CAST")
            SimpleUiState(
                isLoading = false,
                error = errorMessage,
                errorSeverity = error.severity,
                isErrorRecoverable = isRecoverable,
                rawError = error,
                errorType = error.errorType,
            ) as S

        else -> {
            Timber.w("扩展函数无法为类型 ${this::class.java.simpleName} 创建错误状态，返回原始状态")
            this
        }
    }
}

/**
 * 为BaseUiState提供创建加载状态的扩展函数
 */
fun <S : BaseUiState> S.createLoadingState(): S =
    when (this) {
        is CommonUiState ->
            @Suppress("UNCHECKED_CAST")
            SimpleUiState(
                isLoading = true,
                error = null,
                errorSeverity = null,
                isErrorRecoverable = false,
                rawError = null,
                errorType = null,
            ) as S

        else -> {
            Timber.w("扩展函数无法为类型 ${this::class.java.simpleName} 创建加载状态，返回原始状态")
            this
        }
    }
