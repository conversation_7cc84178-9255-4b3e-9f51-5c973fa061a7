package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * ThinkingBoxViewModel - 标准MVI架构 ViewModel
 *
 * 🎯 核心职责：
 * - 继承BaseMviViewModel实现标准MVI模式
 * - 使用ThinkingBoxReducer管理状态转换
 * - 协调Token流解析和状态更新
 * - 处理副作用Effect的分发和执行
 * - 支持完整的生命周期管理
 *
 * 🔥 架构特点：
 * - 标准MVI 2.0架构，符合项目规范
 * - 单向数据流：UI → Intent → Reducer → State → UI
 * - Effect独立处理副作用，不影响State流
 * - 集成DomainMapper和StreamingParser
 * - 支持History写入通过Effect分发
 */
@HiltViewModel
class ThinkingBoxViewModel
    @Inject
    constructor(
        private val thinkingBoxReducer: ThinkingBoxReducer,
        private val domainMapper: DomainMapper,
        private val streamingParser: StreamingThinkingMLParser,
        private val directOutputChannel: DirectOutputChannel,
    ) : BaseMviViewModel<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect>(
        initialState = ThinkingBoxContract.State(),
    ) {

        override val reducer:
            Reducer<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect> =
            thinkingBoxReducer

        // 内部状态管理
        private var mappingContext = DomainMapper.MappingContext()

        // 解析任务
        private var parseJob: Job? = null

        // 🔥 【新增】完成回调监听器
        private var completionListener: com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener? = null

        // 🔥 【新增】内容缓存，用于回调
        private val thinkingProcessBuilder = StringBuilder()
        private val finalContentBuilder = StringBuilder()

        init {
            Timber.tag("TB-VM").d("🚀 [ViewModel初始化] ThinkingBoxViewModel启动")
            initializeEffectHandler()
        }

        /**
         * 初始化Effect处理器
         */
        override fun initializeEffectHandler() {
            viewModelScope.launch {
                effect.collect { effect ->
                    handleEffect(effect)
                }
            }
        }

        /**
         * Effect处理器 - 处理所有副作用
         */
        private suspend fun handleEffect(effect: ThinkingBoxContract.Effect) {
            Timber.tag("TB-EFFECT").d("⚡ [Effect处理] ${effect::class.simpleName}")

            when (effect) {
                is ThinkingBoxContract.Effect.StartTokenStreamListening -> {
                    startTokenStreamListening(effect.messageId)
                }

                is ThinkingBoxContract.Effect.ScrollToBottom -> {
                    // UI层会监听并执行滚动
                }

                is ThinkingBoxContract.Effect.CloseThinkingBox -> {
                    // UI层会监听并关闭思考框
                }

                is ThinkingBoxContract.Effect.NotifyHistoryThinking -> {
                    // UI层会监听并写入思考历史
                }

                is ThinkingBoxContract.Effect.NotifyHistoryFinal -> {
                    // UI层会监听并写入最终答案历史
                }

                is ThinkingBoxContract.Effect.ShowError -> {
                    // UI层会监听并显示错误
                }

                is ThinkingBoxContract.Effect.LogDebug -> {
                    Timber.tag("TB-DEBUG").d("${effect.message}")
                }
            }
        }

        // 🔥 【Plan B重构】简化初始化方法，移除sessionId参数
        fun initialize(messageId: String) =
            dispatch(ThinkingBoxContract.Intent.Initialize(messageId))

        fun onSegmentRendered(
            segmentId: String,
        ) = dispatch(ThinkingBoxContract.Intent.UiSegmentRendered(segmentId))

        fun reset() = dispatch(ThinkingBoxContract.Intent.Reset)
        fun clearError() = dispatch(ThinkingBoxContract.Intent.ClearError)

        // 🔥 【新增】设置完成回调监听器
        fun setCompletionListener(
            listener: com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener?,
        ) {
            completionListener = listener
            Timber.tag("TB-VM").d("🔗 [回调设置] CompletionListener已设置: ${listener != null}")
        }

        /**
         * 启动Token流监听 - 直接订阅DirectOutputChannel
         *
         * 🔥 【修复】直接订阅DirectOutputChannel，无中间适配器
         */
        private fun startTokenStreamListening(messageId: String) {
            Timber.tag("TB-STREAM").i("🎯 [Token流启动] messageId=$messageId")

            // 取消之前的解析任务
            parseJob?.cancel()

            parseJob = viewModelScope.launch {
                try {
                    Timber.tag("TB-STREAM").i("✅ [直接订阅] DirectOutputChannel: messageId=$messageId")

                    // 🔥 【关键修复】直接订阅DirectOutputChannel并创建token流
                    val tokenFlow = directOutputChannel.subscribeToMessage(messageId)
                        .map { outputToken ->
                            Timber.tag("TB-STREAM")
                                .d("📥 [Token接收] content=${outputToken.content}, type=${outputToken.contentType}")

                            // 🔥 【新增】处理错误类型的token
                            if (outputToken.metadata["error"] == true || outputToken.contentType == com.example.gymbro.core.network.detector.ContentType.PLAIN_TEXT) {
                                // 错误消息或纯文本消息，直接显示给用户
                                Timber.tag("TB-STREAM").w("⚠️ [错误Token] 接收到错误或纯文本消息: ${outputToken.content}")

                                // 发送错误显示Effect
                                sendEffect(
                                    ThinkingBoxContract.Effect.ShowError(
                                        com.example.gymbro.core.ui.text.UiText.DynamicString(outputToken.content),
                                    ),
                                )

                                // 返回空字符串，避免进入正常的解析流程
                                return@map ""
                            }

                            outputToken.content
                        }
                        .filter { it.isNotEmpty() } // 过滤掉空字符串

                    // 使用parseTokenStream处理token流
                    streamingParser.parseTokenStream(
                        messageId = messageId,
                        tokens = tokenFlow,
                        onEvent = { semanticEvent ->
                            // 转换为思考事件
                            val mappingResult = domainMapper.mapSemanticToThinking(semanticEvent, mappingContext)
                            mappingContext = mappingResult.context

                            // 处理所有生成的思考事件
                            mappingResult.events.forEach { thinkingEvent ->
                                processThinkingEvent(thinkingEvent)
                            }
                        },
                    )

                    Timber.tag("TB-STREAM").i("✅ [订阅完成] Token流监听已启动: messageId=$messageId")
                } catch (e: Exception) {
                    Timber.tag("TB-ERROR").e(e, "❌ [Token流启动失败] messageId=$messageId")
                    sendEffect(
                        ThinkingBoxContract.Effect.ShowError(
                            com.example.gymbro.core.ui.text.UiText.DynamicString("Token流启动失败: ${e.message}"),
                        ),
                    )
                }
            }
        }

        /**
         * 处理ThinkingEvent - MVI架构版本
         *
         * 🔥 【架构优化】现在作为公共方法，供ThinkingBoxStreamAdapter直接调用
         */
        fun processThinkingEvent(
            event: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent,
        ) {
            // 使用ThinkingBoxReducer处理事件
            val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)

            // 更新状态
            updateState { result.newState }

            // 分发Effects
            result.effects.forEach { effect ->
                sendEffect(effect)
            }
        }

        /**
         * 清理资源
         */
        override fun onCleared() {
            super.onCleared()
            parseJob?.cancel()
            Timber.d("TB-VM: 🧹 ThinkingBoxViewModel 清理完成")
        }
    }
