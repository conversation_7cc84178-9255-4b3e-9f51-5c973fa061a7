package com.example.gymbro.features.exerciselibrary.internal.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroInputField
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.smart.SmartInputConfig
import com.example.gymbro.designSystem.components.smart.ValidationRule
import com.example.gymbro.features.exerciselibrary.internal.presentation.contract.ExerciseLibraryContract
import com.example.gymbro.features.exerciselibrary.internal.presentation.viewmodel.ExerciseLibraryViewModel
import com.example.gymbro.shared.models.exercise.MuscleGroup
import kotlinx.coroutines.flow.collectLatest

/**
 * 添加自定义动作屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AddCustomExerciseScreen(
    navController: NavController,
    modifier: Modifier = Modifier,
    viewModel: ExerciseLibraryViewModel = hiltViewModel(),
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val context = LocalContext.current

    // 监听Effect
    LaunchedEffect(viewModel) {
        viewModel.effect.collectLatest { effect ->
            when (effect) {
                is ExerciseLibraryContract.Effect.CustomExerciseCreated -> {
                    navController.popBackStack()
                }

                is ExerciseLibraryContract.Effect.ClearCustomExerciseForm -> {
                    // 表单已通过状态更新清除
                }

                is ExerciseLibraryContract.Effect.ShowSnackbar -> {
                    // TODO: 显示 Snackbar，这里暂时不处理
                }

                else -> {
                    /* 其他Effect暂不处理 */
                }
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text("添加自定义动作")
                },
                navigationIcon = {
                    IconButton(
                        onClick = {
                            viewModel.handleIntent(
                                ExerciseLibraryContract.Intent.CancelCustomExerciseCreation,
                            )
                            navController.popBackStack()
                        },
                    ) {
                        Icon(
                            imageVector = Icons.Filled.ArrowBack,
                            contentDescription = "返回",
                        )
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            viewModel.handleIntent(ExerciseLibraryContract.Intent.ValidateCustomExerciseForm)
                            if (state.customExerciseValidationErrors.isEmpty()) {
                                viewModel.createCustomExercise()
                            }
                        },
                        enabled = !state.isCreatingCustomExercise,
                    ) {
                        if (state.isCreatingCustomExercise) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp,
                            )
                        } else {
                            Text("保存")
                        }
                    }
                },
            )
        },
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // 动作名称
            GymBroInputField(
                value = state.customExerciseName,
                onValueChange = { viewModel.updateCustomExerciseName(it) },
                label = { Text("动作名称") },
                placeholder = "例如：哑铃飞鸟变式",
                isError = state.customExerciseValidationErrors.containsKey("name"),
                errorMessage = state.customExerciseValidationErrors["name"],
                smartConfig = SmartInputConfig.form(
                    saveKey = "custom_exercise_name",
                    validationRules = listOf(ValidationRule.Required()),
                ),
                modifier = Modifier.fillMaxWidth(),
            )

            // 动作描述
            GymBroInputField(
                value = state.customExerciseDescription,
                onValueChange = { viewModel.updateCustomExerciseDescription(it) },
                label = { Text("动作描述") },
                placeholder = "详细描述动作的执行方法、注意事项等",
                isError = state.customExerciseValidationErrors.containsKey("description"),
                errorMessage = state.customExerciseValidationErrors["description"],
                singleLine = false,
                maxLines = 5,
                smartConfig = SmartInputConfig.form(
                    saveKey = "custom_exercise_description",
                    validationRules = listOf(ValidationRule.Required()),
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
            )

            // 目标肌肉群选择
            Card(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    Text(
                        text = "目标肌肉群",
                        style = MaterialTheme.typography.titleMedium,
                    )

                    if (state.customExerciseValidationErrors.containsKey("muscleGroups")) {
                        Text(
                            text = state.customExerciseValidationErrors["muscleGroups"]!!.asString(),
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                        )
                    }

                    // 肌肉群选择网格
                    MuscleGroupSelectionGrid(
                        selectedMuscleGroups = state.customExerciseMuscleGroups,
                        onMuscleGroupToggle = { muscleGroup ->
                            val updatedList = if (state.customExerciseMuscleGroups.contains(muscleGroup)) {
                                state.customExerciseMuscleGroups - muscleGroup
                            } else {
                                state.customExerciseMuscleGroups + muscleGroup
                            }
                            viewModel.handleIntent(
                                ExerciseLibraryContract.Intent.UpdateCustomExerciseMuscleGroups(updatedList),
                            )
                        },
                    )
                }
            }

            // 器械/设备（可选）
            GymBroInputField(
                value = state.customExerciseEquipment,
                onValueChange = {
                    viewModel.handleIntent(
                        ExerciseLibraryContract.Intent.UpdateCustomExerciseEquipment(it),
                    )
                },
                label = { Text("所需器械（可选）") },
                placeholder = "例如：哑铃、杠铃、自重等",
                smartConfig = SmartInputConfig.basic(saveKey = "custom_exercise_equipment"),
                modifier = Modifier.fillMaxWidth(),
            )

            // 说明文本
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant,
                ),
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    Text(
                        text = "创建提示",
                        style = MaterialTheme.typography.titleSmall,
                    )
                    Text(
                        text = "• 请确保动作名称具有描述性且容易识别\n" +
                            "• 详细的描述有助于正确执行动作\n" +
                            "• 选择准确的目标肌肉群便于分类和搜索\n" +
                            "• 自定义动作仅对您可见",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            }
        }
    }
}

/**
 * 肌肉群选择网格组件
 */
@Composable
private fun MuscleGroupSelectionGrid(
    selectedMuscleGroups: List<MuscleGroup>,
    onMuscleGroupToggle: (MuscleGroup) -> Unit,
    modifier: Modifier = Modifier,
) {
    // 常用肌肉群列表
    val availableMuscleGroups = listOf(
        MuscleGroup.CHEST,
        MuscleGroup.BACK,
        MuscleGroup.SHOULDERS,
        MuscleGroup.BICEPS,
        MuscleGroup.TRICEPS,
        MuscleGroup.LEGS,
        MuscleGroup.GLUTES,
        MuscleGroup.ABS,
        MuscleGroup.CARDIO,
    )

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        availableMuscleGroups.chunked(3).forEach { rowMuscleGroups ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                rowMuscleGroups.forEach { muscleGroup ->
                    FilterChip(
                        selected = selectedMuscleGroups.contains(muscleGroup),
                        onClick = { onMuscleGroupToggle(muscleGroup) },
                        label = {
                            Text(
                                text = muscleGroup.displayName,
                                style = MaterialTheme.typography.bodySmall,
                            )
                        },
                        modifier = Modifier.weight(1f),
                    )
                }
                // 填充空位
                repeat(3 - rowMuscleGroups.size) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
        }
    }
}

/**
 * AddCustomExerciseScreen组件所需的UiText文本资源
 * 为添加自定义动作界面提供统一的文本处理机制
 */
data class AddCustomExerciseScreenUiTexts(
    val addCustomExerciseTitle: UiText,
    val back: UiText,
    val exerciseNameLabel: UiText,
    val exerciseNameHint: UiText,
    val exerciseDescriptionLabel: UiText,
    val exerciseDescriptionHint: UiText,
    val muscleGroupLabel: UiText,
    val equipmentLabel: UiText,
    val equipmentHint: UiText,
    val saveExercise: UiText,
    val cancel: UiText,
    val exerciseNameRequired: UiText,
    val exerciseDescriptionRequired: UiText,
    val muscleGroupRequired: UiText,
    val savingExercise: UiText,
    val exerciseSavedSuccessfully: UiText,
    val failedToSaveExercise: UiText,
)
