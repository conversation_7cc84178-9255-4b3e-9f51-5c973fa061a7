package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.coach.repository.ChatRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map

/**
 * 聊天会话管理用例 - 统一管理所有会话相关操作
 *
 * 合并了以下功能：
 * - 会话创建和加载
 * - 会话列表获取和搜索
 * - 消息保存和历史获取
 *
 * 减少UseCase数量，提高代码复用性和维护性
 */
@Singleton
class ChatSessionManagementUseCase
    @Inject
    constructor(
        private val chatRepository: ChatRepository,
        private val logger: Logger,
    ) {
        // ========== 会话生命周期管理 ==========

        /**
         * 创建新的聊天会话
         *
         * @param userId 用户ID
         * @param title 会话标题，如果为null则自动生成
         * @param firstUserMessage 第一条用户消息，用于生成标题
         * @return 创建的会话
         */
        suspend fun createSession(
            userId: String,
            title: String? = null,
            firstUserMessage: String? = null,
        ): ModernResult<ChatSession> {
            logger.d("创建新聊天会话: userId=$userId, title=$title")

            return try {
                // 生成会话标题
                val sessionTitle =
                    when {
                        !title.isNullOrBlank() -> title
                        !firstUserMessage.isNullOrBlank() -> ChatSession.generateTitleFromMessage(
                            firstUserMessage,
                        )

                        else -> "新的对话"
                    }

                // 创建会话对象
                val session =
                    ChatSession.create(
                        userId = userId,
                        title = sessionTitle,
                    )

                // 保存到数据库
                when (val result = chatRepository.createSession(userId, sessionTitle)) {
                    is ModernResult.Success -> {
                        logger.d("聊天会话创建成功: sessionId=${result.data.id}")

                        // 如果有第一条消息，添加到会话中
                        if (!firstUserMessage.isNullOrBlank()) {
                            val userMessage = CoachMessage.UserMessage(content = firstUserMessage)
                            val addMessageResult = chatRepository.addMessage(result.data.id, userMessage)
                            when (addMessageResult) {
                                is ModernResult.Success -> {
                                    logger.d("第一条消息添加成功")
                                    ModernResult.Success(result.data.copy(messages = listOf(userMessage)))
                                }

                                is ModernResult.Error -> {
                                    logger.w("第一条消息添加失败，但会话已创建")
                                    ModernResult.Success(result.data)
                                }

                                is ModernResult.Loading -> ModernResult.Success(result.data)
                            }
                        } else {
                            ModernResult.Success(result.data)
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("聊天会话创建失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            DataErrors.DataError.unknown(
                                operationName = "ChatSessionManagementUseCase.createSession",
                                message =
                                    UiText.DynamicString(
                                        "创建会话时出现意外的Loading状态",
                                    ),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("创建聊天会话时发生异常", e)
                ModernResult.Error(
                    DataErrors.DataError.unknown(
                        operationName = "ChatSessionManagementUseCase.createSession",
                        message = UiText.DynamicString("创建聊天会话失败"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 加载指定会话的完整信息
         *
         * @param sessionId 会话ID
         * @param includeMessages 是否包含消息列表
         * @return 会话信息
         */
        suspend fun loadSession(
            sessionId: String,
            includeMessages: Boolean = true,
        ): ModernResult<ChatSession> {
            logger.d("加载聊天会话: sessionId=$sessionId, includeMessages=$includeMessages")

            return try {
                when (val result = chatRepository.getSession(sessionId)) {
                    is ModernResult.Success -> {
                        val session = result.data
                        if (session != null) {
                            logger.d(
                                "聊天会话加载成功: title=${session.title}, messageCount=${session.messages.size}",
                            )
                            ModernResult.Success(session)
                        } else {
                            logger.w("会话不存在: sessionId=$sessionId")
                            ModernResult.Error(
                                DataErrors.DataError.notFound(
                                    operationName = "ChatSessionManagementUseCase.loadSession",
                                    message = UiText.DynamicString("会话不存在"),
                                    entityType = "ChatSession",
                                    entityId = sessionId,
                                ),
                            )
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("聊天会话加载失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            DataErrors.DataError.unknown(
                                operationName = "ChatSessionManagementUseCase.loadSession",
                                message =
                                    UiText.DynamicString(
                                        "加载会话时出现意外的Loading状态",
                                    ),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("加载聊天会话时发生异常", e)
                ModernResult.Error(
                    DataErrors.DataError.unknown(
                        operationName = "ChatSessionManagementUseCase.loadSession",
                        message = UiText.DynamicString("加载聊天会话失败"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 加载会话的最近消息（用于上下文传递）
         *
         * @param sessionId 会话ID
         * @param messageCount 消息数量，默认10条
         * @return 包含最近消息的会话
         */
        suspend fun loadSessionWithRecentMessages(
            sessionId: String,
            messageCount: Int = 10,
        ): ModernResult<ChatSession> {
            logger.d("加载会话的最近消息: sessionId=$sessionId, messageCount=$messageCount")

            return try {
                // 直接获取会话信息（已包含消息）
                when (val sessionResult = chatRepository.getSession(sessionId)) {
                    is ModernResult.Success -> {
                        val session = sessionResult.data
                        if (session != null) {
                            // 如果消息数量超过限制，只保留最近的消息
                            val recentMessages =
                                if (session.messages.size > messageCount) {
                                    session.messages.takeLast(messageCount)
                                } else {
                                    session.messages
                                }
                            val sessionWithRecentMessages = session.copy(messages = recentMessages)
                            logger.d("加载会话和最近消息成功: messageCount=${recentMessages.size}")
                            ModernResult.Success(sessionWithRecentMessages)
                        } else {
                            logger.w("会话不存在: sessionId=$sessionId")
                            ModernResult.Error(
                                DataErrors.DataError.notFound(
                                    operationName = "ChatSessionManagementUseCase.loadSessionWithRecentMessages",
                                    message = UiText.DynamicString("会话不存在"),
                                    entityType = "ChatSession",
                                    entityId = sessionId,
                                ),
                            )
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("加载会话信息失败: ${sessionResult.error}")
                        sessionResult
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            DataErrors.DataError.unknown(
                                operationName = "ChatSessionManagementUseCase.loadSessionWithRecentMessages",
                                message =
                                    UiText.DynamicString(
                                        "加载会话信息时出现意外的Loading状态",
                                    ),
                                cause = IllegalStateException("Unexpected loading state"),
                            ),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("加载会话和最近消息时发生异常", e)
                ModernResult.Error(
                    DataErrors.DataError.unknown(
                        operationName = "ChatSessionManagementUseCase.loadSessionWithRecentMessages",
                        message = UiText.DynamicString("加载会话和消息失败"),
                        cause = e,
                    ),
                )
            }
        }

        // ========== 会话列表管理 ==========

        /**
         * 获取用户的所有会话
         *
         * @param userId 用户ID
         * @param includeMessages 是否包含消息
         * @param limit 限制数量
         * @param offset 偏移量
         * @return 会话列表
         */
        fun getUserSessions(
            userId: String,
            includeMessages: Boolean = false,
            limit: Int = 20,
            offset: Int = 0,
        ): Flow<ModernResult<List<ChatSession>>> =
            chatRepository.getUserSessions(userId)
                .map { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val sessions = result.data
                                .drop(offset)
                                .take(limit)
                            ModernResult.Success(sessions)
                        }

                        is ModernResult.Error -> result
                        is ModernResult.Loading -> result
                    }
                }
                .catch { e ->
                    logger.e("获取用户会话列表时发生异常", e)
                    emit(
                        ModernResult.Error(
                            DataErrors.DataError.unknown(
                                operationName = "ChatSessionManagementUseCase.getUserSessions",
                                message = UiText.DynamicString("获取会话列表失败"),
                                cause = e as? Exception ?: Exception(e),
                            ),
                        ),
                    )
                }

        /**
         * 搜索会话
         *
         * @param userId 用户ID
         * @param query 搜索关键词
         * @param offset 偏移量（用于分页）
         * @param limit 限制数量
         * @return 匹配的会话列表
         */
        fun searchSessions(
            userId: String,
            query: String,
            offset: Int = 0,
            limit: Int = 50,
        ): Flow<ModernResult<List<ChatSession>>> =
            chatRepository.getUserSessions(userId)
                .map { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val filteredSessions = result.data.filter { session ->
                                session.title.contains(query, ignoreCase = true) ||
                                    session.messages.any { message ->
                                        when (message) {
                                            is CoachMessage.UserMessage -> message.content.contains(
                                                query,
                                                ignoreCase = true,
                                            )

                                            is CoachMessage.AiMessage -> message.content.contains(query, ignoreCase = true)
                                        }
                                    }
                            }
                            // 应用分页参数
                            val pagedSessions = filteredSessions
                                .drop(offset)
                                .take(limit)
                            ModernResult.Success(pagedSessions)
                        }

                        is ModernResult.Error -> result
                        is ModernResult.Loading -> result
                    }
                }
                .catch { e ->
                    logger.e("搜索会话时发生异常", e)
                    emit(
                        ModernResult.Error(
                            DataErrors.DataError.unknown(
                                operationName = "ChatSessionManagementUseCase.searchSessions",
                                message = UiText.DynamicString("搜索会话失败"),
                                cause = e as? Exception ?: Exception(e),
                            ),
                        ),
                    )
                }

        /**
         * 获取分页会话数据（同步版本，用于PagingSource）
         *
         * @param userId 用户ID
         * @param limit 限制数量
         * @param offset 偏移量
         * @return 会话列表
         */
        suspend fun getSessionsPaged(
            userId: String,
            limit: Int,
            offset: Int,
        ): ModernResult<List<ChatSession>> {
            logger.d("📄 [UseCase] 获取分页会话: userId=$userId, limit=$limit, offset=$offset")
            return try {
                // 🎯 方案1：直接使用Repository的同步方法，避免30秒超时循环
                logger.d("🚀 [UseCase] 直接调用Repository同步方法")
                val result = chatRepository.getUserSessionsPaged(userId, limit, offset)

                when (result) {
                    is ModernResult.Success -> {
                        logger.d("✅ [UseCase] 成功获取${result.data.size}个分页会话")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.e("❌ [UseCase] 获取分页会话失败: ${result.error}")
                        result
                    }

                    is ModernResult.Loading -> {
                        // 这种情况不应该发生，因为Repository的同步方法不会返回Loading
                        logger.w("⚠️ [UseCase] Repository同步方法返回Loading状态，返回空列表")
                        ModernResult.Success(emptyList<ChatSession>())
                    }
                }
            } catch (e: Exception) {
                logger.e("❌ [UseCase] 获取分页会话时发生异常", e)
                ModernResult.Error(
                    DataErrors.DataError.unknown(
                        operationName = "ChatSessionManagementUseCase.getSessionsPaged",
                        message = UiText.DynamicString("获取分页会话失败"),
                        cause = e,
                    ),
                )
            }
        }

        // ========== 消息管理 ==========

        /**
         * 保存用户消息
         */
        suspend fun saveUserMessage(
            sessionId: String,
            message: CoachMessage.UserMessage,
        ): ModernResult<Unit> {
            logger.d("🚀 [USECASE-DEBUG] saveUserMessage 开始: sessionId=$sessionId, messageId=${message.id}")
            logger.d(
                "🚀 [USECASE-DEBUG] 消息内容: ${
                    message.content.take(
                        100,
                    )
                }${if (message.content.length > 100) "..." else ""}",
            )

            return try {
                logger.d("🔄 [USECASE-DEBUG] 调用 chatRepository.addMessage")
                val result = chatRepository.addMessage(sessionId, message)
                logger.d("🔄 [USECASE-DEBUG] chatRepository.addMessage 完成，结果: ${result::class.simpleName}")

                when (result) {
                    is ModernResult.Success -> {
                        logger.d("✅ [USECASE-DEBUG] Repository 保存成功")
                    }

                    is ModernResult.Error -> {
                        logger.e("❌ [USECASE-DEBUG] Repository 保存失败: ${result.error}")
                    }

                    else -> {
                        logger.w("⚠️ [USECASE-DEBUG] Repository 返回未知结果: $result")
                    }
                }

                result
            } catch (e: Exception) {
                logger.e("❌ [USECASE-DEBUG] 保存用户消息时发生异常: ${e.message}", e)
                ModernResult.Error(
                    DataErrors.DataError.unknown(
                        operationName = "ChatSessionManagementUseCase.saveUserMessage",
                        message = UiText.DynamicString("保存用户消息失败"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 保存AI消息
         */
        suspend fun saveAiMessage(
            sessionId: String,
            message: CoachMessage.AiMessage,
        ): ModernResult<Unit> {
            logger.d("保存AI消息: sessionId=$sessionId")
            return try {
                chatRepository.addMessage(sessionId, message)
            } catch (e: Exception) {
                logger.e("保存AI消息时发生异常", e)
                ModernResult.Error(
                    DataErrors.DataError.unknown(
                        operationName = "ChatSessionManagementUseCase.saveAiMessage",
                        message = UiText.DynamicString("保存AI消息失败"),
                        cause = e,
                    ),
                )
            }
        }

        // ========== 便捷方法 ==========

        /**
         * 根据第一条用户消息创建会话（常用场景）
         */
        suspend fun createSessionWithFirstMessage(
            userId: String,
            firstUserMessage: String,
        ): ModernResult<ChatSession> =
            createSession(
                userId = userId,
                title = null,
                firstUserMessage = firstUserMessage,
            )

        /**
         * 获取最近的会话（用于快速访问）
         */
        fun getRecentSessions(
            userId: String,
            count: Int = 5,
        ): Flow<ModernResult<List<ChatSession>>> = getUserSessions(userId, false, count, 0)

        /**
         * 加载会话的基本信息（不包含消息）
         */
        suspend fun loadSessionInfo(
            sessionId: String,
        ): ModernResult<ChatSession> = loadSession(sessionId, includeMessages = false)
    }
