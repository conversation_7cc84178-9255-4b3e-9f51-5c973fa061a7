package com.example.gymbro.domain.workout.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.Difficulty
import com.example.gymbro.domain.workout.model.WorkoutGoal
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.shared.models.workout.WorkoutPlan
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.datetime.Clock
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject

/**
 * 生成结构化训练计划的UseCase
 * 处理AI生成的训练计划JSON数据
 *
 * 功能：
 * - 解析Function Call JSON
 * - 验证计划参数
 * - 生成结构化训练计划
 * - 保存到数据库
 */
@Singleton
class GenerateStructuredPlanUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
    ) {

        /**
         * 根据Function Call JSON生成训练计划
         *
         * @param functionCallJson Function Call的JSON字符串
         * @return 生成结果，包含计划ID和状态
         */
        suspend operator fun invoke(functionCallJson: String): ModernResult<GeneratePlanResult> {
            return try {
                // 1. 解析Function Call JSON
                val jsonObject = Json.parseToJsonElement(functionCallJson).jsonObject
                val functionName = jsonObject["name"]?.toString()?.replace("\"", "")
                val arguments = jsonObject["arguments"]?.jsonObject

                // 2. 验证Function Call格式
                if (functionName != "fc_generate_workout_plan") {
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "generateStructuredPlan",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.AI.ProcessingError,
                            uiMessage = UiText.DynamicString("无效的Function Call: $functionName"),
                            cause = null,
                        ),
                    )
                }

                if (arguments == null) {
                    return ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "generateStructuredPlan",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.AI.ProcessingError,
                            uiMessage = UiText.DynamicString("Function Call缺少参数"),
                            cause = null,
                        ),
                    )
                }

                // 3. 验证基本参数
                val validationResult = validateBasicArguments(arguments)
                if (validationResult != null) {
                    return ModernResult.Error(validationResult)
                }

                // 4. 解析参数并生成计划
                val planArgs = parseWorkoutPlanArguments(arguments)
                val generatedPlan = createWorkoutPlanFromArgs(planArgs)

                // 5. 保存到数据库
                val saveResult = planRepository.savePlan(generatedPlan)
                when (saveResult) {
                    is ModernResult.Success -> {
                        // PlanRepository.savePlan 返回 Unit，我们使用生成的计划ID
                        val planId = generatedPlan.id

                        ModernResult.Success(
                            GeneratePlanResult.Success(
                                planId = planId,
                                planName = planArgs.planName,
                                duration = "${planArgs.weeks ?: planArgs.days}${if (planArgs.weeks != null) "周" else "天"}",
                            ),
                        )
                    }

                    is ModernResult.Error -> {
                        ModernResult.Error(saveResult.error)
                    }

                    is ModernResult.Loading -> {
                        // Loading状态通常不会从repository返回，但为了完整性处理
                        ModernResult.Loading
                    }
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "generateStructuredPlan",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.AI.ProcessingError,
                        uiMessage = UiText.DynamicString("生成训练计划失败"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 验证基本参数
         */
        private fun validateBasicArguments(
            arguments: JsonObject,
        ): com.example.gymbro.core.error.types.ModernDataError? {
            return try {
                val planName = arguments["plan_name"]?.toString()
                val durationType = arguments["duration_type"]?.toString()
                val sessionsPerWeek = arguments["sessions_per_week"]?.toString()?.toIntOrNull()
                val workoutFocus = arguments["workout_focus"]
                val weeklyStructure = arguments["weekly_structure"]

                when {
                    planName.isNullOrBlank() -> com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "validateArguments",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Validation.Required,
                        uiMessage = UiText.DynamicString("计划名称不能为空"),
                        cause = null,
                    )

                    durationType != "\"weeks\"" && durationType != "\"days\"" -> com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "validateArguments",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Validation.InvalidValue,
                        uiMessage = UiText.DynamicString("duration_type 必须是 'weeks' 或 'days'"),
                        cause = null,
                    )

                    sessionsPerWeek == null || sessionsPerWeek !in 1..7 -> com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "validateArguments",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Validation.Range,
                        uiMessage = UiText.DynamicString("每周训练次数必须在1-7之间"),
                        cause = null,
                    )

                    workoutFocus == null -> com.example.gymbro.core.error.types.ModernDataError(
                        operationName = "validateArguments",
                        errorType = com.example.gymbro.core.error.types.GlobalErrorType.Validation.Required,
                        uiMessage = UiText.DynamicString("workout_focus 不能为空"),
                        cause = null,
                    )

                    else -> null
                }
            } catch (e: Exception) {
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "validateArguments",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.Validation.General,
                    uiMessage = UiText.DynamicString("参数验证异常: ${e.message}"),
                    cause = e,
                )
            }
        }

        /**
         * 解析训练计划参数
         */
        private fun parseWorkoutPlanArguments(arguments: JsonObject): ParsedPlanArgs {
            return ParsedPlanArgs(
                planName = arguments["plan_name"]?.toString()?.replace("\"", "") ?: "",
                description = arguments["description"]?.toString()?.replace("\"", "") ?: "",
                durationType = arguments["duration_type"]?.toString()?.replace("\"", "") ?: "weeks",
                weeks = arguments["weeks"]?.toString()?.toIntOrNull(),
                days = arguments["days"]?.toString()?.toIntOrNull(),
                sessionsPerWeek = arguments["sessions_per_week"]?.toString()?.toIntOrNull() ?: 3,
                difficultyLevel = arguments["difficulty_level"]?.toString()?.replace("\"", "") ?: "intermediate",
                workoutFocus = parseStringArray(arguments["workout_focus"]),
                targetMuscles = parseStringArray(arguments["target_muscles"]),
                notes = arguments["notes"]?.toString()?.replace("\"", ""),
                equipmentNeeded = parseStringArray(arguments["equipment_needed"]),
            )
        }

        /**
         * 解析JSON数组为字符串列表
         */
        private fun parseStringArray(jsonElement: kotlinx.serialization.json.JsonElement?): List<String> {
            return try {
                if (jsonElement is kotlinx.serialization.json.JsonArray) {
                    jsonElement.map { it.toString().replace("\"", "") }
                } else {
                    emptyList()
                }
            } catch (e: Exception) {
                emptyList()
            }
        }

        /**
         * 根据解析的参数创建训练计划Domain对象
         */
        private fun createWorkoutPlanFromArgs(args: ParsedPlanArgs): WorkoutPlan {
            // 解析训练目标
            val goal = parseWorkoutGoal(args.workoutFocus)

            // 解析难度级别
            val difficulty = parseDifficulty(args.difficultyLevel)

            // 计算持续周数
            val weeks = when (args.durationType) {
                "weeks" -> args.weeks ?: 4
                "days" -> {
                    val days = args.days ?: 28
                    (days + 6) / 7 // 向上取整
                }

                else -> 4
            }

            // TODO: 需要适配新的WorkoutPlan类型
            // 暂时创建一个简单的计划对象
            return WorkoutPlan(
                id = generatePlanId(),
                name = args.planName,
                description = args.description.ifBlank { "AI生成的结构化训练计划" },
                userId = "current_user", // TODO: 从上下文获取实际用户ID
                targetGoal = (args.targetMuscles?.joinToString(", ") ?: "综合训练"),
                difficultyLevel = when (args.difficultyLevel.lowercase()) {
                    "beginner" -> 1
                    "advanced" -> 4
                    else -> 3
                },
                estimatedDuration = args.sessionsPerWeek * 60,
                planType = com.example.gymbro.shared.models.workout.PlanType.LINEAR,
                dailySchedule = emptyMap(),
                totalDays = weeks * 7,
                tags = listOf("ai_generated", args.difficultyLevel, args.durationType) + args.workoutFocus,
                createdAt = Clock.System.now().toEpochMilliseconds(),
                updatedAt = Clock.System.now().toEpochMilliseconds(),
            )
        }

        /**
         * 解析训练目标
         */
        private fun parseWorkoutGoal(workoutFocus: List<String>): WorkoutGoal {
            return when {
                workoutFocus.contains("strength") -> WorkoutGoal.STRENGTH
                workoutFocus.contains("hypertrophy") -> WorkoutGoal.HYPERTROPHY
                workoutFocus.contains("endurance") -> WorkoutGoal.ENDURANCE
                workoutFocus.contains("fat_loss") -> WorkoutGoal.WEIGHT_LOSS
                else -> WorkoutGoal.GENERAL_FITNESS
            }
        }

        /**
         * 解析难度级别
         */
        private fun parseDifficulty(difficultyLevel: String): Difficulty {
            return when (difficultyLevel.lowercase()) {
                "beginner" -> Difficulty.BEGINNER
                "intermediate" -> Difficulty.INTERMEDIATE
                "advanced" -> Difficulty.ADVANCED
                else -> Difficulty.INTERMEDIATE
            }
        }

        /**
         * 生成唯一的计划ID
         */
        private fun generatePlanId(): String {
            return "plan_${Clock.System.now().toEpochMilliseconds()}_${(1000..9999).random()}"
        }
    }

/**
 * 解析后的计划参数
 */
data class ParsedPlanArgs(
    val planName: String,
    val description: String,
    val durationType: String,
    val weeks: Int?,
    val days: Int?,
    val sessionsPerWeek: Int,
    val difficultyLevel: String,
    val workoutFocus: List<String>,
    val targetMuscles: List<String>?,
    val notes: String?,
    val equipmentNeeded: List<String>?,
)

/**
 * 训练计划生成结果
 */
sealed class GeneratePlanResult {
    data class Success(
        val planId: String,
        val planName: String,
        val duration: String,
    ) : GeneratePlanResult()

    data class Partial(
        val planId: String,
        val planName: String,
        val issues: List<String>,
    ) : GeneratePlanResult()
}
