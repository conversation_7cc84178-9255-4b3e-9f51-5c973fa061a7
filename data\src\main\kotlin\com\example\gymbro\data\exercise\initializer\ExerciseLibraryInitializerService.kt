package com.example.gymbro.data.exercise.initializer

import android.content.Context
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.*
import timber.log.Timber

/**
 * 动作库初始化服务
 *
 * 负责在应用启动时初始化动作库相关数据
 * 包括官方动作种子数据的加载
 */
@Singleton
class ExerciseLibraryInitializerService
    @Inject
    constructor(
        @ApplicationContext private val context: Context,
        private val officialExerciseInitializer: OfficialExerciseInitializer,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) {

        private val initializeScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

        /**
         * 初始化动作库
         * 在应用启动时调用，确保基础动作数据可用
         */
        fun initializeExerciseLibrary() {
            initializeScope.launch {
                try {
                    Timber.d("开始动作库初始化...")

                    // 检查是否已经初始化过
                    if (isExerciseLibraryInitialized()) {
                        Timber.d("动作库已经初始化过，跳过初始化")
                        return@launch
                    }

                    // 初始化官方动作
                    initializeOfficialExercises()

                    // 标记初始化完成
                    markExerciseLibraryInitialized()

                    Timber.i("动作库初始化完成")
                } catch (e: Exception) {
                    Timber.e(e, "动作库初始化失败: ${e.message}")
                    recordInitializationError(e)
                }
            }
        }

        /**
         * 初始化官方动作
         */
        private suspend fun initializeOfficialExercises() = withContext(ioDispatcher) {
            try {
                Timber.d("开始初始化官方动作...")
                officialExerciseInitializer.initializeOfficialExercises()
                Timber.d("官方动作初始化完成")
            } catch (e: Exception) {
                Timber.e(e, "官方动作初始化失败")
                throw e
            }
        }

        /**
         * 检查动作库是否已经初始化
         */
        private fun isExerciseLibraryInitialized(): Boolean {
            val sharedPrefs = context.getSharedPreferences(
                "exercise_library_initialization",
                Context.MODE_PRIVATE,
            )
            return sharedPrefs.getBoolean("exercise_library_initialized", false)
        }

        /**
         * 标记动作库初始化完成
         */
        private fun markExerciseLibraryInitialized() {
            val sharedPrefs = context.getSharedPreferences(
                "exercise_library_initialization",
                Context.MODE_PRIVATE,
            )
            sharedPrefs.edit()
                .putBoolean("exercise_library_initialized", true)
                .putLong("exercise_library_init_time", System.currentTimeMillis())
                .apply()
        }

        /**
         * 记录初始化错误
         */
        private fun recordInitializationError(e: Exception) {
            val sharedPrefs = context.getSharedPreferences(
                "exercise_library_initialization",
                Context.MODE_PRIVATE,
            )
            sharedPrefs.edit()
                .putBoolean("exercise_library_init_error", true)
                .putString("exercise_library_error_message", e.message ?: "Unknown error")
                .putLong("exercise_library_error_time", System.currentTimeMillis())
                .apply()
        }

        /**
         * 强制重新初始化动作库
         * 用于调试或数据重置
         */
        suspend fun forceReinitialize() = withContext(ioDispatcher) {
            try {
                Timber.i("强制重新初始化动作库...")

                // 清除初始化标记
                val sharedPrefs = context.getSharedPreferences(
                    "exercise_library_initialization",
                    Context.MODE_PRIVATE,
                )
                sharedPrefs.edit().clear().apply()

                // 重新初始化
                initializeOfficialExercises()
                markExerciseLibraryInitialized()

                Timber.i("动作库强制重新初始化完成")
            } catch (e: Exception) {
                Timber.e(e, "动作库强制重新初始化失败")
                throw e
            }
        }

        /**
         * 获取初始化状态信息
         */
        fun getInitializationStatus(): ExerciseLibraryInitStatus {
            val sharedPrefs = context.getSharedPreferences(
                "exercise_library_initialization",
                Context.MODE_PRIVATE,
            )

            return ExerciseLibraryInitStatus(
                isInitialized = sharedPrefs.getBoolean("exercise_library_initialized", false),
                initTime = sharedPrefs.getLong("exercise_library_init_time", 0L),
                hasError = sharedPrefs.getBoolean("exercise_library_init_error", false),
                errorMessage = sharedPrefs.getString("exercise_library_error_message", null),
                errorTime = sharedPrefs.getLong("exercise_library_error_time", 0L),
            )
        }
    }

/**
 * 动作库初始化状态
 */
data class ExerciseLibraryInitStatus(
    val isInitialized: Boolean,
    val initTime: Long,
    val hasError: Boolean,
    val errorMessage: String?,
    val errorTime: Long,
)
