package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.SessionExercise
import com.example.gymbro.domain.workout.model.SessionSet
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * 保存训练会话用例
 *
 * 此用例将训练会话保存到仓库，可用于创建新会话、更新现有会话或记录训练组
 */
@Singleton
class SaveWorkoutSessionUseCase
    @Inject
    constructor(
        private val sessionRepository: SessionRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<SaveWorkoutSessionUseCase.Params, String>(dispatcher, logger) {
        /**
         * 参数类，支持多种参数类型
         */
        sealed class Params {
            /**
             * 保存整个训练会话
             */
            data class SaveSession(
                val session: WorkoutSession,
            ) : Params()

            /**
             * 记录训练组
             */
            data class RecordSet(
                val sessionId: String,
                val exerciseId: String,
                val exerciseSet: SessionSet,
            ) : Params()
        }

        /**
         * 执行用例，保存训练会话
         *
         * @param parameters 参数类型，可以是整个会话或单个训练组
         * @return 保存结果，成功时返回会话ID
         */
        override suspend fun execute(parameters: Params): ModernResult<String> =
            when (parameters) {
                is Params.SaveSession -> saveSession(parameters.session)
                is Params.RecordSet ->
                    recordSet(
                        parameters.sessionId,
                        parameters.exerciseId,
                        parameters.exerciseSet,
                    )
            }

        /**
         * 保存整个训练会话
         *
         * @param session 要保存的训练会话
         * @return 保存结果，成功时返回会话ID
         */
        private suspend fun saveSession(
            session: WorkoutSession,
        ): ModernResult<String> = sessionRepository.saveSession(session)

        /**
         * 记录训练组
         *
         * @param sessionId 会话ID
         * @param exerciseId 训练动作ID
         * @param exerciseSet 训练组
         * @return 保存结果，成功时返回会话ID
         */
        private suspend fun recordSet(
            sessionId: String,
            exerciseId: String,
            exerciseSet: SessionSet,
        ): ModernResult<String> {
            // 获取会话
            val sessionResult = sessionRepository.getSessionById(sessionId)
            if (sessionResult !is ModernResult.Success) {
                return ModernResult.Error(
                    DataErrors.DataError.notFound(
                        operationName = "recordSet",
                        message = UiText.DynamicString("训练会话未找到"),
                        entityType = "WorkoutSession",
                        entityId = sessionId,
                    ),
                )
            }
            val session = sessionResult.data

            // 检查会话是否为空
            if (session == null) {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "workoutSession",
                        operationName = "SaveWorkoutSessionUseCase.validation",
                    ),
                )
            }

            // 检查会话是否在进行中
            if (session.status != "IN_PROGRESS") {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "sessionStatus",
                        operationName = "SaveWorkoutSessionUseCase.validation",
                    ),
                )
            }

            // 查找或创建SessionExercise
            val exercises = session.exercises.toMutableList()
            val exerciseIndex = exercises.indexOfFirst { it.exerciseId == exerciseId }

            if (exerciseIndex == -1) {
                // 创建新的SessionExercise
                val newExercise =
                    SessionExercise(
                        id = UUID.randomUUID().toString(),
                        sessionId = sessionId,
                        exerciseId = exerciseId,
                        order = exercises.size,
                        name = "Exercise $exerciseId",
                        targetSets = 1,
                        status = "IN_PROGRESS",
                        sets = listOf(exerciseSet),
                        startTime = System.currentTimeMillis(),
                        endTime = null,
                        notes = null,
                    )
                exercises.add(newExercise)
            } else {
                // 更新现有的SessionExercise
                val exercise = exercises[exerciseIndex]
                val sets = exercise.sets.toMutableList()

                // 查找或添加SessionSet（按setNumber比较）
                val setIndex = sets.indexOfFirst { it.setNumber == exerciseSet.setNumber }
                if (setIndex == -1) {
                    sets.add(exerciseSet)
                } else {
                    sets[setIndex] = exerciseSet
                }

                exercises[exerciseIndex] = exercise.copy(sets = sets)
            }

            // 更新会话
            val updatedSession =
                session.copy(
                    exercises = exercises,
                    lastAutosaveTime = System.currentTimeMillis(),
                )

            // 保存会话
            return sessionRepository.saveSession(updatedSession)
        }

        // 兼容性方法，用于支持直接传入WorkoutSession对象的旧代码
        suspend operator fun invoke(
            session: WorkoutSession,
        ): ModernResult<String> = execute(Params.SaveSession(session))

        // 兼容性方法，用于支持直接记录训练组的旧代码
        suspend operator fun invoke(
            sessionId: String,
            exerciseId: String,
            exerciseSet: SessionSet,
        ): ModernResult<String> = execute(Params.RecordSet(sessionId, exerciseId, exerciseSet))
    }
