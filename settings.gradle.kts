// settings.gradle.kts (Final Corrected Version)

@file:Suppress("UnstableApiUsage")

pluginManagement {
    // 包含 build-logic 模块，使其插件可用于项目
    includeBuild("gradle/build-logic")
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    // 为了解决 Gradle 的生命周期问题，我们在这里直接定义插件版本。
    // 这是目前最稳定和推荐的做法。
    // 这些版本应与 libs.versions.toml 中的版本保持手动同步。
    plugins {
        id("com.android.application") version "8.8.1" apply false
        id("com.android.library") version "8.8.1" apply false
        id("org.jetbrains.kotlin.android") version "2.0.21" apply false
        id("org.jetbrains.kotlin.plugin.compose") version "2.0.21" apply false
        id("org.jetbrains.kotlin.plugin.serialization") version "2.0.21" apply false
        id("com.google.devtools.ksp") version "2.0.21-1.0.25" apply false
        id("com.google.dagger.hilt.android") version "2.53" apply false
        id("io.gitlab.arturbosch.detekt") version "1.23.8" apply false
        id("com.google.gms.google-services") version "4.4.2" apply false
        id("com.google.firebase.crashlytics") version "3.0.2" apply false
        id("com.google.android.libraries.mapsplatform.secrets-gradle-plugin") version "2.0.1" apply false
        id("com.gradle.enterprise") version "3.16.2" apply false
    }
}

dependencyResolutionManagement {
    // 确保所有子项目都使用这里定义的仓库，而不是在自己的 build.gradle.kts 中定义
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        // JitPack 用于一些特殊的开源库
        maven { url = uri("https://jitpack.io") }
    }
}

rootProject.name = "GymBro"
include(
    ":app",
    ":core",
    ":core-arch",
    ":core-ml",
    ":core-network",
    ":shared-models",
    ":data",
    ":detekt",
    ":designSystem",
    ":di",
    ":domain",
    ":navigation",
    ":features:auth",
    ":features:coach",
    ":features:home",
    ":features:profile",
    ":features:subscription",
    ":features:workout",
    "features:exercise-library",
    "core-network",
    "shared-models",
    "features:thinkingbox",
    "core-user-data-center"
)

include("core-ml")

// 配置Gradle Build Scan (CI/CD现代化要求)
plugins {
    id("com.gradle.enterprise")
}

gradleEnterprise {
    buildScan {
        // 在CI环境中自动发布Build Scan
        publishAlways()

        // 接受服务条款
        termsOfServiceUrl = "https://gradle.com/terms-of-service"
        termsOfServiceAgree = "yes"

        // 添加构建信息标签
        tag(if (System.getenv("CI") != null) "CI" else "LOCAL")

        // 添加Git信息
        val gitCommit = System.getenv("GITHUB_SHA") ?: "unknown"
        val gitBranch = System.getenv("GITHUB_REF_NAME") ?: "unknown"

        value("Git Commit", gitCommit)
        value("Git Branch", gitBranch)

        // 添加CI信息
        if (System.getenv("CI") != null) {
            value("CI Provider", "GitHub Actions")
            value("Build Number", System.getenv("GITHUB_RUN_NUMBER") ?: "unknown")
            link(
                "GitHub Actions Run",
                System.getenv("GITHUB_SERVER_URL") + "/" +
                    System.getenv("GITHUB_REPOSITORY") + "/actions/runs/" +
                    System.getenv("GITHUB_RUN_ID"),
            )
        }
    }
}
