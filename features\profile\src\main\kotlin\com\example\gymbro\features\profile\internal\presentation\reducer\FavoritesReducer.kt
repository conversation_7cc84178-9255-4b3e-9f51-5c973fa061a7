package com.example.gymbro.features.profile.internal.presentation.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.features.profile.internal.presentation.contract.FavoritesContract
import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * Favorites功能的Reducer - 完整实现版本
 * 基于Profile模块最佳实践，遵循MVI 2.0纯函数状态管理原则
 *
 * 核心职责：
 * - 处理所有Intent并生成新的State
 * - 生成必要的Effect用于副作用处理
 * - 保持纯函数特性，无副作用操作
 * - 遵循不可变状态更新模式
 */
@Singleton
internal class FavoritesReducer
    @Inject
    constructor() :
    Reducer<FavoritesContract.Intent, FavoritesContract.State, FavoritesContract.Effect> {

        override fun reduce(
            intent: FavoritesContract.Intent,
            currentState: FavoritesContract.State,
        ): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "处理Intent: ${intent::class.simpleName}")

            return when (intent) {
                // === 数据加载相关 ===
                is FavoritesContract.Intent.LoadFavorites -> handleLoadFavorites(currentState)
                is FavoritesContract.Intent.RefreshFavorites -> handleRefreshFavorites(currentState)
                is FavoritesContract.Intent.RetryLoad -> handleRetryLoad(currentState)

                // === 收藏操作相关 ===
                is FavoritesContract.Intent.ToggleTemplateFavorite -> handleToggleTemplateFavorite(
                    intent,
                    currentState,
                )

                is FavoritesContract.Intent.TogglePlanFavorite -> handleTogglePlanFavorite(intent, currentState)

                // === 导航相关 ===
                is FavoritesContract.Intent.OpenTemplateDetail -> handleOpenTemplateDetail(intent, currentState)
                is FavoritesContract.Intent.OpenPlanDetail -> handleOpenPlanDetail(intent, currentState)

                // === 搜索和筛选相关 ===
                is FavoritesContract.Intent.UpdateSearchQuery -> handleUpdateSearchQuery(intent, currentState)
                is FavoritesContract.Intent.UpdateFilter -> handleUpdateFilter(intent, currentState)
                is FavoritesContract.Intent.ClearSearch -> handleClearSearch(currentState)

                // === 错误处理相关 ===
                is FavoritesContract.Intent.ClearError -> handleClearError(currentState)
            }
        }

        // === 数据加载处理 ===

        private fun handleLoadFavorites(state: FavoritesContract.State): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "开始加载收藏数据")
            return ReduceResult.stateOnly(
                state.copy(
                    isLoading = true,
                    error = null,
                ),
            )
        }

        private fun handleRefreshFavorites(state: FavoritesContract.State): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "开始刷新收藏数据")
            return ReduceResult.stateOnly(
                state.copy(
                    isRefreshing = true,
                    error = null,
                ),
            )
        }

        private fun handleRetryLoad(state: FavoritesContract.State): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "重试加载收藏数据")
            return ReduceResult.stateOnly(
                state.copy(
                    isLoading = true,
                    error = null,
                ),
            )
        }

        // === 收藏操作处理 ===

        private fun handleToggleTemplateFavorite(
            intent: FavoritesContract.Intent.ToggleTemplateFavorite,
            state: FavoritesContract.State,
        ): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "切换模板收藏状态: ${intent.templateId}")

            // 查找模板并切换收藏状态
            val updatedTemplates = state.favoriteTemplates.map { template ->
                if (template.id == intent.templateId) {
                    template.copy(isFavorite = !template.isFavorite)
                } else {
                    template
                }
            }

            // 如果模板被取消收藏，从列表中移除
            val filteredTemplates = updatedTemplates.filter { it.isFavorite }

            val newState = state.copy(
                favoriteTemplates = filteredTemplates,
                isEmpty = filteredTemplates.isEmpty() && state.favoritePlans.isEmpty(),
            )

            return ReduceResult.stateOnly(newState)
        }

        private fun handleTogglePlanFavorite(
            intent: FavoritesContract.Intent.TogglePlanFavorite,
            state: FavoritesContract.State,
        ): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "切换计划收藏状态: ${intent.planId}")

            // 查找计划并切换收藏状态
            val updatedPlans = state.favoritePlans.map { plan ->
                if (plan.id == intent.planId) {
                    plan.copy(isFavorite = !plan.isFavorite)
                } else {
                    plan
                }
            }

            // 如果计划被取消收藏，从列表中移除
            val filteredPlans = updatedPlans.filter { it.isFavorite }

            val newState = state.copy(
                favoritePlans = filteredPlans,
                isEmpty = state.favoriteTemplates.isEmpty() && filteredPlans.isEmpty(),
            )

            return ReduceResult.stateOnly(newState)
        }

        // === 导航处理 ===

        private fun handleOpenTemplateDetail(
            intent: FavoritesContract.Intent.OpenTemplateDetail,
            state: FavoritesContract.State,
        ): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "打开模板详情: ${intent.templateId}")
            return ReduceResult.withEffect(
                state,
                FavoritesContract.Effect.NavigateToTemplateDetail(intent.templateId),
            )
        }

        private fun handleOpenPlanDetail(
            intent: FavoritesContract.Intent.OpenPlanDetail,
            state: FavoritesContract.State,
        ): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "打开计划详情: ${intent.planId}")
            return ReduceResult.withEffect(
                state,
                FavoritesContract.Effect.NavigateToPlanDetail(intent.planId),
            )
        }

        // === 搜索和筛选处理 ===

        private fun handleUpdateSearchQuery(
            intent: FavoritesContract.Intent.UpdateSearchQuery,
            state: FavoritesContract.State,
        ): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "更新搜索查询: ${intent.query}")
            return ReduceResult.stateOnly(
                state.copy(searchQuery = intent.query),
            )
        }

        private fun handleUpdateFilter(
            intent: FavoritesContract.Intent.UpdateFilter,
            state: FavoritesContract.State,
        ): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "更新筛选条件: ${intent.filter}")
            return ReduceResult.stateOnly(
                state.copy(selectedFilter = intent.filter),
            )
        }

        private fun handleClearSearch(state: FavoritesContract.State): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "清除搜索条件")
            return ReduceResult.stateOnly(
                state.copy(
                    searchQuery = "",
                    selectedFilter = FavoritesContract.FavoriteFilter.ALL,
                ),
            )
        }

        // === 错误处理 ===

        private fun handleClearError(state: FavoritesContract.State): ReduceResult<FavoritesContract.State, FavoritesContract.Effect> {
            Timber.d("FavoritesReducer", "清除错误状态")
            return ReduceResult.stateOnly(
                state.copy(error = null),
            )
        }
    }
