package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import java.util.*
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.datetime.LocalDate

/**
 * 将训练计划应用到日历用例
 *
 * 此用例将训练计划应用到日历，为计划中的每个训练日创建训练会话
 */
class ApplyPlanToCalendarUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val sessionRepository: SessionRepository,
        private val templateRepository: TemplateRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<ApplyPlanToCalendarUseCase.Params, ApplyPlanToCalendarUseCase.Result>(
        dispatcher,
        logger,
    ) {
        /**
         * 参数类
         *
         * @property planId 训练计划ID
         * @property startDate 开始日期
         * @property endDate 结束日期
         * @property generateCalendarJson 是否生成calendar.json输出
         */
        data class Params(
            val planId: String,
            val startDate: LocalDate,
            val endDate: LocalDate,
            val generateCalendarJson: Boolean = false,
        )

        /**
         * 结果类
         *
         * @property calendarData calendar.json数据（如果请求生成）
         */
        data class Result(
            val calendarData: com.example.gymbro.shared.models.workout.PlanCalendarData? = null,
        )

        /**
         * 执行用例，将训练计划应用到日历
         *
         * 🎯 核心功能：实现Plan层的calendar.json输出要求
         * 遵循Function Call输出模式，支持日历视图展示
         *
         * @param parameters 参数
         * @return 应用结果，包含可选的calendar.json数据
         */
        override suspend fun execute(parameters: Params): ModernResult<Result> {
            val (planId, startDate, endDate) = parameters

            // 获取当前用户ID
            val userIdResult = getCurrentUserIdUseCase().first()
            if (userIdResult !is ModernResult.Success || userIdResult.data == null) {
                return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "applyPlanToCalendar",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap =
                            mapOf(
                                "errorType" to "UNAUTHORIZED",
                                "category" to ErrorCategory.AUTH.name,
                            ),
                    ),
                )
            }
            val userId = userIdResult.data

            // 获取训练计划
            val planResult = planRepository.getPlan(planId)
            if (planResult !is ModernResult.Success) {
                return ModernResult.Error(
                    DataErrors.DataError.notFound(
                        operationName = "applyPlanToCalendar",
                        message = UiText.DynamicString("训练计划未找到"),
                        entityType = "WorkoutPlan",
                        entityId = planId,
                    ),
                )
            }
            val plan = planResult.data

            // 创建训练会话
            var currentDate = startDate
            while (currentDate <= endDate) {
                // 检查该日期是否有训练安排
                // 注意：原实现使用getTemplateForDate，但标准WorkoutPlan类结构不同
                // 这里使用WorkoutPlan的getWorkoutsForWeek方法来获取该周的训练
                val dayOfWeek = currentDate.dayOfWeek.value
                val weekDay = dayOfWeek - 1 // 调整为0-6的索引

                // 获取当前天的训练安排
                val dayNumber = (currentDate.toEpochDays() - startDate.toEpochDays()).toInt() + 1
                val dayPlan = plan?.dailySchedule?.get(dayNumber)
                val workouts: List<String> = if (dayPlan != null && !dayPlan.isRestDay) {
                    dayPlan.templateIds
                } else {
                    emptyList()
                }

                if (workouts.isNotEmpty()) {
                    for (templateId in workouts) {
                        // 获取模板名称
                        val templateResult = templateRepository.getTemplateById(templateId)
                        val templateName =
                            if (templateResult is ModernResult.Success && templateResult.data != null) {
                                templateResult.data?.name ?: "计划训练"
                            } else {
                                "计划训练"
                            }

                        // 计算时间戳
                        val plannedTimestamp = currentDate.toEpochDays().toLong() * 86400000 // 转换为毫秒时间戳

                        // 创建会话
                        val session =
                            WorkoutSession(
                                id = UUID.randomUUID().toString(),
                                userId = userId ?: "",
                                templateId = templateId,
                                planId = planId,
                                name = templateName,
                                status = "PLANNED",
                                startTime = plannedTimestamp,
                                endTime = null,
                                totalDuration = null,
                                totalVolume = null,
                                caloriesBurned = null,
                                notes = "从计划生成的训练",
                                rating = null,
                                lastAutosaveTime = System.currentTimeMillis(),
                                exercises = emptyList(),
                            )
                        sessionRepository.saveSession(session)
                    }
                }

                // 移动到下一天
                currentDate =
                    LocalDate(
                        currentDate.year,
                        currentDate.monthNumber,
                        currentDate.dayOfMonth + 1,
                    )
            }

            // 生成calendar.json数据（如果请求）
            val calendarData = if (parameters.generateCalendarJson) {
                val calendarResult = planRepository.generatePlanCalendarJson(
                    planId = planId,
                    startDate = startDate.toString(),
                )
                when (calendarResult) {
                    is ModernResult.Success -> calendarResult.data
                    is ModernResult.Error -> {
                        logger.w("生成calendar.json失败: ${calendarResult.error}")
                        null
                    }

                    is ModernResult.Loading -> {
                        logger.w("生成calendar.json超时")
                        null
                    }
                }
            } else {
                null
            }

            return ModernResult.Success(Result(calendarData = calendarData))
        }
    }
