package com.example.gymbro.features.thinkingbox.integration

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.network.output.OutputToken
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.AIThinkingCard
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.shouldShowAIThinkingCard
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test

/**
 * ThinkingBox端到端集成测试
 *
 * 🎯 测试目标：验证完整流程和性能基准
 * 📊 测试要求：端到端集成测试，模拟真实用户场景
 * 🔧 测试框架：Compose UI Test + MockK + kotlin.test
 * 🔥 重点：完整数据流验证 + 性能基准测试 + 四条铁律综合验证
 */
class ThinkingBoxEndToEndTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    companion object {
        // 测试标签
        private const val THINKING_BOX_CONTAINER_TAG = "thinking_box_container"
        private const val AI_THINKING_CARD_TAG = "ai_thinking_card"
        private const val FINAL_CONTENT_TAG = "final_content"

        // 性能基准
        private const val MAX_INITIALIZATION_TIME_MS = 1000L
        private const val MAX_SEGMENT_RENDERING_TIME_MS = 2000L
        private const val MAX_COMPLETE_FLOW_TIME_MS = 5000L
    }

    /**
     * 🔥 【完整流程测试】从SegmentStarted到SegmentRendered的完整数据流
     */
    @Test
    fun testCompleteFlow_SegmentStartedToRendered() = runTest {
        // Given - Mock完整的依赖链
        val mockReducer = mockk<ThinkingBoxReducer>()
        val mockDomainMapper = mockk<DomainMapper>()
        val mockStreamingParser = mockk<StreamingThinkingMLParser>()
        val mockDirectOutputChannel = mockk<DirectOutputChannel>()

        // 设置Mock行为 - 模拟完整的事件流
        val testMessageId = "end-to-end-test"
        val testTokens = listOf(
            OutputToken("<thinking>", "text/plain"),
            OutputToken("<phase title=\"分析问题\">", "text/plain"),
            OutputToken("正在分析用户的健身需求...", "text/plain"),
            OutputToken("</phase>", "text/plain"),
            OutputToken("</thinking>", "text/plain"),
            OutputToken("<final>", "text/plain"),
            OutputToken("基于分析，我建议以下健身方案...", "text/plain"),
            OutputToken("</final>", "text/plain"),
        )

        coEvery {
            mockDirectOutputChannel.subscribeToMessage(testMessageId) // 🔥 【Plan B重构】使用新方法
        } returns flowOf(*testTokens.toTypedArray())

        // 模拟语义事件序列
        val semanticEvents = listOf(
            SemanticEvent.ThinkingStart,
            SemanticEvent.PhaseStart("phase1", "分析问题"),
            SemanticEvent.PhaseContent("正在分析用户的健身需求..."),
            SemanticEvent.PhaseEnd("phase1"),
            SemanticEvent.ThinkingEnd,
            SemanticEvent.FinalStart,
            SemanticEvent.FinalContent("基于分析，我建议以下健身方案..."),
            SemanticEvent.FinalEnd,
        )

        // 模拟思考事件序列
        val thinkingEvents = listOf(
            ThinkingEvent.SegmentStarted("phase1", SegmentKind.PHASE, "分析问题"),
            ThinkingEvent.SegmentText("正在分析用户的健身需求..."),
            ThinkingEvent.SegmentClosed("phase1"),
            ThinkingEvent.ThinkingClosed,
            ThinkingEvent.FinalStart,
            ThinkingEvent.FinalContent("基于分析，我建议以下健身方案..."),
            ThinkingEvent.FinalComplete,
        )

        // 设置Parser回调
        coEvery { mockStreamingParser.parseTokenStream(any(), any(), any()) } coAnswers {
            val onEvent = thirdArg<suspend (SemanticEvent) -> Unit>()
            semanticEvents.forEach { event ->
                onEvent(event)
                delay(50) // 模拟解析延迟
            }
        }

        // 设置DomainMapper映射
        semanticEvents.zip(thinkingEvents).forEach { (semantic, thinking) ->
            every { mockDomainMapper.mapSemanticToThinking(semantic, any()) } returns DomainMapper.MappingResult(
                events = listOf(thinking),
                context = DomainMapper.MappingContext(),
            )
        }

        // 设置Reducer状态转换
        val finalState = ThinkingBoxContract.State(
            messageId = testMessageId,
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "phase1",
                    kind = SegmentKind.PHASE,
                    title = "分析问题",
                    content = "正在分析用户的健身需求...",
                    isComplete = true,
                    isRendered = false,
                ),
            ),
            finalReady = true,
            finalContent = "基于分析，我建议以下健身方案...",
            thinkingClosed = true,
        )

        every { mockReducer.reduce(any(), any()) } returns ReduceResult.stateOnly(finalState)
        every { mockReducer.handleThinkingEvent(any(), any()) } returns ReduceResult.stateOnly(finalState)

        // 创建ViewModel
        val viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = mockReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            directOutputChannel = mockDirectOutputChannel,
        )

        var segmentRenderedCount = 0
        val startTime = System.currentTimeMillis()

        composeTestRule.setContent {
            GymBroTheme {
                val state by viewModel.state.collectAsState()

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .testTag(THINKING_BOX_CONTAINER_TAG),
                ) {
                    if (shouldShowAIThinkingCard(state)) {
                        AIThinkingCard(
                            state = state,
                            messageId = testMessageId,
                            modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                            onSegmentRendered = { segmentRenderedCount++ },
                        )
                    }

                    if (state.shouldShowFinalContent) {
                        androidx.compose.material3.Text(
                            text = state.finalContent,
                            modifier = Modifier.testTag(FINAL_CONTENT_TAG),
                        )
                    }
                }
            }
        }

        // When - 启动完整流程
        viewModel.initialize(testMessageId)

        // 等待完整流程完成
        delay(3000)
        composeTestRule.waitForIdle()

        val totalTime = System.currentTimeMillis() - startTime

        // Then - 验证完整流程
        composeTestRule.onNodeWithTag(THINKING_BOX_CONTAINER_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithText("分析问题").assertIsDisplayed()
        composeTestRule.onNodeWithText("正在分析用户的健身需求...").assertIsDisplayed()

        // 验证性能基准
        assertTrue(
            totalTime <= MAX_COMPLETE_FLOW_TIME_MS,
            "完整流程时间应该≤${MAX_COMPLETE_FLOW_TIME_MS}ms，实际: ${totalTime}ms",
        )

        // 验证段渲染回调
        assertTrue(segmentRenderedCount > 0, "应该有段渲染完成")
    }

    /**
     * 🔥 【性能基准测试】ViewModel初始化性能验证
     */
    @Test
    fun testPerformanceBenchmark_ViewModelInitialization() = runTest {
        // Given
        val mockReducer = mockk<ThinkingBoxReducer>()
        val mockDomainMapper = mockk<DomainMapper>()
        val mockStreamingParser = mockk<StreamingThinkingMLParser>()
        val mockDirectOutputChannel = mockk<DirectOutputChannel>()

        // 设置基本Mock
        every { mockReducer.reduce(any(), any()) } returns ReduceResult.stateOnly(ThinkingBoxContract.State())
        coEvery { mockDirectOutputChannel.subscribeToMessage(any()) } returns flowOf() // 🔥 【Plan B重构】使用新方法

        // When - 测量初始化时间
        val startTime = System.currentTimeMillis()

        val viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = mockReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            directOutputChannel = mockDirectOutputChannel,
        )

        composeTestRule.setContent {
            GymBroTheme {
                val state by viewModel.state.collectAsState()

                AIThinkingCard(
                    state = state,
                    messageId = "performance-test",
                    modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                )
            }
        }

        composeTestRule.waitForIdle()
        val initTime = System.currentTimeMillis() - startTime

        // Then - 验证初始化性能
        assertTrue(
            initTime <= MAX_INITIALIZATION_TIME_MS,
            "ViewModel初始化时间应该≤${MAX_INITIALIZATION_TIME_MS}ms，实际: ${initTime}ms",
        )

        viewModel.initialize("performance-test")
        composeTestRule.waitForIdle()
    }

    /**
     * 🔥 【多段内容连续显示】模拟真实AI响应的多阶段思考过程
     */
    @Test
    fun testMultiSegmentFlow_ContinuousDisplay() = runTest {
        // Given - 模拟多阶段思考
        val mockReducer = mockk<ThinkingBoxReducer>()
        val mockDomainMapper = mockk<DomainMapper>()
        val mockStreamingParser = mockk<StreamingThinkingMLParser>()
        val mockDirectOutputChannel = mockk<DirectOutputChannel>()

        val testMessageId = "multi-segment-test"

        // 模拟多段思考内容
        val multiStageTokens = listOf(
            // 第一阶段：问题分析
            OutputToken("<phase title=\"问题分析\">", "text/plain"),
            OutputToken("分析用户的健身目标和当前状态", "text/plain"),
            OutputToken("</phase>", "text/plain"),

            // 第二阶段：方案制定
            OutputToken("<phase title=\"方案制定\">", "text/plain"),
            OutputToken("基于分析制定个性化训练计划", "text/plain"),
            OutputToken("</phase>", "text/plain"),

            // 第三阶段：风险评估
            OutputToken("<phase title=\"风险评估\">", "text/plain"),
            OutputToken("评估训练方案的潜在风险", "text/plain"),
            OutputToken("</phase>", "text/plain"),
        )

        coEvery {
            mockDirectOutputChannel.subscribeToMessage(testMessageId) // 🔥 【Plan B重构】使用新方法
        } returns flowOf(*multiStageTokens.toTypedArray())

        // 设置多段状态
        val multiSegmentState = ThinkingBoxContract.State(
            messageId = testMessageId,
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    "phase1",
                    SegmentKind.PHASE,
                    "问题分析",
                    "分析用户的健身目标和当前状态",
                    true,
                ),
                ThinkingBoxContract.SegmentUi(
                    "phase2",
                    SegmentKind.PHASE,
                    "方案制定",
                    "基于分析制定个性化训练计划",
                    true,
                ),
                ThinkingBoxContract.SegmentUi("phase3", SegmentKind.PHASE, "风险评估", "评估训练方案的潜在风险", true),
            ),
            thinkingClosed = true,
        )

        every { mockReducer.reduce(any(), any()) } returns ReduceResult.stateOnly(multiSegmentState)
        every { mockReducer.handleThinkingEvent(any(), any()) } returns ReduceResult.stateOnly(multiSegmentState)

        val viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = mockReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            directOutputChannel = mockDirectOutputChannel,
        )

        var totalSegmentsRendered = 0
        val renderingTimes = mutableListOf<Long>()

        composeTestRule.setContent {
            GymBroTheme {
                val state by viewModel.state.collectAsState()

                if (shouldShowAIThinkingCard(state)) {
                    AIThinkingCard(
                        state = state,
                        messageId = testMessageId,
                        modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                        onSegmentRendered = { segmentId ->
                            totalSegmentsRendered++
                            renderingTimes.add(System.currentTimeMillis())
                        },
                    )
                }
            }
        }

        // When - 启动多段渲染
        val startTime = System.currentTimeMillis()
        viewModel.initialize(testMessageId)

        // 等待所有段渲染完成
        delay(4000)
        composeTestRule.waitForIdle()

        val totalTime = System.currentTimeMillis() - startTime

        // Then - 验证多段连续显示
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithText("问题分析").assertIsDisplayed()
        composeTestRule.onNodeWithText("方案制定").assertIsDisplayed()
        composeTestRule.onNodeWithText("风险评估").assertIsDisplayed()

        // 验证渲染性能
        assertTrue(
            totalTime <= MAX_COMPLETE_FLOW_TIME_MS,
            "多段渲染时间应该≤${MAX_COMPLETE_FLOW_TIME_MS}ms，实际: ${totalTime}ms",
        )

        // 验证段数量
        assertEquals(3, totalSegmentsRendered, "应该渲染3个段")
    }

    /**
     * 🔥 【四条铁律综合验证】端到端场景下的四条铁律综合测试
     */
    @Test
    fun testFourIronLawsIntegrated_EndToEndScenario() = runTest {
        // Given - 设置综合测试场景
        val mockReducer = mockk<ThinkingBoxReducer>()
        val mockDomainMapper = mockk<DomainMapper>()
        val mockStreamingParser = mockk<StreamingThinkingMLParser>()
        val mockDirectOutputChannel = mockk<DirectOutputChannel>()

        val testMessageId = "four-laws-test"

        // 模拟包含长文本的复杂思考过程
        val complexState = ThinkingBoxContract.State(
            messageId = testMessageId,
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "complex1",
                    kind = SegmentKind.PHASE,
                    title = "复杂分析阶段",
                    content = "这是一个包含大量文本内容的复杂思考阶段。".repeat(50), // 创建长文本测试截断
                    isComplete = true,
                ),
                ThinkingBoxContract.SegmentUi(
                    id = "complex2",
                    kind = SegmentKind.PHASE,
                    title = "方案优化",
                    content = "优化训练方案以达到最佳效果",
                    isComplete = true,
                ),
            ),
            thinkingClosed = false, // 仍在流式传输
        )

        every { mockReducer.reduce(any(), any()) } returns ReduceResult.stateOnly(complexState)
        coEvery { mockDirectOutputChannel.subscribeToMessage(testMessageId) } returns flowOf() // 🔥 【Plan B重构】使用新方法

        val viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = mockReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            directOutputChannel = mockDirectOutputChannel,
        )

        var recompositionCount = 0
        val startRenderTime = System.currentTimeMillis()

        composeTestRule.setContent {
            GymBroTheme {
                recompositionCount++
                val state by viewModel.state.collectAsState()

                Column(modifier = Modifier.fillMaxSize()) {
                    if (shouldShowAIThinkingCard(state)) {
                        AIThinkingCard(
                            state = state,
                            messageId = testMessageId,
                            modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                        )
                    }
                }
            }
        }

        // When - 启动四条铁律综合测试
        viewModel.initialize(testMessageId)

        // 等待渲染和交互完成
        delay(3000)
        composeTestRule.waitForIdle()

        val totalRenderTime = System.currentTimeMillis() - startRenderTime

        // Then - 验证四条铁律综合表现

        // 🔥 【铁律1】UI不重组刷新 - LazyColumn架构
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
        assertTrue(
            recompositionCount <= 5, // 允许合理的重组次数
            "重组次数应该最小化，实际: $recompositionCount",
        )

        // 🔥 【铁律2+4】打字机效果 + 文本截断 - 通过内容显示验证
        composeTestRule.onNodeWithText("复杂分析阶段").assertIsDisplayed()
        composeTestRule.onNodeWithText("方案优化").assertIsDisplayed()

        // 🔥 【铁律3】高度限制 - 通过UI测试验证
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG)
            .assertIsDisplayed()

        // 综合性能验证
        assertTrue(
            totalRenderTime <= MAX_COMPLETE_FLOW_TIME_MS,
            "四条铁律综合场景渲染时间应该≤${MAX_COMPLETE_FLOW_TIME_MS}ms，实际: ${totalRenderTime}ms",
        )
    }

    /**
     * 🔥 【错误恢复测试】异常情况下的系统稳定性验证
     */
    @Test
    fun testErrorRecovery_SystemStability() = runTest {
        // Given - 模拟各种异常情况
        val mockReducer = mockk<ThinkingBoxReducer>()
        val mockDomainMapper = mockk<DomainMapper>()
        val mockStreamingParser = mockk<StreamingThinkingMLParser>()
        val mockDirectOutputChannel = mockk<DirectOutputChannel>()

        val testMessageId = "error-recovery-test"

        // 模拟网络异常
        coEvery {
            mockDirectOutputChannel.subscribeToMessage(testMessageId) // 🔥 【Plan B重构】使用新方法
        } throws RuntimeException("Network error")

        // 设置错误状态
        val errorState = ThinkingBoxContract.State(
            messageId = testMessageId,
            error = com.example.gymbro.core.ui.text.UiText.DynamicString("网络连接失败"),
        )

        every { mockReducer.reduce(any(), any()) } returns ReduceResult.stateOnly(errorState)

        val viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = mockReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            directOutputChannel = mockDirectOutputChannel,
        )

        composeTestRule.setContent {
            GymBroTheme {
                val state by viewModel.state.collectAsState()

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .testTag(THINKING_BOX_CONTAINER_TAG),
                ) {
                    if (shouldShowAIThinkingCard(state)) {
                        AIThinkingCard(
                            state = state,
                            messageId = testMessageId,
                            modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                        )
                    }

                    // 显示错误信息
                    state.error?.let { error ->
                        androidx.compose.material3.Text(
                            text = error.asString(),
                            modifier = Modifier.testTag("error_display"),
                        )
                    }
                }
            }
        }

        // When - 触发错误场景
        viewModel.initialize(testMessageId)

        // 等待错误处理完成
        delay(1000)
        composeTestRule.waitForIdle()

        // Then - 验证错误恢复
        composeTestRule.onNodeWithTag(THINKING_BOX_CONTAINER_TAG).assertIsDisplayed()

        // 系统应该保持稳定，不崩溃
        // 错误信息应该被正确显示或处理

        // 测试错误清除
        viewModel.clearError()
        delay(100)
        composeTestRule.waitForIdle()

        // 验证系统可以从错误中恢复
        assertTrue(true, "系统应该能够从错误中恢复")
    }

    /**
     * 🔥 【内存使用监控】长时间运行的内存稳定性验证
     */
    @Test
    fun testMemoryStability_LongRunning() = runTest {
        // Given
        val mockReducer = mockk<ThinkingBoxReducer>()
        val mockDomainMapper = mockk<DomainMapper>()
        val mockStreamingParser = mockk<StreamingThinkingMLParser>()
        val mockDirectOutputChannel = mockk<DirectOutputChannel>()

        // 模拟大量段的循环创建和销毁
        val largeSegmentList = (1..20).map { i ->
            ThinkingBoxContract.SegmentUi(
                id = "memory-test-$i",
                kind = SegmentKind.PHASE,
                title = "内存测试段$i",
                content = "这是内存测试内容".repeat(100),
                isComplete = true,
            )
        }

        val memoryTestState = ThinkingBoxContract.State(
            messageId = "memory-test",
            segmentsQueue = largeSegmentList,
            thinkingClosed = true,
        )

        every { mockReducer.reduce(any(), any()) } returns ReduceResult.stateOnly(memoryTestState)
        coEvery { mockDirectOutputChannel.subscribeToMessage(any()) } returns flowOf() // 🔥 【Plan B重构】使用新方法

        val viewModel = ThinkingBoxViewModel(
            thinkingBoxReducer = mockReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            directOutputChannel = mockDirectOutputChannel,
        )

        // 记录初始内存使用
        System.gc() // 强制垃圾回收
        val initialMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()

        composeTestRule.setContent {
            GymBroTheme {
                val state by viewModel.state.collectAsState()

                if (shouldShowAIThinkingCard(state)) {
                    AIThinkingCard(
                        state = state,
                        messageId = "memory-test",
                        modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                    )
                }
            }
        }

        // When - 长时间运行测试
        viewModel.initialize("memory-test")

        // 多次重置和重新初始化以测试内存释放
        repeat(5) {
            delay(200)
            viewModel.reset()
            delay(100)
            viewModel.initialize("memory-test-$it")
            composeTestRule.waitForIdle()
        }

        // 最终垃圾回收和内存检查
        System.gc()
        delay(500)
        val finalMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()

        // Then - 验证内存稳定性
        val memoryIncrease = finalMemory - initialMemory
        val memoryIncreaseKB = memoryIncrease / 1024

        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()

        // 内存增长应该在合理范围内（允许一定的内存增长，但不应该有严重泄漏）
        assertTrue(
            memoryIncreaseKB < 50 * 1024, // 50MB以内认为是合理的
            "内存增长应该在合理范围内，实际增长: ${memoryIncreaseKB}KB",
        )
    }
}
