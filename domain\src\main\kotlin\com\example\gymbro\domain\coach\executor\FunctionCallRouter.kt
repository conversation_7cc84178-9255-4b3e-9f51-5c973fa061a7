package com.example.gymbro.domain.coach.executor

import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.coach.executor.modules.*
import com.example.gymbro.domain.workout.model.WorkoutAction
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Function Call路由器
 *
 * 实现"域分离 + 路由器统一"的Function Call架构
 * 负责将Function Call请求路由到对应的业务域模块处理
 *
 * 核心功能：
 * 1. 域分发：根据函数名前缀路由到对应域模块
 * 2. 兼容性：支持旧版本函数名映射
 * 3. 错误处理：统一的错误处理和日志记录
 * 4. 扩展性：新域模块可无缝接入
 *
 * 路由规则：
 * - gymbro.exercise.* → ExerciseFunctionModule
 * - gymbro.template.* → TemplateFunctionModule
 * - gymbro.plan.* → PlanFunctionModule
 * - gymbro.session.* → SessionFunctionModule
 * - gymbro.calendar.* → CalendarFunctionModule
 * - gymbro.nutrition.* → NutritionFunctionModule
 * - gymbro.training.* → TrainingAnalysisFunctionModule
 * - 旧版本函数名 → 映射到新域
 *
 * @property exerciseModule 动作库域模块
 * @property templateModule 训练模板域模块
 * @property planModule 训练计划域模块
 * @property sessionModule 训练会话域模块
 * @property calendarModule 日历域模块
 * @property logger 日志记录器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
@Singleton
class FunctionCallRouter
    @Inject
    constructor(
        private val exerciseModule: ExerciseFunctionModule,
        private val templateModule: TemplateFunctionModule,
        private val planModule: PlanFunctionModule,
        private val sessionModule: SessionFunctionModule,
        private val calendarModule: CalendarFunctionModule,
        private val logger: Logger,
    ) {

        /**
         * 路由Function Call到对应的业务域模块
         *
         * @param functionCall Function Call请求
         * @param onActionTrigger UI动作触发回调
         * @return 函数执行结果
         */
        suspend fun routeFunctionCall(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)? = null,
        ): ModernResult<FunctionResult> = safeCatch {
            logger.d("🎯 Function Call路由: ${functionCall.name}")

            // 记录路由统计
            recordRoutingStats(functionCall.name)

            val result = when {
                // 动作库域
                functionCall.name.startsWith("gymbro.exercise.") -> {
                    logger.d("路由到动作库域: ${functionCall.name}")
                    exerciseModule.handle(functionCall, onActionTrigger)
                }

                // 训练模板域
                functionCall.name.startsWith("gymbro.template.") -> {
                    logger.d("路由到训练模板域: ${functionCall.name}")
                    templateModule.handle(functionCall, onActionTrigger)
                }

                // 训练计划域
                functionCall.name.startsWith("gymbro.plan.") -> {
                    logger.d("路由到训练计划域: ${functionCall.name}")
                    planModule.handle(functionCall, onActionTrigger)
                }

                // 训练会话域
                functionCall.name.startsWith("gymbro.session.") -> {
                    logger.d("路由到训练会话域: ${functionCall.name}")
                    sessionModule.handle(functionCall, onActionTrigger)
                }

                // 日历域
                functionCall.name.startsWith("gymbro.calendar.") -> {
                    logger.d("路由到日历域: ${functionCall.name}")
                    calendarModule.handle(functionCall, onActionTrigger)
                }

                // 营养建议域
                functionCall.name.startsWith("gymbro.nutrition.") -> {
                    logger.d("路由到营养建议域: ${functionCall.name}")
                    // TODO: 需要创建NutritionFunctionModule
                    ModernResult.Success(
                        FunctionResult(
                            success = true,
                            data = "营养建议功能正在开发中，请稍后使用。",
                        ),
                    )
                }

                // 训练分析域
                functionCall.name.startsWith("gymbro.training.") -> {
                    logger.d("路由到训练分析域: ${functionCall.name}")
                    // TODO: 需要创建TrainingAnalysisFunctionModule
                    ModernResult.Success(
                        FunctionResult(
                            success = true,
                            data = "训练分析功能正在开发中，将为您提供详细的训练数据分析和改进建议。",
                        ),
                    )
                }

                // 兼容旧版本函数名
                functionCall.name in LEGACY_FUNCTION_NAMES -> {
                    logger.d("处理兼容函数: ${functionCall.name}")
                    handleLegacyFunction(functionCall, onActionTrigger)
                }

                else -> {
                    logger.w("未知的函数域: ${functionCall.name}")
                    ModernResult.Success(
                        FunctionResult(
                            success = false,
                            error = "未知的函数域: ${functionCall.name}",
                        ),
                    )
                }
            }

            when (result) {
                is ModernResult.Success -> result.data
                is ModernResult.Error -> FunctionResult(
                    success = false,
                    error = "路由执行失败: ${result.error}",
                )

                is ModernResult.Loading -> FunctionResult(
                    success = false,
                    error = "路由执行超时，请稍后重试",
                )
            }
        }

        /**
         * 处理兼容的旧版本函数
         * 将旧函数名映射到新的域架构
         */
        private suspend fun handleLegacyFunction(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> {
            val mappedCall = when (functionCall.name) {
                "search_exercises" -> FunctionCall(
                    name = "gymbro.exercise.search",
                    arguments = functionCall.arguments,
                )

                "start_workout_session" -> FunctionCall(
                    name = "gymbro.session.start",
                    arguments = functionCall.arguments,
                )

                "add_exercise_to_session" -> FunctionCall(
                    name = "gymbro.session.log_set",
                    arguments = functionCall.arguments,
                )

                "log_exercise_set" -> FunctionCall(
                    name = "gymbro.session.log_set",
                    arguments = functionCall.arguments,
                )

                "complete_workout_session" -> FunctionCall(
                    name = "gymbro.session.complete",
                    arguments = functionCall.arguments,
                )

                "get_exercise_recommendations" -> FunctionCall(
                    name = "gymbro.exercise.search",
                    arguments = mapOf("query" to "推荐动作") + functionCall.arguments,
                )

                else -> {
                    logger.w("无法映射的旧函数: ${functionCall.name}")
                    return ModernResult.Success(
                        FunctionResult(
                            success = false,
                            error = "无法映射的旧函数: ${functionCall.name}",
                        ),
                    )
                }
            }

            logger.i("旧函数映射: ${functionCall.name} -> ${mappedCall.name}")

            // 递归调用路由器处理映射后的函数
            return routeFunctionCall(mappedCall, onActionTrigger)
        }

        /**
         * 记录路由统计信息
         * 用于监控和优化
         */
        private fun recordRoutingStats(functionName: String) {
            try {
                val domain = when {
                    functionName.startsWith("gymbro.exercise.") -> "exercise"
                    functionName.startsWith("gymbro.template.") -> "template"
                    functionName.startsWith("gymbro.plan.") -> "plan"
                    functionName.startsWith("gymbro.session.") -> "session"
                    functionName.startsWith("gymbro.calendar.") -> "calendar"
                    functionName.startsWith("gymbro.nutrition.") -> "nutrition"
                    functionName.startsWith("gymbro.training.") -> "training"
                    functionName in LEGACY_FUNCTION_NAMES -> "legacy"
                    else -> "unknown"
                }

                // 这里可以集成到分析系统
                logger.d("Function Call统计: domain=$domain, function=$functionName")
            } catch (e: Exception) {
                logger.w("记录路由统计失败", e)
            }
        }

        /**
         * 获取支持的函数列表
         * 用于调试和文档生成
         */
        fun getSupportedFunctions(): Map<String, List<String>> {
            return mapOf(
                "exercise" to listOf(
                    "gymbro.exercise.search",
                    "gymbro.exercise.get_detail",
                    "gymbro.exercise.upsert",
                ),
                "template" to listOf(
                    "gymbro.template.search",
                    "gymbro.template.generate",
                ),
                "plan" to listOf(
                    "gymbro.plan.search",
                    "gymbro.plan.get_detail",
                    "gymbro.plan.upsert",
                    "gymbro.plan.generate_blank",
                    "gymbro.plan.generate_calendar_json",
                ),
                "session" to listOf(
                    "gymbro.session.start",
                    "gymbro.session.log_set",
                    "gymbro.session.complete",
                ),
                "calendar" to listOf(
                    "gymbro.calendar.add_template",
                    "gymbro.calendar.add_plan",
                    "gymbro.calendar.move_item",
                    "gymbro.calendar.remove_item",
                    "gymbro.calendar.get_schedule",
                    "gymbro.calendar.add_custom_workout",
                ),
                "nutrition" to listOf(
                    "gymbro.nutrition.get_recommendations",
                    "gymbro.nutrition.get_meal_plan",
                ),
                "training" to listOf(
                    "gymbro.training.analyze_workout",
                    "gymbro.training.get_progress_report",
                ),
                "legacy" to LEGACY_FUNCTION_NAMES.toList(),
            )
        }

        companion object {
            /**
             * 兼容旧版本函数名映射
             * 保持向后兼容性
             */
            private val LEGACY_FUNCTION_NAMES = setOf(
                "search_exercises",
                "start_workout_session",
                "add_exercise_to_session",
                "log_exercise_set",
                "complete_workout_session",
                "get_exercise_recommendations",
                "get_workout_history",
                "set_rest_timer",
            )
        }
    }

/**
 * Function Call请求数据类
 * 兼容现有的FunctionCallExecutor
 */
data class FunctionCall(
    val name: String,
    val arguments: Map<String, String>,
)

/**
 * Function Call执行结果
 * 兼容现有的FunctionCallExecutor
 * 🔥 增强：添加resourceId支持精确导航
 */
data class FunctionResult(
    val success: Boolean,
    val data: String? = null,
    val error: String? = null,
    val actionTriggered: String? = null,
    val metadata: Map<String, Any> = emptyMap(),
    val executionPath: String? = null, // 执行路径标识
    val resourceId: String? = null, // 🔥 新增：创建的资源ID
    val resourceType: String? = null, // 🔥 新增：资源类型
)
