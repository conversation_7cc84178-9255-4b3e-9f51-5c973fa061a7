package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.repository.PlanRepository
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first

/**
 * 管理训练计划分类用例
 *
 * 🎯 核心功能：管理Plan的分类状态
 * - 切换收藏状态
 * - 标记AI生成
 *
 * 支持Plan层的三Tab设计分类管理
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Plan层改造设计)
 */
class ManagePlanCategoryUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<ManagePlanCategoryUseCase.Params, Unit>(dispatcher, logger) {

        /**
         * 操作类型枚举
         */
        enum class Action {
            TOGGLE_FAVORITE, // 切换收藏状态
            MARK_AI_GENERATED, // 标记为AI生成
        }

        /**
         * 参数类
         *
         * @property planId 计划ID
         * @property action 操作类型
         */
        data class Params(
            val planId: String,
            val action: Action,
        )

        /**
         * 执行用例，管理训练计划分类
         *
         * @param parameters 参数
         * @return 操作结果
         */
        override suspend fun execute(parameters: Params): ModernResult<Unit> {
            val (planId, action) = parameters

            // 获取当前用户ID
            val userIdResult = getCurrentUserIdUseCase().first()
            if (userIdResult !is ModernResult.Success || userIdResult.data == null) {
                return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "managePlanCategory",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap =
                            mapOf(
                                "errorType" to "UNAUTHORIZED",
                                "category" to ErrorCategory.AUTH.name,
                                "planId" to planId,
                                "action" to action.name,
                            ),
                    ),
                )
            }

            // 验证计划存在性
            val planResult = planRepository.getPlan(planId)
            if (planResult !is ModernResult.Success) {
                return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "managePlanCategory",
                        message = UiText.DynamicString("训练计划不存在"),
                        metadataMap = mapOf(
                            "planId" to planId,
                            "action" to action.name,
                        ),
                    ),
                )
            }

            // 执行相应操作
            return when (action) {
                Action.TOGGLE_FAVORITE -> {
                    logger.d("切换计划收藏状态: planId=$planId")
                    val result = planRepository.togglePlanFavorite(planId)
                    when (result) {
                        is ModernResult.Success -> {
                            logger.d("计划收藏状态切换成功: planId=$planId")
                            result
                        }

                        is ModernResult.Error -> {
                            logger.e("计划收藏状态切换失败: planId=$planId, error=${result.error}")
                            result
                        }

                        is ModernResult.Loading -> {
                            logger.w("计划收藏状态切换超时: planId=$planId")
                            ModernResult.Error(
                                BusinessErrors.BusinessError.rule(
                                    operationName = "managePlanCategory",
                                    message = UiText.DynamicString("操作超时，请稍后重试"),
                                    metadataMap = mapOf("planId" to planId, "action" to "TOGGLE_FAVORITE"),
                                ),
                            )
                        }
                    }
                }

                Action.MARK_AI_GENERATED -> {
                    logger.d("标记计划为AI生成: planId=$planId")
                    val result = planRepository.markPlanAsAIGenerated(planId)
                    when (result) {
                        is ModernResult.Success -> {
                            logger.d("计划AI生成标记成功: planId=$planId")
                            result
                        }

                        is ModernResult.Error -> {
                            logger.e("计划AI生成标记失败: planId=$planId, error=${result.error}")
                            result
                        }

                        is ModernResult.Loading -> {
                            logger.w("计划AI生成标记超时: planId=$planId")
                            ModernResult.Error(
                                BusinessErrors.BusinessError.rule(
                                    operationName = "managePlanCategory",
                                    message = UiText.DynamicString("操作超时，请稍后重试"),
                                    metadataMap = mapOf("planId" to planId, "action" to "MARK_AI_GENERATED"),
                                ),
                            )
                        }
                    }
                }
            }
        }
    }
