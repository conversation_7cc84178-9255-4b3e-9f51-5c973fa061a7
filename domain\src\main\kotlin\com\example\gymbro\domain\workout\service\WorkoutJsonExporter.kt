package com.example.gymbro.domain.workout.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.session.WorkoutSession
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.shared.models.exercise.ExerciseDto
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * 训练数据JSON导出器 - 统一JSON格式输出
 *
 * 🚧 TODO: 重构中 - 需要适配新的Exercise DTO架构
 *
 * 🎯 核心职责：
 * - 为UI组件提供统一的JSON数据格式
 * - 支持Template、Session、Plan的JSON导出
 * - 标准化数据结构，确保组件兼容性
 * - 优化JSON序列化性能
 *
 * 🏗️ 设计原则：
 * - 统一格式：所有训练数据使用相同的JSON结构
 * - 组件友好：专为WorkoutExerciseComponent等UI组件设计
 * - 性能优化：缓存和懒加载策略
 * - 扩展性：支持未来新的数据类型
 *
 * @param logger 日志记录器
 */
@Singleton
class WorkoutJsonExporter
    @Inject
    constructor(
        private val logger: Logger,
    ) {
        private val json =
            Json {
                prettyPrint = false
                ignoreUnknownKeys = true
                encodeDefaults = true
            }

        /**
         * 导出WorkoutTemplate为JSON格式
         *
         * 转换为WorkoutExerciseComponent兼容的JSON格式
         *
         * @param template 训练模板
         * @return JSON字符串
         */
        suspend fun exportTemplateAsJson(template: WorkoutTemplate): ModernResult<String> =
            try {
                logger.d("WorkoutJsonExporter", "开始导出Template JSON: ${template.id}")

                // 转换为ExerciseDto列表（临时简化版本）
                val exerciseDtos =
                    template.exercises.map { templateExercise ->
                        ExerciseDto(
                            id = templateExercise.id,
                            name = "动作 ${templateExercise.exerciseId}", // ExerciseInTemplate没有name字段，使用exerciseId
                            description = templateExercise.notes ?: "",
                            muscleGroup = com.example.gymbro.shared.models.exercise.MuscleGroup.OTHER,
                            imageUrl = null, // TemplateExercise没有imageUrl字段
                            videoUrl = null, // Template中暂无视频URL
                            notes = templateExercise.notes ?: "",
                        )
                    }

                // 创建统一的训练数据结构
                val workoutData =
                    UnifiedWorkoutData(
                        id = template.id,
                        name = template.name,
                        description = template.description,
                        type = "template",
                        exercises = exerciseDtos,
                        metadata =
                            WorkoutMetadata(
                                createdAt = template.createdAt,
                                updatedAt = template.createdAt,
                                totalExercises = exerciseDtos.size,
                                estimatedDuration = calculateEstimatedDuration(exerciseDtos),
                            ),
                    )

                val jsonString = json.encodeToString(UnifiedWorkoutData.serializer(), workoutData)
                logger.d("WorkoutJsonExporter", "成功导出Template JSON: ${template.id}")
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                logger.e("WorkoutJsonExporter", "导出Template JSON失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "exportTemplateAsJson",
                        message = UiText.DynamicString("导出模板JSON失败: ${e.message}"),
                    ),
                )
            }

        /**
         * 导出WorkoutSession为JSON格式
         *
         * 包含执行状态和内嵌Template数据
         *
         * @param session 训练会话
         * @return JSON字符串
         */
        suspend fun exportSessionAsJson(session: WorkoutSession): ModernResult<String> =
            try {
                logger.d("WorkoutJsonExporter", "开始导出Session JSON: ${session.id}")

                // 转换SessionExercise为ExerciseDto列表（临时简化版本）
                val exerciseDtos =
                    session.exercises.map { sessionExercise ->
                        ExerciseDto(
                            id = sessionExercise.id,
                            name = "运动 ${sessionExercise.exerciseId}", // SessionExercise没有name字段，使用exerciseId
                            description = sessionExercise.notes ?: "",
                            muscleGroup = com.example.gymbro.shared.models.exercise.MuscleGroup.OTHER,
                            imageUrl = null,
                            videoUrl = null,
                            notes = sessionExercise.notes ?: "",
                        )
                    }

                // 创建Session特有的训练数据结构
                val workoutData =
                    UnifiedWorkoutData(
                        id = session.id,
                        name = session.name ?: "未命名训练",
                        description = session.description,
                        type = "session",
                        exercises = exerciseDtos,
                        metadata =
                            WorkoutMetadata(
                                createdAt = session.lastModified,
                                updatedAt = session.lastModified,
                                totalExercises = exerciseDtos.size,
                                estimatedDuration = calculateEstimatedDuration(exerciseDtos),
                                sessionStatus = session.status.name,
                                startedAt = session.startTime,
                                completedAt = session.endTime,
                            ),
                    )

                val jsonString = json.encodeToString(UnifiedWorkoutData.serializer(), workoutData)
                logger.d("WorkoutJsonExporter", "成功导出Session JSON: ${session.id}")
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                logger.e("WorkoutJsonExporter", "导出Session JSON失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "exportSessionAsJson",
                        message = UiText.DynamicString("导出会话JSON失败: ${e.message}"),
                    ),
                )
            }

        /**
         * 导出WorkoutPlan为JSON格式
         *
         * 包含调度信息和关联的Template数据
         *
         * @param plan 训练计划
         * @param includeTemplateDetails 是否包含Template详细信息
         * @return JSON字符串
         */
        suspend fun exportPlanAsJson(
            plan: WorkoutPlan,
            includeTemplateDetails: Boolean = false,
        ): ModernResult<String> =
            try {
                logger.d("WorkoutJsonExporter", "开始导出Plan JSON: ${plan.id}")

                // 创建Plan特有的数据结构
                val planData =
                    UnifiedPlanData(
                        id = plan.id,
                        name = plan.name.toString(), // UiText转String
                        description = plan.description?.toString(),
                        type = "plan",
                        totalDays = plan.totalDays,
                        dailySchedule =
                            plan.dailySchedule.mapValues { (_, dayPlan) ->
                                PlanDayData(
                                    dayNumber = dayPlan.dayNumber,
                                    templateIds = dayPlan.templateVersionIds,
                                    isRestDay = dayPlan.isRestDay,
                                    dayNotes = dayPlan.dayNotes?.toString(),
                                    estimatedDuration = null, // DayPlan v3.1 没有estimatedDuration字段
                                )
                            },
                        metadata =
                            PlanMetadata(
                                createdAt = plan.createdAt,
                                updatedAt = plan.updatedAt,
                                totalDays = plan.totalDays,
                                activeDays = plan.dailySchedule.values.count { !it.isRestDay },
                                restDays = plan.dailySchedule.values.count { it.isRestDay },
                            ),
                    )

                val jsonString = json.encodeToString(UnifiedPlanData.serializer(), planData)
                logger.d("WorkoutJsonExporter", "成功导出Plan JSON: ${plan.id}")
                ModernResult.Success(jsonString)
            } catch (e: Exception) {
                logger.e("WorkoutJsonExporter", "导出Plan JSON失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "exportPlanAsJson",
                        message = UiText.DynamicString("导出计划JSON失败: ${e.message}"),
                    ),
                )
            }

        /**
         * 批量导出多个数据为JSON数组
         *
         * @param dataList 数据列表
         * @param dataType 数据类型
         * @return JSON数组字符串
         */
        suspend fun exportBatchAsJson(
            dataList: List<Any>,
            dataType: String,
        ): ModernResult<String> =
            try {
                logger.d("WorkoutJsonExporter", "开始批量导出JSON: $dataType, ${dataList.size}项")

                val jsonResults = mutableListOf<String>()

                for (data in dataList) {
                    val result =
                        when (dataType.lowercase()) {
                            "template" -> {
                                if (data is WorkoutTemplate) {
                                    exportTemplateAsJson(data)
                                } else {
                                    ModernResult.Error(
                                        DataErrors.DataError.create(
                                            operationName = "exportBatchAsJson",
                                            message = UiText.DynamicString("数据类型不匹配: 期望WorkoutTemplate"),
                                        ),
                                    )
                                }
                            }

                            "session" -> {
                                if (data is WorkoutSession) {
                                    exportSessionAsJson(data)
                                } else {
                                    ModernResult.Error(
                                        DataErrors.DataError.create(
                                            operationName = "exportBatchAsJson",
                                            message = UiText.DynamicString("数据类型不匹配: 期望WorkoutSession"),
                                        ),
                                    )
                                }
                            }

                            "plan" -> {
                                if (data is WorkoutPlan) {
                                    exportPlanAsJson(data)
                                } else {
                                    ModernResult.Error(
                                        DataErrors.DataError.create(
                                            operationName = "exportBatchAsJson",
                                            message = UiText.DynamicString("数据类型不匹配: 期望WorkoutPlan"),
                                        ),
                                    )
                                }
                            }

                            else -> {
                                ModernResult.Error(
                                    DataErrors.DataError.create(
                                        operationName = "exportBatchAsJson",
                                        message = UiText.DynamicString("不支持的数据类型: $dataType"),
                                    ),
                                )
                            }
                        }

                    when (result) {
                        is ModernResult.Success -> jsonResults.add(result.data)
                        is ModernResult.Error -> {
                            logger.w("WorkoutJsonExporter", "批量导出中单项失败")
                            // 继续处理其他项，不中断整个批量操作
                        }

                        is ModernResult.Loading -> continue
                    }
                }

                // 构建JSON数组
                val jsonArray = "[${jsonResults.joinToString(",")}]"
                logger.d("WorkoutJsonExporter", "成功批量导出JSON: ${jsonResults.size}项")
                ModernResult.Success(jsonArray)
            } catch (e: Exception) {
                logger.e("WorkoutJsonExporter", "批量导出JSON失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "exportBatchAsJson",
                        message = UiText.DynamicString("批量导出JSON失败: ${e.message}"),
                    ),
                )
            }

        // ========== 私有辅助方法 ==========

        /**
         * 计算预估训练时长（临时简化版本）
         */
        private fun calculateEstimatedDuration(exercises: List<ExerciseDto>): Int {
            // 临时简化：每个动作估算5分钟
            return exercises.size * 300 // 300秒 = 5分钟
        }
    }

// ========== 统一数据结构定义 ==========

/**
 * 统一的训练数据结构 - 用于Template和Session
 */
@Serializable
data class UnifiedWorkoutData(
    val id: String,
    val name: String,
    val description: String? = null,
    val type: String, // template/session
    val exercises: List<ExerciseDto>,
    val metadata: WorkoutMetadata,
)

/**
 * 训练数据元信息
 */
@Serializable
data class WorkoutMetadata(
    val createdAt: Long,
    val updatedAt: Long,
    val totalExercises: Int,
    val estimatedDuration: Int, // 秒
    val sessionStatus: String? = null, // 仅Session使用
    val startedAt: Long? = null, // 仅Session使用
    val completedAt: Long? = null, // 仅Session使用
)

/**
 * 统一的计划数据结构 - 用于Plan
 */
@Serializable
data class UnifiedPlanData(
    val id: String,
    val name: String,
    val description: String? = null,
    val type: String, // plan
    val totalDays: Int,
    val dailySchedule: Map<Int, PlanDayData>,
    val metadata: PlanMetadata,
)

/**
 * 计划天数据
 */
@Serializable
data class PlanDayData(
    val dayNumber: Int,
    val templateIds: List<String>,
    val isRestDay: Boolean,
    val dayNotes: String? = null,
    val estimatedDuration: Int? = null,
)

/**
 * 计划元信息
 */
@Serializable
data class PlanMetadata(
    val createdAt: Long,
    val updatedAt: Long,
    val totalDays: Int,
    val activeDays: Int,
    val restDays: Int,
)
