package com.example.gymbro.features.workout.session

import com.example.gymbro.core.testing.rule.MainDispatcherRule
import com.example.gymbro.domain.workout.model.Exercise
import com.example.gymbro.domain.workout.model.session.SessionExercise
import com.example.gymbro.domain.workout.model.stats.DailyStats
import com.example.gymbro.domain.workout.model.stats.UnifiedWorkoutStatistics
import com.example.gymbro.domain.workout.usecase.stats.CreateDailyStatsUseCase
import com.example.gymbro.domain.workout.usecase.stats.GetStatsUseCase
import com.example.gymbro.features.workout.session.internal.effect.SessionEffectHandler
import com.example.gymbro.features.workout.session.internal.reducer.SessionReducer
import io.mockk.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

/**
 * Session与Stats模块集成测试
 *
 * 测试目标：
 * 1. 验证训练完成后正确触发统计数据计算
 * 2. 验证统计数据的准确性和完整性
 * 3. 验证Session → Stats的数据流转
 * 4. 验证stats数据能够正确反馈到session状态
 */
@ExtendWith(MainDispatcherRule::class)
class SessionStatsIntegrationTest {

    // Mock dependencies
    private lateinit var mockCreateDailyStatsUseCase: CreateDailyStatsUseCase
    private lateinit var mockGetStatsUseCase: GetStatsUseCase

    // Test subjects
    private lateinit var sessionReducer: SessionReducer
    private lateinit var sessionEffectHandler: SessionEffectHandler
    private lateinit var testScope: TestScope

    @BeforeEach
    fun setup() {
        MockKAnnotations.init(this)

        // Setup mocks
        mockCreateDailyStatsUseCase = mockk()
        mockGetStatsUseCase = mockk()

        // Create test subjects
        sessionReducer = SessionReducer()
        sessionEffectHandler = mockk() // 实际测试中需要真实的effectHandler
        testScope = TestScope()
    }

    @Test
    fun `completed session should trigger daily stats calculation with correct data`() = runTest {
        // Given - 完成的训练会话
        val completedSession = createCompletedSessionWithRealData()
        val expectedDailyStats = createExpectedDailyStats(completedSession)

        // Mock stats use case to return expected result
        coEvery { mockCreateDailyStatsUseCase(any()) } returns
            com.example.gymbro.core.error.types.ModernResult.Success(expectedDailyStats)

        // When - 完成会话触发统计计算
        val completeIntent = SessionContract.Intent.CompleteSession
        val result = sessionReducer.reduce(completeIntent, completedSession)

        // Then - 验证触发了正确的stats计算effect
        assertNotNull(result.effect)
        assertTrue(result.effect is SessionContract.Effect.CalculateDailyStats)

        val statsEffect = result.effect as SessionContract.Effect.CalculateDailyStats
        assertEquals(completedSession.sessionId, statsEffect.sessionId)

        // 验证stats数据的准确性
        verifyStatsCalculationInputs(completedSession, expectedDailyStats)
    }

    @Test
    fun `session statistics should reflect real workout data`() = runTest {
        // Given - 真实训练数据的会话
        val sessionWithWorkoutData = createSessionWithCompletedSets()

        // When - 计算会话统计信息
        val sessionStats = sessionWithWorkoutData.toSessionStatistics()

        // Then - 验证统计数据准确性
        verifySessionStatistics(sessionStats, sessionWithWorkoutData)
    }

    @Test
    fun `multiple sessions should accumulate stats correctly`() = runTest {
        // Given - 多个训练会话
        val session1 = createCompletedSession("session_1", 2, 180) // 2个动作，3分钟
        val session2 = createCompletedSession("session_2", 3, 300) // 3个动作，5分钟
        val session3 = createCompletedSession("session_3", 1, 120) // 1个动作，2分钟

        val allSessions = listOf(session1, session2, session3)
        val expectedTotalStats = calculateExpectedTotalStats(allSessions)

        // Mock accumulated stats
        coEvery { mockGetStatsUseCase(any()) } returns
            com.example.gymbro.core.error.types.ModernResult.Success(expectedTotalStats)

        // When - 计算累积统计
        val statsResult = mockGetStatsUseCase.invoke("2024-01-15")

        // Then - 验证累积统计正确
        assertTrue(statsResult is com.example.gymbro.core.error.types.ModernResult.Success)
        val totalStats = statsResult.data

        assertEquals(6, totalStats.totalExercises) // 2+3+1
        assertEquals(600, totalStats.totalDurationSeconds) // 180+300+120
        assertTrue(totalStats.totalVolume > 0)
    }

    @Test
    fun `session progress should update stats in real time`() = runTest {
        // Given - 进行中的训练会话
        val activeSession = createActiveSession()

        // When - 完成一个set
        val completeSetIntent = SessionContract.Intent.CompleteSet("ex1", "set1")
        val result = sessionReducer.reduce(completeSetIntent, activeSession)

        // Then - 验证实时统计更新
        val updatedStats = result.state.toSessionStatistics()
        assertTrue(updatedStats.completedSets > activeSession.toSessionStatistics().completedSets)
        assertTrue(updatedStats.currentVolume > activeSession.toSessionStatistics().currentVolume)
    }

    @Test
    fun `session with different exercise types should calculate volume correctly`() = runTest {
        // Given - 包含不同类型动作的会话（有氧+力量）
        val mixedSession = createMixedWorkoutSession()

        // When - 计算训练量统计
        val completeIntent = SessionContract.Intent.CompleteSession
        val result = sessionReducer.reduce(completeIntent, mixedSession)

        // Then - 验证不同类型动作的统计计算
        assertTrue(result.effect is SessionContract.Effect.CalculateDailyStats)

        val sessionStats = mixedSession.toSessionStatistics()
        assertTrue(sessionStats.strengthVolume > 0) // 力量训练量
        assertTrue(sessionStats.cardioCalories > 0) // 有氧消耗
        assertTrue(sessionStats.totalVolume > 0) // 总训练量
    }

    @Test
    fun `failed session should not affect stats calculation`() = runTest {
        // Given - 未完成/失败的训练会话
        val incompleteSession = createIncompleteSession()

        // When - 尝试完成未完成的会话
        val completeIntent = SessionContract.Intent.CompleteSession
        val result = sessionReducer.reduce(completeIntent, incompleteSession)

        // Then - 应该处理未完成状态，不触发错误的统计计算
        // 具体行为需要根据业务逻辑确定
        if (result.effect is SessionContract.Effect.CalculateDailyStats) {
            // 如果仍然计算统计，应该标记为部分完成
            val sessionStats = incompleteSession.toSessionStatistics()
            assertTrue(sessionStats.completionRate < 1.0f)
        }
    }

    @Test
    fun `stats data should integrate with session UI state correctly`() = runTest {
        // Given - 会话状态和对应的统计数据
        val sessionState = createSessionWithStats()
        val mockStats = createMockUnifiedStats()

        // Mock stats retrieval
        coEvery { mockGetStatsUseCase(any()) } returns
            com.example.gymbro.core.error.types.ModernResult.Success(mockStats)

        // When - 获取统计数据并更新UI状态
        val viewStatsIntent = SessionContract.Intent.ViewStatisticDetail(
            com.example.gymbro.features.workout.session.internal.components.StatisticType.TOTAL_VOLUME,
        )
        val result = sessionReducer.reduce(viewStatsIntent, sessionState)

        // Then - 验证UI状态正确反映统计数据
        assertNotNull(result.state)
        // 这里应该验证具体的UI状态更新逻辑
    }

    // === 辅助方法 ===

    private fun createCompletedSessionWithRealData(): SessionContract.State {
        val exercises = listOf(
            createExerciseWithSets("ex1", "卧推", 80f, 8, 3),
            createExerciseWithSets("ex2", "深蹲", 100f, 10, 3),
            createExerciseWithSets("ex3", "硬拉", 120f, 6, 3),
        )

        return SessionContract.State(
            sessionId = "real_session_001",
            exercises = exercises.toImmutableList(),
            totalExercises = exercises.size,
            completedExercises = exercises.size,
            completedSetsCount = exercises.size * 3,
            elapsedTimeMs = 3600000L, // 1小时
            startedAt = System.currentTimeMillis() - 3600000L,
            isPaused = false,
            workoutSourceState = SessionContract.WorkoutSourceState(),
            scrollState = SessionContract.ScrollState(),
            countdownInfo = SessionContract.CountdownInfo(),
            showCompleteWorkoutDialog = false,
            showTemplateSwitcher = false,
            availableTemplateVersions = persistentListOf(),
        )
    }

    private fun createExerciseWithSets(
        id: String,
        name: String,
        weight: Float,
        reps: Int,
        sets: Int,
    ): SessionContract.SessionExerciseUiModel {
        val exercise = Exercise(
            id = id,
            name = name,
            categoryId = "strength",
            description = "测试动作：$name",
            difficulty = 2,
            muscleGroups = listOf("CHEST"),
            equipmentRequired = listOf("barbell"),
            thumbnailUrl = null,
            videoUrl = null,
            isCustom = false,
        )

        val completedSets = (1..sets).map { setIndex ->
            com.example.gymbro.domain.exercise.model.ExerciseSet(
                id = "${id}_set_$setIndex",
                reps = reps,
                weight = weight,
                restSeconds = 90,
                isCompleted = true,
                completedAt = System.currentTimeMillis(),
            )
        }

        val sessionExercise = SessionExercise(
            sessionId = "real_session_001",
            exerciseId = id,
            order = 0,
            sets = completedSets,
            name = name,
            targetSets = sets,
            completedSets = sets,
            status = "COMPLETED",
            isCompleted = true,
        )

        return SessionContract.SessionExerciseUiModel(
            sessionExercise = sessionExercise,
            exercise = exercise,
        )
    }

    private fun createExpectedDailyStats(session: SessionContract.State): DailyStats {
        val totalVolume = session.exercises.sumOf { exercise ->
            exercise.sessionExercise.sets.sumOf { set ->
                (set.weight * set.reps).toDouble()
            }
        }

        return DailyStats(
            date = "2024-01-15",
            totalExercises = session.exercises.size,
            totalSets = session.completedSetsCount,
            totalVolume = totalVolume.toFloat(),
            totalDurationMinutes = (session.elapsedTimeMs / 60000).toInt(),
            caloriesBurned = (totalVolume * 0.5).toInt(), // 简化计算
            averageIntensity = 0.75f,
            workoutCount = 1,
        )
    }

    private fun createCompletedSession(
        sessionId: String,
        exerciseCount: Int,
        durationSeconds: Int,
    ): SessionContract.State {
        val exercises = (1..exerciseCount).map { index ->
            createExerciseWithSets("ex$index", "动作$index", 50f, 10, 3)
        }

        return SessionContract.State(
            sessionId = sessionId,
            exercises = exercises.toImmutableList(),
            totalExercises = exerciseCount,
            completedExercises = exerciseCount,
            completedSetsCount = exerciseCount * 3,
            elapsedTimeMs = durationSeconds * 1000L,
            startedAt = System.currentTimeMillis() - (durationSeconds * 1000L),
            isPaused = false,
            workoutSourceState = SessionContract.WorkoutSourceState(),
            scrollState = SessionContract.ScrollState(),
            countdownInfo = SessionContract.CountdownInfo(),
            showCompleteWorkoutDialog = false,
            showTemplateSwitcher = false,
            availableTemplateVersions = persistentListOf(),
        )
    }

    private fun calculateExpectedTotalStats(sessions: List<SessionContract.State>): UnifiedWorkoutStatistics {
        return UnifiedWorkoutStatistics(
            totalExercises = sessions.sumOf { it.totalExercises },
            totalSets = sessions.sumOf { it.completedSetsCount },
            totalVolume = sessions.sumOf { session ->
                session.exercises.sumOf { exercise ->
                    exercise.sessionExercise.sets.sumOf { set ->
                        set.weight * set.reps
                    }
                }
            },
            totalDurationSeconds = sessions.sumOf { (it.elapsedTimeMs / 1000).toInt() },
            averageIntensity = 0.75f,
            caloriesBurned = sessions.sumOf { it.toSessionStatistics().estimatedCalories },
            workoutFrequency = sessions.size,
        )
    }

    private fun createActiveSession(): SessionContract.State {
        val exercises = listOf(
            createExerciseWithSets("ex1", "卧推", 80f, 8, 3),
        )

        return SessionContract.State(
            sessionId = "active_session_001",
            exercises = exercises.toImmutableList(),
            totalExercises = 1,
            completedExercises = 0,
            completedSetsCount = 1, // 已完成1组
            currentExerciseIndex = 0,
            elapsedTimeMs = 900000L, // 15分钟
            startedAt = System.currentTimeMillis() - 900000L,
            isPaused = false,
            workoutSourceState = SessionContract.WorkoutSourceState(),
            scrollState = SessionContract.ScrollState(),
            countdownInfo = SessionContract.CountdownInfo(),
            showCompleteWorkoutDialog = false,
            showTemplateSwitcher = false,
            availableTemplateVersions = persistentListOf(),
        )
    }

    private fun createMixedWorkoutSession(): SessionContract.State {
        // 包含力量训练和有氧训练的混合会话
        val strengthExercises = listOf(
            createExerciseWithSets("strength1", "卧推", 80f, 8, 3),
            createExerciseWithSets("strength2", "深蹲", 100f, 10, 3),
        )

        val cardioExercises = listOf(
            createCardioExercise("cardio1", "跑步", 600), // 10分钟跑步
        )

        val allExercises = strengthExercises + cardioExercises

        return SessionContract.State(
            sessionId = "mixed_session_001",
            exercises = allExercises.toImmutableList(),
            totalExercises = allExercises.size,
            completedExercises = allExercises.size,
            completedSetsCount = strengthExercises.size * 3 + cardioExercises.size,
            elapsedTimeMs = 2400000L, // 40分钟
            startedAt = System.currentTimeMillis() - 2400000L,
            workoutSourceState = SessionContract.WorkoutSourceState(),
            scrollState = SessionContract.ScrollState(),
            countdownInfo = SessionContract.CountdownInfo(),
            showCompleteWorkoutDialog = false,
            showTemplateSwitcher = false,
            availableTemplateVersions = persistentListOf(),
        )
    }

    private fun createCardioExercise(
        id: String,
        name: String,
        durationSeconds: Int,
    ): SessionContract.SessionExerciseUiModel {
        val exercise = Exercise(
            id = id,
            name = name,
            categoryId = "cardio",
            description = "有氧训练：$name",
            difficulty = 2,
            muscleGroups = listOf("CARDIOVASCULAR"),
            equipmentRequired = listOf("treadmill"),
            thumbnailUrl = null,
            videoUrl = null,
            isCustom = false,
        )

        val cardioSet = com.example.gymbro.domain.exercise.model.ExerciseSet(
            id = "${id}_cardio_set",
            reps = 1, // 有氧训练用1次表示
            weight = 0f, // 有氧训练无重量
            restSeconds = 0,
            durationSeconds = durationSeconds,
            isCompleted = true,
            completedAt = System.currentTimeMillis(),
        )

        val sessionExercise = SessionExercise(
            sessionId = "mixed_session_001",
            exerciseId = id,
            order = 0,
            sets = listOf(cardioSet),
            name = name,
            targetSets = 1,
            completedSets = 1,
            status = "COMPLETED",
            isCompleted = true,
        )

        return SessionContract.SessionExerciseUiModel(
            sessionExercise = sessionExercise,
            exercise = exercise,
        )
    }

    private fun createIncompleteSession(): SessionContract.State {
        val exercises = listOf(
            createExerciseWithSets("ex1", "卧推", 80f, 8, 3),
        )

        return SessionContract.State(
            sessionId = "incomplete_session_001",
            exercises = exercises.toImmutableList(),
            totalExercises = 3, // 计划3个动作
            completedExercises = 1, // 只完成1个
            completedSetsCount = 2, // 只完成2组
            elapsedTimeMs = 1800000L, // 30分钟
            startedAt = System.currentTimeMillis() - 1800000L,
            workoutSourceState = SessionContract.WorkoutSourceState(),
            scrollState = SessionContract.ScrollState(),
            countdownInfo = SessionContract.CountdownInfo(),
            showCompleteWorkoutDialog = false,
            showTemplateSwitcher = false,
            availableTemplateVersions = persistentListOf(),
        )
    }

    private fun createSessionWithStats(): SessionContract.State {
        return createCompletedSessionWithRealData()
    }

    private fun createMockUnifiedStats(): UnifiedWorkoutStatistics {
        return UnifiedWorkoutStatistics(
            totalExercises = 15,
            totalSets = 45,
            totalVolume = 12500f,
            totalDurationSeconds = 7200,
            averageIntensity = 0.8f,
            caloriesBurned = 450,
            workoutFrequency = 5,
        )
    }

    private fun createSessionWithCompletedSets(): SessionContract.State {
        return createCompletedSessionWithRealData()
    }

    private fun verifyStatsCalculationInputs(
        session: SessionContract.State,
        expectedStats: DailyStats,
    ) {
        assertEquals(session.exercises.size, expectedStats.totalExercises)
        assertEquals(session.completedSetsCount, expectedStats.totalSets)
        assertEquals((session.elapsedTimeMs / 60000).toInt(), expectedStats.totalDurationMinutes)
        assertTrue(expectedStats.totalVolume > 0)
    }

    private fun verifySessionStatistics(
        stats: SessionContract.SessionStatistics,
        session: SessionContract.State,
    ) {
        assertEquals(session.totalExercises, stats.totalExercises)
        assertEquals(session.completedSetsCount, stats.completedSets)
        assertEquals(session.elapsedTimeMs, stats.elapsedTimeMs)
        assertTrue(stats.currentVolume >= 0)
        assertTrue(stats.estimatedCalories >= 0)
    }
}

// 扩展函数，用于将session state转换为统计信息
private fun SessionContract.State.toSessionStatistics(): SessionContract.SessionStatistics {
    val totalVolume = exercises.sumOf { exercise ->
        exercise.sessionExercise.sets.sumOf { set ->
            set.weight * set.reps
        }
    }

    val strengthVolume = exercises.filter {
        it.exercise.categoryId == "strength"
    }.sumOf { exercise ->
        exercise.sessionExercise.sets.sumOf { set ->
            set.weight * set.reps
        }
    }

    val cardioCalories = exercises.filter {
        it.exercise.categoryId == "cardio"
    }.sumOf { exercise ->
        exercise.sessionExercise.sets.sumOf { set ->
            (set.durationSeconds ?: 0) * 0.1 // 简化的卡路里计算
        }
    }.toInt()

    return SessionContract.SessionStatistics(
        totalExercises = totalExercises,
        completedSets = completedSetsCount,
        currentVolume = totalVolume,
        strengthVolume = strengthVolume,
        cardioCalories = cardioCalories,
        totalVolume = totalVolume,
        elapsedTimeMs = elapsedTimeMs,
        estimatedCalories = (totalVolume * 0.5 + cardioCalories).toInt(),
        completionRate = if (totalExercises > 0) completedExercises.toFloat() / totalExercises else 0f,
    )
}
