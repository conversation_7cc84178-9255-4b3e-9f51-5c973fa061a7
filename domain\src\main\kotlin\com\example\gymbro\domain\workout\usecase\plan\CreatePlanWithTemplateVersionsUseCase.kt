package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.shared.models.workout.DayPlan
import com.example.gymbro.shared.models.workout.WorkoutPlan
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first

/**
 * 创建计划并自动解析TemplateVersion用例
 *
 * ✅ Phase 3: UseCase层适配 - 复用Template系统成功模式
 * - 集成PlanRepository.createPlanWithVersionResolution
 * - 自动解析Template ID为TemplateVersion ID
 * - 保持与原CreatePlanUseCase的API兼容性
 *
 * 核心功能：
 * - 接收含有templateIds的DayPlan
 * - 调用Repository自动解析为templateVersionIds
 * - 返回完整的Plan对象（包含版本信息）
 */
@Singleton
class CreatePlanWithTemplateVersionsUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<CreatePlanWithTemplateVersionsUseCase.Params, WorkoutPlan>(dispatcher, logger) {

        /**
         * 创建计划参数
         */
        data class Params(
            val name: String,
            val description: String? = null,
            val userId: String? = null, // 可选，会自动获取当前用户
            val dailySchedule: Map<Int, DayPlan>,
        )

        /**
         * 执行创建计划并解析TemplateVersion
         */
        override suspend fun execute(parameters: Params): ModernResult<WorkoutPlan> {
            logger.d("创建Plan并解析TemplateVersion: ${parameters.name}")

            // 1. 获取当前用户ID
            val userId = parameters.userId ?: run {
                val userIdResult = getCurrentUserIdUseCase().first()
                when (userIdResult) {
                    is ModernResult.Success -> userIdResult.data
                    is ModernResult.Error -> return ModernResult.Error(
                        com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                            operationName = "createPlanWithTemplateVersions",
                            message = UiText.DynamicString("无法获取当前用户信息"),
                        ),
                    )

                    is ModernResult.Loading -> return ModernResult.Error(
                        com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                            operationName = "createPlanWithTemplateVersions",
                            message = UiText.DynamicString("获取用户信息超时"),
                        ),
                    )
                }
            } ?: return ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "createPlanWithTemplateVersions",
                    message = UiText.DynamicString("用户ID无效"),
                ),
            )

            // 2. 验证参数
            if (parameters.name.isBlank()) {
                return ModernResult.Error(
                    com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                        operationName = "createPlanWithTemplateVersions",
                        message = UiText.DynamicString("计划名称不能为空"),
                    ),
                )
            }

            if (parameters.dailySchedule.isEmpty()) {
                return ModernResult.Error(
                    com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                        operationName = "createPlanWithTemplateVersions",
                        message = UiText.DynamicString("计划必须包含至少一天的安排"),
                    ),
                )
            }

            // 3. 构建WorkoutPlan（使用Template ID，待Repository解析为TemplateVersion）
            val plan = WorkoutPlan(
                id = UUID.randomUUID().toString(),
                name = parameters.name,
                description = parameters.description ?: "",
                userId = userId,
                targetGoal = "训练计划", // 默认目标
                difficultyLevel = 3, // 中等难度
                estimatedDuration = 180, // 3小时默认
                planType = com.example.gymbro.shared.models.workout.PlanType.CUSTOM,
                dailySchedule = parameters.dailySchedule,
                totalDays = parameters.dailySchedule.keys.maxOrNull() ?: 1,
                tags = listOf("模板版本控制"),
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
            )

            // 4. ✅ 使用Repository的TemplateVersion解析功能
            return planRepository.createPlanWithVersionResolution(plan)
        }

        /**
         * 便捷调用方法 - 简化参数
         */
        suspend fun invoke(
            name: String,
            dailySchedule: Map<Int, DayPlan>,
            description: String? = null,
        ): ModernResult<WorkoutPlan> = execute(
            Params(
                name = name,
                description = description,
                dailySchedule = dailySchedule,
            ),
        )
    }

/**
 * 请求数据类 - 用于Function Call和API调用
 */
data class CreatePlanRequest(
    val name: String,
    val description: String? = null,
    val userId: String? = null,
    val dailySchedule: Map<Int, DayPlan>,
) {
    /**
     * 转换为UseCase参数
     */
    fun toUseCaseParams(): CreatePlanWithTemplateVersionsUseCase.Params {
        return CreatePlanWithTemplateVersionsUseCase.Params(
            name = name,
            description = description,
            userId = userId,
            dailySchedule = dailySchedule,
        )
    }
}
