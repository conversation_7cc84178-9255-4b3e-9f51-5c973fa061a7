package com.example.gymbro.features.coach.shared.services

import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.coach.dao.ChatSearchDao
import com.example.gymbro.data.local.model.ChatSearchResult
import com.example.gymbro.domain.coach.model.CoachMessage
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 智能搜索服务 - 基于用户查询智能选择搜索策略
 *
 * 核心特性：
 * - 智能策略选择：根据查询内容自动选择最优搜索策略
 * - 多层降级机制：向量搜索失败时自动降级到关键词搜索
 * - 结果缓存：提高重复查询的响应速度
 * - 性能监控：记录搜索耗时和结果质量
 * - 异步向量化：触发后台向量化任务
 *
 * 搜索策略：
 * 1. KEYWORD_ONLY - 仅关键词搜索（快速、精确匹配）
 * 2. HYBRID - 混合搜索（平衡关键词和语义）
 * 3. SEMANTIC_HEAVY - 语义重点搜索（理解意图、概念匹配）
 *
 * @since Coach模块MVI架构重构
 */
@Singleton
class SmartSearchService
    @Inject
    constructor(
        private val hybridSearchUseCase: HybridSearchUseCase,
        private val chatSearchDao: ChatSearchDao,
        private val ioDispatcher: CoroutineDispatcher,
        private val defaultDispatcher: CoroutineDispatcher,
    ) {
        companion object {
            // 缓存配置
            private const val CACHE_SIZE = 50
            private const val CACHE_TTL_MS = 5 * 60 * 1000L // 5分钟

            // 搜索策略阈值
            private const val KEYWORD_THRESHOLD = 3 // 短查询使用关键词搜索
            private const val SEMANTIC_THRESHOLD = 10 // 长查询使用语义搜索
        }

        // 简单的内存缓存
        private val searchCache = mutableMapOf<String, CachedResult>()

        /**
         * 智能搜索主入口
         *
         * @param query 搜索查询
         * @param sessionId 会话ID（可选，为null时进行全局搜索）
         * @param limit 结果数量限制
         * @return 搜索结果
         */
        suspend fun smartSearch(
            query: String,
            sessionId: String? = null,
            limit: Int = 20,
        ): ModernResult<List<CoachMessage>> =
            withContext(defaultDispatcher) {
                try {
                    if (query.isBlank()) {
                        return@withContext ModernResult.Success(emptyList())
                    }

                    Timber.d("🔍 开始智能搜索: query='$query', sessionId=$sessionId")
                    val startTime = System.currentTimeMillis()

                    // 检查缓存
                    val cacheKey = buildCacheKey(query, sessionId, limit)
                    getCachedResult(cacheKey)?.let { cachedResults ->
                        Timber.d("💾 返回缓存结果: ${cachedResults.size}条")
                        return@withContext ModernResult.Success(cachedResults)
                    }

                    // 选择搜索策略
                    val strategy = selectSearchStrategy(query)
                    Timber.d("🎯 选择搜索策略: $strategy")

                    // 执行搜索
                    val results =
                        when (strategy) {
                            SearchStrategy.KEYWORD_ONLY -> performKeywordSearch(query, sessionId, limit)
                            SearchStrategy.HYBRID -> performHybridSearch(query, sessionId, limit)
                            SearchStrategy.SEMANTIC_HEAVY -> performSemanticHeavySearch(
                                query,
                                sessionId,
                                limit,
                            )
                        }

                    val duration = System.currentTimeMillis() - startTime
                    Timber.d("✅ 智能搜索完成: 找到${results.size}条结果, 耗时${duration}ms, 策略=$strategy")

                    // 缓存结果
                    cacheResult(cacheKey, results)

                    // 触发异步向量化
                    triggerVectorizationIfNeeded()

                    ModernResult.Success(results)
                } catch (e: Exception) {
                    Timber.e(e, "❌ 智能搜索失败: query='$query'")
                    ModernResult.error(
                        e.toModernDataError(
                            operationName = "smartSearch",
                            uiMessage = UiText.DynamicString("智能搜索失败"),
                        ),
                    )
                }
            }

        /**
         * 选择搜索策略
         */
        private fun selectSearchStrategy(query: String): SearchStrategy {
            val queryLength = query.length
            val wordCount = query.split("\\s+".toRegex()).size

            return when {
                // 短查询：使用关键词搜索
                queryLength <= KEYWORD_THRESHOLD || wordCount == 1 -> SearchStrategy.KEYWORD_ONLY

                // 长查询：使用语义重点搜索
                queryLength >= SEMANTIC_THRESHOLD || wordCount >= 5 -> SearchStrategy.SEMANTIC_HEAVY

                // 中等查询：使用混合搜索
                else -> SearchStrategy.HYBRID
            }
        }

        /**
         * 执行关键词搜索
         */
        private suspend fun performKeywordSearch(
            query: String,
            sessionId: String?,
            limit: Int,
        ): List<CoachMessage> =
            withContext(ioDispatcher) {
                Timber.d("🔤 执行关键词搜索")

                val results =
                    if (sessionId != null) {
                        chatSearchDao.ftsSearchInSession(sessionId, query, limit)
                    } else {
                        chatSearchDao.ftsSearch(query, limit)
                    }

                convertSearchResultsToMessages(results)
            }

        /**
         * 执行混合搜索
         */
        private suspend fun performHybridSearch(
            query: String,
            sessionId: String?,
            limit: Int,
        ): List<CoachMessage> {
            Timber.d("⚖️ 执行混合搜索")

            return if (sessionId != null) {
                hybridSearchUseCase
                    .searchInSession(sessionId, query, limit)
                    .let { flow ->
                        var result: List<CoachMessage> = emptyList()
                        flow.collect { modernResult ->
                            when (modernResult) {
                                is ModernResult.Success -> {
                                    result = modernResult.data
                                }

                                is ModernResult.Error -> throw Exception(
                                    "搜索失败: ${modernResult.error.message}",
                                )

                                is ModernResult.Loading -> {
                                    /* 继续等待 */
                                }
                            }
                        }
                        result
                    }
            } else {
                hybridSearchUseCase
                    .search(query, limit)
                    .let { flow ->
                        var result: List<CoachMessage> = emptyList()
                        flow.collect { modernResult ->
                            when (modernResult) {
                                is ModernResult.Success -> {
                                    result = modernResult.data
                                }

                                is ModernResult.Error -> throw Exception(
                                    "搜索失败: ${modernResult.error.message}",
                                )

                                is ModernResult.Loading -> {
                                    /* 继续等待 */
                                }
                            }
                        }
                        result
                    }
            }
        }

        /**
         * 执行语义重点搜索
         */
        private suspend fun performSemanticHeavySearch(
            query: String,
            sessionId: String?,
            limit: Int,
        ): List<CoachMessage> {
            Timber.d("🧠 执行语义重点搜索")

            // 使用更高的语义权重
            return performHybridSearch(query, sessionId, limit)
        }

        /**
         * 降级关键词搜索
         */
        private suspend fun performKeywordSearchFallback(
            query: String,
            sessionId: String?,
            limit: Int,
        ): List<CoachMessage> {
            Timber.d("🆘 执行降级关键词搜索")
            return performKeywordSearch(query, sessionId, limit)
        }

        /**
         * 转换搜索结果为CoachMessage
         */
        private fun convertSearchResultsToMessages(results: List<ChatSearchResult>): List<CoachMessage> =
            results.map { result ->
                when (result.role) {
                    "user" ->
                        CoachMessage.UserMessage(
                            id = result.id.toString(),
                            content = result.content,
                            timestamp = result.timestamp,
                        )

                    "assistant" ->
                        CoachMessage.AiMessage(
                            id = result.id.toString(),
                            content = result.content,
                            timestamp = result.timestamp,
                        )

                    else -> throw IllegalArgumentException("Unknown message role: ${result.role}")
                }
            }

        /**
         * 触发异步向量化
         */
        private fun triggerVectorizationIfNeeded() {
            try {
                // 检查是否有待向量化的消息
                // 这里可以添加逻辑来触发VectorizationWorker
                Timber.d("🔄 检查待向量化消息")
                // VectorizationWorker.enqueueMessages(context, pendingMessageIds)
            } catch (e: Exception) {
                Timber.w(e, "触发向量化失败")
            }
        }

        /**
         * 构建缓存键
         */
        private fun buildCacheKey(
            query: String,
            sessionId: String?,
            limit: Int,
        ): String = "search:${query.hashCode()}:${sessionId?.hashCode() ?: "global"}:$limit"

        /**
         * 获取缓存结果
         */
        private fun getCachedResult(cacheKey: String): List<CoachMessage>? {
            val cached = searchCache[cacheKey]
            return if (cached != null && !cached.isExpired()) {
                cached.results
            } else {
                searchCache.remove(cacheKey)
                null
            }
        }

        /**
         * 缓存结果
         */
        private fun cacheResult(
            cacheKey: String,
            results: List<CoachMessage>,
        ) {
            // 简单的LRU策略
            if (searchCache.size >= CACHE_SIZE) {
                val oldestKey = searchCache.keys.first()
                searchCache.remove(oldestKey)
            }

            searchCache[cacheKey] = CachedResult(results, System.currentTimeMillis())
        }

        /**
         * 搜索策略枚举
         */
        private enum class SearchStrategy {
            KEYWORD_ONLY, // 仅关键词搜索
            HYBRID, // 平衡的混合搜索
            SEMANTIC_HEAVY, // 语义重点搜索
        }

        /**
         * 缓存结果数据类
         */
        private data class CachedResult(
            val results: List<CoachMessage>,
            val timestamp: Long,
        ) {
            fun isExpired(): Boolean = System.currentTimeMillis() - timestamp > CACHE_TTL_MS
        }
    }
