package com.example.gymbro.features.profile.internal.presentation.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.model.user.WorkoutDay
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.Effect
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.Intent
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.LoadState
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.State
import com.example.gymbro.features.profile.internal.presentation.model.ProfileUiModel
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import timber.log.Timber

@Singleton
internal class ProfileReducer
    @Inject
    constructor() : Reducer<Intent, State, Effect> {
        /**
         * 根据当前状态和用户意图，计算新的状态和副作用
         *
         * @param intent 用户意图
         * @param currentState 当前状态
         * @return 包含新状态和副作用的结果
         */
        override fun reduce(
            intent: Intent,
            currentState: State,
        ): ReduceResult<State, Effect> {
            Timber.d("🔄 ProfileReducer - Reducing Intent: ${intent::class.simpleName}")

            return when (intent) {
                // === 数据加载相关 ===
                is Intent.LoadUserProfile -> ReduceResult.stateOnly(handleLoadUserProfile(currentState))
                is Intent.RetryLoad -> ReduceResult.stateOnly(handleRetryLoad(currentState))

                // === 编辑模式相关 ===
                is Intent.EnterEditMode -> ReduceResult.stateOnly(handleEnterEditMode(currentState))
                is Intent.ExitEditMode -> ReduceResult.stateOnly(handleExitEditMode(currentState))
                // 🔥 移除手动保存，改为自动保存

                // === 字段更新相关 ===
                is Intent.UpdateDisplayName -> ReduceResult.stateOnly(
                    handleUpdateDisplayName(currentState, intent.displayName),
                )

                is Intent.UpdateUsername -> ReduceResult.stateOnly(
                    handleUpdateUsername(currentState, intent.username),
                )

                is Intent.UpdateBio -> ReduceResult.stateOnly(handleUpdateBio(currentState, intent.bio))
                is Intent.UpdateEmail -> ReduceResult.stateOnly(handleUpdateEmail(currentState, intent.email))
                is Intent.UpdatePhoneNumber -> ReduceResult.stateOnly(
                    handleUpdatePhoneNumber(currentState, intent.phoneNumber),
                )

                is Intent.UpdateHeight -> ReduceResult.stateOnly(
                    handleUpdateHeight(currentState, intent.height),
                )

                is Intent.UpdateWeight -> ReduceResult.stateOnly(
                    handleUpdateWeight(currentState, intent.weight),
                )

                is Intent.UpdateGender -> ReduceResult.stateOnly(
                    handleUpdateGender(currentState, intent.gender),
                )

                is Intent.UpdateFitnessLevel -> ReduceResult.stateOnly(
                    handleUpdateFitnessLevel(currentState, intent.level),
                )

                is Intent.UpdateFitnessGoals -> ReduceResult.stateOnly(
                    handleUpdateFitnessGoals(currentState, intent.goals),
                )

                is Intent.UpdateWorkoutDays -> ReduceResult.stateOnly(
                    handleUpdateWorkoutDays(currentState, intent.days),
                )

                // === 通知设置相关 ===
                is Intent.UpdateWorkoutReminder -> handleUpdateWorkoutReminder(currentState, intent.enabled)
                is Intent.UpdateFriendsActivity -> handleUpdateFriendsActivity(currentState, intent.enabled)

                // === 新增：详细通知设置相关 ===
                // 倒计时通知设置
                is Intent.UpdateRestTimerNotification -> handleUpdateRestTimerNotification(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateRestTimerSound -> handleUpdateRestTimerSound(currentState, intent.enabled)
                is Intent.UpdateRestTimerVibration -> handleUpdateRestTimerVibration(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateRestTimerLockScreen -> handleUpdateRestTimerLockScreen(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateRestTimerAutoStart -> handleUpdateRestTimerAutoStart(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateRestTimerInterval -> handleUpdateRestTimerInterval(
                    currentState,
                    intent.interval,
                )

                // 训练提醒设置
                is Intent.UpdateWorkoutReminderEnabled -> handleUpdateWorkoutReminderEnabled(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateDailyReminder -> handleUpdateDailyReminder(currentState, intent.enabled)
                is Intent.UpdateReminderTime -> handleUpdateReminderTime(currentState, intent.time)
                is Intent.UpdateWeeklyPlanReminder -> handleUpdateWeeklyPlanReminder(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateRestDayReminder -> handleUpdateRestDayReminder(currentState, intent.enabled)
                is Intent.UpdateMotivationalMessages -> handleUpdateMotivationalMessages(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateWorkoutReminderSound -> handleUpdateWorkoutReminderSound(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateWorkoutReminderVibration -> handleUpdateWorkoutReminderVibration(
                    currentState,
                    intent.enabled,
                )

                // 日历同步设置
                is Intent.UpdateCalendarSyncEnabled -> handleUpdateCalendarSyncEnabled(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateSystemCalendarSync -> handleUpdateSystemCalendarSync(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdatePlanUpdateNotification -> handleUpdatePlanUpdateNotification(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateScheduleChangeNotification -> handleUpdateScheduleChangeNotification(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateUpcomingWorkoutReminder -> handleUpdateUpcomingWorkoutReminder(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateReminderMinutesBefore -> handleUpdateReminderMinutesBefore(
                    currentState,
                    intent.minutes,
                )

                is Intent.UpdateCalendarSyncSound -> handleUpdateCalendarSyncSound(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateCalendarSyncVibration -> handleUpdateCalendarSyncVibration(
                    currentState,
                    intent.enabled,
                )

                // 全局通知设置
                is Intent.UpdateGlobalVibration -> handleUpdateGlobalVibration(currentState, intent.enabled)
                is Intent.UpdateGlobalNotificationTime -> handleUpdateGlobalNotificationTime(
                    currentState,
                    intent.time,
                )

                // 状态栏通知设置
                is Intent.UpdateStatusBarNotificationEnabled -> handleUpdateStatusBarNotificationEnabled(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateStatusBarOngoingWorkout -> handleUpdateStatusBarOngoingWorkout(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateStatusBarRestTimer -> handleUpdateStatusBarRestTimer(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateStatusBarProgress -> handleUpdateStatusBarProgress(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateStatusBarPriority -> handleUpdateStatusBarPriority(
                    currentState,
                    intent.priority,
                )

                is Intent.UpdateStatusBarAutoHide -> handleUpdateStatusBarAutoHide(
                    currentState,
                    intent.enabled,
                )

                is Intent.UpdateStatusBarAutoHideDelay -> handleUpdateStatusBarAutoHideDelay(
                    currentState,
                    intent.delayMinutes,
                )

                // === 主题设置相关 ===
                is Intent.UpdateThemeStyle -> handleUpdateThemeStyle(currentState, intent.style)
                is Intent.UpdateColorMode -> handleUpdateColorMode(currentState, intent.mode)
                is Intent.UpdateDynamicColor -> handleUpdateDynamicColor(currentState, intent.enabled)

                // === 状态清理相关 ===
                is Intent.ClearError -> ReduceResult.stateOnly(handleClearError(currentState))
                is Intent.ClearSaveSuccess -> ReduceResult.stateOnly(handleClearSaveSuccess(currentState))

                // === AI上下文相关 (预留) ===
                is Intent.GenerateAiContext -> ReduceResult.stateOnly(handleGenerateAiContext(currentState))
                is Intent.RefreshAiContext -> ReduceResult.stateOnly(handleRefreshAiContext(currentState))

                // === 对话框相关 ===
                is Intent.ShowDisplayNameDialog -> ReduceResult.stateOnly(
                    handleShowDisplayNameDialog(currentState),
                )

                is Intent.ShowUsernameDialog -> ReduceResult.stateOnly(handleShowUsernameDialog(currentState))
                is Intent.ShowEmailDialog -> ReduceResult.stateOnly(handleShowEmailDialog(currentState))
                is Intent.ShowGenderDialog -> ReduceResult.stateOnly(handleShowGenderDialog(currentState))
                is Intent.ShowHeightDialog -> ReduceResult.stateOnly(handleShowHeightDialog(currentState))
                is Intent.ShowWeightDialog -> ReduceResult.stateOnly(handleShowWeightDialog(currentState))
                is Intent.ShowFitnessLevelDialog -> ReduceResult.stateOnly(
                    handleShowFitnessLevelDialog(currentState),
                )

                is Intent.ShowGoalsDialog -> ReduceResult.stateOnly(handleShowGoalsDialog(currentState))
                is Intent.ShowWorkoutDaysDialog -> ReduceResult.stateOnly(
                    handleShowWorkoutDaysDialog(currentState),
                )

                is Intent.ShowAvatarEditDialog -> ReduceResult.stateOnly(
                    handleShowAvatarEditDialog(currentState),
                )

                is Intent.DismissDialog -> ReduceResult.stateOnly(handleDismissDialog(currentState))

                // === 临时编辑相关 ===
                is Intent.UpdateTempDisplayName -> ReduceResult.stateOnly(
                    handleUpdateTempDisplayName(currentState, intent.value),
                )

                is Intent.UpdateTempUsername -> ReduceResult.stateOnly(
                    handleUpdateTempUsername(currentState, intent.value),
                )

                is Intent.UpdateTempEmail -> ReduceResult.stateOnly(
                    handleUpdateTempEmail(currentState, intent.value),
                )

                is Intent.UpdateTempHeight -> ReduceResult.stateOnly(
                    handleUpdateTempHeight(currentState, intent.value),
                )

                is Intent.UpdateTempWeight -> ReduceResult.stateOnly(
                    handleUpdateTempWeight(currentState, intent.value),
                )

                is Intent.UpdateTempGender -> ReduceResult.stateOnly(
                    handleUpdateTempGender(currentState, intent.value),
                )

                is Intent.UpdateTempFitnessLevel -> ReduceResult.stateOnly(
                    handleUpdateTempFitnessLevel(currentState, intent.value),
                )

                is Intent.UpdateTempGoal -> ReduceResult.stateOnly(
                    handleUpdateTempGoal(currentState, intent.value),
                )

                is Intent.UpdateTempWorkoutDays -> ReduceResult.stateOnly(
                    handleUpdateTempWorkoutDays(currentState, intent.value),
                )

                // === 确认编辑相关 ===
                is Intent.ConfirmDisplayName -> handleConfirmDisplayName(currentState)
                is Intent.ConfirmUsername -> handleConfirmUsername(currentState)
                is Intent.ConfirmEmail -> handleConfirmEmail(currentState)
                is Intent.ConfirmHeight -> handleConfirmHeight(currentState)
                is Intent.ConfirmWeight -> handleConfirmWeight(currentState)
                is Intent.ConfirmGender -> handleConfirmGender(currentState)
                is Intent.ConfirmFitnessLevel -> handleConfirmFitnessLevel(currentState)
                is Intent.ConfirmGoal -> handleConfirmGoal(currentState)
                is Intent.ConfirmWorkoutDays -> handleConfirmWorkoutDays(currentState)

                // === 内部Intent处理 (来自EffectHandler) ===
                is Intent.ProfileLoaded -> ReduceResult.stateOnly(
                    handleProfileLoaded(currentState, intent.userProfile),
                )

                is Intent.ProfileLoadError -> ReduceResult.stateOnly(
                    handleProfileLoadError(currentState, intent.error),
                )

                is Intent.UserSettingsLoaded -> ReduceResult.stateOnly(
                    handleUserSettingsLoaded(currentState, intent.userSettings),
                )

                is Intent.ProfileSaveSuccess -> ReduceResult.stateOnly(handleProfileSaveSuccess(currentState))
                is Intent.ProfileSaveError -> ReduceResult.stateOnly(
                    handleProfileSaveError(currentState, intent.error),
                )

                is Intent.AiContextGenerated -> ReduceResult.stateOnly(
                    handleAiContextGenerated(currentState, intent.aiContext),
                )

                is Intent.AiContextError -> ReduceResult.stateOnly(
                    handleAiContextError(currentState, intent.error),
                )

                // === 主题配置处理 ===
                is Intent.ThemeConfigLoaded ->
                    ReduceResult.stateOnly(
                        handleThemeConfigLoaded(
                            currentState,
                            intent.themeStyle,
                            intent.colorMode,
                            intent.dynamicColorEnabled,
                        ),
                    )

                is Intent.ThemeStyleChangeSuccess -> ReduceResult.stateOnly(
                    handleThemeStyleChangeSuccess(currentState, intent.style),
                )

                is Intent.ThemeStyleChangeError -> ReduceResult.stateOnly(
                    handleThemeStyleChangeError(currentState, intent.error),
                )

                is Intent.ColorModeChangeSuccess -> ReduceResult.stateOnly(
                    handleColorModeChangeSuccess(currentState, intent.mode),
                )

                is Intent.ColorModeChangeError -> ReduceResult.stateOnly(
                    handleColorModeChangeError(currentState, intent.error),
                )

                is Intent.DynamicColorChangeSuccess ->
                    ReduceResult.stateOnly(
                        handleDynamicColorChangeSuccess(
                            currentState,
                            intent.enabled,
                        ),
                    )

                is Intent.DynamicColorChangeError -> ReduceResult.stateOnly(
                    handleDynamicColorChangeError(currentState, intent.error),
                )
            }
        }

        // === 数据加载处理 ===

        internal fun handleLoadUserProfile(state: State): State =
            state.copy(
                isLoading = true,
                profileLoadState = LoadState.Loading,
                error = null,
            )

        internal fun handleRetryLoad(state: State): State =
            state.copy(
                isLoading = true,
                profileLoadState = LoadState.Loading,
                error = null,
            )

        // === 编辑模式处理 ===

        internal fun handleEnterEditMode(state: State): State =
            if (state.editableProfile != null) {
                state.copy(
                    isEditMode = true,
                    originalProfile = state.editableProfile, // 保存原始状态用于取消
                    error = null,
                    saveSuccess = false,
                )
            } else {
                state // 没有数据时不能进入编辑模式
            }

        internal fun handleExitEditMode(state: State): State =
            state.copy(
                isEditMode = false,
                editableProfile = state.originalProfile, // 恢复到原始状态
                originalProfile = null,
                error = null,
                saveSuccess = false,
            )

        internal fun handleSaveProfile(state: State): ReduceResult<State, Effect> =
            if (state.canSave && state.editableProfile != null) {
                val newState = state.copy(
                    isSaving = true,
                    error = null,
                    saveSuccess = false,
                )
                // 🔥 关键修复：分发SaveUserProfile Effect到EffectHandler进行实际保存操作
                val effect = Effect.SaveUserProfile(state.editableProfile)
                Timber.d(
                    "ProfileReducer",
                    "分发SaveUserProfile Effect - profile: ${state.editableProfile.displayName}",
                )
                ReduceResult.withEffect(newState, effect)
            } else {
                val newState = state.copy(
                    error = UiText.DynamicString("无法保存：数据无效或无更改"),
                )
                ReduceResult.stateOnly(newState)
            }

        // === 字段更新处理 ===

        private fun handleUpdateDisplayName(
            state: State,
            displayName: String,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(displayName = displayName)
            }

        private fun handleUpdateUsername(
            state: State,
            username: String,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(username = username)
            }

        private fun handleUpdateBio(
            state: State,
            bio: String,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(bio = bio)
            }

        private fun handleUpdateEmail(
            state: State,
            email: String,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(email = email)
            }

        private fun handleUpdatePhoneNumber(
            state: State,
            phoneNumber: String,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(phoneNumber = phoneNumber)
            }

        private fun handleUpdateHeight(
            state: State,
            height: Float,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(height = height)
            }

        private fun handleUpdateWeight(
            state: State,
            weight: Float,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(weight = weight)
            }

        private fun handleUpdateGender(
            state: State,
            gender: Gender,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(gender = gender)
            }

        private fun handleUpdateFitnessLevel(
            state: State,
            level: FitnessLevel,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(fitnessLevel = level)
            }

        private fun handleUpdateFitnessGoals(
            state: State,
            goals: List<FitnessGoal>,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(fitnessGoals = goals)
            }

        private fun handleUpdateWorkoutDays(
            state: State,
            days: List<WorkoutDay>,
        ): State =
            updateEditableProfile(state) { profile ->
                profile.copy(workoutDays = days)
            }

        // === 通知设置处理 ===

        private fun handleUpdateWorkoutReminder(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedNotifications = currentSettings.notifications.copy(workout = enabled)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(
                    userSettings = updatedSettings,
                    error = null,
                )
                // 🔥 即时保存：通知设置变更后立即触发保存
                val effect = Effect.SaveNotificationSettings(
                    workoutReminder = updatedNotifications.workout,
                    friendsActivity = updatedNotifications.friendRequest,
                )
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(
                state.copy(
                    error = UiText.DynamicString("用户设置不存在，无法更新通知设置"),
                ),
            )

        private fun handleUpdateFriendsActivity(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedNotifications = currentSettings.notifications.copy(friendRequest = enabled)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(
                    userSettings = updatedSettings,
                    error = null,
                )
                // 🔥 即时保存：通知设置变更后立即触发保存
                val effect = Effect.SaveNotificationSettings(
                    workoutReminder = updatedNotifications.workout,
                    friendsActivity = updatedNotifications.friendRequest,
                )
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(
                state.copy(
                    error = UiText.DynamicString("用户设置不存在，无法更新通知设置"),
                ),
            )

        // === 新增：详细通知设置处理函数 ===

        // 倒计时通知设置处理
        private fun handleUpdateRestTimerNotification(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedRestTimer = currentSettings.notifications.restTimerNotification.copy(enabled = enabled)
                val updatedNotifications = currentSettings.notifications.copy(restTimerNotification = updatedRestTimer)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(userSettings = updatedSettings, error = null)
                val effect = Effect.SaveRestTimerNotificationSettings(updatedRestTimer)
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(state.copy(error = UiText.DynamicString("用户设置不存在")))

        private fun handleUpdateRestTimerSound(state: State, enabled: Boolean): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedRestTimer = currentSettings.notifications.restTimerNotification.copy(soundEnabled = enabled)
                val updatedNotifications = currentSettings.notifications.copy(restTimerNotification = updatedRestTimer)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(userSettings = updatedSettings, error = null)
                val effect = Effect.SaveRestTimerNotificationSettings(updatedRestTimer)
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(state.copy(error = UiText.DynamicString("用户设置不存在")))

        private fun handleUpdateRestTimerVibration(state: State, enabled: Boolean): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedRestTimer = currentSettings.notifications.restTimerNotification.copy(vibrationEnabled = enabled)
                val updatedNotifications = currentSettings.notifications.copy(restTimerNotification = updatedRestTimer)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(userSettings = updatedSettings, error = null)
                val effect = Effect.SaveRestTimerNotificationSettings(updatedRestTimer)
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(state.copy(error = UiText.DynamicString("用户设置不存在")))

        private fun handleUpdateRestTimerLockScreen(state: State, enabled: Boolean): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedRestTimer = currentSettings.notifications.restTimerNotification.copy(showOnLockScreen = enabled)
                val updatedNotifications = currentSettings.notifications.copy(restTimerNotification = updatedRestTimer)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(userSettings = updatedSettings, error = null)
                val effect = Effect.SaveRestTimerNotificationSettings(updatedRestTimer)
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(state.copy(error = UiText.DynamicString("用户设置不存在")))

        private fun handleUpdateRestTimerAutoStart(state: State, enabled: Boolean): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedRestTimer = currentSettings.notifications.restTimerNotification.copy(autoStartNext = enabled)
                val updatedNotifications = currentSettings.notifications.copy(restTimerNotification = updatedRestTimer)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(userSettings = updatedSettings, error = null)
                val effect = Effect.SaveRestTimerNotificationSettings(updatedRestTimer)
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(state.copy(error = UiText.DynamicString("用户设置不存在")))

        private fun handleUpdateRestTimerInterval(state: State, interval: Int): ReduceResult<State, Effect> =
            state.userSettings?.let { currentSettings ->
                val updatedRestTimer = currentSettings.notifications.restTimerNotification.copy(reminderInterval = interval)
                val updatedNotifications = currentSettings.notifications.copy(restTimerNotification = updatedRestTimer)
                val updatedSettings = currentSettings.copy(notifications = updatedNotifications)
                val updatedState = state.copy(userSettings = updatedSettings, error = null)
                val effect = Effect.SaveRestTimerNotificationSettings(updatedRestTimer)
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(state.copy(error = UiText.DynamicString("用户设置不存在")))

        // 训练提醒设置处理 - 简化实现
        private fun handleUpdateWorkoutReminderEnabled(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateDailyReminder(state: State, enabled: Boolean): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateReminderTime(state: State, time: String): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateWeeklyPlanReminder(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateRestDayReminder(state: State, enabled: Boolean): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateMotivationalMessages(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateWorkoutReminderSound(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateWorkoutReminderVibration(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        // 日历同步设置处理 - 简化实现
        private fun handleUpdateCalendarSyncEnabled(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateSystemCalendarSync(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdatePlanUpdateNotification(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateScheduleChangeNotification(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateUpcomingWorkoutReminder(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateReminderMinutesBefore(
            state: State,
            minutes: Int,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateCalendarSyncSound(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateCalendarSyncVibration(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        // 全局通知设置处理 - 简化实现
        private fun handleUpdateGlobalVibration(state: State, enabled: Boolean): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateGlobalNotificationTime(
            state: State,
            time: String,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        // 状态栏通知设置处理 - 简化实现
        private fun handleUpdateStatusBarNotificationEnabled(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateStatusBarOngoingWorkout(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateStatusBarRestTimer(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateStatusBarProgress(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateStatusBarPriority(
            state: State,
            priority: com.example.gymbro.domain.profile.model.user.settings.NotificationPriority,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateStatusBarAutoHide(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        private fun handleUpdateStatusBarAutoHideDelay(
            state: State,
            delayMinutes: Int,
        ): ReduceResult<State, Effect> =
            ReduceResult.stateOnly(state) // TODO: 实现

        // === 状态清理处理 ===

        internal fun handleClearError(state: State): State =
            state.copy(
                error = null,
                aiContextError = null,
            )

        internal fun handleClearSaveSuccess(state: State): State = state.copy(saveSuccess = false)

        // === AI上下文处理 (预留) ===

        internal fun handleGenerateAiContext(state: State): State =
            if (state.editableProfile != null) {
                state.copy(
                    isGeneratingAiContext = true,
                    aiContextError = null,
                )
            } else {
                state.copy(
                    aiContextError = UiText.DynamicString("无法生成AI上下文：用户资料不存在"),
                )
            }

        internal fun handleRefreshAiContext(state: State): State =
            state.copy(
                isGeneratingAiContext = true,
                aiContextError = null,
                userContextForAi = null, // 清除旧的上下文
            )

        // === 内部Intent处理 (来自EffectHandler) ===

        private fun handleProfileLoaded(
            state: State,
            userProfile: UserProfile,
        ): State {
            val profileUiModel = userProfile.toUiModel()
            return state.copy(
                isLoading = false,
                profileLoadState = LoadState.Success,
                editableProfile = profileUiModel,
                originalProfile = null, // 清除原始状态
                error = null,
            )
        }

        private fun handleProfileLoadError(
            state: State,
            error: UiText,
        ): State =
            state.copy(
                isLoading = false,
                profileLoadState = LoadState.Error,
                error = error,
            )

        private fun handleUserSettingsLoaded(
            state: State,
            userSettings: UserSettings,
        ): State =
            state.copy(
                userSettings = userSettings,
                error = null,
            )

        internal fun handleProfileSaveSuccess(state: State): State =
            state.copy(
                isSaving = false,
                // 🔥 修复：PersonalInfoScreen是专门的编辑界面，保存成功后应保持编辑模式
                isEditMode = true, // 保持编辑模式，允许继续编辑
                saveSuccess = true,
                originalProfile = state.editableProfile, // 更新原始状态
                error = null,
            )

        private fun handleProfileSaveError(
            state: State,
            error: UiText,
        ): State =
            state.copy(
                isSaving = false,
                error = error,
                saveSuccess = false,
            )

        private fun handleAiContextGenerated(
            state: State,
            aiContext: ProfileContract.UserAiContext,
        ): State =
            state.copy(
                isGeneratingAiContext = false,
                userContextForAi = aiContext,
                aiContextError = null,
            )

        private fun handleAiContextError(
            state: State,
            error: UiText,
        ): State =
            state.copy(
                isGeneratingAiContext = false,
                aiContextError = error,
            )

        // === 辅助函数 ===

        // === 对话框处理 ===
        private fun handleShowDisplayNameDialog(
            state: State,
        ): State = state.copy(
            showDisplayNameDialog = true,
            tempDisplayName = state.editableProfile?.displayName,
        )

        private fun handleShowUsernameDialog(state: State): State = state.copy(
            showUsernameDialog = true,
            tempUsername = state.editableProfile?.username,
        )

        private fun handleShowEmailDialog(state: State): State = state.copy(
            showEmailDialog = true,
            tempEmail = state.editableProfile?.email,
        )

        private fun handleShowGenderDialog(state: State): State = state.copy(
            showGenderDialog = true,
            tempGender = state.editableProfile?.gender,
        )

        private fun handleShowHeightDialog(state: State): State = state.copy(
            showHeightDialog = true,
            tempHeight = state.editableProfile?.height?.toString(),
        )

        private fun handleShowWeightDialog(state: State): State = state.copy(
            showWeightDialog = true,
            tempWeight = state.editableProfile?.weight?.toString(),
        )

        private fun handleShowFitnessLevelDialog(state: State): State = state.copy(
            showFitnessLevelDialog = true,
            tempFitnessLevel = state.editableProfile?.fitnessLevel,
        )

        private fun handleShowGoalsDialog(state: State): State = state.copy(showGoalsDialog = true)

        private fun handleShowWorkoutDaysDialog(state: State): State =
            state.copy(
                showWorkoutDaysDialog = true,
                tempWorkoutDays = state.editableProfile?.workoutDays?.toImmutableList() ?: persistentListOf(),
            )

        private fun handleShowAvatarEditDialog(state: State): State = state.copy(showAvatarEditDialog = true)

        private fun handleDismissDialog(state: State): State =
            state.copy(
                showDisplayNameDialog = false,
                showUsernameDialog = false,
                showEmailDialog = false,
                showGenderDialog = false,
                showHeightDialog = false,
                showWeightDialog = false,
                showFitnessLevelDialog = false,
                showGoalsDialog = false,
                showWorkoutDaysDialog = false,
                showAvatarEditDialog = false,
                // 清除临时状态
                tempDisplayName = null,
                tempUsername = null,
                tempEmail = null,
                tempHeight = null,
                tempWeight = null,
                tempGender = null,
                tempFitnessLevel = null,
                tempGoal = null,
                tempWorkoutDays = persistentListOf(),
            )

        // === 临时编辑处理 ===
        private fun handleUpdateTempDisplayName(
            state: State,
            value: String,
        ): State = state.copy(tempDisplayName = value)

        private fun handleUpdateTempUsername(
            state: State,
            value: String,
        ): State = state.copy(tempUsername = value)

        private fun handleUpdateTempEmail(
            state: State,
            value: String,
        ): State = state.copy(tempEmail = value)

        private fun handleUpdateTempHeight(
            state: State,
            value: String,
        ): State = state.copy(tempHeight = value)

        private fun handleUpdateTempWeight(
            state: State,
            value: String,
        ): State = state.copy(tempWeight = value)

        private fun handleUpdateTempGender(
            state: State,
            value: Gender,
        ): State = state.copy(tempGender = value)

        private fun handleUpdateTempFitnessLevel(
            state: State,
            value: FitnessLevel,
        ): State = state.copy(tempFitnessLevel = value)

        private fun handleUpdateTempGoal(
            state: State,
            value: FitnessGoal,
        ): State = state.copy(tempGoal = value)

        private fun handleUpdateTempWorkoutDays(
            state: State,
            value: ImmutableList<WorkoutDay>,
        ): State = state.copy(tempWorkoutDays = value)

        // === 确认编辑处理 ===
        private fun handleConfirmDisplayName(state: State): ReduceResult<State, Effect> =
            state.tempDisplayName?.let { newName ->
                Timber.d("ProfileReducer", "确认昵称: $newName, 当前editableProfile: ${state.editableProfile?.displayName}")
                val updatedState =
                    updateEditableProfile(state.copy(showDisplayNameDialog = false, tempDisplayName = null)) { profile ->
                        profile.copy(displayName = newName)
                    }
                Timber.d("ProfileReducer", "更新后editableProfile: ${updatedState.editableProfile?.displayName}")
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    Timber.d("ProfileReducer", "触发SaveUserProfile Effect: ${profile.displayName}")
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showDisplayNameDialog = false, tempDisplayName = null))

        private fun handleConfirmUsername(state: State): ReduceResult<State, Effect> =
            state.tempUsername?.let { newUsername ->
                val updatedState =
                    updateEditableProfile(state.copy(showUsernameDialog = false, tempUsername = null)) { profile ->
                        profile.copy(username = newUsername)
                    }
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showUsernameDialog = false, tempUsername = null))

        private fun handleConfirmEmail(state: State): ReduceResult<State, Effect> =
            state.tempEmail?.let { newEmail ->
                val updatedState =
                    updateEditableProfile(state.copy(showEmailDialog = false, tempEmail = null)) { profile ->
                        profile.copy(email = newEmail)
                    }
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showEmailDialog = false, tempEmail = null))

        private fun handleConfirmHeight(state: State): ReduceResult<State, Effect> =
            state.tempHeight?.toFloatOrNull()?.let { newHeight ->
                val updatedState =
                    updateEditableProfile(state.copy(showHeightDialog = false, tempHeight = null)) { profile ->
                        profile.copy(height = newHeight)
                    }
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showHeightDialog = false, tempHeight = null))

        private fun handleConfirmWeight(state: State): ReduceResult<State, Effect> =
            state.tempWeight?.toFloatOrNull()?.let { newWeight ->
                val updatedState =
                    updateEditableProfile(state.copy(showWeightDialog = false, tempWeight = null)) { profile ->
                        profile.copy(weight = newWeight)
                    }
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showWeightDialog = false, tempWeight = null))

        private fun handleConfirmGender(state: State): ReduceResult<State, Effect> =
            state.tempGender?.let { newGender ->
                val updatedState =
                    updateEditableProfile(state.copy(showGenderDialog = false, tempGender = null)) { profile ->
                        profile.copy(gender = newGender)
                    }
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showGenderDialog = false, tempGender = null))

        private fun handleConfirmFitnessLevel(state: State): ReduceResult<State, Effect> =
            state.tempFitnessLevel?.let { newLevel ->
                val updatedState =
                    updateEditableProfile(state.copy(showFitnessLevelDialog = false, tempFitnessLevel = null)) { profile ->
                        profile.copy(fitnessLevel = newLevel)
                    }
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showFitnessLevelDialog = false, tempFitnessLevel = null))

        private fun handleConfirmGoal(state: State): ReduceResult<State, Effect> =
            state.tempGoal?.let { newGoal ->
                val updatedState =
                    updateEditableProfile(state.copy(showGoalsDialog = false, tempGoal = null)) { profile ->
                        val updatedGoals = profile.fitnessGoals.toMutableList()
                        if (!updatedGoals.contains(newGoal)) {
                            updatedGoals.add(newGoal)
                        }
                        profile.copy(fitnessGoals = updatedGoals)
                    }
                // 🔥 即时保存：确认后立即触发保存
                updatedState.editableProfile?.let { profile ->
                    val effect = Effect.SaveUserProfile(profile)
                    ReduceResult.withEffect(updatedState, effect)
                } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
            } ?: ReduceResult.stateOnly(state.copy(showGoalsDialog = false, tempGoal = null))

        private fun handleConfirmWorkoutDays(state: State): ReduceResult<State, Effect> {
            val updatedState = updateEditableProfile(
                state.copy(showWorkoutDaysDialog = false, tempWorkoutDays = persistentListOf()),
            ) { profile ->
                profile.copy(workoutDays = state.tempWorkoutDays)
            }
            // 🔥 即时保存：确认后立即触发保存
            return updatedState.editableProfile?.let { profile ->
                val effect = Effect.SaveUserProfile(profile)
                ReduceResult.withEffect(updatedState, effect)
            } ?: ReduceResult.stateOnly(updatedState.copy(error = UiText.DynamicString("无法保存：用户资料不存在")))
        }

        // === 主题设置处理 ===
        private fun handleUpdateThemeStyle(
            state: State,
            style: com.example.gymbro.core.theme.ThemeStyle,
        ): ReduceResult<State, Effect> {
            val updatedState = state.copy(
                currentThemeStyle = style,
                error = null,
            )
            // 🔥 即时保存：主题设置变更后立即触发保存
            val effect = Effect.SaveThemeSettings(
                themeStyle = updatedState.currentThemeStyle ?: com.example.gymbro.core.theme.ThemeStyle.GROK,
                colorMode = updatedState.currentColorMode ?: com.example.gymbro.core.theme.ColorMode.SYSTEM,
                useDynamicColor = updatedState.isDynamicColorEnabled,
            )
            return ReduceResult.withEffect(updatedState, effect)
        }

        private fun handleUpdateColorMode(
            state: State,
            mode: com.example.gymbro.core.theme.ColorMode,
        ): ReduceResult<State, Effect> {
            val updatedState = state.copy(
                currentColorMode = mode,
                error = null,
            )
            // 🔥 即时保存：主题设置变更后立即触发保存
            val effect = Effect.SaveThemeSettings(
                themeStyle = updatedState.currentThemeStyle ?: com.example.gymbro.core.theme.ThemeStyle.GROK,
                colorMode = updatedState.currentColorMode ?: com.example.gymbro.core.theme.ColorMode.SYSTEM,
                useDynamicColor = updatedState.isDynamicColorEnabled,
            )
            return ReduceResult.withEffect(updatedState, effect)
        }

        private fun handleUpdateDynamicColor(
            state: State,
            enabled: Boolean,
        ): ReduceResult<State, Effect> {
            val updatedState = state.copy(
                isDynamicColorEnabled = enabled,
                error = null,
            )
            // 🔥 即时保存：主题设置变更后立即触发保存
            val effect = Effect.SaveThemeSettings(
                themeStyle = updatedState.currentThemeStyle ?: com.example.gymbro.core.theme.ThemeStyle.GROK,
                colorMode = updatedState.currentColorMode ?: com.example.gymbro.core.theme.ColorMode.SYSTEM,
                useDynamicColor = updatedState.isDynamicColorEnabled,
            )
            return ReduceResult.withEffect(updatedState, effect)
        }

        // === 主题配置加载和更新结果处理 ===

        private fun handleThemeConfigLoaded(
            state: State,
            themeStyle: com.example.gymbro.core.theme.ThemeStyle,
            colorMode: com.example.gymbro.core.theme.ColorMode,
            dynamicColorEnabled: Boolean,
        ): State =
            state.copy(
                currentThemeStyle = themeStyle,
                currentColorMode = colorMode,
                isDynamicColorEnabled = dynamicColorEnabled,
                error = null,
            )

        private fun handleThemeStyleChangeSuccess(
            state: State,
            style: com.example.gymbro.core.theme.ThemeStyle,
        ): State =
            state.copy(
                currentThemeStyle = style,
                error = null,
            )

        private fun handleThemeStyleChangeError(
            state: State,
            error: UiText,
        ): State = state.copy(error = error)

        private fun handleColorModeChangeSuccess(
            state: State,
            mode: com.example.gymbro.core.theme.ColorMode,
        ): State =
            state.copy(
                currentColorMode = mode,
                error = null,
            )

        private fun handleColorModeChangeError(
            state: State,
            error: UiText,
        ): State = state.copy(error = error)

        private fun handleDynamicColorChangeSuccess(
            state: State,
            enabled: Boolean,
        ): State =
            state.copy(
                isDynamicColorEnabled = enabled,
                error = null,
            )

        private fun handleDynamicColorChangeError(
            state: State,
            error: UiText,
        ): State = state.copy(error = error)

        /**
         * 安全地更新可编辑的用户资料
         */
        private fun updateEditableProfile(
            state: State,
            update: (profile: ProfileUiModel) -> ProfileUiModel,
        ): State =
            if (state.isEditMode && state.editableProfile != null) {
                state.copy(
                    editableProfile = update(state.editableProfile),
                    error = null, // 清除之前的错误
                )
            } else {
                state // 非编辑模式下不允许更新
            }
    }

// === 扩展函数 ===

internal fun UserProfile.toUiModel(): ProfileUiModel =
    ProfileUiModel(
        id = this.userId,
        username = this.username ?: "",
        displayName = this.displayName ?: "",
        email = this.email ?: "",
        phoneNumber = this.phoneNumber ?: "",
        photoUrl = this.avatarUrl ?: "",
        bio = this.bio ?: "",
        gender = this.gender,
        height = this.height ?: 0.0f,
        weight = this.weight ?: 0.0f,
        weightUnit = this.weightUnit.name.lowercase(), // 转换为字符串
        fitnessLevel = this.fitnessLevel,
        fitnessGoals = this.fitnessGoals,
        workoutDays = this.workoutDays,
        allowPartnerMatching = this.allowPartnerMatching,
        totalActivityCount = this.totalActivityCount,
        weeklyActiveMinutes = this.weeklyActiveMinutes,
        likesReceived = this.likesReceived,
        isAnonymous = this.isAnonymous,
    )
