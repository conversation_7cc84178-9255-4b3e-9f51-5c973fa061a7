package com.example.gymbro.features.profile.internal.presentation.workoutdays

import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.profile.usecase.GetWorkoutDaysUseCase
import com.example.gymbro.domain.profile.usecase.SaveWorkoutDaysUseCase
import com.example.gymbro.domain.workout.model.WeekDay
import javax.inject.Inject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * WorkoutDays功能的副作用处理器
 * 负责处理所有副作用操作，如数据加载、保存等
 *
 * 遵循MVI架构原则，将副作用与UI状态分离
 */
internal class WorkoutDaysEffectHandler
    @Inject
    constructor(
        private val getWorkoutDaysUseCase: GetWorkoutDaysUseCase,
        private val saveWorkoutDaysUseCase: SaveWorkoutDaysUseCase,
        private val logger: Logger,
    ) {

        /**
         * 处理副作用
         *
         * @param effect 要处理的副作用
         * @param coroutineScope 协程作用域
         * @param dispatch Intent分发器
         * @return Job对象，用于生命周期管理
         */
        fun handle(
            effect: WorkoutDaysContract.Effect,
            coroutineScope: CoroutineScope,
            dispatch: (WorkoutDaysContract.Intent) -> Unit,
        ): Job {
            logger.d("WorkoutDaysEffectHandler", "处理Effect: ${effect::class.simpleName}")

            return when (effect) {
                is WorkoutDaysContract.Effect.LoadWorkoutDays -> handleLoadWorkoutDays(coroutineScope, dispatch)
                is WorkoutDaysContract.Effect.SaveWorkoutDays -> handleSaveWorkoutDays(
                    effect,
                    coroutineScope,
                    dispatch,
                )

                is WorkoutDaysContract.Effect.ShowSnackbar -> handleShowSnackbar(effect, coroutineScope)
                is WorkoutDaysContract.Effect.ShowError -> handleShowError(effect, coroutineScope)
                is WorkoutDaysContract.Effect.NavigateBack -> handleNavigateBack(coroutineScope)
            }
        }

        /**
         * 处理训练日数据加载
         */
        private fun handleLoadWorkoutDays(
            coroutineScope: CoroutineScope,
            dispatch: (WorkoutDaysContract.Intent) -> Unit,
        ): Job {
            return coroutineScope.launch {
                try {
                    logger.d("WorkoutDaysEffectHandler", "开始加载训练日数据")

                    val result = getWorkoutDaysUseCase()

                    if (result.isSuccess) {
                        val workoutDaysList = result.getOrNull() ?: emptyList()
                        // 将Int列表转换为WeekDay集合
                        val weekDays = workoutDaysList.mapNotNull { dayIndex ->
                            when (dayIndex) {
                                1 -> WeekDay.MONDAY
                                2 -> WeekDay.TUESDAY
                                3 -> WeekDay.WEDNESDAY
                                4 -> WeekDay.THURSDAY
                                5 -> WeekDay.FRIDAY
                                6 -> WeekDay.SATURDAY
                                7 -> WeekDay.SUNDAY
                                else -> null
                            }
                        }.toSet()

                        logger.d("WorkoutDaysEffectHandler", "训练日数据加载成功: $weekDays")
                        dispatch(WorkoutDaysContract.Intent.WorkoutDaysLoaded(weekDays))
                    } else {
                        val error = result.exceptionOrNull()
                        logger.e("WorkoutDaysEffectHandler", "训练日数据加载失败", error)
                        dispatch(
                            WorkoutDaysContract.Intent.WorkoutDaysLoadError(
                                UiText.DynamicString("加载训练日设置失败: ${error?.message ?: "未知错误"}"),
                            ),
                        )
                    }
                } catch (e: Exception) {
                    logger.e("WorkoutDaysEffectHandler", "训练日数据加载异常", e)
                    dispatch(
                        WorkoutDaysContract.Intent.WorkoutDaysLoadError(
                            UiText.DynamicString("加载训练日设置时发生异常: ${e.message}"),
                        ),
                    )
                }
            }
        }

        /**
         * 处理训练日数据保存
         */
        private fun handleSaveWorkoutDays(
            effect: WorkoutDaysContract.Effect.SaveWorkoutDays,
            coroutineScope: CoroutineScope,
            dispatch: (WorkoutDaysContract.Intent) -> Unit,
        ): Job {
            return coroutineScope.launch {
                try {
                    logger.d("WorkoutDaysEffectHandler", "开始保存训练日数据: ${effect.workoutDays}")

                    // 将WeekDay集合转换为Int列表
                    val workoutDaysList = effect.workoutDays.map { weekDay ->
                        when (weekDay) {
                            WeekDay.MONDAY -> 1
                            WeekDay.TUESDAY -> 2
                            WeekDay.WEDNESDAY -> 3
                            WeekDay.THURSDAY -> 4
                            WeekDay.FRIDAY -> 5
                            WeekDay.SATURDAY -> 6
                            WeekDay.SUNDAY -> 7
                        }
                    }.sorted()

                    val result = saveWorkoutDaysUseCase(workoutDaysList)

                    if (result.isSuccess) {
                        logger.d("WorkoutDaysEffectHandler", "训练日数据保存成功")
                        dispatch(WorkoutDaysContract.Intent.WorkoutDaysSaveSuccess)
                    } else {
                        val error = result.exceptionOrNull()
                        logger.e("WorkoutDaysEffectHandler", "训练日数据保存失败", error)
                        dispatch(
                            WorkoutDaysContract.Intent.WorkoutDaysSaveError(
                                UiText.DynamicString("保存训练日设置失败: ${error?.message ?: "未知错误"}"),
                            ),
                        )
                    }
                } catch (e: Exception) {
                    logger.e("WorkoutDaysEffectHandler", "训练日数据保存异常", e)
                    dispatch(
                        WorkoutDaysContract.Intent.WorkoutDaysSaveError(
                            UiText.DynamicString("保存训练日设置时发生异常: ${e.message}"),
                        ),
                    )
                }
            }
        }

        /**
         * 处理显示Snackbar
         */
        private fun handleShowSnackbar(
            effect: WorkoutDaysContract.Effect.ShowSnackbar,
            coroutineScope: CoroutineScope,
        ): Job {
            return coroutineScope.launch {
                logger.d("WorkoutDaysEffectHandler", "显示Snackbar: ${effect.message}")
                // 这里可以集成SnackbarHostState或其他UI反馈机制
            }
        }

        /**
         * 处理显示错误
         */
        private fun handleShowError(
            effect: WorkoutDaysContract.Effect.ShowError,
            coroutineScope: CoroutineScope,
        ): Job {
            return coroutineScope.launch {
                logger.e("WorkoutDaysEffectHandler", "显示错误: ${effect.error}")
                // 这里可以集成错误显示机制
            }
        }

        /**
         * 处理导航返回
         */
        private fun handleNavigateBack(coroutineScope: CoroutineScope): Job {
            return coroutineScope.launch {
                logger.d("WorkoutDaysEffectHandler", "处理导航返回")
                // 这里可以集成导航逻辑
            }
        }
    }
