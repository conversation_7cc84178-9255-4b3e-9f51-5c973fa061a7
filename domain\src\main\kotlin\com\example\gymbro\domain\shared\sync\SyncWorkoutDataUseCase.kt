package com.example.gymbro.domain.shared.sync

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.shared.sync.model.DataType
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

/**
 * 同步训练数据的UseCase
 * 实现完整的数据同步逻辑，包括网络检查、重试机制和错误恢复
 */
@Singleton
class SyncWorkoutDataUseCase
    @Inject
    constructor(
        private val syncCoordinator: SyncCoordinator,
        private val networkMonitor: NetworkMonitor,
        private val sessionRepository: SessionRepository,
        private val templateRepository: TemplateRepository,
        @IoDispatcher dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<SyncWorkoutDataUseCase.Params, SyncWorkoutDataUseCase.SyncResult>(dispatcher, logger) {
        /**
         * 参数类
         */
        data class Params(
            val forceSync: Boolean = false,
            val syncTypes: List<SyncType> = listOf(SyncType.ALL),
        )

        /**
         * 同步结果类
         */
        data class SyncResult(
            val syncedItems: Int,
            val failedItems: Int,
            val syncDuration: Duration,
            val lastSyncTime: Instant,
        )

        /**
         * 同步类型枚举
         */
        enum class SyncType {
            WORKOUT,
            TEMPLATE,
            ALL,
        }

        /**
         * 同步项目结果
         */
        private data class SyncItemResult(
            val syncedCount: Int,
            val failedCount: Int,
        )

        /**
         * 执行训练数据同步
         *
         * @param parameters 包含同步参数的对象
         * @return 同步结果
         */
        override suspend fun execute(parameters: Params): ModernResult<SyncResult> {
            logger.d("开始同步训练数据: forceSync=${parameters.forceSync}")

            // 1. 检查网络状态
            val networkCheck = checkNetworkStatus()
            if (networkCheck is ModernResult.Error) {
                return networkCheck
            }

            // 2. 检查是否需要同步
            if (!parameters.forceSync && !shouldSync()) {
                logger.d("无需同步，跳过")
                return ModernResult.Companion.success(getLastSyncResult())
            }

            // 3. 执行同步
            val startTime = Clock.System.now()
            return try {
                val syncResults = mutableListOf<SyncItemResult>()

                for (syncType in parameters.syncTypes) {
                    when (syncType) {
                        SyncType.WORKOUT -> {
                            syncResults.add(syncWorkoutSessions())
                        }

                        SyncType.TEMPLATE -> {
                            syncResults.add(syncWorkoutTemplates())
                        }

                        SyncType.ALL -> {
                            syncResults.add(syncWorkoutSessions())
                            syncResults.add(syncWorkoutTemplates())
                        }
                    }
                }

                val endTime = Clock.System.now()
                val result =
                    SyncResult(
                        syncedItems = syncResults.sumOf { it.syncedCount },
                        failedItems = syncResults.sumOf { it.failedCount },
                        syncDuration = endTime - startTime,
                        lastSyncTime = endTime,
                    )

                logger.d("同步完成: ${result.syncedItems}个成功, ${result.failedItems}个失败")
                ModernResult.Companion.success(result)
            } catch (e: Exception) {
                logger.e(e, "同步过程中发生异常")
                ModernResult.Companion.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "syncWorkoutData",
                        message = UiText.DynamicString("同步失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }

        /**
         * 检查网络状态 - 直接使用NetworkMonitor，简化架构
         */
        private suspend fun checkNetworkStatus(): ModernResult<Unit> =
            try {
                val isOnline = networkMonitor.isOnline
                if (isOnline) {
                    ModernResult.Companion.success(Unit)
                } else {
                    ModernResult.Companion.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "checkNetworkStatus",
                            message = UiText.DynamicString("网络不可用，无法同步"),
                            recoverable = true,
                        ),
                    )
                }
            } catch (e: Exception) {
                logger.e(e, "检查网络状态失败")
                ModernResult.Companion.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "checkNetworkStatus",
                        message = UiText.DynamicString("检查网络状态失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }

        /**
         * 检查是否需要同步
         */
        private suspend fun shouldSync(): Boolean =
            try {
                // 使用SyncCoordinator检查是否可以同步
                syncCoordinator.canSync()
            } catch (e: Exception) {
                logger.e(e, "检查同步状态失败")
                false
            }

        /**
         * 获取最后一次同步结果
         */
        private suspend fun getLastSyncResult(): SyncResult =
            try {
                val lastResult = syncCoordinator.getLastSyncResult(DataType.ALL)
                SyncResult(
                    syncedItems = lastResult.itemsUploaded + lastResult.itemsDownloaded,
                    failedItems = 0,
                    syncDuration = Duration.Companion.ZERO,
                    lastSyncTime = Instant.Companion.fromEpochMilliseconds(lastResult.syncEndTime),
                )
            } catch (e: Exception) {
                logger.e(e, "获取最后同步结果失败")
                SyncResult(
                    syncedItems = 0,
                    failedItems = 0,
                    syncDuration = Duration.Companion.ZERO,
                    lastSyncTime = Clock.System.now(),
                )
            }

        /**
         * 同步训练会话数据
         */
        private suspend fun syncWorkoutSessions(): SyncItemResult =
            try {
                logger.d("开始同步训练会话数据")

                // 使用SyncCoordinator执行同步
                when (val syncResult = syncCoordinator.startSync(DataType.WORKOUT)) {
                    is ModernResult.Success -> {
                        val result = syncResult.data
                        SyncItemResult(
                            syncedCount = result.itemsUploaded + result.itemsDownloaded,
                            failedCount = result.itemsWithConflicts,
                        )
                    }

                    is ModernResult.Error -> {
                        logger.e("同步训练会话失败: ${syncResult.error}")
                        SyncItemResult(syncedCount = 0, failedCount = 1)
                    }

                    is ModernResult.Loading -> {
                        SyncItemResult(syncedCount = 0, failedCount = 0)
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "同步训练会话异常")
                SyncItemResult(syncedCount = 0, failedCount = 1)
            }

        /**
         * 同步训练模板数据
         */
        private suspend fun syncWorkoutTemplates(): SyncItemResult =
            try {
                logger.d("开始同步训练模板数据")

                // 使用SyncCoordinator执行同步
                when (val syncResult = syncCoordinator.startSync(DataType.TEMPLATE)) {
                    is ModernResult.Success -> {
                        val result = syncResult.data
                        SyncItemResult(
                            syncedCount = result.itemsUploaded + result.itemsDownloaded,
                            failedCount = result.itemsWithConflicts,
                        )
                    }

                    is ModernResult.Error -> {
                        logger.e("同步训练模板失败: ${syncResult.error}")
                        SyncItemResult(syncedCount = 0, failedCount = 1)
                    }

                    is ModernResult.Loading -> {
                        SyncItemResult(syncedCount = 0, failedCount = 0)
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "同步训练模板异常")
                SyncItemResult(syncedCount = 0, failedCount = 1)
            }
    }
