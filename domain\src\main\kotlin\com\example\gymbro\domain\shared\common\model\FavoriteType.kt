package com.example.gymbro.domain.shared.common.model

import com.example.gymbro.domain.shared.common.model.FavoriteType.*

/**
 * 收藏类型枚举
 * 定义用户可以收藏的不同内容类型
 *
:start_line:7
-------
 * @property WORKOUT 锻炼收藏类型
 * @property EXERCISE 锻炼动作收藏类型
 * @property TRAINING_PLAN 训练计划收藏类型
 * @property TEMPLATE 锻炼模板收藏类型
 * @property AI_CONVERSATION AI对话收藏类型
 * @property COACH 教练资料收藏类型
 * @property ARTICLE 文章内容收藏类型
 * @property RECIPE 营养食谱收藏类型
 * @property OTHER 其他收藏类型，用于处理未知类型
 */
enum class FavoriteType {
    WORKOUT,
    EXERCISE,
    TRAINING_PLAN,
    TEMPLATE,
    AI_CONVERSATION,
    COACH,
    ARTICLE,
    RECIPE,
    OTHER,
    ;

    companion object {
        /**
         * 根据字符串获取收藏类型
         *
         * @param value 收藏类型的字符串表示
         * @return 匹配的收藏类型，如果没有匹配项则返回OTHER
         */
        fun fromString(
            value: String?,
        ): FavoriteType = values().find { it.name.equals(value, ignoreCase = true) } ?: OTHER
    }
}
