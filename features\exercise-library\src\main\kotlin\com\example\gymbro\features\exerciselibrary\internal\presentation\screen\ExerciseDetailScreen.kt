package com.example.gymbro.features.exerciselibrary.internal.presentation.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.StarBorder
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.features.exerciselibrary.internal.presentation.contract.ExerciseLibraryContract
import com.example.gymbro.features.exerciselibrary.internal.presentation.viewmodel.ExerciseLibraryViewModel

/**
 * 动作详情屏幕
 *
 * 显示单个训练动作的详细信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExerciseDetailScreen(
    navController: NavController,
    exerciseId: String,
    modifier: Modifier = Modifier,
    onAddToWorkout: () -> Unit = {},
    onToggleFavorite: () -> Unit = {},
) {
    // 🔧 使用真实数据：集成ViewModel和UseCase
    val viewModel: ExerciseLibraryViewModel = hiltViewModel()
    val state by viewModel.state.collectAsStateWithLifecycle()

    var isFavorite by remember { mutableStateOf(false) }
    var currentExercise by remember { mutableStateOf<Exercise?>(null) }

    // 🔧 加载真实动作数据
    LaunchedEffect(exerciseId) {
        // 先从已加载的动作中查找
        val foundExercise = state.exercises.find { it.id == exerciseId }
        if (foundExercise != null) {
            currentExercise = foundExercise
        } else {
            // 如果没找到，触发加载
            viewModel.handleIntent(ExerciseLibraryContract.Intent.LoadExercises)
        }
    }

    // 🔧 监听状态变化，更新当前动作
    LaunchedEffect(state.exercises) {
        if (currentExercise == null) {
            currentExercise = state.exercises.find { it.id == exerciseId }
        }
    }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = currentExercise?.let {
                            when (val nameValue = it.name) {
                                is UiText.DynamicString -> nameValue.value
                                is UiText.StringResource -> "动作详情"
                                else -> nameValue.toString()
                            }
                        } ?: "动作详情",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回",
                        )
                    }
                },
                actions = {
                    IconButton(onClick = {
                        isFavorite = !isFavorite
                        onToggleFavorite()
                    }) {
                        Icon(
                            imageVector = if (isFavorite) Icons.Filled.Star else Icons.Outlined.StarBorder,
                            contentDescription = if (isFavorite) "取消收藏" else "收藏",
                            tint = if (isFavorite) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant,
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                ),
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = onAddToWorkout,
                containerColor = MaterialTheme.colorScheme.primary,
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加到训练",
                    tint = MaterialTheme.colorScheme.onPrimary,
                )
            }
        },
    ) { paddingValues ->
        if (currentExercise == null) {
            // 🔧 动作未找到或正在加载
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center,
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        text = "动作未找到",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "ID: $exerciseId",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            }
        } else {
            // 🔧 动作详情内容：使用真实数据
            val exerciseDetail = currentExercise!!.toSampleExerciseDetail()
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                // 动作图片和基本信息
                ExerciseHeaderSection(
                    exercise = exerciseDetail,
                    modifier = Modifier.fillMaxWidth(),
                )

                Spacer(modifier = Modifier.height(24.dp))

                // 基本信息卡片
                ExerciseInfoCard(
                    exercise = exerciseDetail,
                    modifier = Modifier.fillMaxWidth(),
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 动作描述
                ExerciseDescriptionCard(
                    exercise = exerciseDetail,
                    modifier = Modifier.fillMaxWidth(),
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 训练建议
                ExerciseRecommendationCard(
                    exercise = exerciseDetail,
                    modifier = Modifier.fillMaxWidth(),
                )

                // 为FAB留出空间
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Composable
private fun ExerciseHeaderSection(
    exercise: SampleExerciseDetail,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // 动作图片占位符
        Box(
            modifier = Modifier
                .size(200.dp)
                .clip(RoundedCornerShape(16.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant),
            contentAlignment = Alignment.Center,
        ) {
            Icon(
                imageVector = Icons.Default.FitnessCenter,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            // 讲解标签
            if (exercise.hasExplanation) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(12.dp),
                ) {
                    Surface(
                        color = MaterialTheme.colorScheme.primary,
                        shape = RoundedCornerShape(8.dp),
                    ) {
                        Text(
                            text = "讲解",
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontWeight = FontWeight.Bold,
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 动作名称
        Text(
            text = exercise.name,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 肌肉群和器械类型
        Text(
            text = "${exercise.muscleGroup} • ${exercise.equipmentType}",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
    }
}

@Composable
private fun ExerciseInfoCard(
    exercise: SampleExerciseDetail,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface,
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Text(
                text = "基本信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface,
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 信息行
            InfoRow(label = "目标肌群", value = exercise.muscleGroup)
            InfoRow(label = "器械类型", value = exercise.equipmentType)
            InfoRow(label = "建议组数", value = "${exercise.defaultSets}组")
            InfoRow(label = "建议次数", value = "${exercise.repetitions}次")
            if (exercise.defaultWeight > 0) {
                InfoRow(label = "建议重量", value = "${exercise.defaultWeight}kg")
            }
        }
    }
}

@Composable
private fun ExerciseDescriptionCard(
    exercise: SampleExerciseDetail,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface,
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Text(
                text = "动作描述",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface,
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = exercise.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                lineHeight = 20.sp,
            )
        }
    }
}

@Composable
private fun ExerciseRecommendationCard(
    exercise: SampleExerciseDetail,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface,
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Text(
                text = "训练建议",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface,
            )

            Spacer(modifier = Modifier.height(12.dp))

            exercise.tips.forEach { tip ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                ) {
                    Text(
                        text = "•",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(end = 8.dp),
                    )
                    Text(
                        text = tip,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.weight(1f),
                    )
                }
            }
        }
    }
}

@Composable
private fun InfoRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium,
        )
    }
}

// 示例数据
private data class SampleExerciseDetail(
    val id: String,
    val name: String,
    val muscleGroup: String,
    val equipmentType: String,
    val repetitions: Int,
    val defaultSets: Int,
    val defaultWeight: Float,
    val hasExplanation: Boolean,
    val description: String,
    val tips: List<String>,
)

private fun getSampleExerciseById(id: String): SampleExerciseDetail? {
    val exercises = listOf(
        SampleExerciseDetail(
            id = "1",
            name = "杠铃卧推",
            muscleGroup = "胸部",
            equipmentType = "杠铃",
            repetitions = 90,
            defaultSets = 3,
            defaultWeight = 60f,
            hasExplanation = true,
            description = "杠铃卧推是最经典的胸部训练动作之一，主要锻炼胸大肌、三角肌前束和肱三头肌。这个动作可以有效增强上肢推举力量，是力量训练的基础动作。",
            tips = listOf(
                "保持肩胛骨收紧，双脚平稳着地",
                "下降时控制速度，感受胸部肌肉拉伸",
                "推举时保持直线轨迹，避免晃动",
                "选择合适重量，确保动作质量",
            ),
        ),
        SampleExerciseDetail(
            id = "2",
            name = "暂停卧推",
            muscleGroup = "胸部",
            equipmentType = "杠铃",
            repetitions = 65,
            defaultSets = 3,
            defaultWeight = 50f,
            hasExplanation = true,
            description = "暂停卧推在胸部停留1-2秒，消除弹性势能，更好地刺激胸部肌肉，提高肌肉控制能力。",
            tips = listOf(
                "杠铃接触胸部后暂停1-2秒",
                "保持紧张状态，不要完全放松",
                "重量应比正常卧推轻10-15%",
                "专注于胸部肌肉的收缩感",
            ),
        ),
    )

    return exercises.find { it.id == id }
}

/**
 * Exercise模型转换为SampleExerciseDetail的扩展函数
 * 用于将数据库中的Exercise转换为UI详情显示格式
 */
private fun Exercise.toSampleExerciseDetail(): SampleExerciseDetail {
    val exerciseName = when (val nameValue = this.name) {
        is UiText.DynamicString -> nameValue.value
        is UiText.StringResource -> "自定义动作"
        else -> nameValue.toString()
    }

    val muscleGroupName = when (this.muscleGroup) {
        com.example.gymbro.shared.models.exercise.MuscleGroup.CHEST -> "胸部"
        com.example.gymbro.shared.models.exercise.MuscleGroup.BACK -> "背部"
        com.example.gymbro.shared.models.exercise.MuscleGroup.QUADRICEPS,
        com.example.gymbro.shared.models.exercise.MuscleGroup.HAMSTRINGS,
        com.example.gymbro.shared.models.exercise.MuscleGroup.GLUTES,
        -> "腿部"

        com.example.gymbro.shared.models.exercise.MuscleGroup.SHOULDERS -> "肩部"
        com.example.gymbro.shared.models.exercise.MuscleGroup.BICEPS -> "二头肌"
        com.example.gymbro.shared.models.exercise.MuscleGroup.TRICEPS -> "三头肌"
        com.example.gymbro.shared.models.exercise.MuscleGroup.ABS -> "腹部"
        com.example.gymbro.shared.models.exercise.MuscleGroup.CORE -> "核心"
        com.example.gymbro.shared.models.exercise.MuscleGroup.CALVES -> "小腿"
        com.example.gymbro.shared.models.exercise.MuscleGroup.FOREARMS -> "前臂"
        com.example.gymbro.shared.models.exercise.MuscleGroup.FULL_BODY -> "全身"
        else -> "自定义"
    }

    val equipmentName = if (this.equipment.isNotEmpty()) {
        when (this.equipment.first()) {
            com.example.gymbro.shared.models.exercise.Equipment.BARBELL -> "杠铃"
            com.example.gymbro.shared.models.exercise.Equipment.DUMBBELL -> "哑铃"
            com.example.gymbro.shared.models.exercise.Equipment.NONE -> "自重"
            else -> "自定义器械"
        }
    } else {
        "自定义器械"
    }

    val descriptionText = when (val descValue = this.description) {
        is UiText.DynamicString -> descValue.value
        is UiText.StringResource -> "这是一个自定义动作，请根据个人情况进行训练。"
        else -> descValue.toString()
    }

    return SampleExerciseDetail(
        id = this.id,
        name = exerciseName,
        muscleGroup = muscleGroupName,
        equipmentType = equipmentName,
        repetitions = this.defaultReps,
        defaultSets = this.defaultSets,
        defaultWeight = this.defaultWeight,
        hasExplanation = false, // 自定义动作暂时没有讲解
        description = descriptionText,
        tips = listOf(
            "注意动作标准，避免受伤",
            "选择合适的重量进行训练",
            "保持呼吸节奏，专注肌肉感受",
            "如有不适请立即停止训练",
        ),
    )
}

/**
 * ExerciseDetailScreen组件所需的UiText文本资源
 * 为动作详情界面提供统一的文本处理机制
 */
data class ExerciseDetailScreenUiTexts(
    val exerciseDetailTitle: UiText,
    val back: UiText,
    val unfavoriteContentDescription: UiText,
    val favoriteContentDescription: UiText,
    val editContentDescription: UiText,
    val exerciseNotFound: UiText,
    val addToWorkout: UiText,
    val basicInformationLabel: UiText,
    val muscleGroupLabel: UiText,
    val equipmentLabel: UiText,
    val suggestedSetsLabel: UiText,
    val suggestedRepsLabel: UiText,
    val suggestedWeightLabel: UiText,
    val weightKgFormat: UiText,
    val exerciseDescriptionLabel: UiText,
    val exerciseStepsLabel: UiText,
    val exerciseTipsLabel: UiText,
    val relatedExercisesLabel: UiText,
    val loadingRelatedExercises: UiText,
)

/**
 * ExerciseDetailScreen 的预览组件。
 * 遵循 GymBro 预览规范。
 */
@Preview
@Composable
private fun ExerciseDetailScreenPreview() {
    GymBroTheme {
        ExerciseDetailScreen(
            navController = rememberNavController(),
            exerciseId = "1",
        )
    }
}
