package com.example.gymbro.di.domain

import com.example.gymbro.core.ai.config.PromptConfig
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.core.ml.service.BgeEngineManager
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.repository.AICoachRepository
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.coach.usecase.*
import com.example.gymbro.domain.profile.repository.user.UserAggregateRepository
import com.example.gymbro.domain.service.workout.AiInteractionService
import com.example.gymbro.domain.shared.common.GetQuickActionCategoriesUseCase
import com.example.gymbro.domain.shared.common.repository.QuickActionRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Coach相关UseCase的依赖注入模块
 *
 * 提供以下UseCase：
 * - AiCoachUseCase - AI教练交互
 * - ChatSessionManagementUseCase - 统一聊天会话管理（合并了原有的5个会话相关UseCase）
 * - StartOrContinueChatUseCase - 开始或继续AI教练对话
 * - SendChatMessageAndGetResponseUseCase - 发送消息给AI教练并获取流式响应
 * - GenerateWorkoutFromAIChatUseCase - 基于AI对话生成训练计划草稿
 * - PrepareAiContextUseCase - 准备AI上下文（RAG检索编排核心UseCase）
 *
 * 注意：原有的以下UseCase已合并到ChatSessionManagementUseCase中：
 * - CreateChatSessionUseCase
 * - LoadChatSessionUseCase
 * - GetChatSessionsUseCase
 * - SaveChatMessageUseCase
 * - SearchChatSessionsUseCase
 */
@Module
@InstallIn(SingletonComponent::class)
object CoachUseCaseModule {
    /**
     * 提供AI教练用例 - 专注于AI交互
     */
    @Provides
    fun provideAiCoachUseCase(
        aiCoachRepository: AICoachRepository,
        logger: Logger,
    ): AiCoachUseCase = AiCoachUseCase(aiCoachRepository, logger)

    /**
     * 提供聊天会话管理用例 - 统一管理所有会话操作
     *
     * 合并了以下UseCase的功能：
     * - CreateChatSessionUseCase
     * - LoadChatSessionUseCase
     * - GetChatSessionsUseCase
     * - SaveChatMessageUseCase
     * - SearchChatSessionsUseCase
     */
    @Provides
    fun provideChatSessionManagementUseCase(
        chatRepository: ChatRepository,
        logger: Logger,
    ): ChatSessionManagementUseCase = ChatSessionManagementUseCase(chatRepository, logger)

    /**
     * 提供开始或继续聊天用例
     */
    @Provides
    fun provideStartOrContinueChatUseCase(
        chatSessionManagementUseCase: ChatSessionManagementUseCase,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher dispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
        logger: Logger,
    ): StartOrContinueChatUseCase =
        StartOrContinueChatUseCase(
            chatSessionManagementUseCase,
            dispatcher,
            logger,
        )

    /**
     * 提供单一职责的消息发送用例
     *
     * 🔥 【架构重构】专门负责消息发送和prompt构建，不处理AI响应
     * 使用LayeredPromptBuilder确保完整的prompt构建流程
     */
    @Provides
    fun provideSendMessageUseCase(
        aiCoachUseCase: AiCoachUseCase,
        chatSessionManagementUseCase: ChatSessionManagementUseCase,
        bgeEmbeddingService: com.example.gymbro.core.ml.service.BgeEmbeddingService,
        hybridSearchRepository: com.example.gymbro.domain.exercise.repository.HybridSearchRepository,
        initializeBgeEngineUseCase: InitializeBgeEngineUseCase,
        layeredPromptBuilder: com.example.gymbro.core.ai.prompt.builder.LayeredPromptBuilder,
        getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        userDataProvider: com.example.gymbro.domain.user.repository.UserDataProvider,
        memoryContextBuilder: com.example.gymbro.core.ai.prompt.memory.MemoryContextBuilder,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher dispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
        logger: com.example.gymbro.core.logging.Logger,
    ): SendMessageUseCase =
        SendMessageUseCase(
            aiCoachUseCase,
            chatSessionManagementUseCase,
            bgeEmbeddingService,
            hybridSearchRepository,
            initializeBgeEngineUseCase,
            layeredPromptBuilder,
            getCurrentUserIdUseCase,
            userDataProvider,
            memoryContextBuilder,
            dispatcher,
            logger,
        )

    /**
     * 提供基于AI对话生成训练计划用例
     */
    @Provides
    fun provideGenerateWorkoutFromAIChatUseCase(
        aiInteractionService: AiInteractionService,
        getWorkoutContextUseCase: com.example.gymbro.domain.usecase.workout.feedback.GetWorkoutContextUseCase,
        authRepository: AuthRepository,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher dispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
        logger: Logger,
    ): GenerateWorkoutFromAIChatUseCase =
        GenerateWorkoutFromAIChatUseCase(
            aiInteractionService,
            getWorkoutContextUseCase,
            authRepository,
            dispatcher,
            logger,
        )

    /**
     * 提供生成并保存概要用例
     */
    @Provides
    fun provideGenerateAndSaveSummaryUseCase(
        chatRepository: ChatRepository,
        chatSummaryService: com.example.gymbro.domain.service.coach.ChatSummaryService,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher dispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
        logger: Logger,
    ): GenerateAndSaveSummaryUseCase =
        GenerateAndSaveSummaryUseCase(
            chatRepository,
            chatSummaryService,
            dispatcher,
            logger,
        )

    /**
     * 提供获取快捷动作列表用例
     */
    @Provides
    fun provideGetQuickActionsUseCase(
        quickActionRepository: QuickActionRepository,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher dispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
        logger: Logger,
    ): GetQuickActionsUseCase =
        GetQuickActionsUseCase(
            quickActionRepository,
            dispatcher,
            logger,
        )

    /**
     * 提供根据ID获取快捷动作用例
     */
    @Provides
    fun provideGetQuickActionByIdUseCase(
        quickActionRepository: QuickActionRepository,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher dispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
        logger: Logger,
    ): GetQuickActionByIdUseCase =
        GetQuickActionByIdUseCase(
            quickActionRepository,
            dispatcher,
            logger,
        )

    /**
     * 提供获取快捷动作分类组用例
     */
    @Provides
    fun provideGetQuickActionCategoriesUseCase(
        quickActionRepository: QuickActionRepository,
    ): GetQuickActionCategoriesUseCase =
        GetQuickActionCategoriesUseCase(
            quickActionRepository,
        )

    /**
     * 🔥 提供初始化BGE引擎用例 - 确保AI功能可用
     */
    @Provides
    fun provideInitializeBgeEngineUseCase(
        bgeEngineManager: BgeEngineManager,
    ): InitializeBgeEngineUseCase = InitializeBgeEngineUseCase(bgeEngineManager)

    /**
     * 提供准备AI上下文用例 - RAG检索编排核心UseCase
     * Task2-CoachContext数据中心集成：修复AiCoachEffectHandler依赖注入问题
     */
    @Provides
    fun providePrepareAiContextUseCase(
        embeddingEngine: EmbeddingEngine,
        chatRepository: ChatRepository,
        templateRepository: TemplateRepository,
        userAggregateRepository: UserAggregateRepository,
        getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        promptConfig: PromptConfig,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher ioDispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
    ): PrepareAiContextUseCase =
        PrepareAiContextUseCase(
            embeddingEngine = embeddingEngine,
            chatRepository = chatRepository,
            templateRepository = templateRepository,
            userAggregateRepository = userAggregateRepository,
            getCurrentUserIdUseCase = getCurrentUserIdUseCase,
            promptConfig = promptConfig,
            ioDispatcher = ioDispatcher,
        )

    /**
     * 🔥 提供History上下文桥接用例 - BGE检索→智能摘要→高密度注入
     */
    @Provides
    fun provideHistoryContextBridgeUseCase(
        historyStateRepository: com.example.gymbro.domain.coach.repository.HistoryStateRepository,
        logger: Logger,
        tokenizerService: com.example.gymbro.core.ai.tokenizer.TokenizerService,
        @com.example.gymbro.core.di.qualifiers.IoDispatcher ioDispatcher:
            kotlinx.coroutines.CoroutineDispatcher,
    ): com.example.gymbro.domain.coach.usecase.HistoryContextBridgeUseCase =
        com.example.gymbro.domain.coach.usecase.HistoryContextBridgeUseCase(
            historyStateRepository = historyStateRepository,
            logger = logger,
            tokenizerService = tokenizerService,
            ioDispatcher = ioDispatcher,
        )
}
