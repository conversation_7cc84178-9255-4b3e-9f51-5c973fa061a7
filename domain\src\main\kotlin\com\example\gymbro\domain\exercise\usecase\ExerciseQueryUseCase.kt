package com.example.gymbro.domain.exercise.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.model.getDisplayName
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 统一的训练动作查询用例
 *
 * 合并了原有的GetExercisesUseCase、FilterExercisesUseCase、GetExercisesByIdsUseCase
 * 提供统一的查询接口，支持多种查询条件组合
 */
@Singleton
class ExerciseQueryUseCase
    @Inject
    constructor(
        private val exerciseRepository: ExerciseRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val injectedLogger: Logger,
    ) : ModernFlowUseCase<ExerciseQueryParams, List<Exercise>>(dispatcher, injectedLogger) {
        /**
         * 创建查询流
         * @param parameters 查询参数
         * @return 训练动作列表的Flow
         */
        override fun createFlow(parameters: ExerciseQueryParams): Flow<ModernResult<List<Exercise>>> {
            injectedLogger.d("执行动作查询: %s", parameters)

            return when (parameters) {
                is ExerciseQueryParams.All -> getAllExercises(parameters.userId)
                is ExerciseQueryParams.ByIds -> getExercisesByIds(parameters.ids, parameters.userId)
                is ExerciseQueryParams.Filtered -> getFilteredExercises(parameters)
            }
        }

        /**
         * 获取所有训练动作
         */
        private fun getAllExercises(userId: String): Flow<ModernResult<List<Exercise>>> =
            exerciseRepository.getExercises().map { result ->
                when (result) {
                    is ModernResult.Success -> {
                        val exercises = result.data
                        val filteredExercises =
                            if (userId.isNotBlank()) {
                                exercises.filter { !it.isCustom || it.createdByUserId == userId }
                            } else {
                                exercises
                            }
                        ModernResult.Success(filteredExercises)
                    }

                    is ModernResult.Error -> result
                    is ModernResult.Loading -> ModernResult.loading()
                }
            }

        /**
         * 根据ID列表获取训练动作
         */
        private fun getExercisesByIds(
            ids: List<String>,
            userId: String,
        ): Flow<ModernResult<List<Exercise>>> =
            exerciseRepository.getExercisesByIds(ids).map { result ->
                when (result) {
                    is ModernResult.Success -> {
                        val exercises = result.data
                        val filteredExercises =
                            if (userId.isNotBlank()) {
                                exercises.filter { !it.isCustom || it.createdByUserId == userId }
                            } else {
                                exercises
                            }
                        ModernResult.Success(filteredExercises)
                    }

                    is ModernResult.Error -> result
                    is ModernResult.Loading -> ModernResult.loading()
                }
            }

        /**
         * 获取过滤后的训练动作
         */
        private fun getFilteredExercises(
            params: ExerciseQueryParams.Filtered,
        ): Flow<ModernResult<List<Exercise>>> =
            exerciseRepository.getExercises().map { result ->
                when (result) {
                    is ModernResult.Success -> {
                        var filteredList = result.data

                        // 按用户ID过滤
                        if (params.userId.isNotBlank()) {
                            filteredList =
                                filteredList.filter { exercise ->
                                    !exercise.isCustom || exercise.createdByUserId == params.userId
                                }
                        }

                        // 按肌肉群过滤
                        if (params.muscleGroup != null) {
                            filteredList =
                                filteredList.filter { exercise ->
                                    exercise.targetMuscles.any { muscle ->
                                        muscle.getDisplayName().equals(params.muscleGroup, true)
                                    }
                                }
                        }

                        // 按器械过滤
                        if (params.equipment != null) {
                            filteredList =
                                filteredList.filter { exercise ->
                                    exercise.equipment.any { equip ->
                                        equip.getDisplayName().contains(params.equipment, true)
                                    }
                                }
                        }

                        // 按搜索关键词过滤
                        if (!params.searchQuery.isNullOrBlank()) {
                            filteredList = applySearchFilter(filteredList, params.searchQuery)
                        }

                        // 排序结果
                        val finalList =
                            if (params.searchQuery?.isNotBlank() == true) {
                                sortByRelevance(filteredList, params.searchQuery)
                            } else {
                                filteredList.sortedBy { it.name.toString() }
                            }

                        ModernResult.Success(finalList)
                    }

                    is ModernResult.Error -> result
                    is ModernResult.Loading -> ModernResult.loading()
                }
            }

        /**
         * 应用搜索过滤
         */
        private fun applySearchFilter(
            exercises: List<Exercise>,
            query: String,
        ): List<Exercise> {
            if (query.isBlank()) return exercises

            val keywords = query.trim().split(Regex("\\s+")).filter { it.isNotBlank() }
            if (keywords.isEmpty()) return exercises

            return exercises.filter { exercise ->
                keywords.all { keyword ->
                    nameContains(exercise, keyword) ||
                        muscleGroupContains(exercise, keyword) ||
                        equipmentContains(exercise, keyword) ||
                        descriptionContains(exercise, keyword) ||
                        instructionsContain(exercise, keyword)
                }
            }
        }

        /**
         * 按相关性排序
         */
        private fun sortByRelevance(
            exercises: List<Exercise>,
            query: String,
        ): List<Exercise> =
            exercises.sortedByDescending { exercise ->
                var score = 0
                val lowerQuery = query.lowercase()
                val exerciseName = exercise.name.toString().lowercase()

                // 名称完全匹配得分最高
                if (exerciseName == lowerQuery) {
                    score += 100
                } // 名称开头匹配
                else if (exerciseName.startsWith(lowerQuery)) {
                    score += 50
                } // 名称包含
                else if (exerciseName.contains(lowerQuery)) {
                    score += 25
                }

                // 肌肉群匹配
                if (exercise.targetMuscles.any { it.getDisplayName().lowercase().contains(lowerQuery) }) {
                    score += 10
                }

                // 器械匹配
                if (exercise.equipment.any { it.getDisplayName().lowercase().contains(lowerQuery) }) {
                    score += 5
                }

                score
            }

        // 辅助方法
        private fun nameContains(
            exercise: Exercise,
            keyword: String,
        ): Boolean = exercise.name.toString().contains(keyword, true)

        private fun muscleGroupContains(
            exercise: Exercise,
            keyword: String,
        ): Boolean = exercise.targetMuscles.any { it.getDisplayName().contains(keyword, true) }

        private fun equipmentContains(
            exercise: Exercise,
            keyword: String,
        ): Boolean = exercise.equipment.any { it.getDisplayName().contains(keyword, true) }

        private fun descriptionContains(
            exercise: Exercise,
            keyword: String,
        ): Boolean = exercise.description.toString().contains(keyword, true)

        private fun instructionsContain(
            exercise: Exercise,
            keyword: String,
        ): Boolean = exercise.instructions.any { it.toString().contains(keyword, true) }
    }

/**
 * 训练动作查询参数
 */
sealed class ExerciseQueryParams {
    /**
     * 获取所有训练动作
     */
    data class All(
        val userId: String = "",
    ) : ExerciseQueryParams()

    /**
     * 根据ID列表获取训练动作
     */
    data class ByIds(
        val ids: List<String>,
        val userId: String = "",
    ) : ExerciseQueryParams()

    /**
     * 过滤查询训练动作
     */
    data class Filtered(
        val muscleGroup: String? = null,
        val equipment: String? = null,
        val difficulty: String? = null,
        val searchQuery: String? = null,
        val userId: String = "",
    ) : ExerciseQueryParams()
}
