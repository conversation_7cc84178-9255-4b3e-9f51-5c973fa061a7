package com.example.gymbro.domain.coach.history

import android.util.Log
import com.example.gymbro.core.ui.text.UiText
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * History功能级别枚举
 *
 * 实现四级降级机制，确保即使在最差情况下仍有基础功能可用
 */
sealed class HistoryFeatureLevel {
    object Full : HistoryFeatureLevel() // BGE + RAG + AutoSave
    object Basic : HistoryFeatureLevel() // 仅AutoSave + 基础历史
    object Essential : HistoryFeatureLevel() // 手动保存
    object Disabled : HistoryFeatureLevel() // 功能关闭

    val displayName: String
        get() = when (this) {
            is Full -> "完整功能"
            is Basic -> "基础功能"
            is Essential -> "核心功能"
            is Disabled -> "功能禁用"
        }

    val description: String
        get() = when (this) {
            is Full -> "BGE向量化 + RAG检索 + 自动保存"
            is Basic -> "基础历史记录 + 自动保存"
            is Essential -> "仅手动保存功能"
            is Disabled -> "历史记录功能已禁用"
        }
}

/**
 * History功能管理器
 *
 * 基于 history汇报.md 的功能降级机制设计，实现：
 * - 四级功能降级：Full → Basic → Essential → Disabled
 * - 自动降级逻辑：BGE失败时自动降级到Basic
 * - 功能级别状态管理：实时监控和状态切换
 * - 用户友好：低内存设备可选择Essential模式
 * - 生产可靠：即使部分组件异常，核心功能仍可用
 */
@Singleton
class HistoryFeatureManager
    @Inject
    constructor() {

        private val _currentLevel = MutableStateFlow<HistoryFeatureLevel>(HistoryFeatureLevel.Full)
        val currentLevel: StateFlow<HistoryFeatureLevel> = _currentLevel.asStateFlow()

        private val _degradationHistory = MutableStateFlow<List<DegradationRecord>>(emptyList())
        val degradationHistory: StateFlow<List<DegradationRecord>> = _degradationHistory.asStateFlow()

        /**
         * 降级记录
         */
        data class DegradationRecord(
            val fromLevel: HistoryFeatureLevel,
            val toLevel: HistoryFeatureLevel,
            val reason: String,
            val timestamp: Long = System.currentTimeMillis(),
        )

        /**
         * 降级到指定功能级别
         *
         * @param level 目标功能级别
         * @param reason 降级原因
         */
        suspend fun degradeToLevel(level: HistoryFeatureLevel, reason: String) {
            val currentLevel = _currentLevel.value

            if (currentLevel == level) {
                Log.d("HistoryFeatureManager", "History功能级别已经是 ${level.displayName}，无需降级")
                return
            }

            Log.w(
                "HistoryFeatureManager",
                "History功能降级: ${currentLevel.displayName} → ${level.displayName}, 原因: $reason",
            )

            // 记录降级历史
            val record = DegradationRecord(
                fromLevel = currentLevel,
                toLevel = level,
                reason = reason,
            )
            _degradationHistory.value = _degradationHistory.value + record

            // 更新当前级别
            _currentLevel.value = level

            // 根据降级级别执行相应的清理操作
            when (level) {
                is HistoryFeatureLevel.Basic -> {
                    Log.i("HistoryFeatureManager", "切换到基础模式：禁用BGE和RAG功能，保留AutoSave")
                }

                is HistoryFeatureLevel.Essential -> {
                    Log.i("HistoryFeatureManager", "切换到核心模式：禁用所有高级功能，仅保留手动保存")
                }

                is HistoryFeatureLevel.Disabled -> {
                    Log.i("HistoryFeatureManager", "禁用History功能：所有历史记录功能已关闭")
                }

                is HistoryFeatureLevel.Full -> {
                    Log.i("HistoryFeatureManager", "恢复到完整模式：所有功能已启用")
                }
            }
        }

        /**
         * 尝试升级功能级别
         *
         * @param targetLevel 目标功能级别
         * @return 是否成功升级
         */
        suspend fun tryUpgradeToLevel(targetLevel: HistoryFeatureLevel): Boolean {
            val currentLevel = _currentLevel.value

            // 只能升级，不能降级
            if (!canUpgradeTo(currentLevel, targetLevel)) {
                Log.w("HistoryFeatureManager", "无法从 ${currentLevel.displayName} 升级到 ${targetLevel.displayName}")
                return false
            }

            Log.i(
                "HistoryFeatureManager",
                "History功能升级: ${currentLevel.displayName} → ${targetLevel.displayName}",
            )
            _currentLevel.value = targetLevel
            return true
        }

        /**
         * 重置到完整功能级别
         */
        suspend fun resetToFull() {
            Log.i("HistoryFeatureManager", "重置History功能到完整模式")
            _currentLevel.value = HistoryFeatureLevel.Full
            _degradationHistory.value = emptyList()
        }

        /**
         * 检查是否可以升级到目标级别
         */
        private fun canUpgradeTo(from: HistoryFeatureLevel, to: HistoryFeatureLevel): Boolean {
            val levelOrder = listOf(
                HistoryFeatureLevel.Disabled::class,
                HistoryFeatureLevel.Essential::class,
                HistoryFeatureLevel.Basic::class,
                HistoryFeatureLevel.Full::class,
            )

            val fromIndex = levelOrder.indexOfFirst { it.isInstance(from) }
            val toIndex = levelOrder.indexOfFirst { it.isInstance(to) }

            return toIndex > fromIndex
        }

        /**
         * 获取当前功能级别的能力描述
         */
        fun getCurrentCapabilities(): FeatureCapabilities {
            return when (_currentLevel.value) {
                is HistoryFeatureLevel.Full -> FeatureCapabilities(
                    bgeEmbedding = true,
                    ragRetrieval = true,
                    autoSave = true,
                    manualSave = true,
                    search = true,
                    export = true,
                )

                is HistoryFeatureLevel.Basic -> FeatureCapabilities(
                    bgeEmbedding = false,
                    ragRetrieval = false,
                    autoSave = true,
                    manualSave = true,
                    search = true,
                    export = true,
                )

                is HistoryFeatureLevel.Essential -> FeatureCapabilities(
                    bgeEmbedding = false,
                    ragRetrieval = false,
                    autoSave = false,
                    manualSave = true,
                    search = false,
                    export = false,
                )

                is HistoryFeatureLevel.Disabled -> FeatureCapabilities(
                    bgeEmbedding = false,
                    ragRetrieval = false,
                    autoSave = false,
                    manualSave = false,
                    search = false,
                    export = false,
                )
            }
        }

        /**
         * 功能能力描述
         */
        data class FeatureCapabilities(
            val bgeEmbedding: Boolean, // BGE嵌入向量生成
            val ragRetrieval: Boolean, // RAG上下文检索
            val autoSave: Boolean, // 自动保存
            val manualSave: Boolean, // 手动保存
            val search: Boolean, // 搜索功能
            val export: Boolean, // 导出功能
        )

        /**
         * 获取降级建议
         */
        fun getDegradationSuggestion(error: String): HistoryFeatureLevel {
            return when {
                error.contains("BGE", ignoreCase = true) ||
                    error.contains("embedding", ignoreCase = true) -> HistoryFeatureLevel.Basic

                error.contains("memory", ignoreCase = true) ||
                    error.contains("OutOfMemory", ignoreCase = true) -> HistoryFeatureLevel.Essential

                error.contains("storage", ignoreCase = true) ||
                    error.contains("database", ignoreCase = true) -> HistoryFeatureLevel.Essential

                else -> HistoryFeatureLevel.Basic
            }
        }

        /**
         * 获取用户友好的状态描述
         */
        fun getStatusMessage(): UiText {
            val level = _currentLevel.value
            return when (level) {
                is HistoryFeatureLevel.Full -> UiText.DynamicString("历史记录功能正常运行")
                is HistoryFeatureLevel.Basic -> UiText.DynamicString("历史记录运行在基础模式")
                is HistoryFeatureLevel.Essential -> UiText.DynamicString("历史记录运行在核心模式")
                is HistoryFeatureLevel.Disabled -> UiText.DynamicString("历史记录功能已禁用")
            }
        }
    }
