package com.example.gymbro.features.profile.internal.presentation.effect

// ❌ IndexProfileUseCase已删除 - RAG逻辑由Coach模块统一管理
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.theme.ThemeManager
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase
import com.example.gymbro.domain.profile.usecase.GetUserSettingsUseCase
import com.example.gymbro.domain.profile.usecase.UpdateUserProfileUseCase
import com.example.gymbro.domain.profile.usecase.UpdateUserSettingsUseCase
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.Intent
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.State
import com.example.gymbro.features.profile.internal.presentation.model.ProfileUiModel
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

@Singleton
class ProfileEffectHandler
    @Inject
    constructor(
        private val getUserProfileUseCase: GetUserProfileUseCase,
        private val updateUserProfileUseCase: UpdateUserProfileUseCase,
        private val getUserSettingsUseCase: GetUserSettingsUseCase,
        private val updateUserSettingsUseCase: UpdateUserSettingsUseCase,
        private val themeManager: ThemeManager,
        private val logger: Logger,
    ) {
        /**
         * 🔥 统一的Effect处理入口 - 遵循MVI黄金标准
         *
         * @param effect 需要处理的副作用
         * @param dispatch 分发新意图的函数
         */
        internal suspend fun handleEffect(
            effect: ProfileContract.Effect,
            dispatch: (Intent) -> Unit,
        ) {
            when (effect) {
                is ProfileContract.Effect.SaveUserProfile -> {
                    handleSaveUserProfileEffect(effect.profile, dispatch)
                }

                is ProfileContract.Effect.ShowError -> {
                    // UI Effect，通常由ViewModel直接处理
                    logger.d("ProfileEffectHandler", "UI Effect: ${effect.message}")
                }

                is ProfileContract.Effect.ShowToast -> {
                    // UI Effect，通常由ViewModel直接处理
                    logger.d("ProfileEffectHandler", "UI Effect: ${effect.message}")
                }

                is ProfileContract.Effect.ShowSnackbar -> {
                    // UI Effect，通常由ViewModel直接处理
                    logger.d("ProfileEffectHandler", "UI Effect: ${effect.message}")
                }

                else -> {
                    logger.d("ProfileEffectHandler", "未处理的Effect: ${effect::class.simpleName}")
                }
            }
        }

        /**
         * 处理SaveUserProfile Effect的具体实现
         */
        private suspend fun handleSaveUserProfileEffect(
            profile: com.example.gymbro.features.profile.internal.presentation.model.ProfileUiModel,
            dispatch: (Intent) -> Unit,
        ) {
            try {
                logger.d("ProfileEffectHandler", "开始保存用户资料 - ${profile.displayName}")

                // 将UI模型转换为领域模型
                val domainProfile = profile.toDomain()

                // 调用UseCase
                val result = updateUserProfileUseCase(domainProfile)
                when (result) {
                    is com.example.gymbro.core.error.types.ModernResult.Success -> {
                        logger.d("ProfileEffectHandler", "用户资料保存成功")
                        dispatch(Intent.ProfileSaveSuccess)
                        dispatch(Intent.LoadUserProfile) // 重新加载数据
                    }

                    is com.example.gymbro.core.error.types.ModernResult.Error -> {
                        logger.e("ProfileEffectHandler", "保存用户资料失败: ${result.error}")
                        dispatch(Intent.ProfileSaveError(UiText.DynamicString("保存用户资料失败")))
                    }

                    is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                        // 保持保存状态
                        logger.d("ProfileEffectHandler", "用户资料保存中...")
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "保存用户资料时发生异常")
                dispatch(Intent.ProfileSaveError(UiText.DynamicString("保存用户资料时发生异常")))
            }
        }

        /**
         * 处理副作用 (Legacy方法，保持兼容性)
         *
         * @param intent 触发副作用的意图
         * @param state 当前状态
         * @param scope 协程作用域
         * @param dispatch 分发新意图的函数
         */
        internal fun handle(
            intent: Intent,
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            when (intent) {
                // === 数据加载相关 ===
                is Intent.LoadUserProfile -> handleLoadUserProfile(scope, dispatch)
                is Intent.RetryLoad -> handleLoadUserProfile(scope, dispatch)

                // === 保存相关 ===
                // 🔥 移除手动保存，改为自动保存

                // === 字段更新相关 (这些需要副作用处理) ===
                is Intent.UpdateDisplayName -> handleFieldUpdate(scope, dispatch) { /* 可选：验证逻辑 */ }
                is Intent.UpdateUsername -> handleFieldUpdate(scope, dispatch) { /* 可选：用户名唯一性检查 */ }
                is Intent.UpdateBio -> handleFieldUpdate(scope, dispatch) { /* 可选：内容过滤 */ }
                is Intent.UpdateEmail -> handleFieldUpdate(scope, dispatch) { /* 可选：邮箱格式验证 */ }
                is Intent.UpdatePhoneNumber -> handleFieldUpdate(scope, dispatch) { /* 可选：电话格式验证 */ }
                is Intent.UpdateHeight -> handleFieldUpdate(scope, dispatch) { /* 可选：范围验证 */ }
                is Intent.UpdateWeight -> handleFieldUpdate(scope, dispatch) { /* 可选：范围验证 */ }
                is Intent.UpdateGender -> handleFieldUpdate(scope, dispatch) { /* 直接更新 */ }
                is Intent.UpdateFitnessLevel -> handleFieldUpdate(scope, dispatch) { /* 直接更新 */ }
                is Intent.UpdateFitnessGoals -> handleFieldUpdate(scope, dispatch) { /* 可选：目标兼容性检查 */ }
                is Intent.UpdateWorkoutDays -> handleFieldUpdate(scope, dispatch) { /* 可选：天数合理性检查 */ }

                // === 对话框确认操作 (需要副作用处理) ===
                is Intent.ConfirmDisplayName -> handleConfirmDisplayName(state, scope, dispatch)
                is Intent.ConfirmUsername -> handleConfirmUsername(state, scope, dispatch)
                is Intent.ConfirmEmail -> handleConfirmEmail(state, scope, dispatch)
                is Intent.ConfirmHeight -> handleConfirmHeight(state, scope, dispatch)
                is Intent.ConfirmWeight -> handleConfirmWeight(state, scope, dispatch)
                is Intent.ConfirmGender -> handleConfirmGender(state, scope, dispatch)
                is Intent.ConfirmFitnessLevel -> handleConfirmFitnessLevel(state, scope, dispatch)
                is Intent.ConfirmGoal -> handleConfirmGoals(state, scope, dispatch)
                is Intent.ConfirmWorkoutDays -> handleConfirmWorkoutDays(state, scope, dispatch)

                // === 通知设置相关 ===
                is Intent.UpdateWorkoutReminder -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateFriendsActivity -> handleUpdateUserSettings(state, scope, dispatch)

                // === 新增：详细通知设置相关 ===
                // 倒计时通知设置
                is Intent.UpdateRestTimerNotification -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateRestTimerSound -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateRestTimerVibration -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateRestTimerLockScreen -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateRestTimerAutoStart -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateRestTimerInterval -> handleUpdateUserSettings(state, scope, dispatch)

                // 训练提醒设置
                is Intent.UpdateWorkoutReminderEnabled -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateDailyReminder -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateReminderTime -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateWeeklyPlanReminder -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateRestDayReminder -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateMotivationalMessages -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateWorkoutReminderSound -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateWorkoutReminderVibration -> handleUpdateUserSettings(state, scope, dispatch)

                // 日历同步设置
                is Intent.UpdateCalendarSyncEnabled -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateSystemCalendarSync -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdatePlanUpdateNotification -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateScheduleChangeNotification -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateUpcomingWorkoutReminder -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateReminderMinutesBefore -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateCalendarSyncSound -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateCalendarSyncVibration -> handleUpdateUserSettings(state, scope, dispatch)

                // 全局通知设置
                is Intent.UpdateGlobalVibration -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateGlobalNotificationTime -> handleUpdateUserSettings(state, scope, dispatch)

                // 状态栏通知设置
                is Intent.UpdateStatusBarNotificationEnabled -> handleUpdateUserSettings(
                    state,
                    scope,
                    dispatch,
                )

                is Intent.UpdateStatusBarOngoingWorkout -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateStatusBarRestTimer -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateStatusBarProgress -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateStatusBarPriority -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateStatusBarAutoHide -> handleUpdateUserSettings(state, scope, dispatch)
                is Intent.UpdateStatusBarAutoHideDelay -> handleUpdateUserSettings(state, scope, dispatch)

                // === 主题设置相关 ===
                is Intent.UpdateThemeStyle -> handleUpdateThemeStyle(intent.style, scope, dispatch)
                is Intent.UpdateColorMode -> handleUpdateColorMode(intent.mode, scope, dispatch)
                is Intent.UpdateDynamicColor -> handleUpdateDynamicColor(intent.enabled, scope, dispatch)

                // === AI上下文相关 (预留) ===
                is Intent.GenerateAiContext -> handleGenerateAiContext(state, scope, dispatch)
                is Intent.RefreshAiContext -> handleGenerateAiContext(state, scope, dispatch)

                // === 只需UI状态更新的Intent (由Reducer处理，不需要副作用) ===
                is Intent.EnterEditMode,
                is Intent.ExitEditMode,
                is Intent.ShowDisplayNameDialog,
                is Intent.ShowUsernameDialog,
                is Intent.ShowEmailDialog,
                is Intent.ShowGenderDialog,
                is Intent.ShowHeightDialog,
                is Intent.ShowWeightDialog,
                is Intent.ShowFitnessLevelDialog,
                is Intent.ShowGoalsDialog,
                is Intent.ShowWorkoutDaysDialog,
                is Intent.ShowAvatarEditDialog,
                is Intent.DismissDialog,
                is Intent.UpdateTempDisplayName,
                is Intent.UpdateTempUsername,
                is Intent.UpdateTempEmail,
                is Intent.UpdateTempHeight,
                is Intent.UpdateTempWeight,
                is Intent.UpdateTempGender,
                is Intent.UpdateTempFitnessLevel,
                is Intent.UpdateTempGoal,
                is Intent.UpdateTempWorkoutDays,
                is Intent.ClearError,
                is Intent.ClearSaveSuccess,
                is Intent.ProfileLoaded,
                is Intent.ProfileLoadError,
                is Intent.UserSettingsLoaded,
                is Intent.ProfileSaveSuccess,
                is Intent.ProfileSaveError,
                is Intent.AiContextGenerated,
                is Intent.AiContextError,
                is Intent.ThemeConfigLoaded,
                is Intent.ThemeStyleChangeSuccess,
                is Intent.ThemeStyleChangeError,
                is Intent.ColorModeChangeSuccess,
                is Intent.ColorModeChangeError,
                is Intent.DynamicColorChangeSuccess,
                is Intent.DynamicColorChangeError,
                -> {
                    // 这些Intent只需要更新UI状态，由Reducer处理
                    // EffectHandler不需要处理副作用
                }
            }
        }

        // === 数据加载处理 ===

        private fun handleLoadUserProfile(
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "开始加载用户资料")

                    // 先加载用户资料
                    val profileResult = getUserProfileUseCase()
                    when (profileResult) {
                        is ModernResult.Success -> {
                            val userProfile = profileResult.data
                            if (userProfile != null) {
                                logger.d("ProfileEffectHandler", "用户资料加载成功")
                                dispatch(Intent.ProfileLoaded(userProfile))

                                // 然后使用黄金范式加载用户设置
                                loadUserSettings(scope, dispatch)

                                // 加载当前主题配置
                                loadCurrentThemeConfig(scope, dispatch)
                            } else {
                                logger.w("ProfileEffectHandler", "用户资料为空")
                                dispatch(Intent.ProfileLoadError(UiText.DynamicString("用户资料不存在")))
                            }
                        }

                        is ModernResult.Error -> {
                            logger.e("ProfileEffectHandler", "加载用户资料失败: ${profileResult.error}")
                            dispatch(Intent.ProfileLoadError(UiText.DynamicString("加载用户资料失败")))
                        }

                        is ModernResult.Loading -> {
                            // 保持加载状态
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "加载用户资料时发生异常")
                    dispatch(Intent.ProfileLoadError(UiText.DynamicString("加载用户资料时发生异常")))
                }
            }
        }

        private fun loadUserSettings(
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            // 使用黄金范式：onEach/catch/launchIn
            getUserSettingsUseCase(Unit)
                .onEach { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val userSettings = result.data
                            if (userSettings != null) {
                                logger.d("ProfileEffectHandler", "用户设置加载成功")
                                dispatch(Intent.UserSettingsLoaded(userSettings))
                            } else {
                                logger.d("ProfileEffectHandler", "用户设置为空，使用默认设置")
                            }
                        }

                        is ModernResult.Error -> {
                            logger.w("ProfileEffectHandler", "加载用户设置失败: ${result.error}")
                            // 设置加载失败不影响主流程
                        }

                        is ModernResult.Loading -> {
                            logger.d("ProfileEffectHandler", "用户设置加载中")
                        }
                    }
                }.catch { exception ->
                    logger.e("ProfileEffectHandler", "加载用户设置时发生异常", exception)
                    // 设置加载异常不影响主流程
                }.launchIn(scope)
        }

        // === 主题配置加载处理 ===

        private fun loadCurrentThemeConfig(
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "加载当前主题配置")

                    // 从ThemeManager获取当前主题配置
                    val currentConfig = themeManager.getCurrentThemeConfig()

                    // 分别设置到State中（通过专门的Intent）
                    dispatch(
                        Intent.ThemeConfigLoaded(
                            themeStyle = currentConfig.themeStyle,
                            colorMode = currentConfig.colorMode,
                            dynamicColorEnabled = currentConfig.useDynamicColor,
                        ),
                    )

                    logger.d("ProfileEffectHandler", "主题配置加载成功: $currentConfig")
                } catch (e: Exception) {
                    logger.e(e, "加载主题配置时发生异常")
                    // 主题配置加载失败不影响主流程，使用默认配置
                }
            }
        }

        // === 保存处理 ===

        private fun handleSaveProfile(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            val editableProfile = state.editableProfile
            if (editableProfile == null) {
                dispatch(Intent.ProfileSaveError(UiText.DynamicString("没有可保存的数据")))
                return
            }

            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "开始保存用户资料")

                    // 将UI模型转换为领域模型
                    val domainProfile = editableProfile.toDomain()

                    val result = updateUserProfileUseCase(domainProfile)
                    when (result) {
                        is ModernResult.Success -> {
                            logger.d("ProfileEffectHandler", "用户资料保存成功")
                            dispatch(Intent.ProfileSaveSuccess)

                            // 保存成功后重新加载数据以确保同步
                            dispatch(Intent.LoadUserProfile)

                            // ✅ RAG索引已移至Coach模块，Profile模块专注于数据CRUD
                        }

                        is ModernResult.Error -> {
                            logger.e("ProfileEffectHandler", "保存用户资料失败: ${result.error}")
                            dispatch(Intent.ProfileSaveError(UiText.DynamicString("保存用户资料失败")))
                        }

                        is ModernResult.Loading -> {
                            // 保持保存状态
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "保存用户资料时发生异常")
                    dispatch(Intent.ProfileSaveError(UiText.DynamicString("保存用户资料时发生异常")))
                }
            }
        }

        // === 主题设置处理 ===

        private fun handleUpdateThemeStyle(
            style: com.example.gymbro.core.theme.ThemeStyle,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "更新主题风格: $style")

                    // 调用ThemeManager更新主题风格
                    themeManager.setThemeStyle(style)

                    // 分发成功效果
                    dispatch(Intent.ThemeStyleChangeSuccess(style))

                    logger.i("ProfileEffectHandler", "主题风格更新成功: $style")
                } catch (e: Exception) {
                    logger.e(e, "更新主题风格时发生异常")
                    dispatch(Intent.ThemeStyleChangeError(UiText.DynamicString("主题风格更新失败")))
                }
            }
        }

        private fun handleUpdateColorMode(
            mode: com.example.gymbro.core.theme.ColorMode,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "更新颜色模式: $mode")

                    // 调用ThemeManager更新颜色模式
                    themeManager.setColorMode(mode)

                    // 分发成功效果
                    dispatch(Intent.ColorModeChangeSuccess(mode))

                    logger.i("ProfileEffectHandler", "颜色模式更新成功: $mode")
                } catch (e: Exception) {
                    logger.e(e, "更新颜色模式时发生异常")
                    dispatch(Intent.ColorModeChangeError(UiText.DynamicString("颜色模式更新失败")))
                }
            }
        }

        private fun handleUpdateDynamicColor(
            enabled: Boolean,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "更新动态颜色: $enabled")

                    // 调用ThemeManager更新动态颜色
                    themeManager.setDynamicColor(enabled)

                    // 分发成功效果
                    dispatch(Intent.DynamicColorChangeSuccess(enabled))

                    logger.i("ProfileEffectHandler", "动态颜色更新成功: $enabled")
                } catch (e: Exception) {
                    logger.e(e, "更新动态颜色时发生异常")
                    dispatch(Intent.DynamicColorChangeError(UiText.DynamicString("动态颜色更新失败")))
                }
            }
        }

        // === 用户设置处理 ===

        private fun handleUpdateUserSettings(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            val userSettings = state.userSettings
            if (userSettings == null) {
                logger.w("ProfileEffectHandler", "用户设置为空，无法保存")
                return
            }

            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "开始保存用户设置")

                    val result = updateUserSettingsUseCase(userSettings)
                    when (result) {
                        is ModernResult.Success -> {
                            logger.d("ProfileEffectHandler", "用户设置保存成功")
                            // 设置保存成功，无需特殊处理
                        }

                        is ModernResult.Error -> {
                            logger.e("ProfileEffectHandler", "保存用户设置失败: ${result.error}")
                            // 设置保存失败，可以显示错误但不影响主流程
                        }

                        is ModernResult.Loading -> {
                            // 保持保存状态
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "保存用户设置时发生异常")
                }
            }
        }

        // === 字段更新处理 ===

        private fun handleFieldUpdate(
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
            validation: suspend () -> Unit = {},
        ) {
            scope.launch {
                try {
                    // 执行可选的验证逻辑
                    validation()

                    // 字段更新本身由Reducer处理，这里只处理验证等副作用
                    logger.d("ProfileEffectHandler", "字段更新验证完成")
                } catch (e: Exception) {
                    logger.e(e, "字段更新验证失败")
                    // 可以分发错误Intent
                }
            }
        }

        // === 对话框确认处理 ===

        private fun handleConfirmDisplayName(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempValue = state.tempDisplayName
                    if (!tempValue.isNullOrBlank()) {
                        logger.d("ProfileEffectHandler", "确认显示名称: $tempValue")
                        dispatch(Intent.UpdateDisplayName(tempValue))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认显示名称时发生异常")
                }
            }
        }

        private fun handleConfirmUsername(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempValue = state.tempUsername
                    if (!tempValue.isNullOrBlank()) {
                        logger.d("ProfileEffectHandler", "确认用户名: $tempValue")
                        dispatch(Intent.UpdateUsername(tempValue))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认用户名时发生异常")
                }
            }
        }

        private fun handleConfirmEmail(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempValue = state.tempEmail
                    if (!tempValue.isNullOrBlank()) {
                        logger.d("ProfileEffectHandler", "确认邮箱: $tempValue")
                        dispatch(Intent.UpdateEmail(tempValue))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认邮箱时发生异常")
                }
            }
        }

        private fun handleConfirmHeight(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempValue = state.tempHeight?.toFloatOrNull()
                    if (tempValue != null && tempValue > 0) {
                        logger.d("ProfileEffectHandler", "确认身高: $tempValue")
                        dispatch(Intent.UpdateHeight(tempValue))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认身高时发生异常")
                }
            }
        }

        private fun handleConfirmWeight(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempValue = state.tempWeight?.toFloatOrNull()
                    if (tempValue != null && tempValue > 0) {
                        logger.d("ProfileEffectHandler", "确认体重: $tempValue")
                        dispatch(Intent.UpdateWeight(tempValue))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认体重时发生异常")
                }
            }
        }

        private fun handleConfirmGender(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempValue = state.tempGender
                    if (tempValue != null) {
                        logger.d("ProfileEffectHandler", "确认性别: $tempValue")
                        dispatch(Intent.UpdateGender(tempValue))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认性别时发生异常")
                }
            }
        }

        private fun handleConfirmFitnessLevel(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempValue = state.tempFitnessLevel
                    if (tempValue != null) {
                        logger.d("ProfileEffectHandler", "确认健身水平: $tempValue")
                        dispatch(Intent.UpdateFitnessLevel(tempValue))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认健身水平时发生异常")
                }
            }
        }

        private fun handleConfirmGoals(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempGoal = state.tempGoal
                    if (tempGoal != null) {
                        val currentGoals = state.editableProfile?.fitnessGoals?.toMutableList() ?: mutableListOf()

                        // 切换目标状态
                        if (currentGoals.contains(tempGoal)) {
                            currentGoals.remove(tempGoal)
                        } else {
                            currentGoals.add(tempGoal)
                        }

                        logger.d("ProfileEffectHandler", "确认健身目标: $currentGoals")
                        dispatch(Intent.UpdateFitnessGoals(currentGoals.toImmutableList()))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认健身目标时发生异常")
                }
            }
        }

        private fun handleConfirmWorkoutDays(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val tempDays = state.tempWorkoutDays
                    if (tempDays.isNotEmpty()) {
                        logger.d("ProfileEffectHandler", "确认训练日: $tempDays")
                        dispatch(Intent.UpdateWorkoutDays(tempDays))
                        dispatch(Intent.DismissDialog)
                    }
                } catch (e: Exception) {
                    logger.e(e, "确认训练日时发生异常")
                }
            }
        }

        // === AI上下文处理 (预留) ===

        private fun handleGenerateAiContext(
            state: State,
            scope: CoroutineScope,
            dispatch: (Intent) -> Unit,
        ) {
            val editableProfile = state.editableProfile
            if (editableProfile == null) {
                dispatch(Intent.AiContextError(UiText.DynamicString("无法生成AI上下文：用户资料不存在")))
                return
            }

            scope.launch {
                try {
                    logger.d("ProfileEffectHandler", "开始生成AI上下文")

                    // 将ProfileUiModel转换为UserAiContext
                    val aiContext =
                        ProfileContract.UserAiContext(
                            userId = editableProfile.id,
                            displayName = editableProfile.displayName,
                            fitnessLevel = editableProfile.fitnessLevel.name,
                            height = editableProfile.height.toFloat(),
                            weight = editableProfile.weight.toFloat(),
                            gender = editableProfile.gender.name,
                            fitnessGoals = editableProfile.fitnessGoals.map { it.name }.toImmutableList(),
                            workoutDays = editableProfile.workoutDays.map { it.name }.toImmutableList(),
                            allowPartnerMatching = editableProfile.allowPartnerMatching,
                            bio = editableProfile.bio,
                            totalActivityCount = editableProfile.totalActivityCount,
                            weeklyActiveMinutes = editableProfile.weeklyActiveMinutes,
                        )

                    logger.d("ProfileEffectHandler", "AI上下文生成成功: ${aiContext.summary}")
                    dispatch(Intent.AiContextGenerated(aiContext))
                } catch (e: Exception) {
                    logger.e(e, "生成AI上下文时发生异常")
                    dispatch(Intent.AiContextError(UiText.DynamicString("生成AI上下文失败")))
                }
            }
        }
    }

// === 扩展函数 ===

internal fun ProfileUiModel.toDomain(): UserProfile =
    UserProfile(
        userId = this.id,
        username = this.username,
        displayName = this.displayName,
        email = this.email,
        phoneNumber = this.phoneNumber,
        avatarUrl = this.photoUrl,
        bio = this.bio,
        gender = this.gender,
        height = if (this.height > 0) this.height else null,
        weight = if (this.weight > 0) this.weight else null,
        fitnessLevel = this.fitnessLevel,
        fitnessGoals = this.fitnessGoals,
        workoutDays = this.workoutDays,
        allowPartnerMatching = this.allowPartnerMatching,
        totalActivityCount = this.totalActivityCount,
        weeklyActiveMinutes = this.weeklyActiveMinutes,
        likesReceived = this.likesReceived,
        isAnonymous = this.isAnonymous,
    )
