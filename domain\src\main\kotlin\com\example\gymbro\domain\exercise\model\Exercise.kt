package com.example.gymbro.domain.exercise.model

import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.profile.model.user.enums.Gender
import com.example.gymbro.domain.workout.model.GenderNote
import com.example.gymbro.domain.workout.model.MediaAsset
import com.example.gymbro.shared.models.exercise.Equipment as SharedEquipment
import com.example.gymbro.shared.models.exercise.MuscleGroup as SharedMuscleGroup
import kotlinx.serialization.Serializable

/**
 * 运动练习领域模型
 * 表示单个训练动作/练习，包含业务逻辑和UI表现
 *
 * 注意：此模型专注于业务逻辑，数据传输使用 shared-models 中的 ExerciseDto
 */
@Serializable
data class Exercise(
    val id: String,
    val name: UiText,
    val muscleGroup: SharedMuscleGroup,
    val equipment: List<SharedEquipment>,
    val description: UiText,
    val imageUrl: String? = null,
    val videoUrl: String? = null,
    val defaultSets: Int = 3,
    val defaultReps: Int = 10,
    val defaultWeight: Float = 0f,
    val steps: List<UiText> = emptyList(),
    val tips: List<UiText> = emptyList(),
    val userId: String? = null,
    val isCustom: Boolean = false,
    val isFavorite: Boolean = false,
    val difficultyLevel: Int = 1,
    val calories: Int = 0,
    val targetMuscles: List<SharedMuscleGroup> = emptyList(),
    val instructions: List<UiText> = emptyList(),
    val genderNotes: List<GenderNote> = emptyList(),
    val mediaAssets: List<MediaAsset> = emptyList(),
    val exerciseMuscles: List<ExerciseMuscle> = emptyList(),
    val exerciseEquipments: List<ExerciseEquipment> = emptyList(),
    val createdAt: Long? = null,
    val updatedAt: Long? = null,
    val createdByUserId: String = "",
) {
    /**
     * 获取主要目标肌肉的格式化字符串
     * @return 格式化的肌肉列表
     */
    fun getPrimaryMusclesFormatted(): UiText =
        if (targetMuscles.isEmpty()) {
            UiText.DynamicString("Not specified")
        } else {
            UiText.DynamicString(targetMuscles.joinToString(", ") { muscle -> muscle.getDisplayName() })
        }

    /**
     * 检查该动作是否需要器材
     * @return 是否需要器材
     */
    fun requiresEquipment(): Boolean = equipment.isNotEmpty() && !equipment.contains(SharedEquipment.NONE)

    /**
     * 获取指定性别的指导信息
     * @param gender 目标性别
     * @return 该性别的指导信息列表
     */
    fun getGenderNotes(
        gender: Gender,
    ): List<GenderNote> = genderNotes.filter { it.gender == gender || it.gender == Gender.OTHER }

    /**
     * 获取图片媒体资源
     * @return 图片资源列表
     */
    fun getImageAssets(): List<MediaAsset> = mediaAssets.filter { it.isImage }

    /**
     * 获取视频媒体资源
     * @return 视频资源列表
     */
    fun getVideoAssets(): List<MediaAsset> = mediaAssets.filter { it.isVideo }

    /**
     * 获取主要展示图片
     * @return 第一个图片资源的URL，如果没有则返回imageUrl
     */
    fun getPrimaryImageUrl(): String? = getImageAssets().firstOrNull()?.url ?: imageUrl

    /**
     * 获取主要展示视频
     * @return 第一个视频资源的URL，如果没有则返回videoUrl
     */
    fun getPrimaryVideoUrl(): String? = getVideoAssets().firstOrNull()?.url ?: videoUrl

    /**
     * 获取主要肌群
     * @return 主要肌群列表
     */
    fun getPrimaryMuscles(): List<SharedMuscleGroup> = exerciseMuscles.filter {
        it.isPrimary
    }.map { it.muscleGroup }

    /**
     * 获取辅助肌群
     * @return 辅助肌群列表
     */
    fun getSecondaryMuscles(): List<SharedMuscleGroup> = exerciseMuscles.filter {
        !it.isPrimary
    }.map { it.muscleGroup }

    /**
     * 获取所有相关肌群（按激活程度排序）
     * @return 按激活程度降序排列的肌群列表
     */
    fun getAllMusclesByActivation(): List<SharedMuscleGroup> = exerciseMuscles.sortedByDescending {
        it.activationLevel
    }.map { it.muscleGroup }

    /**
     * 获取必需器械
     * @return 必需器械列表
     */
    fun getRequiredEquipments(): List<SharedEquipment> = exerciseEquipments.filter {
        it.isRequired
    }.map { it.equipment }

    /**
     * 获取可选器械
     * @return 可选器械列表
     */
    fun getOptionalEquipments(): List<SharedEquipment> = exerciseEquipments.filter {
        !it.isRequired
    }.map { it.equipment }

    /**
     * 获取所有器械（按重要性排序）
     * @return 按重要性降序排列的器械列表
     */
    fun getAllEquipmentsByImportance(): List<SharedEquipment> = exerciseEquipments.sortedByDescending {
        it.importance.level
    }.map { it.equipment }

    /**
     * 检查是否激活指定肌群
     * @param muscleGroup 目标肌群
     * @return 是否激活该肌群
     */
    fun activatesMuscle(
        muscleGroup: SharedMuscleGroup,
    ): Boolean = exerciseMuscles.any { it.muscleGroup == muscleGroup }

    /**
     * 检查是否需要指定器械
     * @param equipment 目标器械
     * @return 是否需要该器械
     */
    fun requiresEquipment(
        equipment: SharedEquipment,
    ): Boolean = exerciseEquipments.any { it.equipment == equipment && it.isRequired }

    /**
     * 获取肌群的激活程度
     * @param muscleGroup 目标肌群
     * @return 激活程度（1-5），如果不激活则返回0
     */
    fun getMuscleActivationLevel(
        muscleGroup: SharedMuscleGroup,
    ): Int = exerciseMuscles.find { it.muscleGroup == muscleGroup }?.activationLevel ?: 0

    /**
     * 计算动作的复合程度 - 激活的肌群数量
     * @return 激活的肌群数量
     */
    fun getComplexityScore(): Int = exerciseMuscles.size

    /**
     * 获取动作的主要类别
     * @return 根据主要肌群判断的训练类别
     */
    fun getExerciseCategory(): ExerciseCategory = when {
        muscleGroup in listOf(
            SharedMuscleGroup.CHEST,
            SharedMuscleGroup.BACK,
            SharedMuscleGroup.SHOULDERS,
            SharedMuscleGroup.ARMS,
            SharedMuscleGroup.BICEPS,
            SharedMuscleGroup.TRICEPS,
            SharedMuscleGroup.FOREARMS,
            SharedMuscleGroup.TRAPEZIUS,
        ) -> ExerciseCategory.UPPER_BODY

        muscleGroup in listOf(
            SharedMuscleGroup.LEGS,
            SharedMuscleGroup.QUADRICEPS,
            SharedMuscleGroup.HAMSTRINGS,
            SharedMuscleGroup.GLUTES,
            SharedMuscleGroup.CALVES,
        ) -> ExerciseCategory.LOWER_BODY

        muscleGroup in listOf(
            SharedMuscleGroup.ABS,
            SharedMuscleGroup.CORE,
            SharedMuscleGroup.OBLIQUES,
            SharedMuscleGroup.LOWER_BACK,
        ) -> ExerciseCategory.CORE

        muscleGroup == SharedMuscleGroup.FULL_BODY -> ExerciseCategory.FULL_BODY
        muscleGroup == SharedMuscleGroup.CARDIO -> ExerciseCategory.CARDIO
        else -> ExerciseCategory.OTHER
    }

    /**
     * 判断是否为复合动作
     * @return 是否为复合动作（激活多个肌群）
     */
    fun isCompoundExercise(): Boolean = exerciseMuscles.filter { it.isPrimary }.size > 1

    /**
     * 判断是否为孤立动作
     * @return 是否为孤立动作（主要激活单一肌群）
     */
    fun isIsolationExercise(): Boolean = !isCompoundExercise()

    /**
     * 获取预估训练时长（分钟）
     * @param sets 组数
     * @param restTimeSeconds 组间休息时间（秒）
     * @return 预估总时长（分钟）
     */
    fun getEstimatedDuration(sets: Int = defaultSets, restTimeSeconds: Int = 60): Int {
        val exerciseTime = sets * 30 // 每组约30秒
        val restTime = (sets - 1) * restTimeSeconds
        return (exerciseTime + restTime) / 60
    }

    companion object {
        /**
         * 创建一个空的动作对象
         * @param id 动作ID
         * @return 空的动作对象
         */
        fun empty(id: String): Exercise =
            Exercise(
                id = id,
                name = UiText.DynamicString("Untitled Exercise"),
                muscleGroup = SharedMuscleGroup.OTHER,
                equipment = emptyList(),
                description = UiText.DynamicString("No description"),
                imageUrl = null,
                videoUrl = null,
                defaultSets = 3,
                defaultReps = 10,
                defaultWeight = 0f,
                steps = emptyList(),
                tips = emptyList(),
                userId = null,
                isCustom = false,
                isFavorite = false,
                difficultyLevel = 1,
                calories = 0,
                targetMuscles = emptyList(),
                instructions = emptyList(),
                genderNotes = emptyList(),
                mediaAssets = emptyList(),
                exerciseMuscles = emptyList(),
                exerciseEquipments = emptyList(),
                createdAt = null,
                updatedAt = null,
                createdByUserId = "",
            )

        /**
         * 根据肌群创建基础动作模板
         * @param muscleGroup 目标肌群
         * @param name 动作名称
         * @return 基础动作模板
         */
        fun createTemplate(muscleGroup: SharedMuscleGroup, name: String): Exercise =
            Exercise(
                id = "",
                name = UiText.DynamicString(name),
                muscleGroup = muscleGroup,
                equipment = listOf(SharedEquipment.NONE),
                description = UiText.DynamicString(""),
                targetMuscles = listOf(muscleGroup),
                isCustom = true,
            )
    }
}

/**
 * 训练动作类别枚举
 * 用于对训练动作进行分类
 */
@Serializable
enum class ExerciseCategory(
    val displayName: String,
) {
    UPPER_BODY("上肢训练"),
    LOWER_BODY("下肢训练"),
    CORE("核心训练"),
    FULL_BODY("全身训练"),
    CARDIO("有氧训练"),
    FLEXIBILITY("柔韧性训练"),
    OTHER("其他"),
}

/**
 * SharedMuscleGroup 扩展函数
 * 为 shared-models 中的枚举添加显示名称
 */
fun SharedMuscleGroup.getDisplayName(): String = when (this) {
    SharedMuscleGroup.CHEST -> "胸肌"
    SharedMuscleGroup.BACK -> "背肌"
    SharedMuscleGroup.SHOULDERS -> "肩膀"
    SharedMuscleGroup.ARMS -> "手臂"
    SharedMuscleGroup.BICEPS -> "二头肌"
    SharedMuscleGroup.TRICEPS -> "三头肌"
    SharedMuscleGroup.FOREARMS -> "前臂"
    SharedMuscleGroup.LEGS -> "腿部"
    SharedMuscleGroup.QUADRICEPS -> "股四头肌"
    SharedMuscleGroup.HAMSTRINGS -> "腿筋"
    SharedMuscleGroup.GLUTES -> "臀部"
    SharedMuscleGroup.CALVES -> "小腿"
    SharedMuscleGroup.CORE -> "核心"
    SharedMuscleGroup.ABS -> "腹肌"
    SharedMuscleGroup.OBLIQUES -> "腹斜肌"
    SharedMuscleGroup.LOWER_BACK -> "下背部"
    SharedMuscleGroup.TRAPEZIUS -> "斜方肌"
    SharedMuscleGroup.FULL_BODY -> "全身"
    SharedMuscleGroup.CARDIO -> "有氧"
    SharedMuscleGroup.OTHER -> "其他"
}

/**
 * SharedEquipment 扩展函数
 * 为 shared-models 中的枚举添加显示名称
 */
fun SharedEquipment.getDisplayName(): String = when (this) {
    SharedEquipment.NONE -> "无器材"
    SharedEquipment.BARBELL -> "杠铃"
    SharedEquipment.DUMBBELL -> "哑铃"
    SharedEquipment.KETTLEBELL -> "壶铃"
    SharedEquipment.CABLE -> "拉索"
    SharedEquipment.CABLES -> "拉索组"
    SharedEquipment.MACHINE -> "健身机器"
    SharedEquipment.PULL_UP_BAR -> "引体向上杠"
    SharedEquipment.PARALLEL_BARS -> "双杠"
    SharedEquipment.RESISTANCE_BAND -> "阻力带"
    SharedEquipment.MEDICINE_BALL -> "药球"
    SharedEquipment.STABILITY_BALL -> "稳定球"
    SharedEquipment.FOAM_ROLLER -> "泡沫轴"
    SharedEquipment.BENCH -> "卧推凳"
    SharedEquipment.SQUAT_RACK -> "深蹲架"
    SharedEquipment.SMITH_MACHINE -> "史密斯机"
    SharedEquipment.LEG_PRESS -> "腿举机"
    SharedEquipment.LAT_PULLDOWN -> "高位下拉"
    SharedEquipment.ROWING_MACHINE -> "划船机"
    SharedEquipment.TREADMILL -> "跑步机"
    SharedEquipment.STATIONARY_BIKE -> "动感单车"
    SharedEquipment.BIKE -> "自行车"
    SharedEquipment.ELLIPTICAL -> "椭圆机"
    SharedEquipment.ROWER -> "划船器"
    SharedEquipment.SUSPENSION_TRAINER -> "悬挂训练器"
    SharedEquipment.BATTLE_ROPES -> "战绳"
    SharedEquipment.BATTLE_ROPE -> "战绳"
    SharedEquipment.TRX -> "TRX悬挂训练系统"
    SharedEquipment.PLYO_BOX -> "跳箱"
    SharedEquipment.AGILITY_LADDER -> "敏捷梯"
    SharedEquipment.BALANCE_BALL -> "平衡球"
    SharedEquipment.OTHER -> "其他"
}
