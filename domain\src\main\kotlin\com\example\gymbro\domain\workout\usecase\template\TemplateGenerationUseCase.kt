package com.example.gymbro.domain.workout.usecase.template

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.template.TemplateExercise
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.TemplateRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * 模板生成UseCase - AI和生成相关功能
 *
 * Phase 0: UseCase架构重构 - 聚合设计
 * 整合原有的GenerateWorkoutTemplateUseCase, GenerateTemplateFromAiResponseUseCase等
 *
 * 功能覆盖:
 * - AI生成模板 (替代GenerateWorkoutTemplateUseCase)
 * - 从AI响应生成模板 (替代GenerateTemplateFromAiResponseUseCase)
 * - 从Function Call生成模板
 */
@Singleton
class TemplateGenerationUseCase
    @Inject
    constructor(
        private val repository: TemplateRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * AI生成训练模板
         * 替代原有的GenerateWorkoutTemplateUseCase
         */
        inner class GenerateFromAi : ModernUseCase<GenerateFromAiParams, WorkoutTemplate>(dispatcher, logger) {
            override suspend fun execute(params: GenerateFromAiParams): ModernResult<WorkoutTemplate> {
                logger.d("AI生成模板: ${params.prompt}")

                if (params.prompt.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "generateFromAi",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("生成提示不能为空"),
                        ),
                    )
                }

                val userId = getCurrentUserIdUseCase.invoke()?.toString() ?: "unknown"

                // 模拟AI生成的模板数据
                val generatedTemplate = WorkoutTemplate(
                    id = com.example.gymbro.core.util.CompactIdGenerator.generateId("ai_tmpl"),
                    name = params.templateName ?: "AI生成的训练模板",
                    description = "由AI根据提示生成：${params.prompt}",
                    targetMuscleGroups = listOf("全身"),
                    difficulty = 3, // 中等难度
                    estimatedDuration = 45, // 45分钟
                    userId = userId,
                    isPublic = false,
                    isFavorite = false,
                    tags = listOf("AI生成"),
                    exercises = listOf(
                        TemplateExercise(
                            id = com.example.gymbro.core.util.CompactIdGenerator.generateId("ai_ex"),
                            exerciseId = "default_exercise_1",
                            name = "AI推荐的基础动作",
                            order = 1,
                            sets = 3,
                            reps = 10,
                            restSeconds = 90,
                            weight = null,
                            notes = "AI推荐的基础动作",
                        ),
                    ),
                )

                // 保存生成的模板
                return when (val saveResult = repository.saveTemplate(generatedTemplate)) {
                    is ModernResult.Success -> {
                        logger.d("成功生成并保存AI模板: ${generatedTemplate.name}")
                        ModernResult.Success(generatedTemplate.copy(id = saveResult.data.toString()))
                    }

                    is ModernResult.Error -> {
                        logger.e("保存AI生成模板失败: ${saveResult.error}")
                        saveResult.mapError { it }
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "generateFromAi",
                                errorType = GlobalErrorType.System.General,
                                uiMessage = UiText.DynamicString("生成模板失败"),
                            ),
                        )
                    }
                }
            }
        }

        /**
         * 从AI响应生成模板
         * 替代原有的GenerateTemplateFromAiResponseUseCase
         */
        inner class GenerateFromResponse : ModernUseCase<GenerateFromResponseParams, WorkoutTemplate>(
            dispatcher,
            logger,
        ) {
            override suspend fun execute(params: GenerateFromResponseParams): ModernResult<WorkoutTemplate> {
                logger.d("从AI响应生成模板")

                if (params.response.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "generateFromResponse",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("AI响应不能为空"),
                        ),
                    )
                }

                val userId = getCurrentUserIdUseCase.invoke()?.toString() ?: "unknown"

                try {
                    // 解析AI响应并生成模板
                    val parsedTemplate = parseAiResponse(params.response, userId)

                    // 保存解析后的模板
                    return when (val saveResult = repository.saveTemplate(parsedTemplate)) {
                        is ModernResult.Success -> {
                            logger.d("成功从AI响应生成模板: ${parsedTemplate.name}")
                            ModernResult.Success(parsedTemplate.copy(id = saveResult.data.toString()))
                        }

                        is ModernResult.Error -> {
                            logger.e("保存AI响应模板失败: ${saveResult.error}")
                            saveResult.mapError { it }
                        }

                        is ModernResult.Loading -> {
                            ModernResult.Error(
                                ModernDataError(
                                    operationName = "generateFromResponse",
                                    errorType = GlobalErrorType.System.General,
                                    uiMessage = UiText.DynamicString("生成模板失败"),
                                ),
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "解析AI响应失败")
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "generateFromResponse",
                            errorType = GlobalErrorType.Data.General,
                            uiMessage = UiText.DynamicString("解析AI响应失败"),
                        ),
                    )
                }
            }
        }

        /**
         * 从Function Call生成模板
         * 新增功能：处理Function Call结果
         */
        inner class GenerateFromFunctionCall : ModernUseCase<GenerateFromFunctionCallParams, WorkoutTemplate>(
            dispatcher,
            logger,
        ) {
            override suspend fun execute(params: GenerateFromFunctionCallParams): ModernResult<WorkoutTemplate> {
                logger.d("从Function Call生成模板")

                val userId = getCurrentUserIdUseCase.invoke()?.toString() ?: "unknown"

                try {
                    // 解析Function Call载荷并生成模板
                    val parsedTemplate = parseFunctionCallPayload(params.payload, userId)

                    // 保存解析后的模板
                    return when (val saveResult = repository.saveTemplate(parsedTemplate)) {
                        is ModernResult.Success -> {
                            logger.d("成功从Function Call生成模板: ${parsedTemplate.name}")
                            ModernResult.Success(parsedTemplate.copy(id = saveResult.data.toString()))
                        }

                        is ModernResult.Error -> {
                            logger.e("保存Function Call模板失败: ${saveResult.error}")
                            saveResult.mapError { it }
                        }

                        is ModernResult.Loading -> {
                            ModernResult.Error(
                                ModernDataError(
                                    operationName = "generateFromFunctionCall",
                                    errorType = GlobalErrorType.System.General,
                                    uiMessage = UiText.DynamicString("生成模板失败"),
                                ),
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "解析Function Call载荷失败")
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "generateFromFunctionCall",
                            errorType = GlobalErrorType.Data.General,
                            uiMessage = UiText.DynamicString("解析Function Call失败"),
                        ),
                    )
                }
            }
        }

        // ==================== 私有辅助方法 ====================

        /**
         * 解析AI响应为模板
         */
        private fun parseAiResponse(response: String, userId: String): WorkoutTemplate {
            // 简化解析逻辑，实际应该有更复杂的解析逻辑
            val lines = response.split("\n").filter { it.isNotBlank() }
            val templateName = lines.firstOrNull()?.removePrefix("模板名称：") ?: "AI生成模板"

            return WorkoutTemplate(
                id = java.util.UUID.randomUUID().toString(),
                name = templateName,
                description = "从AI响应解析生成",
                targetMuscleGroups = listOf("全身"),
                difficulty = 3,
                estimatedDuration = 30,
                userId = userId,
                isPublic = false,
                isFavorite = false,
                tags = listOf("AI解析"),
                exercises = parseExercisesFromResponse(lines.drop(1)),
            )
        }

        /**
         * 解析Function Call载荷为模板
         */
        private fun parseFunctionCallPayload(payload: Any, userId: String): WorkoutTemplate {
            // 简化解析逻辑，实际应该有更复杂的JSON解析
            return WorkoutTemplate(
                id = java.util.UUID.randomUUID().toString(),
                name = "Function Call生成模板",
                description = "从Function Call解析生成",
                targetMuscleGroups = listOf("全身"),
                difficulty = 3,
                estimatedDuration = 30,
                userId = userId,
                isPublic = false,
                isFavorite = false,
                tags = listOf("Function Call"),
                exercises = listOf(
                    TemplateExercise(
                        id = java.util.UUID.randomUUID().toString(),
                        exerciseId = "fc_exercise_1",
                        name = "Function Call生成",
                        order = 1,
                        sets = 3,
                        reps = 8,
                        restSeconds = 90,
                        weight = null,
                        notes = "Function Call生成",
                    ),
                ),
            )
        }

        /**
         * 从响应行解析动作列表
         */
        private fun parseExercisesFromResponse(lines: List<String>): List<TemplateExercise> {
            // 简化解析逻辑
            return lines.mapIndexed { index, line ->
                TemplateExercise(
                    id = java.util.UUID.randomUUID().toString(),
                    exerciseId = "parsed_exercise_${index + 1}",
                    name = line.take(50), // 取前50个字符作为备注
                    order = index + 1,
                    sets = 3,
                    reps = 10,
                    restSeconds = 90,
                    weight = null,
                    notes = line.take(50), // 取前50个字符作为备注
                )
            }
        }

        // ==================== 参数类 ====================

        /**
         * AI生成参数
         */
        data class GenerateFromAiParams(
            val prompt: String,
            val templateName: String? = null,
            val difficulty: Int = 3,
            val duration: Int = 45,
        )

        /**
         * 从AI响应生成参数
         */
        data class GenerateFromResponseParams(
            val response: String,
            val templateName: String? = null,
        )

        /**
         * 从Function Call生成参数
         */
        data class GenerateFromFunctionCallParams(
            val payload: Any,
            val templateName: String? = null,
        )
    }
