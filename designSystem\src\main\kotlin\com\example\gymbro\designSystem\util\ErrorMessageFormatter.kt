package com.example.gymbro.designSystem.util

import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import javax.inject.Inject

/**
 * 错误消息格式化工具
 *
 * 提供智能的错误消息转换功能，将技术错误转为友好的用户消息
 * 支持基于错误类型的消息定制，以及动态参数注入
 */
class ErrorMessageFormatter
    @Inject
    constructor(
        private val resourceProvider: ResourceProvider,
    ) {
        /**
         * 将ModernDataError转换为用户友好的UiText
         *
         * @param error 待处理的错误
         * @return 用户友好的错误消息
         */
        fun format(error: ModernDataError): UiText {
            // 首先尝试使用错误自带的uiMessage
            error.uiMessage?.let { uiMessage ->
                if (!uiMessage.toString().isBlank()) {
                    return uiMessage
                }
            }

            // 根据错误类型提供友好的默认消息
            return when (val errorType = error.errorType) {
                // 网络错误
                is GlobalErrorType.Network -> formatNetworkError(errorType)

                // 认证错误
                is GlobalErrorType.Auth -> formatAuthError(errorType)

                // 业务错误
                is GlobalErrorType.Business -> formatBusinessError(errorType, error)

                // 数据错误
                is GlobalErrorType.Data -> formatDataError(errorType)

                // 验证错误
                is GlobalErrorType.Validation -> formatValidationError(errorType)

                // 系统错误
                is GlobalErrorType.System -> formatSystemError(errorType)

                // 未知错误
                else -> UiText.DynamicString("发生了未知错误，请稍后重试")
            }
        }

        /**
         * 格式化网络错误
         */
        private fun formatNetworkError(errorType: GlobalErrorType.Network): UiText =
            when (errorType) {
                is GlobalErrorType.Network.Connection -> UiText.DynamicString("网络连接问题，请检查您的网络设置")
                is GlobalErrorType.Network.Timeout -> UiText.DynamicString("请求超时，服务器未能及时响应")
                else -> UiText.DynamicString("网络问题，请稍后重试")
            }

        /**
         * 格式化认证错误
         */
        private fun formatAuthError(errorType: GlobalErrorType.Auth): UiText =
            when (errorType) {
                is GlobalErrorType.Auth.InvalidCredentials -> UiText.DynamicString("用户名或密码不正确")
                is GlobalErrorType.Auth.TokenExpired -> UiText.DynamicString("登录已过期，请重新登录")
                is GlobalErrorType.Auth.Unauthorized -> UiText.DynamicString("您没有权限执行此操作")
                is GlobalErrorType.Auth.Forbidden -> UiText.DynamicString("账户已被锁定，请联系客服")
                else -> UiText.DynamicString("登录问题，请重新登录")
            }

        /**
         * 格式化业务错误
         */
        private fun formatBusinessError(
            errorType: GlobalErrorType.Business,
            error: ModernDataError,
        ): UiText {
            // 尝试从元数据中获取更多信息
            val details = error.metadataMap["message"] as? String
            val entityName = error.metadataMap["entityName"] as? String ?: "记录"

            return when (errorType) {
                is GlobalErrorType.Business.NotFound -> {
                    val defaultMessage = "找不到请求的$entityName"
                    if (details != null) {
                        UiText.DynamicString("$defaultMessage: $details")
                    } else {
                        UiText.DynamicString(defaultMessage)
                    }
                }

                is GlobalErrorType.Business.Conflict -> UiText.DynamicString("${entityName}已存在")
                is GlobalErrorType.Business.InvalidState -> {
                    val defaultMessage = "操作无效"
                    if (details != null) {
                        UiText.DynamicString("$defaultMessage: $details")
                    } else {
                        UiText.DynamicString(defaultMessage)
                    }
                }

                is GlobalErrorType.Business.Rule -> {
                    val limit = error.metadataMap["quotaLimit"] as? String
                    val resourceType = error.metadataMap["resourceType"] as? String ?: "资源"
                    if (limit != null) {
                        UiText.DynamicString("已达到${resourceType}最大限制($limit)")
                    } else {
                        UiText.DynamicString("已达到${resourceType}最大限制")
                    }
                }

                else -> UiText.DynamicString("操作无法完成")
            }
        }

        /**
         * 格式化数据错误
         */
        private fun formatDataError(errorType: GlobalErrorType.Data): UiText =
            when (errorType) {
                is GlobalErrorType.Data.NotFound -> UiText.DynamicString("找不到请求的数据")
                is GlobalErrorType.Data.Corrupted -> UiText.DynamicString("数据已损坏，无法读取")
                is GlobalErrorType.Data.Access -> UiText.DynamicString("无法访问数据，权限不足")
                is GlobalErrorType.Data.Constraint -> UiText.DynamicString("数据冲突，请刷新后重试")
                else -> UiText.DynamicString("数据处理问题")
            }

        /**
         * 格式化验证错误
         */
        private fun formatValidationError(errorType: GlobalErrorType.Validation): UiText =
            when (errorType) {
                is GlobalErrorType.Validation.Required -> UiText.DynamicString("必填字段不能为空")
                is GlobalErrorType.Validation.Format -> UiText.DynamicString("格式不正确")
                is GlobalErrorType.Validation.Range -> UiText.DynamicString("值超出允许范围")
                is GlobalErrorType.Validation.InvalidValue -> UiText.DynamicString("输入内容无效")
                else -> UiText.DynamicString("输入内容无效")
            }

        /**
         * 格式化系统错误
         */
        private fun formatSystemError(errorType: GlobalErrorType.System): UiText =
            when (errorType) {
                is GlobalErrorType.System.Internal -> UiText.DynamicString("应用内部错误，请重启应用")
                is GlobalErrorType.System.Resource -> UiText.DynamicString("系统资源不足，请关闭其他应用后重试")
                is GlobalErrorType.System.Configuration -> UiText.DynamicString("系统配置错误")
                is GlobalErrorType.System.General -> UiText.DynamicString("系统错误，请稍后重试")
                else -> UiText.DynamicString("系统错误，请稍后重试")
            }

        /**
         * 根据错误严重程度添加前缀
         */
        fun addSeverityPrefix(
            message: UiText,
            severity: ErrorSeverity,
        ): UiText {
            val prefix =
                when (severity) {
                    ErrorSeverity.INFO -> "提示："
                    ErrorSeverity.WARNING -> "警告："
                    ErrorSeverity.ERROR -> "错误："
                    ErrorSeverity.CRITICAL -> "严重错误："
                }

            return UiText.DynamicString("$prefix$message")
        }

        /**
         * 添加恢复建议
         */
        fun addRecoverySuggestion(
            message: UiText,
            errorType: GlobalErrorType,
        ): UiText {
            val suggestion =
                when (errorType) {
                    is GlobalErrorType.Network.Connection -> "请检查您的网络连接。"
                    is GlobalErrorType.Network.Timeout -> "请稍后重试。"
                    is GlobalErrorType.Auth.TokenExpired -> "请重新登录。"
                    is GlobalErrorType.System.Resource -> "请关闭其他应用后重试。"
                    is GlobalErrorType.System.Internal -> "请重启应用。"
                    else -> null
                }

            return if (suggestion != null) {
                UiText.DynamicString("$message $suggestion")
            } else {
                message
            }
        }

        /**
         * 组合错误消息并添加用户行动建议
         */
        fun formatWithAction(error: ModernDataError): Pair<UiText, UiText?> {
            val message = format(error)
            val actionText =
                when (error.errorType) {
                    is GlobalErrorType.Network.Connection -> UiText.DynamicString("重新连接")
                    is GlobalErrorType.Auth.TokenExpired -> UiText.DynamicString("重新登录")
                    is GlobalErrorType.Network.Timeout -> UiText.DynamicString("重试")
                    is GlobalErrorType.Data.NotFound -> UiText.DynamicString("刷新")
                    else -> if (error.recoverable) UiText.DynamicString("重试") else null
                }

            return Pair(message, actionText)
        }

        /**
         * 获取字段名称的友好显示
         */
        private fun getFieldNameDisplay(fieldName: String): String =
            when (fieldName.lowercase()) {
                "username" -> "用户名"
                "password" -> "密码"
                "email" -> "电子邮件"
                "phone" -> "手机号"
                "firstname" -> "名字"
                "lastname" -> "姓氏"
                "birthdate" -> "出生日期"
                "address" -> "地址"
                "weight" -> "体重"
                "height" -> "身高"
                "age" -> "年龄"
                "title" -> "标题"
                "description" -> "描述"
                "name" -> "名称"
                "date" -> "日期"
                "time" -> "时间"
                "duration" -> "时长"
                "reps" -> "重复次数"
                "sets" -> "组数"
                "amount" -> "金额"
                else -> fieldName // 无法识别的字段名直接返回原始值
            }
    }
