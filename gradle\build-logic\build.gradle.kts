plugins {
    `kotlin-dsl`
}

group = "com.example.gymbro.buildlogic"

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

repositories {
    google()
    mavenCentral()
    gradlePluginPortal()
}



dependencies {
    implementation(libs.android.gradlePlugin)
    implementation(libs.kotlin.gradlePlugin)
    implementation(libs.ksp.gradlePlugin)
    implementation(libs.hilt.gradlePlugin)
    // Compose plugin is part of Kotlin plugin, no separate dependency needed

    // Kotlin Serialization plugin
    implementation("org.jetbrains.kotlin:kotlin-serialization:2.0.21")

    // CI/CD & Quality Tools
    implementation("org.jlleitschuh.gradle:ktlint-gradle:12.1.1")
    implementation("org.owasp:dependency-check-gradle:11.1.0")
    implementation("io.gitlab.arturbosch.detekt:detekt-gradle-plugin:1.23.8")

    // Detekt API for custom rules
    implementation("io.gitlab.arturbosch.detekt:detekt-api:1.23.8")
    implementation("io.gitlab.arturbosch.detekt:detekt-psi-utils:1.23.8")


}

gradlePlugin {
    plugins {
        register("androidApplication") {
            id = "gymbro.android.application"
            implementationClass = "com.example.gymbro.buildlogic.AndroidApplicationConventionPlugin"
        }
        register("androidLibrary") {
            id = "gymbro.android.library"
            implementationClass = "com.example.gymbro.buildlogic.AndroidLibraryConventionPlugin"
        }
        register("featureLibrary") {
            id = "gymbro.feature.library"
            implementationClass = "com.example.gymbro.buildlogic.FeatureLibraryConventionPlugin"
        }
        register("hiltLibrary") {
            id = "gymbro.hilt.library"
            implementationClass = "com.example.gymbro.buildlogic.HiltConventionPlugin"
        }
        register("composeLibrary") {
            id = "gymbro.compose.library"
            implementationClass = "com.example.gymbro.buildlogic.ComposeConventionPlugin"
        }
        register("domainModule") {
            id = "gymbro.domain.module"
            implementationClass = "com.example.gymbro.buildlogic.DomainModuleConventionPlugin"
        }
        register("dataModule") {
            id = "gymbro.data.module"
            implementationClass = "com.example.gymbro.buildlogic.DataModuleConventionPlugin"
        }
        register("testingLibrary") {
            id = "gymbro.testing.library"
            implementationClass = "com.example.gymbro.buildlogic.TestingConventionPlugin"
        }
        register("kotlinSerialization") {
            id = "gymbro.kotlin.serialization"
            implementationClass = "com.example.gymbro.buildlogic.KotlinSerializationConventionPlugin"
        }
        register("coreDependencies") {
            id = "gymbro.core.dependencies"
            implementationClass = "com.example.gymbro.buildlogic.CoreDependenciesConventionPlugin"
        }
        register("firebase") {
            id = "gymbro.firebase"
            implementationClass = "com.example.gymbro.buildlogic.FirebaseConventionPlugin"
        }
        register("room") {
            id = "gymbro.room"
            implementationClass = "com.example.gymbro.buildlogic.RoomConventionPlugin"
        }
        register("kotlinJvm") {
            id = "gymbro.kotlin.jvm"
            implementationClass = "com.example.gymbro.buildlogic.KotlinJvmConventionPlugin"
        }
        register("jacoco") {
            id = "gymbro.jacoco"
            implementationClass = "com.example.gymbro.buildlogic.JacocoConventionPlugin"
        }
        register("ktlint") {
            id = "gymbro.ktlint"
            implementationClass = "com.example.gymbro.buildlogic.KtlintConventionPlugin"
        }
        register("security") {
            id = "gymbro.security"
            implementationClass = "com.example.gymbro.buildlogic.SecurityConventionPlugin"
        }
        register("detekt") {
            id = "gymbro.detekt"
            implementationClass = "com.example.gymbro.buildlogic.DetektConventionPlugin"
        }
        register("quality") {
            id = "gymbro.quality"
            implementationClass = "com.example.gymbro.buildlogic.QualityConventionPlugin"
        }
        register("testing-official") {
            id = "gymbro.testing.official"
            implementationClass = "com.example.gymbro.buildlogic.OfficialTestingStandardPlugin"
        }
        register("performanceOptimization") {
            id = "gymbro.performance.optimization"
            implementationClass = "com.example.gymbro.buildlogic.PerformanceOptimizationPlugin"
        }
    }
}
