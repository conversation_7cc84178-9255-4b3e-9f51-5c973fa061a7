package com.example.gymbro.features.coach.history.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import com.example.gymbro.core.ui.text.asString
import com.example.gymbro.features.coach.history.internal.resources.HistoryStrings

/**
 * Paging3状态管理包装器
 *
 * 基于 history_code_review.md 建议，统一处理分页加载、错误和空状态的通用组件。
 * 消除重复的状态处理代码，提供一致的用户体验。
 */
@Composable
fun <T : Any> PagingStateWrapper(
    pagingItems: LazyPagingItems<T>,
    modifier: Modifier = Modifier,
    loadingIndicator: @Composable () -> Unit = {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            contentAlignment = Alignment.Center,
        ) {
            CircularProgressIndicator(modifier = Modifier.size(24.dp))
        }
    },
    errorContent: @Composable (error: Throwable, retry: () -> Unit) -> Unit = { error, retry ->
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer,
            ),
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                Text(
                    text = HistoryStrings.pagingLoadError(error.message ?: "").asString(LocalContext.current),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                )
                Button(
                    onClick = retry,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error,
                    ),
                ) {
                    Text(HistoryStrings.retryLoad.asString(LocalContext.current))
                }
            }
        }
    },
    emptyContent: @Composable () -> Unit,
    content: LazyListScope.() -> Unit,
) {
    val listState = rememberLazyListState()

    LazyColumn(
        state = listState,
        contentPadding = PaddingValues(vertical = 4.dp),
        modifier = modifier,
    ) {
        // 处理刷新状态
        when (val refreshState = pagingItems.loadState.refresh) {
            is LoadState.Loading -> {
                if (pagingItems.itemCount == 0) {
                    item(key = "loading") {
                        loadingIndicator()
                    }
                }
            }

            is LoadState.Error -> {
                item(key = "error") {
                    errorContent(refreshState.error) { pagingItems.retry() }
                }
            }

            is LoadState.NotLoading -> {
                if (pagingItems.itemCount == 0) {
                    item(key = "empty") {
                        emptyContent()
                    }
                }
            }
        }

        // 主要内容
        content()

        // 处理底部加载状态
        when (val appendState = pagingItems.loadState.append) {
            is LoadState.Loading -> {
                item(key = "append_loading") {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center,
                    ) {
                        CircularProgressIndicator(modifier = Modifier.size(20.dp))
                    }
                }
            }

            is LoadState.Error -> {
                item(key = "append_error") {
                    Text(
                        text = HistoryStrings.loadMoreFailed.asString(LocalContext.current) + ": ${appendState.error.message}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                    )
                }
            }

            else -> {
                /* NotLoading - 不需要显示内容 */
            }
        }
    }
}
