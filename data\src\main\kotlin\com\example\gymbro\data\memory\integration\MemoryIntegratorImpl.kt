package com.example.gymbro.data.memory.integration

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.ai.prompt.memory.MemoryContext
import com.example.gymbro.core.ai.prompt.memory.MemoryContextType
import com.example.gymbro.core.ai.prompt.memory.MemoryIntegrator
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.domain.memory.usecase.RecallMemoriesUseCase
import com.example.gymbro.shared.models.memory.MemorySearchResult
import com.example.gymbro.shared.models.memory.MemoryTier
import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * Memory系统集成器实现
 *
 * 负责将4层记忆系统（ECM/DWM/UPM/GIM）集成到Prompt构建流程中
 * 遵循Clean Architecture原则，在data层实现core层定义的接口
 *
 * @since 618重构 - 架构修复
 */
@Singleton
class MemoryIntegratorImpl
    @Inject
    constructor(
        private val recallMemoriesUseCase: RecallMemoriesUseCase,
        private val tokenizer: TokenizerService,
    ) : MemoryIntegrator {

        companion object {
            private const val DEFAULT_TOKEN_BUDGET = 1000
            private const val MIN_IMPORTANCE_THRESHOLD = 3
        }

        /**
         * 构建Memory相关的ChatMessage列表
         */
        override suspend fun buildMemoryMessages(
            context: MemoryContext?,
            tokenBudget: Int,
        ): List<CoreChatMessage> {
            if (context == null) {
                Timber.d("MemoryIntegrator: 无Memory上下文，跳过构建")
                return emptyList()
            }

            Timber.d("MemoryIntegrator: 开始构建Memory消息，预算=$tokenBudget tokens")

            try {
                // 1. 召回相关记忆
                val memories = recallRelevantMemories(context)
                if (memories.isEmpty()) {
                    Timber.d("MemoryIntegrator: 未召回到相关记忆")
                    return emptyList()
                }

                // 2. 按优先级和token预算筛选
                val selectedMemories = selectMemoriesWithinBudget(memories, tokenBudget)

                // 3. 格式化为ChatMessage
                val messages = formatMemoriesToMessages(selectedMemories)

                Timber.d("MemoryIntegrator: Memory消息构建完成，返回${messages.size}条")
                return messages
            } catch (e: Exception) {
                Timber.e(e, "MemoryIntegrator: 构建Memory消息失败")
                return emptyList()
            }
        }

        /**
         * 召回相关记忆
         */
        private suspend fun recallRelevantMemories(
            context: MemoryContext,
        ): List<MemorySearchResult> {
            // 根据上下文类型决定召回策略
            val tiers = when (context.contextType) {
                MemoryContextType.CONVERSATION -> listOf(MemoryTier.ECM, MemoryTier.DWM)
                MemoryContextType.TRAINING -> listOf(MemoryTier.DWM, MemoryTier.UPM)
                MemoryContextType.PROFILE -> listOf(MemoryTier.UPM, MemoryTier.GIM)
                MemoryContextType.GENERAL -> listOf(MemoryTier.ECM, MemoryTier.DWM, MemoryTier.UPM)
            }

            val params = RecallMemoriesUseCase.Params(
                userId = context.userId,
                query = context.query,
                tokenBudget = 600,
            )

            return when (val result = recallMemoriesUseCase(params)) {
                is com.example.gymbro.core.error.types.ModernResult.Success -> result.data
                is com.example.gymbro.core.error.types.ModernResult.Error -> {
                    Timber.w("MemoryIntegrator: 召回记忆失败 - ${result.error.uiMessage}")
                    emptyList()
                }

                is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                    Timber.d("MemoryIntegrator: 正在召回记忆...")
                    emptyList()
                }
            }
        }

        /**
         * 按token预算筛选记忆
         */
        private fun selectMemoriesWithinBudget(
            memories: List<MemorySearchResult>,
            tokenBudget: Int,
        ): List<MemorySearchResult> {
            val selected = mutableListOf<MemorySearchResult>()
            var usedTokens = 0

            // 按重要性和相似度排序
            val sorted = memories.sortedWith(
                compareByDescending<MemorySearchResult> { it.memory.importance }
                    .thenByDescending { it.similarity },
            )

            for (result in sorted) {
                // 跳过低重要性记忆
                if (result.memory.importance < MIN_IMPORTANCE_THRESHOLD) {
                    continue
                }

                val content = formatMemoryContent(result)
                val tokens = tokenizer.countTokens(content)

                if (usedTokens + tokens <= tokenBudget) {
                    selected.add(result)
                    usedTokens += tokens
                } else {
                    // 预算耗尽，停止添加
                    break
                }
            }

            Timber.d("MemoryIntegrator: 筛选${selected.size}条记忆，使用$usedTokens/$tokenBudget tokens")
            return selected
        }

        /**
         * 格式化记忆为ChatMessage
         */
        private fun formatMemoriesToMessages(
            memories: List<MemorySearchResult>,
        ): List<CoreChatMessage> {
            // 按记忆层级分组
            val groupedByTier = memories.groupBy { it.memory.tier }

            return buildList {
                // ECM - 临时上下文记忆
                groupedByTier[MemoryTier.ECM]?.let { ecmMemories ->
                    val content = buildString {
                        appendLine("[临时上下文记忆]")
                        ecmMemories.forEach { result ->
                            appendLine("• ${formatMemoryContent(result)}")
                        }
                    }
                    add(CoreChatMessage("tool", content.trim()))
                }

                // DWM - 工作记忆
                groupedByTier[MemoryTier.DWM]?.let { dwmMemories ->
                    val content = buildString {
                        appendLine("[近期对话记忆]")
                        dwmMemories.forEach { result ->
                            appendLine("• ${formatMemoryContent(result)}")
                        }
                    }
                    add(CoreChatMessage("tool", content.trim()))
                }

                // UPM - 用户档案记忆
                groupedByTier[MemoryTier.UPM]?.let { upmMemories ->
                    val content = buildString {
                        appendLine("[用户档案记忆]")
                        upmMemories.forEach { result ->
                            appendLine("• ${formatMemoryContent(result)}")
                        }
                    }
                    add(CoreChatMessage("tool", content.trim()))
                }

                // GIM - 全局洞察记忆
                groupedByTier[MemoryTier.GIM]?.let { gimMemories ->
                    val content = buildString {
                        appendLine("[全局洞察记忆]")
                        gimMemories.forEach { result ->
                            appendLine("• ${formatMemoryContent(result)}")
                        }
                    }
                    add(CoreChatMessage("tool", content.trim()))
                }
            }
        }

        /**
         * 格式化单条记忆内容
         */
        private fun formatMemoryContent(result: MemorySearchResult): String {
            return when (result.memory.tier) {
                MemoryTier.ECM -> {
                    // ECM记忆直接显示内容
                    result.memory.payload.toString()
                }

                MemoryTier.DWM -> {
                    // DWM记忆添加时间信息
                    val timeAgo = formatTimeAgo(result.memory.createdAt)
                    "${result.memory.payload} ($timeAgo)"
                }

                MemoryTier.UPM -> {
                    // UPM记忆强调关键信息
                    "【${result.memory.importance}级重要】${result.memory.payload}"
                }

                MemoryTier.GIM -> {
                    // GIM记忆突出洞察性质
                    "💡 ${result.memory.payload}"
                }
            }
        }

        /**
         * 格式化时间差
         */
        private fun formatTimeAgo(timestamp: Long): String {
            val now = System.currentTimeMillis()
            val diff = now - timestamp

            return when {
                diff < 60_000 -> "刚刚"
                diff < 3600_000 -> "${diff / 60_000}分钟前"
                diff < 86400_000 -> "${diff / 3600_000}小时前"
                else -> "${diff / 86400_000}天前"
            }
        }
    }
