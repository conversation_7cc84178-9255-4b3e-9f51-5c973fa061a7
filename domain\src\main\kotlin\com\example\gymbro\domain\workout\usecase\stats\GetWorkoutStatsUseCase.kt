package com.example.gymbro.domain.workout.usecase.stats

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.shared.common.model.TimeRangeSpan
import com.example.gymbro.domain.workout.model.stats.UserWorkoutStatistics
import com.example.gymbro.domain.workout.repository.SessionRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.firstOrNull

/**
 * 获取训练统计数据的用例
 *
 * 该用例负责根据指定的时间范围获取用户的训练统计数据。
 * 支持预定义的时间范围（如最近7天、30天等）或自定义时间范围。
 */
@Singleton
class GetWorkoutStatsUseCase
    @Inject
    constructor(
        private val sessionRepository: SessionRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<TimeRangeSpan, UserWorkoutStatistics>(dispatcher, logger) {
        /**
         * 执行获取训练统计的核心逻辑
         *
         * @param parameters 训练时间范围
         * @return 训练统计数据结果，包装在ModernResult中
         */
        override suspend fun execute(parameters: TimeRangeSpan): ModernResult<UserWorkoutStatistics> {
            val userIdResult =
                getCurrentUserIdUseCase().firstOrNull() ?: return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "GetWorkoutStatsUseCase.getStats",
                        message = UiText.DynamicString("无法获取用户ID"),
                    ),
                )

            when (userIdResult) {
                is ModernResult.Success<*> -> {
                    val data = userIdResult.data
                    val userId: String
                    if (data is String && data.isNotBlank()) {
                        userId = data
                    } else {
                        return ModernResult.Error(
                            DataErrors.DataError.notFound(
                                operationName = "GetWorkoutStatsUseCase.getStats",
                                message = UiText.DynamicString("用户ID为空"),
                                entityType = "User",
                            ),
                        )
                    }

                    // TODO: 需要适配新的SessionRepository接口
                    // 暂时返回错误
                    return ModernResult.Error(
                        DataErrors.DataError.notFound(
                            operationName = "GetWorkoutStatsUseCase.getStats",
                            message = UiText.DynamicString("功能暂未实现，等待接口适配"),
                            entityType = "UserWorkoutStatistics",
                        ),
                    )

                    // TODO: 已经在上面返回错误了，这里不会执行到
                }

                is ModernResult.Error -> {
                    val errorToReturn = userIdResult.error
                    return ModernResult.Error(errorToReturn)
                }

                is ModernResult.Loading -> {
                    return ModernResult.loading()
                }
            }
        }
    }
