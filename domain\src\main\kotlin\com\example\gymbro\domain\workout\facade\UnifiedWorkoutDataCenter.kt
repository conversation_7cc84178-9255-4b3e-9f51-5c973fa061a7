package com.example.gymbro.domain.workout.facade

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.exercise.model.ExerciseSelection
import com.example.gymbro.domain.exercise.service.TemplateComposer
import com.example.gymbro.domain.workout.model.Difficulty
import com.example.gymbro.domain.workout.model.WorkoutGoal
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.service.SessionManager
import com.example.gymbro.domain.workout.service.TemplateService
import com.example.gymbro.domain.workout.service.WorkoutJsonExporter
import com.example.gymbro.shared.models.workout.WorkoutPlan
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 统一训练数据中心 - 门面模式
 *
 * 协调所有训练相关操作，提供对外统一接口，隐藏内部复杂性
 *
 * 🏗️ 架构职责：
 * - 门面模式：统一对外接口，隐藏内部复杂性
 * - 协调器：协调TemplateComposer、TemplateService、SessionManager
 * - 业务流程：管理完整的训练业务流程
 * - 错误处理：统一错误处理和日志记录
 *
 * 🔄 数据流：
 * 1. 用户创建训练 → TemplateComposer → Template
 * 2. 用户创建计划 → TemplateService → Plan
 * 3. 用户开始训练 → SessionManager → Session
 * 4. AI建议插入 → TemplateComposer → Template更新
 */
@Singleton
class UnifiedWorkoutDataCenter
    @Inject
    constructor(
        private val templateComposer: TemplateComposer,
        private val templateService: TemplateService,
        private val sessionManager: SessionManager,
        private val workoutJsonExporter: WorkoutJsonExporter,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val logger: Logger,
    ) {
        // ===== 核心业务流程 =====

        /**
         * 创建用户训练模板
         *
         * 完整流程：用户选择动作 → TemplateComposer生成Template → 保存
         *
         * @param templateName 模板名称
         * @param exerciseSelections 动作选择列表
         * @return 创建的训练模板
         */
        suspend fun createUserWorkout(
            templateName: String,
            exerciseSelections: List<ExerciseSelection>,
        ): ModernResult<com.example.gymbro.shared.models.workout.WorkoutTemplate> {
            return try {
                logger.d("UnifiedWorkoutDataCenter", "开始创建用户训练模板: $templateName")

                // 1. 验证用户权限
                val userIdResult = getCurrentUserIdUseCase()
                if (userIdResult !is ModernResult.Success<String>) {
                    return ModernResult.Error(
                        createError("createUserWorkout", "用户认证失败"),
                    )
                }
                val userId = userIdResult.data!!

                // 2. 使用TemplateComposer生成Template
                val templateResult =
                    templateComposer.composeTemplate(
                        userId = userId,
                        templateName = templateName,
                        exerciseSelections = exerciseSelections,
                    )

                when (templateResult) {
                    is ModernResult.Success -> {
                        logger.d("UnifiedWorkoutDataCenter", "成功创建用户训练模板: ${templateResult.data.id}")
                        templateResult
                    }

                    is ModernResult.Error -> {
                        logger.w("UnifiedWorkoutDataCenter", "创建用户训练模板失败")
                        templateResult
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            createError("createUserWorkout", "创建训练模板超时"),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "创建用户训练模板失败", e)
                ModernResult.Error(
                    createError("createUserWorkout", "创建训练模板失败: ${e.message ?: "未知错误"}"),
                )
            }
        }

        /**
         * 插入AI建议到现有模板
         *
         * 完整流程：现有Template + AI响应 → TemplateComposer处理 → 更新Template
         *
         * @param existingTemplate 现有模板（可为null）
         * @param aiResponse AI响应内容
         * @return 更新后的训练模板
         */
        suspend fun insertAiSuggestion(
            existingTemplate: com.example.gymbro.shared.models.workout.WorkoutTemplate?,
            aiResponse: String,
        ): ModernResult<com.example.gymbro.shared.models.workout.WorkoutTemplate> {
            return try {
                logger.d("UnifiedWorkoutDataCenter", "开始插入AI建议")

                // 1. 验证用户权限
                val userIdResult = getCurrentUserIdUseCase()
                if (userIdResult !is ModernResult.Success<String>) {
                    return ModernResult.Error(
                        createError("insertAiSuggestion", "用户认证失败"),
                    )
                }
                val userId = userIdResult.data!!

                // 2. 使用TemplateComposer处理AI建议
                // TODO: 实现AI建议解析和Template更新逻辑
                // 这里需要TemplateComposer支持AI建议插入功能

                // 临时实现：如果有现有模板则返回，否则创建空模板
                val updatedTemplate =
                    existingTemplate ?: com.example.gymbro.shared.models.workout.WorkoutTemplate(
                        id =
                            java.util.UUID
                                .randomUUID()
                                .toString(),
                        name = "AI生成的训练模板",
                        description = "基于AI建议生成的训练模板",
                        exercises = emptyList(),
                        metadata =
                            com.example.gymbro.shared.models.workout.TemplateMetadata
                                .createDefault(),
                        createdAt = System.currentTimeMillis(),
                    )

                logger.d("UnifiedWorkoutDataCenter", "成功插入AI建议: ${updatedTemplate.id}")
                ModernResult.Success(updatedTemplate)
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "插入AI建议失败", e)
                ModernResult.Error(
                    createError("insertAiSuggestion", "插入AI建议失败: ${e.message ?: "未知错误"}"),
                )
            }
        }

        /**
         * 创建训练计划（基于DayPlan新架构）
         *
         * 完整流程：Template列表 → TemplateService调度 → Plan
         *
         * @param planName 计划名称
         * @param templateIds Template ID列表
         * @param totalDays 计划总天数
         * @return 创建的训练计划
         */
        suspend fun createWorkoutPlan(
            planName: String,
            templateIds: List<String>,
            totalDays: Int = 28,
        ): ModernResult<WorkoutPlan> =
            try {
                logger.d("UnifiedWorkoutDataCenter", "开始创建训练计划: $planName")

                // 使用TemplateService的循环计划功能
                val result =
                    templateService.createCyclePlan(
                        planName = planName,
                        templateIds = templateIds,
                        cycleDays = totalDays,
                        restDays = setOf(7, 14, 21, 28), // 每周休息一天
                    )

                when (result) {
                    is ModernResult.Success -> {
                        logger.d("UnifiedWorkoutDataCenter", "成功创建训练计划: ${result.data.id}")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.w("UnifiedWorkoutDataCenter", "创建训练计划失败")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            createError("createWorkoutPlan", "创建训练计划超时"),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "创建训练计划异常", e)
                ModernResult.Error(
                    createError("createWorkoutPlan", "创建训练计划失败: ${e.message ?: "未知错误"}"),
                )
            }

        /**
         * 创建自定义调度训练计划
         *
         * 支持用户完全自定义的天数调度
         *
         * @param planName 计划名称
         * @param dailySchedule 天数 → Template ID列表的映射
         * @param totalDays 计划总天数
         * @return 创建的训练计划
         */
        suspend fun createCustomSchedulePlan(
            planName: String,
            dailySchedule: Map<Int, List<String>>,
            totalDays: Int,
        ): ModernResult<WorkoutPlan> =
            try {
                logger.d("UnifiedWorkoutDataCenter", "开始创建自定义调度计划: $planName")

                // 使用TemplateService的DayPlan调度功能
                val result =
                    templateService.createDailySchedulePlan(
                        planName = planName,
                        dailySchedule = dailySchedule,
                        totalDays = totalDays,
                    )

                when (result) {
                    is ModernResult.Success -> {
                        logger.d("UnifiedWorkoutDataCenter", "成功创建自定义调度计划: ${result.data.id}")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.w("UnifiedWorkoutDataCenter", "创建自定义调度计划失败")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            createError("createCustomSchedulePlan", "创建自定义调度计划超时"),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "创建自定义调度计划异常", e)
                ModernResult.Error(
                    createError("createCustomSchedulePlan", "创建自定义调度计划失败: ${e.message ?: "未知错误"}"),
                )
            }

        /**
         * 开始训练会话
         *
         * 完整流程：Template → SessionManager → Session
         *
         * @param templateId 模板ID
         * @return 创建的训练会话
         */
        suspend fun startWorkoutSession(
            templateId: String,
        ): ModernResult<com.example.gymbro.domain.workout.model.WorkoutSession> =
            try {
                logger.d("UnifiedWorkoutDataCenter", "开始训练会话: $templateId")

                // 使用SessionManager创建Session
                val result = sessionManager.startSessionFromTemplate(templateId)

                when (result) {
                    is ModernResult.Success -> {
                        logger.d("UnifiedWorkoutDataCenter", "成功开始训练会话: ${result.data.id}")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.w("UnifiedWorkoutDataCenter", "开始训练会话失败")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            createError("startWorkoutSession", "开始训练会话超时"),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "开始训练会话异常", e)
                ModernResult.Error(
                    createError("startWorkoutSession", "开始训练会话失败: ${e.message ?: "未知错误"}"),
                )
            }

        // ===== 便捷方法 =====

        /**
         * 获取用户的所有模板
         */
        suspend fun getUserTemplates(): ModernResult<List<WorkoutTemplate>> {
            return try {
                val userIdResult = getCurrentUserIdUseCase()
                if (userIdResult !is ModernResult.Success<String>) {
                    return ModernResult.Error(
                        createError("getUserTemplates", "用户认证失败"),
                    )
                }
                val userId = userIdResult.data!!

                templateService.getUserTemplates(userId)
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "获取用户模板失败", e)
                ModernResult.Error(
                    createError("getUserTemplates", "获取用户模板失败: ${e.message ?: "未知错误"}"),
                )
            }
        }

        /**
         * 获取模板详情
         */
        suspend fun getTemplate(templateId: String): ModernResult<WorkoutTemplate> =
            try {
                templateService.getTemplate(templateId)
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "获取模板详情失败", e)
                ModernResult.Error(
                    createError("getTemplate", "获取模板详情失败: ${e.message ?: "未知错误"}"),
                )
            }

        /**
         * 推荐模板组合
         */
        suspend fun recommendTemplatesCombination(
            goal: WorkoutGoal,
            difficulty: Difficulty,
            daysPerWeek: Int,
        ): ModernResult<List<String>> {
            return try {
                val userIdResult = getCurrentUserIdUseCase()
                if (userIdResult !is ModernResult.Success<String>) {
                    return ModernResult.Error(
                        createError("recommendTemplatesCombination", "用户认证失败"),
                    )
                }
                val userId = userIdResult.data!!

                templateService.recommendTemplatesCombination(userId, goal, difficulty, daysPerWeek)
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "推荐模板组合失败", e)
                ModernResult.Error(
                    createError("recommendTemplatesCombination", "推荐模板组合失败: ${e.message ?: "未知错误"}"),
                )
            }
        }

        /**
         * 智能推荐完整训练模板
         *
         * 基于用户历史和偏好推荐合适的训练模板
         *
         * @param goal 训练目标
         * @param difficulty 难度级别
         * @param daysPerWeek 每周训练天数
         * @return 推荐的训练模板列表
         */
        suspend fun recommendWorkoutTemplates(
            goal: WorkoutGoal,
            difficulty: Difficulty,
            daysPerWeek: Int,
        ): ModernResult<List<WorkoutTemplate>> =
            try {
                logger.d("UnifiedWorkoutDataCenter", "开始推荐训练模板: $goal, $difficulty")

                // 1. 获取推荐的Template ID列表
                val recommendResult = recommendTemplatesCombination(goal, difficulty, daysPerWeek)

                when (recommendResult) {
                    is ModernResult.Success -> {
                        val templateIds = recommendResult.data
                        val templates = mutableListOf<WorkoutTemplate>()

                        // 2. 获取推荐的Template详情
                        for (templateId in templateIds) {
                            when (val templateResult = getTemplate(templateId)) {
                                is ModernResult.Success -> templates.add(templateResult.data)
                                is ModernResult.Error -> {
                                    logger.w("UnifiedWorkoutDataCenter", "获取推荐模板失败: $templateId")
                                }

                                is ModernResult.Loading -> continue
                            }
                        }

                        logger.d("UnifiedWorkoutDataCenter", "成功推荐${templates.size}个训练模板")
                        ModernResult.Success(templates)
                    }

                    is ModernResult.Error -> {
                        logger.w("UnifiedWorkoutDataCenter", "推荐训练模板失败")
                        recommendResult.let { ModernResult.Error(it.error) }
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            createError("recommendWorkoutTemplates", "推荐训练模板超时"),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "推荐训练模板异常", e)
                ModernResult.Error(
                    createError("recommendWorkoutTemplates", "推荐训练模板失败: ${e.message ?: "未知错误"}"),
                )
            }

        /**
         * 获取训练数据的统一JSON格式
         *
         * 为UI组件提供统一的JSON数据格式，使用WorkoutJsonExporter
         *
         * @param dataType 数据类型（template/session/plan）
         * @param dataId 数据ID
         * @return JSON格式的训练数据
         */
        suspend fun getWorkoutDataAsJson(
            dataType: String,
            dataId: String,
        ): ModernResult<String> =
            try {
                logger.d("UnifiedWorkoutDataCenter", "获取训练数据JSON: $dataType, $dataId")

                when (dataType.lowercase()) {
                    "template" -> {
                        when (val templateResult = getTemplate(dataId)) {
                            is ModernResult.Success -> {
                                workoutJsonExporter.exportTemplateAsJson(templateResult.data)
                            }

                            is ModernResult.Error -> templateResult.let { ModernResult.Error(it.error) }
                            is ModernResult.Loading ->
                                ModernResult.Error(
                                    createError("getWorkoutDataAsJson", "获取模板数据超时"),
                                )
                        }
                    }

                    "session" -> {
                        // TODO: 实现Session获取逻辑
                        ModernResult.Error(
                            createError("getWorkoutDataAsJson", "Session JSON导出功能待实现"),
                        )
                    }

                    "plan" -> {
                        // TODO: 实现Plan获取逻辑
                        ModernResult.Error(
                            createError("getWorkoutDataAsJson", "Plan JSON导出功能待实现"),
                        )
                    }

                    else -> {
                        ModernResult.Error(
                            createError("getWorkoutDataAsJson", "不支持的数据类型: $dataType"),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "获取训练数据JSON失败", e)
                ModernResult.Error(
                    createError("getWorkoutDataAsJson", "获取训练数据JSON失败: ${e.message ?: "未知错误"}"),
                )
            }

        /**
         * 批量获取训练数据的JSON格式
         *
         * 为UI组件提供批量JSON数据导出
         *
         * @param dataType 数据类型
         * @param dataIds 数据ID列表
         * @return JSON数组字符串
         */
        suspend fun getBatchWorkoutDataAsJson(
            dataType: String,
            dataIds: List<String>,
        ): ModernResult<String> {
            return try {
                logger.d("UnifiedWorkoutDataCenter", "批量获取训练数据JSON: $dataType, ${dataIds.size}项")

                val dataList = mutableListOf<Any>()

                for (dataId in dataIds) {
                    when (dataType.lowercase()) {
                        "template" -> {
                            when (val result = getTemplate(dataId)) {
                                is ModernResult.Success -> dataList.add(result.data)
                                is ModernResult.Error -> {
                                    logger.w("UnifiedWorkoutDataCenter", "获取模板失败: $dataId")
                                }

                                is ModernResult.Loading -> continue
                            }
                        }

                        "session" -> {
                            // TODO: 实现Session批量获取逻辑
                            logger.w("UnifiedWorkoutDataCenter", "Session批量获取功能待实现: $dataId")
                        }

                        "plan" -> {
                            // TODO: 实现Plan批量获取逻辑
                            logger.w("UnifiedWorkoutDataCenter", "Plan批量获取功能待实现: $dataId")
                        }

                        else -> {
                            return ModernResult.Error(
                                createError("getBatchWorkoutDataAsJson", "不支持的数据类型: $dataType"),
                            )
                        }
                    }
                }

                workoutJsonExporter.exportBatchAsJson(dataList, dataType)
            } catch (e: Exception) {
                logger.e("UnifiedWorkoutDataCenter", "批量获取训练数据JSON失败", e)
                ModernResult.Error(
                    createError("getBatchWorkoutDataAsJson", "批量获取训练数据JSON失败: ${e.message ?: "未知错误"}"),
                )
            }
        }

        // ===== 私有辅助方法 =====

        private fun createError(
            operation: String,
            message: String,
        ) = com.example.gymbro.core.error.types.data.DataErrors.DataError.create(
            operationName = "UnifiedWorkoutDataCenter.$operation",
            message = UiText.DynamicString(message),
        )
    }
