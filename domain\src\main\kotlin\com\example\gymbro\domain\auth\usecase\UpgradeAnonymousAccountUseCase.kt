package com.example.gymbro.domain.auth.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.model.auth.Credentials
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first

/**
 * 匿名账户升级UseCase
 * 专门处理匿名账户升级为正式账户的业务逻辑
 */
@Singleton
class UpgradeAnonymousAccountUseCase
    @Inject
    constructor(
        private val authRepository: AuthRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<UpgradeAnonymousAccountUseCase.Params, AuthUser>(dispatcher, logger) {
        override suspend fun execute(parameters: Params): ModernResult<AuthUser> =
            upgradeAnonymousAccount(parameters)

        private suspend fun upgradeAnonymousAccount(params: Params): ModernResult<AuthUser> {
            logger.d("开始升级匿名账户")

            // 先执行升级操作
            val upgradeResult =
                when (params) {
                    is Params.ToEmail -> {
                        val credentials = Credentials.EmailPassword(params.email, params.password)
                        authRepository.upgradeAnonymousUser("", credentials)
                    }

                    is Params.ToGoogle -> {
                        val credentials = Credentials.ThirdParty("google", params.idToken)
                        authRepository.upgradeAnonymousUser("", credentials)
                    }
                }

            // 如果升级成功，获取当前用户信息
            return when (upgradeResult) {
                is ModernResult.Success -> {
                    // 升级成功后获取当前用户
                    val userResult = authRepository.getCurrentUser().first()
                    when (userResult) {
                        is ModernResult.Success -> {
                            val user = userResult.data
                            if (user != null) {
                                ModernResult.Success(user)
                            } else {
                                ModernResult.Error(
                                    ModernDataError(
                                        operationName = "upgradeAnonymousAccount",
                                        errorType = GlobalErrorType.System.General,
                                        uiMessage =
                                            UiText
                                                .DynamicString("升级成功但获取用户信息失败"),
                                    ),
                                )
                            }
                        }

                        is ModernResult.Error -> ModernResult.Error(userResult.error)
                        is ModernResult.Loading -> ModernResult.Loading
                    }
                }

                is ModernResult.Error -> ModernResult.Error(upgradeResult.error)
                is ModernResult.Loading -> ModernResult.Loading
            }
        }

        sealed class Params {
            data class ToEmail(
                val email: String,
                val password: String,
            ) : Params()

            data class ToGoogle(
                val idToken: String,
            ) : Params()
        }
    }
