package com.example.gymbro.features.profile.internal.presentation.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.profile.usecase.UpdateUserProfileUseCase
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.Effect
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.Intent
import com.example.gymbro.features.profile.internal.presentation.contract.ProfileContract.State
import com.example.gymbro.features.profile.internal.presentation.effect.ProfileEffectHandler
import com.example.gymbro.features.profile.internal.presentation.effect.toDomain
import com.example.gymbro.features.profile.internal.presentation.reducer.ProfileReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

@HiltViewModel
internal class ModernUserProfileViewModelMVI
    @Inject
    constructor(
        private val reducer: ProfileReducer,
        private val effectHandler: ProfileEffectHandler,
        // 🎯 架构迁移：使用 ProfileAutoSaveAdapter 替代 ProfileAutoSaveService
        private val profileAutoSaveAdapter: com.example.gymbro.data.autosave.adapter.ProfileAutoSaveAdapter,
        private val updateUserProfileUseCase: UpdateUserProfileUseCase, // 用于保存测试数据到Repository
        private val logger: Logger,
    ) : ViewModel() {
        // === MVI核心组件 ===

        private val _state = MutableStateFlow(State())
        val state: StateFlow<State> = _state.asStateFlow()

        private val _effects = Channel<Effect>(Channel.UNLIMITED)
        val effects: Flow<Effect> = _effects.receiveAsFlow()

        // === 自动保存会话管理 ===
        private var autoSaveSessionId: String? = null

        // === 初始化 ===

        init {
            logger.d("ModernUserProfileViewModelMVI", "ViewModel初始化，开始加载用户资料")
            dispatch(Intent.LoadUserProfile)
            initializeAutoSave()
            // 🔥 移除测试数据初始化，确保数据来自用户真实输入
        }

        // 🔥 已移除 initializeTestData 方法
        // 确保所有用户数据来自真实的用户输入，而不是硬编码的测试数据

        /**
         * 初始化自动保存功能
         */
        private fun initializeAutoSave() {
            viewModelScope.launch {
                try {
                    logger.d("ModernUserProfileViewModelMVI", "初始化Profile自动保存")

                    // 使用当前用户ID创建自动保存会话
                    // 注意：这里假设用户已登录，实际项目中可能需要从认证状态获取用户ID
                    val userId = "current_user" // TODO: 从认证状态获取实际用户ID

                    val result = profileAutoSaveAdapter.createAutoSave(userId, viewModelScope)
                    when (result) {
                        is com.example.gymbro.core.error.types.ModernResult.Success -> {
                            autoSaveSessionId = result.data
                            logger.d("ModernUserProfileViewModelMVI", "自动保存会话创建成功: ${result.data}")

                            // 启动自动保存
                            profileAutoSaveAdapter.startAutoSave(result.data, userId)
                        }

                        is com.example.gymbro.core.error.types.ModernResult.Error -> {
                            logger.e("ModernUserProfileViewModelMVI", "创建自动保存会话失败", result.error.cause)
                            // 降级到原有保存逻辑，不影响正常功能
                        }

                        is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                            logger.d("ModernUserProfileViewModelMVI", "自动保存会话创建中...")
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "初始化自动保存失败")
                    // 降级到原有保存逻辑，不影响正常功能
                }
            }
        }

        // === 公共API ===

        internal fun dispatch(intent: Intent) {
            viewModelScope.launch {
                try {
                    logger.d("ModernUserProfileViewModelMVI", "处理Intent: ${intent::class.simpleName}")

                    val currentState = _state.value

                    // 1. 通过Reducer计算新状态和Effect
                    val reduceResult = reducer.reduce(intent, currentState)
                    val newState = reduceResult.newState

                    // 2. 更新状态
                    if (newState != currentState) {
                        _state.value = newState
                        logger.d("ModernUserProfileViewModelMVI", "状态已更新")

                        // 3. 检查是否需要触发自动保存
                        checkAndTriggerAutoSave(intent, currentState, newState)
                    }

                    // 3. 处理Effect - 转发给EffectHandler而不是自己处理
                    reduceResult.effects.forEach { effect ->
                        logger.d("ModernUserProfileViewModelMVI", "🔥 分发Effect: ${effect::class.simpleName}")
                        handleEffect(effect)
                    }

                    // 4. 处理Intent副作用 (保持兼容性)
                    effectHandler.handle(intent, newState, viewModelScope) { effectIntent ->
                        dispatch(effectIntent)
                    }
                } catch (e: Exception) {
                    logger.e(e, "处理Intent时发生异常: ${intent::class.simpleName}")
                    emitEffect(
                        Effect.ShowSnackbar(
                            UiText
                                .DynamicString("操作失败"),
                        ),
                    )
                }
            }
        }

        // === 便捷方法 (为UI层提供简化的API) ===

        internal fun enterEditMode() {
            dispatch(Intent.EnterEditMode)
        }

        internal fun exitEditMode() {
            dispatch(Intent.ExitEditMode)
        }

        // 🔥 移除手动保存方法，改为自动保存

        internal fun updateDisplayName(displayName: String) {
            dispatch(Intent.UpdateDisplayName(displayName))
        }

        internal fun updateUsername(username: String) {
            dispatch(Intent.UpdateUsername(username))
        }

        internal fun updateBio(bio: String) {
            dispatch(Intent.UpdateBio(bio))
        }

        internal fun updateEmail(email: String) {
            dispatch(Intent.UpdateEmail(email))
        }

        internal fun updatePhoneNumber(phoneNumber: String) {
            dispatch(Intent.UpdatePhoneNumber(phoneNumber))
        }

        internal fun updateHeight(height: Float) {
            dispatch(Intent.UpdateHeight(height))
        }

        internal fun updateWeight(weight: Float) {
            dispatch(Intent.UpdateWeight(weight))
        }

        internal fun clearError() {
            dispatch(Intent.ClearError)
        }

        internal fun clearSaveSuccess() {
            dispatch(Intent.ClearSaveSuccess)
        }

        internal fun retryLoad() {
            dispatch(Intent.RetryLoad)
        }

        // === 通知设置API ===

        internal fun updateWorkoutReminderEnabled(enabled: Boolean) {
            dispatch(Intent.UpdateWorkoutReminder(enabled))
        }

        internal fun updateFriendsActivityEnabled(enabled: Boolean) {
            dispatch(Intent.UpdateFriendsActivity(enabled))
        }

        // === 预留：Coach模块交互API ===

        internal fun generateAiContext() {
            logger.d("ModernUserProfileViewModelMVI", "开始生成AI上下文")
            dispatch(Intent.GenerateAiContext)
        }

        internal fun refreshAiContext() {
            logger.d("ModernUserProfileViewModelMVI", "刷新AI上下文")
            dispatch(Intent.RefreshAiContext)
        }

        internal fun getCurrentAiContext(): ProfileContract.UserAiContext? = _state.value.userContextForAi

        /**
         * 检查并触发自动保存
         * 当Profile相关字段更新时自动触发保存
         */
        private fun checkAndTriggerAutoSave(intent: Intent, oldState: State, newState: State) {
            // 只有在编辑模式下且Profile数据发生变化时才触发自动保存
            if (!newState.isEditMode || newState.editableProfile == null) {
                return
            }

            // 检查是否是Profile字段更新的Intent
            val shouldTriggerAutoSave = when (intent) {
                is Intent.UpdateDisplayName,
                is Intent.UpdateUsername,
                is Intent.UpdateBio,
                is Intent.UpdateEmail,
                is Intent.UpdatePhoneNumber,
                is Intent.UpdateHeight,
                is Intent.UpdateWeight,
                is Intent.UpdateGender,
                is Intent.UpdateFitnessLevel,
                is Intent.UpdateFitnessGoals,
                is Intent.UpdateWorkoutDays,
                // 🔥 添加确认编辑Intent，这些也会更新Profile数据
                is Intent.ConfirmDisplayName,
                is Intent.ConfirmUsername,
                is Intent.ConfirmEmail,
                is Intent.ConfirmHeight,
                is Intent.ConfirmWeight,
                is Intent.ConfirmGender,
                is Intent.ConfirmFitnessLevel,
                is Intent.ConfirmGoal,
                is Intent.ConfirmWorkoutDays,
                -> true

                else -> false
            }

            if (shouldTriggerAutoSave && oldState.editableProfile != newState.editableProfile) {
                triggerAutoSave()
            }
        }

        /**
         * 触发自动保存Profile数据
         * 当Profile数据更新时调用此方法
         *
         * 🎯 架构迁移：使用 ProfileAutoSaveAdapter 替代 ProfileAutoSaveService
         */
        private fun triggerAutoSave() {
            val sessionId = autoSaveSessionId
            val currentProfile = _state.value.editableProfile

            if (sessionId != null && currentProfile != null) {
                try {
                    logger.d("ModernUserProfileViewModelMVI", "触发Profile自动保存")
                    val domainProfile = currentProfile.toDomain()
                    profileAutoSaveAdapter.updateProfile(sessionId, domainProfile)

                    // 触发保存成功状态，保持UI一致性
                    dispatch(Intent.ProfileSaveSuccess)
                } catch (e: Exception) {
                    logger.e(e, "触发自动保存失败")
                    // 降级到原有保存逻辑，不影响用户体验
                    dispatch(Intent.ProfileSaveError(UiText.DynamicString("自动保存失败")))
                }
            } else {
                logger.w("ModernUserProfileViewModelMVI", "自动保存会话未初始化或Profile数据为空")
            }
        }

        // === 内部方法 ===

        internal fun emitEffect(effect: Effect) {
            viewModelScope.launch {
                _effects.send(effect)
            }
        }

        // === 🔥 核心修复：Effect转发方法 ===

        private suspend fun handleEffect(effect: Effect) {
            logger.d("ModernUserProfileViewModelMVI", "转发Effect到EffectHandler: ${effect::class.simpleName}")
            // ViewModel只负责转发，不处理具体业务逻辑
            effectHandler.handleEffect(effect) { resultIntent ->
                dispatch(resultIntent)
            }
        }

        // === 调试和监控 ===

        internal fun getStateSummary(): String {
            val state = _state.value
            return buildString {
                append("ProfileState(")
                append("loading=${state.isLoading}, ")
                append("editing=${state.isEditMode}, ")
                append("saving=${state.isSaving}, ")
                append("hasProfile=${state.editableProfile != null}, ")
                append("hasError=${state.error != null}, ")
                append("hasAiContext=${state.userContextForAi != null}")
                append(")")
            }
        }

        override fun onCleared() {
            super.onCleared()

            // 清理自动保存会话
            autoSaveSessionId?.let { sessionId ->
                viewModelScope.launch {
                    try {
                        logger.d("ModernUserProfileViewModelMVI", "清理自动保存会话: $sessionId")
                        profileAutoSaveAdapter.stopAutoSave(sessionId)
                    } catch (e: Exception) {
                        logger.e(e, "清理自动保存会话失败")
                    }
                }
            }

            logger.d("ModernUserProfileViewModelMVI", "ViewModel清理完成")
        }
    }

// === 扩展函数 ===

/**
 * 检查状态是否可以进行编辑操作
 */
internal val ProfileContract.State.canEdit: Boolean
    get() = !isLoading && !isSaving && editableProfile != null

/**
 * 检查是否有数据
 */
internal val ProfileContract.State.hasData: Boolean
    get() = editableProfile != null

/**
 * 检查是否处于错误状态
 */
internal val ProfileContract.State.hasError: Boolean
    get() = error != null || aiContextError != null
