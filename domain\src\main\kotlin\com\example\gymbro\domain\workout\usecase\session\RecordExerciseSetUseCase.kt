package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.ExerciseSet
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.repository.SessionRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * 记录训练中的单组动作数据用例
 *
 * 业务场景：记录训练中的单组动作数据
 *
 * 功能说明：
 * - 获取训练会话
 * - 查找对应的动作
 * - 添加训练组数据
 * - 更新并保存会话
 *
 * @param activeWorkoutRepository 训练会话仓库
 * @param dispatcher IO调度器
 * @param logger 日志记录器
 */
@Singleton
class RecordExerciseSetUseCase
    @Inject
    constructor(
        private val sessionRepository: SessionRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<RecordExerciseSetUseCase.Params, Unit>(dispatcher, logger) {
        /**
         * 参数数据类
         *
         * @property sessionId 会话ID
         * @property performedExerciseId 执行的动作ID
         * @property set 训练组数据
         */
        data class Params(
            val sessionId: String,
            val performedExerciseId: String,
            val set: ExerciseSet,
        )

        /**
         * 执行记录训练组数据逻辑
         *
         * @param parameters 包含会话ID、动作ID和训练组数据的参数
         * @return 操作结果
         */
        override suspend fun execute(parameters: Params): ModernResult<Unit> {
            logger.d(
                "记录训练组数据: sessionId=${parameters.sessionId}, exerciseId=${parameters.performedExerciseId}",
            )

            return try {
                // 验证参数
                if (parameters.sessionId.isBlank()) {
                    logger.w("会话ID为空")
                    return ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "recordExerciseSet",
                            message = UiText.DynamicString("会话ID不能为空"),
                            metadataMap = mapOf<String, Any>("sessionId" to parameters.sessionId),
                        ),
                    )
                }

                if (parameters.performedExerciseId.isBlank()) {
                    logger.w("动作ID为空")
                    return ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "recordExerciseSet",
                            message = UiText.DynamicString("动作ID不能为空"),
                            metadataMap =
                                mapOf<String, Any>(
                                    "sessionId" to parameters.sessionId,
                                    "exerciseId" to parameters.performedExerciseId,
                                ),
                        ),
                    )
                }

                // TODO: 需要适配新的SessionRepository接口
                // 暂时返回错误
                return ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "recordExerciseSet",
                        message = UiText.DynamicString("功能暂未实现，等待接口适配"),
                        metadataMap = mapOf<String, Any>("sessionId" to parameters.sessionId),
                    ),
                )
            /*
            val sessionResult = sessionRepository.getSession(parameters.sessionId).first()
            if (sessionResult !is ModernResult.Success) {
                logger.e("获取训练会话失败: $sessionResult")
                return sessionResult.map { }
            }

            val session = sessionResult.data
            if (session == null) {
                logger.w("训练会话不存在: sessionId=${parameters.sessionId}")
                return ModernResult.error(
                    DataErrors.DataError.notFound(
                        operationName = "recordExerciseSet",
                        message = UiText.DynamicString("训练会话不存在"),
                        entityType = "WorkoutSession",
                        entityId = parameters.sessionId,
                    ),
                )
            }

            logger.d("获取训练会话成功: sessionId=${session.id}")

            // 2. 查找对应的动作
            val performedExercise = session.exercises.find { it.id == parameters.performedExerciseId }
            if (performedExercise == null) {
                logger.w("动作不存在: exerciseId=${parameters.performedExerciseId}")
                return ModernResult.error(
                    DataErrors.DataError.notFound(
                        operationName = "recordExerciseSet",
                        message = UiText.DynamicString("动作不存在"),
                        entityType = "PerformedExercise",
                        entityId = parameters.performedExerciseId,
                    ),
                )
            }

            logger.d("找到对应动作: exerciseId=${performedExercise.id}")

            // 3. 添加训练组数据
            val updatedSets = performedExercise.sets.toMutableList()
            val setWithSessionInfo =
                parameters.set.copy(
                    sessionId = parameters.sessionId,
                    exerciseId = parameters.performedExerciseId,
                )
            updatedSets.add(setWithSessionInfo)

            val updatedExercise = performedExercise.copy(sets = updatedSets)
            val updatedExercises =
                session.exercises.map {
                    if (it.id == parameters.performedExerciseId) updatedExercise else it
                }

            // 4. 更新会话
            val updatedSession =
                session.copy(
                    exercises = updatedExercises,
                    lastModified = System.currentTimeMillis(),
                )

            logger.d("准备保存更新后的会话")

            // TODO: 需要适配新的SessionRepository接口
            // 暂时返回成功
            ModernResult.Success(Unit)
             */
            } catch (e: Exception) {
                logger.e(e, "记录训练组数据失败")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "recordExerciseSet",
                        message = UiText.DynamicString("记录训练数据失败: ${e.message}"),
                        metadataMap =
                            mapOf<String, Any>(
                                "sessionId" to parameters.sessionId,
                                "exerciseId" to parameters.performedExerciseId,
                            ),
                        recoverable = true,
                    ),
                )
            }
        }
    }
