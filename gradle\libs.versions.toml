[versions]
# Core & Build Tools - 配置为 detekt 1.23.8 官方兼容版本
agp = "8.8.1"               # detekt 1.23.8 官方支持版本
kotlin = "2.0.21"           # detekt 1.23.8 官方支持版本
ksp = "2.0.21-1.0.25"       # 与 Kotlin 2.0.21 兼容的 KSP 版本
compileSdk = "35"
minSdk = "26"
targetSdk = "34"
jvmTarget = "17"
desugarJdkLibsVersion = "2.0.4"
kotlinxDatetime = "0.6.1"   # 小版本升级
gradleEnterprise = "3.16.2" # 添加版本号

# Protobuf - 解决依赖冲突 (统一版本管理)
protobuf = "3.25.3"           # 统一 protobuf 版本

# Jetpack Core & AppCompat
coreKtx = "1.13.1"
appcompat = "1.7.0"
activity = "1.9.0"
fragment = "1.7.1"
constraintlayout = "2.1.4" # 2.2.x 仍为 alpha/beta 版，2.1.4 是最新的稳定版
lifecycle = "2.8.0"
navigation = "2.7.7"
room = "2.6.1"
datastore = "1.1.1"
workManager = "2.9.0"

# Compose
compose-bom = "2025.01.01"      # 升级到最新稳定版，映射到 Compose 1.8.2
accompanist = "0.37.0"          # 升级到与 Compose 1.8 兼容的版本

# Material Design
material = "1.12.0"

# Google Play Services & Maps
play-services-auth = "21.2.0"
play-services-location = "21.3.0"
play-services-maps = "18.2.0"
maps-compose = "6.6.0"          # 升级到最新稳定版，修复 Android 14+ 兼容问题
googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"

# Firebase
firebase-bom = "33.1.0"           # 最新稳定版 Firebase BOM
firebase-crashlytics-gradle = "3.0.1" # 最新版 Crashlytics Gradle 插件
google-services-plugin = "4.4.2"

# Networking
retrofit = "2.11.0"
okhttp = "4.12.0"
gson = "2.10.1"
moshi = "1.15.1"

# Dependency Injection (Hilt)
dagger = { group = "com.google.dagger", name = "dagger", version.ref = "hilt" }
hilt = "2.53"               # 升级到最新版本以支持新 Kotlin
hiltNavigationCompose = "1.2.0"
hiltCompiler = "1.2.0"
hiltWork = "1.2.0"

# Coroutines & Serialization
coroutines = "1.10.1"           # 升级到最新版本，提升性能
coroutinesPlayServices = "1.10.1" # 保持版本一致
kotlinxSerialization = "1.8.0"   # 升级序列化库
immutableCollections = "0.3.7"  # kotlinx-collections-immutable for MVI performance

# Image Loading
glide = "4.16.0"              # Glide 5 仍是预发布版, 4.16.0 是最新的稳定版
glide-compose = "1.0.0-beta01"
coil = "2.6.0"

# Credentials & Identity
credentials = "1.5.0"
googleid = "1.1.0"

# Utilities
timber = "5.0.1"
circleimageview = "3.1.0"

# Build Logic Dependencies
kotlin-gradlePlugin = "2.0.21"     # 匹配 Kotlin 版本
ksp-gradlePlugin = "2.0.21-1.0.25"  # 匹配 KSP 版本
hilt-gradlePlugin = "2.53"          # 兼容 Kotlin 2.0.21

# Testing
junit = "4.13.2"
junitVersion = "1.1.5"        # androidx.test.ext:junit
espressoCore = "3.5.1"
monitor = "1.7.1"             # androidx.test:monitor
billingKtx = "7.0.0"
mockk = "1.13.11"
mockito-core = "5.12.0"
mockito-kotlin = "5.3.1"
robolectric = "4.12.1"
androidx-test-core = "1.5.0"
androidx-arch-core-testing = "2.2.0"  # InstantTaskExecutorRule
turbine = "1.1.0"             # Flow testing
ktlint-gradle = "12.1.1"
detekt = "1.23.8"

# CI/CD & Quality Tools
jacoco = "0.8.12"
owasp-dependency-check = "11.1.0"
sonarqube = "5.1.0.4882"
danger-kotlin = "3.0.0"

# Machine Learning & AI - 完全迁移到LiteRT 1.3.0
onnxruntime = "1.17.1"             # ONNX Runtime for cross-platform ML
truth = "1.4.4"                    # Google Truth testing library
litert = "1.3.0"                   # LiteRT (TensorFlow Lite 官方继任者) - 稳定版

# Markdown & Rich Text - 富文本功能
markwon = "4.6.2"                  # Markwon Markdown渲染库
prism4j = "2.0.0"                  # Prism4j 代码语法高亮

[libraries]
# Core & Build Tools
desugarJdkLibs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "desugarJdkLibsVersion" }
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib", version.ref = "kotlin" }
kotlin-reflect = { group = "org.jetbrains.kotlin", name = "kotlin-reflect", version.ref = "kotlin" }

# Protobuf Libraries
protobuf-javalite = { group = "com.google.protobuf", name = "protobuf-javalite", version.ref = "protobuf" }
protobuf-java = { group = "com.google.protobuf", name = "protobuf-java", version.ref = "protobuf" }

# Jetpack Core & AppCompat
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-activity-ktx = { group = "androidx.activity", name = "activity-ktx", version.ref = "activity" }
androidx-fragment-ktx = { group = "androidx.fragment", name = "fragment-ktx", version.ref = "fragment" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation" }
androidx-work-runtime-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "workManager" }
androidx-startup-runtime = { group = "androidx.startup", name = "startup-runtime", version = "1.1.1" }

# Room
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-paging = { group = "androidx.room", name = "room-paging", version.ref = "room" }

# DataStore
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastore" }
androidx-datastore-core = { group = "androidx.datastore", name = "datastore-core", version.ref = "datastore" }

# Compose - BOM controls versions unless overridden
compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "compose-bom" }
compose-ui = { group = "androidx.compose.ui", name = "ui" }
compose-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
compose-runtime = { group = "androidx.compose.runtime", name = "runtime" }
compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
compose-foundation-layout = { group = "androidx.compose.foundation", name = "foundation-layout"}
compose-material3 = { group = "androidx.compose.material3", name = "material3" }
compose-material-icons-core = { group = "androidx.compose.material", name = "material-icons-core" }
compose-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended" }
compose-activity = { group = "androidx.activity", name = "activity-compose", version.ref = "activity" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycle" }
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycle" }

# Accompanist Libraries (升级到与 Compose 1.8 兼容的版本)
accompanist-systemuicontroller = { group = "com.google.accompanist", name = "accompanist-systemuicontroller", version.ref = "accompanist" }
accompanist-permissions = { group = "com.google.accompanist", name = "accompanist-permissions", version.ref = "accompanist" }
accompanist-swiperefresh = { group = "com.google.accompanist", name = "accompanist-swiperefresh", version.ref = "accompanist" }

# Material Design
material = { group = "com.google.android.material", name = "material", version.ref = "material" }

# Google Play Services & Maps
play-services-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "play-services-auth"}
play-services-location = { group = "com.google.android.gms", name = "play-services-location", version.ref = "play-services-location" }
play-services-maps = { group = "com.google.android.gms", name = "play-services-maps", version.ref = "play-services-maps" }
maps-compose = { group = "com.google.maps.android", name = "maps-compose", version.ref = "maps-compose" }

# Firebase - BOM controls versions
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase-bom" }
firebase-analytics-ktx = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-auth-ktx = { group = "com.google.firebase", name = "firebase-auth-ktx" }
firebase-crashlytics-ktx = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-firestore-ktx = { group = "com.google.firebase", name = "firebase-firestore-ktx" }
firebase-database-ktx = { group = "com.google.firebase", name = "firebase-database-ktx" }
firebase-remoteconfig-ktx = { group = "com.google.firebase", name = "firebase-config-ktx" }
firebase-storage-ktx = { group = "com.google.firebase", name = "firebase-storage-ktx" }

# Networking
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
retrofit-converter-scalars = { group = "com.squareup.retrofit2", name = "converter-scalars", version.ref = "retrofit" }
retrofit-kotlinx-serialization = { group = "com.jakewharton.retrofit", name = "retrofit2-kotlinx-serialization-converter", version = "1.0.0" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }
moshi-kotlin = { group = "com.squareup.moshi", name = "moshi-kotlin", version.ref = "moshi" }
moshi-adapters = { group = "com.squareup.moshi", name = "moshi-adapters", version.ref = "moshi" }

# Dependency Injection (Hilt)
dagger = { group = "com.google.dagger", name = "dagger", version.ref = "hilt" }
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hiltNavigationCompose" }
androidx-hilt-compiler = { group = "androidx.hilt", name = "hilt-compiler", version.ref = "hiltCompiler" }
androidx-hilt-work = { group = "androidx.hilt", name = "hilt-work", version.ref = "hiltWork" }

# Coroutines & Serialization
kotlinx-coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "coroutines" }
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutines" }
kotlinx-coroutines-play-services = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-play-services", version.ref = "coroutinesPlayServices" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerialization" }
kotlinx-collections-immutable = { group = "org.jetbrains.kotlinx", name = "kotlinx-collections-immutable", version.ref = "immutableCollections" }

# Image Loading
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }
glide-compose = { group = "com.github.bumptech.glide", name = "compose", version.ref = "glide-compose" }
glide-ksp = { group = "com.github.bumptech.glide", name = "ksp", version.ref = "glide" }
coil-compose = { group = "io.coil-kt", name = "coil-compose", version.ref = "coil" }
coil-gif = { group = "io.coil-kt", name = "coil-gif", version.ref = "coil" }
coil-svg = { group = "io.coil-kt", name = "coil-svg", version.ref = "coil" }

# Credentials & Identity
androidx-credentials = { group = "androidx.credentials", name = "credentials", version.ref = "credentials" }
androidx-credentials-playservices = { group = "androidx.credentials", name = "credentials-play-services-auth", version.ref = "credentials" }
googleid = { group = "com.google.android.libraries.identity.googleid", name = "googleid", version.ref = "googleid" }

# Utilities
timber = { group = "com.jakewharton.timber", name = "timber", version.ref = "timber" }
circleimageview = { group = "de.hdodenhof", name = "circleimageview", version.ref = "circleimageview" }
kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "kotlinxDatetime" }

# Testing
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-monitor = { group = "androidx.test", name = "monitor", version.ref = "monitor" }
androidx-room-testing = { group = "androidx.room", name = "room-testing", version.ref = "room" }
kotlinx-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "coroutines" }
mockk = { group = "io.mockk", name = "mockk", version.ref = "mockk" }
mockito-core = { group = "org.mockito", name = "mockito-core", version.ref = "mockito-core" }
mockito-kotlin = { group = "org.mockito.kotlin", name = "mockito-kotlin", version.ref = "mockito-kotlin" }
robolectric = { group = "org.robolectric", name = "robolectric", version.ref = "robolectric" }
androidx-test-core = { group = "androidx.test", name = "core", version.ref = "androidx-test-core" }
androidx-arch-core-testing = { group = "androidx.arch.core", name = "core-testing", version.ref = "androidx-arch-core-testing" }
turbine = { group = "app.cash.turbine", name = "turbine", version.ref = "turbine" }

# Billing Client
androidx-billing-ktx = { group = "com.android.billingclient", name = "billing-ktx", version.ref = "billingKtx" }

# Machine Learning & AI - 完全基于LiteRT 1.3.0 (移除所有旧TensorFlow Lite依赖)
onnxruntime-android = { group = "com.microsoft.onnxruntime", name = "onnxruntime-android", version.ref = "onnxruntime" }

# LiteRT 1.3.0 官方推荐依赖 (完全替代TensorFlow Lite)
litert = { group = "com.google.ai.edge.litert", name = "litert", version.ref = "litert" }
litert-support = { group = "com.google.ai.edge.litert", name = "litert-support", version.ref = "litert" }

# Markdown & Rich Text Libraries - 富文本功能
markwon-core = { group = "io.noties.markwon", name = "core", version.ref = "markwon" }
markwon-syntax-highlight = { group = "io.noties.markwon", name = "syntax-highlight", version.ref = "markwon" }
prism4j-core = { group = "io.noties", name = "prism4j", version.ref = "prism4j" }

# Testing Libraries
junit4 = { group = "junit", name = "junit", version.ref = "junit" }
truth = { group = "com.google.truth", name = "truth", version.ref = "truth" }
androidx-test-ext = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-test-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }

# Build Logic Dependencies
android-gradlePlugin = { group = "com.android.tools.build", name = "gradle", version.ref = "agp" }
kotlin-gradlePlugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin-gradlePlugin" }
ksp-gradlePlugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp-gradlePlugin" }
hilt-gradlePlugin = { group = "com.google.dagger", name = "hilt-android-gradle-plugin", version.ref = "hilt-gradlePlugin" }

# Hilt Testing
hilt-android-testing = { group = "com.google.dagger", name = "hilt-android-testing", version.ref = "hilt" }
kotlin-test = { group = "org.jetbrains.kotlin", name = "kotlin-test", version.ref = "kotlin" }

[bundles]
compose-core = [
    "compose-ui",
    "compose-ui-graphics",
    "compose-foundation",
    "compose-foundation-layout",
    "compose-ui-tooling-preview",
    "compose-material3",
    "compose-material-icons-extended",
    "compose-activity",
    "androidx-lifecycle-viewmodel-compose",
    "androidx-lifecycle-runtime-compose",
]
compose-debug = [
    "compose-ui-tooling",
    "compose-ui-test-manifest",
]
compose-test = [
    "compose-ui-test-junit4",
]

testing-android = [
    "androidx-junit",
    "androidx-espresso-core",
    "androidx-monitor",
    "androidx-test-core",
]
testing-unit = [
    "junit",
    "kotlinx-coroutines-test",
    "mockk",
    "mockito-core",
    "mockito-kotlin",
    "robolectric",
    "androidx-arch-core-testing",
    "turbine",
]

[plugins]
# Core Android & Kotlin
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

# Build & Code Quality
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
ktlint-gradle = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "ktlint-gradle" }
owasp-dependency-check = { id = "org.owasp.dependencycheck", version.ref = "owasp-dependency-check" }
sonarqube = { id = "org.sonarqube", version.ref = "sonarqube" }
danger-kotlin = { id = "com.github.detekt.danger-kotlin", version.ref = "danger-kotlin" }

# Firebase & Maps
google-services = { id = "com.google.gms.google-services", version.ref = "google-services-plugin" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebase-crashlytics-gradle" }
maps-platform-secrets = { id = "com.google.android.libraries.mapsplatform.secrets-gradle-plugin", version.ref = "googleAndroidLibrariesMapsplatformSecretsGradlePlugin" }

gradle-enterprise = { id = "com.gradle.enterprise", version.ref = "gradleEnterprise" }
