package com.example.gymbro.data.repository.user

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.local.dao.user.UserProfileDao
import com.example.gymbro.data.mapper.user.toDomain
import com.example.gymbro.data.mapper.user.toDto
import com.example.gymbro.data.mapper.user.toEntity
import com.example.gymbro.data.network.api.UserProfileApiService
import com.example.gymbro.domain.profile.model.settings.UserPreferences
import com.example.gymbro.domain.profile.model.user.User
import com.example.gymbro.domain.profile.model.user.UserFitnessProfile
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.model.user.UserStats
import com.example.gymbro.domain.profile.repository.user.UserAggregateRepository
import com.example.gymbro.domain.profile.repository.user.UserBatchUpdate
import com.example.gymbro.domain.profile.repository.user.UserCompleteInfo
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

// ❌ RAG集成扩展函数已删除 - Profile模块不再需要生成显示名称用于AI上下文
// 这些扩展函数将移动到Coach模块中，供AI上下文生成使用

/**
 * UserProfileRepository接口的实现类
 *
 * 提供用户资料相关的数据访问功能
 * 使用UserProfileDao进行真实的数据库操作，符合Clean Architecture原则
 */
class UserProfileRepositoryImpl
    @Inject
    constructor(
        private val userProfileDao: UserProfileDao,
        private val userProfileApiService: UserProfileApiService,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        private val logger: Logger,
        // ❌ IndexProfileUseCase依赖已移除 - RAG逻辑由Coach模块统一管理
    ) : UserAggregateRepository {
        /**
         * 内部Flow方法实现 - 为UserAggregateRepository提供支持
         */
        private fun getUserProfileFlow(userId: String): Flow<ModernResult<UserProfile?>> =
            userProfileDao
                .getUserProfile(userId)
                .map { entity ->
                    try {
                        if (entity != null) {
                            val domainProfile = entity.toDomain()
                            logger.d("UserProfileRepositoryImpl", "成功获取用户资料: $userId")
                            ModernResult.Success(domainProfile)
                        } else {
                            logger.d("UserProfileRepositoryImpl", "用户资料不存在: $userId")
                            ModernResult.Success(null)
                        }
                    } catch (e: Exception) {
                        logger.e(e, "转换用户资料失败: $userId")
                        ModernResult.Error(
                            e.toModernDataError(
                                uiMessage = UiText.DynamicString("获取用户资料失败"),
                                operationName = "UserProfileRepositoryImpl.getUserProfileFlow",
                            ),
                        )
                    }
                }.catch { throwable ->
                    if (throwable !is kotlinx.coroutines.CancellationException) {
                        logger.e(throwable, "获取用户资料流失败: $userId")
                        emit(
                            ModernResult.Error(
                                throwable.toModernDataError(
                                    uiMessage = UiText.DynamicString("获取用户资料失败"),
                                    operationName = "UserProfileRepositoryImpl.getUserProfileFlow",
                                ),
                            ),
                        )
                    } else {
                        // 重新抛出CancellationException，让协程结构化并发处理
                        throw throwable
                    }
                }

        // ==================== UserAggregateRepository 实现 ====================
        // 基于现有UserProfileRepository扩展，添加RAG集成功能

        // === 用户基本信息 ===
        override suspend fun getUser(userId: String): ModernResult<User?> =
            withContext(ioDispatcher) {
                try {
                    // TODO: 实现User相关逻辑，目前先返回null
                    logger.d("UserProfileRepositoryImpl", "getUser暂未实现: $userId")
                    ModernResult.Success(null)
                } catch (e: Exception) {
                    logger.e(e, "获取用户基本信息失败: $userId")
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "UserProfileRepositoryImpl.getUser",
                            uiMessage = UiText.DynamicString("获取用户信息失败"),
                        ),
                    )
                }
            }

        override fun observeUser(userId: String): Flow<ModernResult<User?>> {
            logger.d("UserProfileRepositoryImpl", "observeUser暂未实现: $userId")
            return kotlinx.coroutines.flow.flowOf(ModernResult.Success(null))
        }

        override suspend fun createUser(user: User): ModernResult<String> =
            withContext(ioDispatcher) {
                try {
                    logger.d("UserProfileRepositoryImpl", "createUser暂未实现")
                    ModernResult.Success(user.userId)
                } catch (e: Exception) {
                    logger.e(e, "创建用户失败")
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "UserProfileRepositoryImpl.createUser",
                            uiMessage = UiText.DynamicString("创建用户失败"),
                        ),
                    )
                }
            }

        override suspend fun updateUser(user: User): ModernResult<Unit> =
            withContext(ioDispatcher) {
                try {
                    logger.d("UserProfileRepositoryImpl", "updateUser暂未实现")
                    ModernResult.Success(Unit)
                } catch (e: Exception) {
                    logger.e(e, "更新用户失败")
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "UserProfileRepositoryImpl.updateUser",
                            uiMessage = UiText.DynamicString("更新用户失败"),
                        ),
                    )
                }
            }

        override suspend fun deleteUser(userId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                try {
                    logger.d("UserProfileRepositoryImpl", "deleteUser暂未实现: $userId")
                    ModernResult.Success(Unit)
                } catch (e: Exception) {
                    logger.e(e, "删除用户失败: $userId")
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "UserProfileRepositoryImpl.deleteUser",
                            uiMessage = UiText.DynamicString("删除用户失败"),
                        ),
                    )
                }
            }

        // === 用户资料管理 ===
        // UserAggregateRepository 接口要求的suspend方法
        override suspend fun getUserProfile(userId: String): ModernResult<UserProfile?> =
            withContext(ioDispatcher) {
                try {
                    val entity = userProfileDao.getUserProfileSync(userId)
                    val domainProfile = entity?.toDomain()

                    // 🔥 [诊断2] 检查数据库查询
                    println("🔥 [诊断2] 查询用户资料: userId=$userId")
                    println("🔥 [诊断2] 数据库实体: $entity")
                    println("🔥 [诊断2] 转换后的domain对象: $domainProfile")
                    if (domainProfile == null) {
                        println("🔥 [诊断2] ❌ 用户资料为空，可能用户未设置个人信息")
                    } else {
                        println("🔥 [诊断2] ✅ 用户资料获取成功")
                    }

                    logger.d("UserProfileRepositoryImpl", "获取用户资料成功: $userId")
                    ModernResult.Success(domainProfile)
                } catch (e: Exception) {
                    logger.e(e, "获取用户资料失败: $userId")
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "UserProfileRepositoryImpl.getUserProfile.suspend",
                            uiMessage = UiText.DynamicString("获取用户资料失败"),
                        ),
                    )
                }
            }

        override fun observeUserProfile(userId: String): Flow<ModernResult<UserProfile?>> {
            return getUserProfileFlow(userId) // 复用现有Flow方法
        }

        override suspend fun updateUserProfile(
            userId: String,
            profile: UserProfile,
        ): ModernResult<Unit> =
            withContext(ioDispatcher) {
                try {
                    logger.d("UserProfileRepositoryImpl", "开始保存用户资料: ${profile.userId}")

                    // Step 1: 本地优先策略 - 先保存到本地数据库
                    val entity = profile.toEntity()
                    val insertedId = userProfileDao.insertOrUpdateUserProfile(entity)

                    logger.d(
                        "UserProfileRepositoryImpl",
                        "本地用户资料保存成功: ${profile.userId}, insertedId: $insertedId",
                    )

                    // Step 2: 后台同步到SharedModels (API) - 使用协程异步处理
                    launch(ioDispatcher) {
                        syncProfileToApi(profile)
                    }

                    // ❌ RAG集成逻辑已移除 - Profile模块只负责"人"的数据存储
                    // Coach模块将按需从Profile模块获取数据并进行向量化处理

                    // 立即返回成功，不等待网络同步
                    ModernResult.Success(Unit)
                } catch (e: Exception) {
                    logger.e(e, "保存用户资料失败: ${profile.userId}")
                    ModernResult.Error(
                        e.toModernDataError(
                            uiMessage = UiText.DynamicString("保存用户资料失败"),
                            operationName = "UserProfileRepositoryImpl.updateUserProfile",
                        ),
                    )
                }
            }

        /**
         * 将用户资料同步到API (SharedModels)
         * 后台异步执行，不影响主业务流程
         * 使用NetworkResult<T>统一错误处理
         */
        private suspend fun syncProfileToApi(profile: UserProfile) {
            try {
                logger.d("UserProfileRepositoryImpl", "开始同步用户资料到API: ${profile.userId}")

                // 将Domain模型转换为SharedModels DTO
                val profileDto = profile.toDto()

                // 调用API更新用户资料 - 使用NetworkResult统一处理
                val networkResult = userProfileApiService.updateUserProfile(profile.userId, profileDto)

                when (networkResult) {
                    is com.example.gymbro.shared.models.network.NetworkResult.Success -> {
                        logger.i("UserProfileRepositoryImpl", "用户资料API同步成功: ${profile.userId}")
                    }

                    is com.example.gymbro.shared.models.network.NetworkResult.Error -> {
                        logger.w(
                            "UserProfileRepositoryImpl",
                            "用户资料API同步失败: ${profile.userId}, error: ${networkResult.error}",
                        )
                    }

                    is com.example.gymbro.shared.models.network.NetworkResult.Loading -> {
                        logger.d("UserProfileRepositoryImpl", "用户资料API同步中: ${profile.userId}")
                    }
                }
            } catch (e: Exception) {
                // 网络同步失败不影响本地操作成功
                logger.w(e, "用户资料API同步异常: ${profile.userId} - 本地数据已保存，将稍后重试")

                // TODO: 可以考虑加入重试队列或同步任务队列
                // 这里可以将失败的同步任务加入队列，稍后重试
            }
        }

        // ❌ generateProfileSummary方法已删除 - Profile摘要生成将由Coach模块负责

        override suspend fun uploadAvatar(
            userId: String,
            imageUri: String,
        ): ModernResult<String> =
            withContext(ioDispatcher) {
                try {
                    // TODO: 实现头像上传功能
                    // 目前返回本地路径作为占位符
                    ModernResult.Success(imageUri)
                } catch (e: Exception) {
                    logger.e(e, "上传头像失败")
                    ModernResult.Error(
                        e.toModernDataError(
                            uiMessage = UiText.DynamicString("上传头像失败"),
                            operationName = "UserProfileRepositoryImpl.uploadAvatar",
                        ),
                    )
                }
            }

        override suspend fun removeAvatar(userId: String): ModernResult<Unit> =
            withContext(ioDispatcher) {
                try {
                    logger.d("UserProfileRepositoryImpl", "removeAvatar暂未实现: $userId")
                    ModernResult.Success(Unit)
                } catch (e: Exception) {
                    logger.e(e, "移除头像失败: $userId")
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "UserProfileRepositoryImpl.removeAvatar",
                            uiMessage = UiText.DynamicString("移除头像失败"),
                        ),
                    )
                }
            }

        // === 其他接口方法（暂时返回默认值） ===
        override suspend fun getUserPreferences(
            userId: String,
        ): ModernResult<UserPreferences?> = ModernResult.Success(null)

        override fun observeUserPreferences(userId: String): Flow<ModernResult<UserPreferences?>> =
            kotlinx.coroutines.flow.flowOf(
                ModernResult.Success(null),
            )

        override suspend fun updateUserPreferences(
            userId: String,
            preferences: UserPreferences,
        ): ModernResult<Unit> = ModernResult.Success(Unit)

        override suspend fun getUserFitnessProfile(userId: String): ModernResult<UserFitnessProfile?> =
            ModernResult.Success(
                null,
            )

        override suspend fun updateUserFitnessProfile(
            userId: String,
            fitnessProfile: UserFitnessProfile,
        ): ModernResult<Unit> = ModernResult.Success(Unit)

        override suspend fun getUserStats(userId: String): ModernResult<UserStats?> = ModernResult.Success(
            null,
        )

        override fun observeUserStats(userId: String): Flow<ModernResult<UserStats?>> = kotlinx.coroutines.flow.flowOf(
            ModernResult.Success(null),
        )

        override suspend fun updateUserStats(
            userId: String,
            stats: UserStats,
        ): ModernResult<Unit> = ModernResult.Success(Unit)

        override suspend fun getUserComplete(userId: String): ModernResult<UserCompleteInfo?> = ModernResult.Success(
            null,
        )

        override suspend fun updateUserBatch(
            userId: String,
            updates: UserBatchUpdate,
        ): ModernResult<Unit> = ModernResult.Success(Unit)

        // ❌ RAG集成核心方法已删除 - Profile模块不再承担AI相关职责
        // Coach模块将通过getUserProfile()获取原始数据，并在Coach侧进行向量化和摘要生成
    }
