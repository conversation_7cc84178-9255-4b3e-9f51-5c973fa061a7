package com.example.gymbro.buildlogic.detekt.design

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.KtCallExpression

/**
 * GymBro 自定义规则：禁止硬编码尺寸值。
 *
 * 目的：强制使用 Design System 的 Tokens，确保设计一致性。
 * 这有助于：
 * 1. 保持设计系统的一致性
 * 2. 便于主题切换和响应式设计
 * 3. 减少魔法数字
 * 4. 提高可维护性
 *
 * 检测模式：
 * - Modifier.padding(16.dp)
 * - Modifier.size(24.dp)
 * - Modifier.height(48.dp)
 * - 等等...
 */
class NoHardcodedDimension(config: Config = Config.empty) : Rule(config) {

    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "禁止使用硬编码的尺寸值，请使用 Design System 的 Tokens。",
        Debt.FIVE_MINS
    )

    private val dimensionMethods = setOf(
        "padding", "size", "width", "height", "offset",
        "fillMaxWidth", "fillMaxHeight", "wrapContentWidth", "wrapContentHeight",
        "requiredSize", "requiredWidth", "requiredHeight",
        "defaultMinSize", "sizeIn", "widthIn", "heightIn"
    )

    private val allowedTokenPatterns = setOf(
        "Tokens.Spacing.",
        "Tokens.Size.",
        "Tokens.Dimension.",
        "designSystem.spacing.",
        "designSystem.size.",
        "theme.spacing.",
        "theme.size."
    )

    override fun visitCallExpression(expression: KtCallExpression) {
        super.visitCallExpression(expression)

        val callText = expression.text
        val callName = expression.calleeExpression?.text

        // 检查是否是尺寸相关的方法调用
        if (callName != null && dimensionMethods.contains(callName)) {
            checkForHardcodedDimensions(expression, callText)
        }

        // 检查 Modifier 链式调用
        if (callText.contains("Modifier.") && containsDimensionMethod(callText)) {
            checkModifierChain(expression, callText)
        }
    }

    private fun checkForHardcodedDimensions(expression: KtCallExpression, callText: String) {
        // 检查是否包含 .dp 但不是来自 Tokens
        if (callText.contains(".dp") && !isUsingDesignTokens(callText)) {
            val dpPattern = Regex("""(\d+(?:\.\d+)?\.dp)""")
            val matches = dpPattern.findAll(callText)

            for (match in matches) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(expression),
                        "发现硬编码尺寸值 '${match.value}'。" +
                            "请使用 Design System 的 Tokens，例如：Tokens.Spacing.medium 或 Tokens.Size.large。" +
                            "\n建议的替换：" +
                            "\n- 小间距：Tokens.Spacing.small" +
                            "\n- 中等间距：Tokens.Spacing.medium" +
                            "\n- 大间距：Tokens.Spacing.large" +
                            "\n- 图标尺寸：Tokens.Size.icon" +
                            "\n- 按钮高度：Tokens.Size.buttonHeight"
                    )
                )
            }
        }
    }

    private fun checkModifierChain(expression: KtCallExpression, callText: String) {
        // 检查 Modifier 链中的硬编码值
        val modifierPattern = Regex("""Modifier\.(\w+)\([^)]*(\d+(?:\.\d+)?\.dp)[^)]*\)""")
        val matches = modifierPattern.findAll(callText)

        for (match in matches) {
            if (!isUsingDesignTokens(match.value)) {
                val methodName = match.groupValues[1]
                val dpValue = match.groupValues[2]

                report(
                    CodeSmell(
                        issue,
                        Entity.from(expression),
                        "在 Modifier.$methodName() 中发现硬编码尺寸值 '$dpValue'。" +
                            "请使用 Design System 的 Tokens。" +
                            "\n示例：Modifier.$methodName(Tokens.Spacing.medium)"
                    )
                )
            }
        }
    }

    private fun containsDimensionMethod(text: String): Boolean {
        return dimensionMethods.any { method ->
            text.contains(".$method(") || text.contains("Modifier.$method(")
        }
    }

    private fun isUsingDesignTokens(text: String): Boolean {
        return allowedTokenPatterns.any { pattern ->
            text.contains(pattern)
        }
    }
}
