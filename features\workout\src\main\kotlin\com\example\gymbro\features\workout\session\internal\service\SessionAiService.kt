package com.example.gymbro.features.workout.session.internal.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.features.FeatureErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.json.VectorizedWorkoutState
import com.example.gymbro.domain.workout.usecase.SmartExerciseSearchUseCase
import com.example.gymbro.domain.workout.usecase.SmartSearchResult
import com.example.gymbro.domain.workout.usecase.VectorizeWorkoutStateUseCase
import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * 训练会话AI服务
 *
 * Features层的AI服务组装器，负责：
 * - 组装来自Domain层的各种UseCase
 * - 提供训练会话相关的AI功能
 * - 处理用户交互和AI响应
 * - 管理训练状态的智能分析
 *
 * 符合Clean Architecture：Features层组装Domain层和Core层功能
 */
@Singleton
class SessionAiService
    @Inject
    constructor(
        private val vectorizeWorkoutStateUseCase: VectorizeWorkoutStateUseCase,
        private val smartExerciseSearchUseCase: SmartExerciseSearchUseCase,
    ) {

        /**
         * 获取训练会话的AI上下文
         *
         * @param sessionId 训练会话ID
         * @return AI上下文数据，包含向量化状态
         */
        suspend fun getSessionAiContext(sessionId: String): ModernResult<SessionAiContext> {
            return try {
                if (sessionId.isBlank()) {
                    return ModernResult.Error(
                        FeatureErrors.CoachError.invalidInput(
                            operationName = "SessionAiService.getSessionAiContext",
                            message = UiText.DynamicString("会话ID不能为空"),
                            inputType = "empty_session_id",
                            value = sessionId,
                        ),
                    )
                }

                // 向量化当前训练状态
                val vectorizedStateResult = vectorizeWorkoutStateUseCase(sessionId)

                when (vectorizedStateResult) {
                    is ModernResult.Success -> {
                        val context = SessionAiContext(
                            sessionId = sessionId,
                            vectorizedState = vectorizedStateResult.data,
                            contextType = determineContextType(vectorizedStateResult.data),
                            isActive = vectorizedStateResult.data?.sessionId?.isNotBlank() ?: false,
                            lastUpdate = System.currentTimeMillis(),
                        )

                        Timber.d("SessionAiService: AI上下文获取成功 sessionId=$sessionId")
                        ModernResult.Success(context)
                    }

                    is ModernResult.Error -> ModernResult.Error(vectorizedStateResult.error)
                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                Timber.e(e, "SessionAiService: 获取AI上下文异常 sessionId=$sessionId")
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "SessionAiService.getSessionAiContext",
                        message = UiText.DynamicString("获取训练会话AI上下文失败"),
                        processType = "ai_context_generation",
                        reason = "context_generation_exception",
                        cause = e,
                        metadataMap = mapOf("session_id" to sessionId),
                    ),
                )
            }
        }

        /**
         * 基于训练状态的智能动作推荐
         *
         * @param sessionId 训练会话ID
         * @param query 用户查询（可选）
         * @param topK 推荐数量
         * @return 智能推荐结果
         */
        suspend fun getSmartExerciseRecommendations(
            sessionId: String,
            query: String = "",
            topK: Int = 5,
        ): ModernResult<SmartRecommendationResult> {
            return try {
                // 1. 获取AI上下文
                val contextResult = getSessionAiContext(sessionId)
                val context = when (contextResult) {
                    is ModernResult.Success -> contextResult.data
                    is ModernResult.Error -> return ModernResult.Error(contextResult.error)
                    is ModernResult.Loading -> return ModernResult.Loading
                }

                // 2. 构建搜索查询
                val searchQuery = if (query.isBlank()) {
                    buildDefaultQuery(context.vectorizedState)
                } else {
                    query
                }

                // 3. 执行智能搜索
                val searchResult = smartExerciseSearchUseCase(
                    query = searchQuery,
                    currentState = context.vectorizedState,
                    topK = topK,
                )

                when (searchResult) {
                    is ModernResult.Success -> {
                        val recommendationResult = SmartRecommendationResult(
                            sessionId = sessionId,
                            query = searchQuery,
                            searchResult = searchResult.data,
                            context = context,
                            recommendationType = determineRecommendationType(context, query),
                            confidence = calculateConfidence(searchResult.data),
                        )

                        Timber.d(
                            "SessionAiService: 智能推荐成功 sessionId=$sessionId, 结果数=${searchResult.data.primaryMatches.size}",
                        )
                        ModernResult.Success(recommendationResult)
                    }

                    is ModernResult.Error -> ModernResult.Error(searchResult.error)
                    is ModernResult.Loading -> ModernResult.Loading
                }
            } catch (e: Exception) {
                Timber.e(e, "SessionAiService: 智能推荐异常 sessionId=$sessionId")
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "SessionAiService.getSmartExerciseRecommendations",
                        message = UiText.DynamicString("智能动作推荐失败"),
                        processType = "smart_exercise_recommendation",
                        reason = "recommendation_exception",
                        cause = e,
                        metadataMap = mapOf(
                            "session_id" to sessionId,
                            "query" to query,
                        ),
                    ),
                )
            }
        }

        /**
         * 分析训练会话状态
         *
         * @param sessionId 训练会话ID
         * @return 训练状态分析结果
         */
        suspend fun analyzeWorkoutSession(sessionId: String): ModernResult<SessionAnalysisResult> {
            return try {
                val contextResult = getSessionAiContext(sessionId)
                val context = when (contextResult) {
                    is ModernResult.Success -> contextResult.data
                    is ModernResult.Error -> return ModernResult.Error(contextResult.error)
                    is ModernResult.Loading -> return ModernResult.Loading
                }

                val analysis = SessionAnalysisResult(
                    sessionId = sessionId,
                    progressPercentage = calculateProgressPercentage(context.vectorizedState),
                    currentPhase = determineWorkoutPhase(context.vectorizedState),
                    insights = generateInsights(context.vectorizedState),
                    recommendations = generateRecommendations(context.vectorizedState),
                    nextActions = suggestNextActions(context.vectorizedState),
                )

                Timber.d("SessionAiService: 训练分析完成 sessionId=$sessionId")
                ModernResult.Success(analysis)
            } catch (e: Exception) {
                Timber.e(e, "SessionAiService: 训练分析异常 sessionId=$sessionId")
                ModernResult.Error(
                    FeatureErrors.CoachError.processingFailed(
                        operationName = "SessionAiService.analyzeWorkoutSession",
                        message = UiText.DynamicString("训练会话分析失败"),
                        processType = "session_analysis",
                        reason = "analysis_exception",
                        cause = e,
                        metadataMap = mapOf("session_id" to sessionId),
                    ),
                )
            }
        }

        // ========== 私有辅助方法 ==========

        private fun determineContextType(state: VectorizedWorkoutState): String {
            return when {
                state.exerciseId != null -> "exercise_context"
                state.progress > 0 -> "session_progress"
                else -> "session_start"
            }
        }

        private fun buildDefaultQuery(state: VectorizedWorkoutState): String {
            return when {
                state.exerciseId != null -> "相关动作推荐"
                state.progress == 0f -> "开始训练动作"
                else -> "下一个动作推荐"
            }
        }

        private fun determineRecommendationType(context: SessionAiContext, query: String): String {
            return when {
                query.isNotBlank() -> "query_based"
                context.vectorizedState.exerciseId != null -> "context_based"
                else -> "general_recommendation"
            }
        }

        private fun calculateConfidence(searchResult: SmartSearchResult): Float {
            val avgSimilarity = searchResult.primaryMatches.map { it.similarity }.average().toFloat()
            return (avgSimilarity * 100).coerceIn(0f, 100f)
        }

        private fun calculateProgressPercentage(state: VectorizedWorkoutState): Int {
            return state.getProgressPercentage()
        }

        private fun determineWorkoutPhase(state: VectorizedWorkoutState): String {
            val progress = calculateProgressPercentage(state)
            return when {
                progress == 0 -> "准备阶段"
                progress < 30 -> "热身阶段"
                progress < 70 -> "主训练阶段"
                progress < 100 -> "收尾阶段"
                else -> "完成阶段"
            }
        }

        private fun generateInsights(state: VectorizedWorkoutState): List<String> {
            val insights = mutableListOf<String>()

            // 从metadata获取会话时长
            val sessionDurationMin = state.metadata["session_duration_min"] as? Int ?: 0
            if (sessionDurationMin > 60) {
                insights.add("训练时间较长，注意保持训练强度")
            }

            if (state.progress > 0) {
                insights.add("训练进度 ${state.getProgressPercentage()}%，保持良好节奏")
            }

            // 从metadata获取当前重量
            val currentWeight = state.metadata["current_weight"] as? Float
            currentWeight?.let { weight ->
                if (weight > 0) {
                    insights.add("当前使用重量 ${weight}kg，根据感觉调整")
                }
            }

            return insights
        }

        private fun generateRecommendations(state: VectorizedWorkoutState): List<String> {
            val recommendations = mutableListOf<String>()

            // 从metadata获取会话时长
            val sessionDurationMin = state.metadata["session_duration_min"] as? Int ?: 0
            if (sessionDurationMin > 90) {
                recommendations.add("考虑开始收尾动作")
            }

            if (state.progress == 0f) {
                recommendations.add("选择合适的热身动作开始")
            }

            return recommendations
        }

        private fun suggestNextActions(state: VectorizedWorkoutState): List<String> {
            val actions = mutableListOf<String>()

            if (state.exerciseId != null) {
                actions.add("完成当前动作")
                actions.add("记录训练数据")
            } else {
                actions.add("选择下一个动作")
            }

            return actions
        }
    }

/**
 * 训练会话AI上下文
 */
data class SessionAiContext(
    val sessionId: String,
    val vectorizedState: VectorizedWorkoutState,
    val contextType: String,
    val isActive: Boolean,
    val lastUpdate: Long,
)

/**
 * 智能推荐结果
 */
data class SmartRecommendationResult(
    val sessionId: String,
    val query: String,
    val searchResult: SmartSearchResult,
    val context: SessionAiContext,
    val recommendationType: String,
    val confidence: Float,
)

/**
 * 训练会话分析结果
 */
data class SessionAnalysisResult(
    val sessionId: String,
    val progressPercentage: Int,
    val currentPhase: String,
    val insights: List<String>,
    val recommendations: List<String>,
    val nextActions: List<String>,
)
