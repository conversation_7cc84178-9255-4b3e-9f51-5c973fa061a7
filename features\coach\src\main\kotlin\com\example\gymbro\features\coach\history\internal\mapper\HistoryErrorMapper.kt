package com.example.gymbro.features.coach.history.internal.mapper

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.coach.history.HistoryContract
import com.example.gymbro.features.coach.history.internal.resources.HistoryStrings
import javax.inject.Inject

/**
 * History模块错误映射器
 *
 * 负责将各种异常和错误类型映射为用户友好的UiText消息
 * 遵循GymBro项目的错误处理标准，提供一致的用户体验
 */
internal class HistoryErrorMapper
    @Inject
    constructor() {

        /**
         * 将Throwable映射为HistoryContract.ErrorState
         */
        fun mapToErrorState(
            throwable: Throwable,
            retryAction: (() -> Unit)? = null,
        ): HistoryContract.ErrorState {
            val (message, type) = when (throwable) {
                is ModernDataError -> mapModernDataError(throwable)
                is java.net.UnknownHostException,
                is java.net.ConnectException,
                is java.net.SocketTimeoutException,
                -> {
                    HistoryStrings.networkError to HistoryContract.ErrorType.NETWORK
                }

                is IllegalArgumentException,
                is IllegalStateException,
                -> {
                    HistoryStrings.errorGeneral to HistoryContract.ErrorType.STORAGE
                }

                else -> HistoryStrings.errorGeneral to HistoryContract.ErrorType.STORAGE
            }

            return HistoryContract.ErrorState(
                message = message,
                type = type,
                retryAction = retryAction,
            )
        }

        /**
         * 将ModernDataError映射为UiText和ErrorType
         */
        private fun mapModernDataError(error: ModernDataError): Pair<UiText, HistoryContract.ErrorType> {
            return when (error.errorType) {
                is GlobalErrorType.Network -> {
                    HistoryStrings.networkError to HistoryContract.ErrorType.NETWORK
                }

                is GlobalErrorType.Database -> {
                    HistoryStrings.errorStorage to HistoryContract.ErrorType.STORAGE
                }

                is GlobalErrorType.System.Internal -> {
                    HistoryStrings.errorInitialization to HistoryContract.ErrorType.STORAGE
                }

                else -> {
                    HistoryStrings.errorGeneral to HistoryContract.ErrorType.STORAGE
                }
            }
        }

        /**
         * 将分页加载错误映射为UiText
         */
        fun mapPagingError(throwable: Throwable): UiText {
            return when (throwable) {
                is java.net.UnknownHostException,
                is java.net.ConnectException,
                -> HistoryStrings.networkError

                is java.net.SocketTimeoutException -> HistoryStrings.loadFailed
                else -> HistoryStrings.pagingLoadError(throwable.message ?: "未知错误")
            }
        }

        /**
         * 将搜索错误映射为UiText
         */
        fun mapSearchError(throwable: Throwable): UiText {
            return when (throwable) {
                is java.net.UnknownHostException,
                is java.net.ConnectException,
                -> HistoryStrings.networkError

                else -> HistoryStrings.errorSearch
            }
        }

        /**
         * 将RAG相关错误映射为UiText
         */
        fun mapRagError(throwable: Throwable): UiText {
            return when (throwable) {
                is IllegalArgumentException -> HistoryStrings.errorEmbedding
                is java.net.UnknownHostException,
                is java.net.ConnectException,
                -> HistoryStrings.networkError

                else -> HistoryStrings.ragSearchFailed
            }
        }

        /**
         * 为不同错误类型提供重试操作文本
         */
        fun getRetryActionText(errorType: HistoryContract.ErrorType): UiText {
            return when (errorType) {
                HistoryContract.ErrorType.NETWORK -> HistoryStrings.actionRetry
                HistoryContract.ErrorType.SEARCH -> HistoryStrings.retryLoad
                HistoryContract.ErrorType.EMBEDDING -> HistoryStrings.actionRetry
                HistoryContract.ErrorType.STORAGE -> HistoryStrings.retryLoad
            }
        }
    }
