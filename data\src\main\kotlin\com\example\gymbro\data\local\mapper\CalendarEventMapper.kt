package com.example.gymbro.data.local.mapper

import com.example.gymbro.data.local.entity.CalendarEventEntity
import com.example.gymbro.domain.workout.model.calendar.CalendarItem
import com.example.gymbro.domain.workout.model.calendar.CalendarItemType
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.datetime.*
import kotlinx.serialization.json.Json

/**
 * 日历事件映射器
 *
 * 🎯 功能特性：
 * - Entity与Domain对象之间的双向转换
 * - 处理日期时间的时区转换
 * - 序列化和反序列化metadata
 * - 类型安全的枚举转换
 * - 完整的字段映射
 *
 * @param json Json序列化器
 */
@Singleton
class CalendarEventMapper
    @Inject
    constructor(
        private val json: Json,
    ) {

        /**
         * Entity转Domain对象
         */
        fun toDomain(entity: CalendarEventEntity): CalendarItem {
            // 时间戳转LocalDate
            val date = Instant.fromEpochMilliseconds(entity.date)
                .toLocalDateTime(TimeZone.currentSystemDefault())
                .date

            // 类型转换
            val itemType = when (entity.eventType) {
                "TEMPLATE" -> CalendarItemType.TEMPLATE
                "PLAN" -> CalendarItemType.PLAN
                "CUSTOM_WORKOUT" -> CalendarItemType.CUSTOM_WORKOUT
                "REST_DAY" -> CalendarItemType.REST_DAY
                else -> CalendarItemType.CUSTOM_WORKOUT
            }

            // 解析metadata（如果存在description且包含JSON）
            val metadata = try {
                if (entity.description?.startsWith("{") == true) {
                    json.decodeFromString<Map<String, String>>(entity.description)
                } else {
                    emptyMap()
                }
            } catch (e: Exception) {
                emptyMap()
            }

            return CalendarItem(
                id = entity.id,
                type = itemType,
                sourceId = entity.workoutId ?: entity.id,
                date = date,
                name = entity.title,
                estimatedDuration = entity.durationMinutes,
                isCompleted = entity.isCompleted,
                canDrag = !entity.isCompleted && !entity.cancelled,
                order = 0,
                metadata = metadata.plus(
                    mapOf(
                        "userId" to entity.userId,
                        "color" to (entity.color ?: ""),
                        "isAllDay" to entity.isAllDay.toString(),
                        "reminderMinutes" to (entity.reminderMinutesBefore?.toString() ?: ""),
                        "recurrenceRule" to (entity.recurrenceRule ?: ""),
                        "isSynced" to entity.isSynced.toString(),
                        "description" to (
                            if (entity.description?.startsWith("{") != true) {
                                entity.description
                                    ?: ""
                            } else {
                                ""
                            }
                            ),
                        "completionDate" to (entity.completionDate?.toString() ?: ""),
                    ).filterValues { it.isNotEmpty() },
                ),
            )
        }

        /**
         * Domain对象转Entity
         */
        fun toEntity(item: CalendarItem): CalendarEventEntity {
            // LocalDate转时间戳
            val timestamp = item.date.atTime(0, 0)
                .toInstant(TimeZone.currentSystemDefault())
                .toEpochMilliseconds()

            // 类型转换
            val eventType = when (item.type) {
                CalendarItemType.TEMPLATE -> "TEMPLATE"
                CalendarItemType.PLAN -> "PLAN"
                CalendarItemType.CUSTOM_WORKOUT -> "CUSTOM_WORKOUT"
                CalendarItemType.REST_DAY -> "REST_DAY"
            }

            // 处理metadata
            val metadataJson = if (item.metadata.isNotEmpty()) {
                json.encodeToString(item.metadata)
            } else {
                null
            }

            val userId = item.metadata["userId"] ?: "unknown_user"
            val color = item.metadata["color"]
            val isAllDay = item.metadata["isAllDay"]?.toBooleanStrictOrNull() ?: false
            val reminderMinutes = item.metadata["reminderMinutes"]?.toIntOrNull()
            val recurrenceRule = item.metadata["recurrenceRule"]
            val isSynced = item.metadata["isSynced"]?.toBooleanStrictOrNull() ?: false

            return CalendarEventEntity(
                id = item.id,
                userId = userId,
                workoutId = item.sourceId,
                eventType = eventType,
                date = timestamp,
                title = item.name,
                description = item.metadata["description"] ?: metadataJson,
                durationMinutes = item.estimatedDuration,
                color = color,
                isAllDay = isAllDay,
                reminderMinutesBefore = reminderMinutes,
                isCompleted = item.isCompleted,
                completionDate = item.metadata["completionDate"]?.toLongOrNull(),
                cancelled = false, // TODO: 从item中获取cancelled状态
                recurrenceRule = recurrenceRule,
                modifiedAt = System.currentTimeMillis(),
                isSynced = isSynced,
            )
        }

        /**
         * Entity列表转Domain列表
         */
        fun toDomainList(entities: List<CalendarEventEntity>): List<CalendarItem> {
            return entities.map { toDomain(it) }
        }

        /**
         * Domain列表转Entity列表
         */
        fun toEntityList(items: List<CalendarItem>): List<CalendarEventEntity> {
            return items.map { toEntity(it) }
        }
    }
