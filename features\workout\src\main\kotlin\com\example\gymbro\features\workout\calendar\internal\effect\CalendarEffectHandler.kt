package com.example.gymbro.features.workout.calendar.internal.effect

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.model.WorkoutAction
import com.example.gymbro.domain.workout.repository.CalendarRepository
import com.example.gymbro.features.workout.calendar.internal.CalendarContract
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.datetime.*

/**
 * Calendar 副作用处理器
 *
 * 处理日历相关的副作用，包括数据加载、WorkoutAction处理等
 * 集成CalendarRepository实现真实数据加载
 */
@Singleton
class CalendarEffectHandler
    @Inject
    constructor(
        private val logger: Logger,
        private val calendarRepository: CalendarRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    ) {

        /**
         * 处理副作用
         */
        fun handleEffect(
            effect: CalendarContract.Effect,
            dispatch: (CalendarContract.Intent) -> Unit,
        ) {
            logger.d("CalendarEffectHandler", "处理 Effect: ${effect::class.simpleName}")

            when (effect) {
                is CalendarContract.Effect.NavigateToSession -> {
                    logger.d("CalendarEffectHandler", "导航到训练会话: ${effect.sessionId}")
                }

                is CalendarContract.Effect.NavigateToTemplateDetail -> {
                    logger.d("CalendarEffectHandler", "导航到模板详情: ${effect.templateId}")
                }

                is CalendarContract.Effect.NavigateToPlanDetail -> {
                    logger.d("CalendarEffectHandler", "导航到计划详情: ${effect.planId}")
                }

                is CalendarContract.Effect.ShowToast -> {
                    logger.d("CalendarEffectHandler", "显示 Toast: ${effect.message}")
                }

                is CalendarContract.Effect.ShowError -> {
                    logger.d("CalendarEffectHandler", "显示错误: ${effect.message}")
                }

                is CalendarContract.Effect.TriggerHapticFeedback -> {
                    logger.d("CalendarEffectHandler", "触觉反馈")
                }

                is CalendarContract.Effect.TriggerHapticFeedbackType -> {
                    logger.d("CalendarEffectHandler", "触觉反馈类型: ${effect.type}")
                }

                is CalendarContract.Effect.TriggerLoadCurrentWeek -> {
                    logger.d("CalendarEffectHandler", "触发加载当前周")
                    // 🔥 修复无限循环：直接执行数据加载逻辑，而不是重新触发 Intent
                    handleLoadCurrentWeekData(kotlinx.coroutines.GlobalScope, dispatch)
                }

                is CalendarContract.Effect.TriggerLoadWeek -> {
                    logger.d("CalendarEffectHandler", "触发加载周: ${effect.weekStart}")
                    dispatch(CalendarContract.Intent.LoadWeek(effect.weekStart))
                }

                else -> {
                    logger.d("CalendarEffectHandler", "未处理的 Effect: ${effect::class.simpleName}")
                }
            }
        }

        /**
         * 处理 WorkoutAction - 完整实现
         */
        fun handleWorkoutAction(
            action: WorkoutAction,
            scope: CoroutineScope,
            dispatch: (CalendarContract.Intent) -> Unit,
        ) {
            logger.d("CalendarEffectHandler", "处理 WorkoutAction: ${action::class.simpleName}")

            scope.launch {
                try {
                    when (action) {
                        is WorkoutAction.TemplateAddedToCalendar -> {
                            logger.d("CalendarEffectHandler", "模板已添加到日历")
                            // 刷新当前周数据以显示新添加的模板
                            dispatch(CalendarContract.Intent.RefreshCurrentWeek)
                        }

                        is WorkoutAction.PlanAddedToCalendar -> {
                            logger.d("CalendarEffectHandler", "计划已添加到日历")
                            // 刷新当前周数据以显示新添加的计划
                            dispatch(CalendarContract.Intent.RefreshCurrentWeek)
                        }

                        is WorkoutAction.CalendarItemMoved -> {
                            logger.d("CalendarEffectHandler", "日历项目已移动")
                            // 刷新数据以反映移动后的状态
                            dispatch(CalendarContract.Intent.RefreshCurrentWeek)
                        }

                        is WorkoutAction.CalendarItemRemoved -> {
                            logger.d("CalendarEffectHandler", "日历项目已删除")
                            // 刷新数据以反映删除后的状态
                            dispatch(CalendarContract.Intent.RefreshCurrentWeek)
                        }

                        is WorkoutAction.CustomWorkoutAdded -> {
                            logger.d("CalendarEffectHandler", "自定义训练已添加")
                            // 刷新数据以显示新添加的自定义训练
                            dispatch(CalendarContract.Intent.RefreshCurrentWeek)
                        }

                        is WorkoutAction.CalendarViewModeChanged -> {
                            logger.d("CalendarEffectHandler", "日历视图模式已切换: ${action.mode}")
                            // 根据模式切换加载相应数据
                            val viewMode = when (action.mode) {
                                "week" -> CalendarContract.CalendarViewMode.WEEK
                                "month" -> CalendarContract.CalendarViewMode.MONTH
                                "year" -> CalendarContract.CalendarViewMode.YEAR
                                else -> CalendarContract.CalendarViewMode.COMPACT
                            }
                            dispatch(CalendarContract.Intent.ToggleViewMode(viewMode))
                        }

                        is WorkoutAction.StartDraggingCalendarItem -> {
                            logger.d("CalendarEffectHandler", "开始拖拽日历项目: ${action.itemId}")
                            // 拖拽开始时的处理逻辑
                            dispatch(CalendarContract.Intent.RefreshCurrentWeek)
                        }

                        is WorkoutAction.CompleteDraggingCalendarItem -> {
                            logger.d(
                                "CalendarEffectHandler",
                                "完成拖拽日历项目: ${action.itemId} -> ${action.targetDate}",
                            )
                            // 处理拖拽完成，可以触发数据更新
                            dispatch(CalendarContract.Intent.RefreshCurrentWeek)
                        }

                        else -> {
                            logger.d("CalendarEffectHandler", "未处理的 WorkoutAction: ${action::class.simpleName}")
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "处理 WorkoutAction 失败: ${action::class.simpleName}")
                }
            }
        }

        /**
         * 处理当前周数据加载 - 完整实现
         * 避免重新触发 LoadCurrentWeek Intent 造成循环
         */
        private fun handleLoadCurrentWeekData(
            scope: CoroutineScope,
            dispatch: (CalendarContract.Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    logger.d("CalendarEffectHandler", "开始加载当前周数据")

                    // 获取当前用户ID
                    val userIdResult = getCurrentUserIdUseCase().first()

                    if (userIdResult is ModernResult.Success && userIdResult.data != null) {
                        val userId = userIdResult.data!! // 确保非空

                        // 计算当前周的开始和结束日期
                        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
                        val dayOfWeek = today.dayOfWeek.value // 1=Monday, 7=Sunday
                        val weekStart = today.minus((dayOfWeek - 1).toLong(), DateTimeUnit.DAY)
                        val weekEnd = weekStart.plus(6, DateTimeUnit.DAY)

                        logger.d("CalendarEffectHandler", "加载周数据: $weekStart 到 $weekEnd")

                        // 调用仓库获取日历数据
                        val calendarDataResult = calendarRepository.getCalendarData(
                            startDate = weekStart,
                            endDate = weekEnd,
                            userId = userId,
                        )

                        // 获取统计数据
                        val statsResult = calendarRepository.getCalendarStats(
                            startDate = weekStart,
                            endDate = weekEnd,
                            userId = userId,
                        )

                        when {
                            calendarDataResult is ModernResult.Success && statsResult is ModernResult.Success -> {
                                // 转换数据格式 - 基于实际的CalendarDayInfo结构
                                val entries = calendarDataResult.data.map { (date, dayInfo) ->
                                    CalendarContract.CalendarEntry(
                                        date = date,
                                        status = mapStatusFromDayInfo(dayInfo),
                                        // Plan信息暂时为空，后续可通过其他数据源补充
                                        planName = null,
                                        planWeek = null,
                                        planDay = null,
                                        // 模板信息暂时为空，后续可通过CalendarItem补充
                                        templates = emptyList(),
                                        // Session状态基于dayInfo的完成状态推断
                                        sessionStatus = if (dayInfo.hasCompletedWorkout) {
                                            CalendarContract.SessionStatus.COMPLETED
                                        } else if (dayInfo.workoutCount > 0) {
                                            CalendarContract.SessionStatus.IN_PROGRESS
                                        } else {
                                            CalendarContract.SessionStatus.NOT_STARTED
                                        },
                                        sessionCompletionRate = if (dayInfo.workoutCount > 0) {
                                            dayInfo.completedCount.toFloat() / dayInfo.workoutCount
                                        } else {
                                            0f
                                        },
                                        // 统计信息暂时使用默认值，后续可通过stats数据补充
                                        totalWeight = 0.0,
                                        completedSets = dayInfo.completedCount,
                                        estimatedDuration = null,
                                        actualDuration = null,
                                        planId = null,
                                    )
                                }

                                // 发送成功结果
                                dispatch(
                                    CalendarContract.Intent.WeekDataLoaded(
                                        entries = entries,
                                        stats = statsResult.data,
                                    ),
                                )

                                logger.d("CalendarEffectHandler", "当前周数据加载完成: ${entries.size} 条记录")
                            }

                            calendarDataResult is ModernResult.Error -> {
                                logger.e("CalendarEffectHandler", "日历数据加载失败: ${calendarDataResult.error.message}")
                                dispatch(
                                    CalendarContract.Intent.WeekDataLoadingFailed(
                                        UiText.DynamicString("日历数据加载失败: ${calendarDataResult.error.message}"),
                                    ),
                                )
                            }

                            statsResult is ModernResult.Error -> {
                                logger.e("CalendarEffectHandler", "统计数据加载失败: ${statsResult.error.message}")
                                dispatch(
                                    CalendarContract.Intent.WeekDataLoadingFailed(
                                        UiText.DynamicString("统计数据加载失败: ${statsResult.error.message}"),
                                    ),
                                )
                            }

                            else -> {
                                logger.e("CalendarEffectHandler", "数据加载失败: 未知错误")
                                dispatch(
                                    CalendarContract.Intent.WeekDataLoadingFailed(
                                        UiText.DynamicString("数据加载失败: 未知错误"),
                                    ),
                                )
                            }
                        }
                    } else {
                        logger.e("CalendarEffectHandler", "获取用户ID失败")
                        dispatch(
                            CalendarContract.Intent.WeekDataLoadingFailed(
                                UiText.DynamicString("获取用户ID失败"),
                            ),
                        )
                    }
                } catch (e: Exception) {
                    logger.e(e, "当前周数据加载失败")
                    // 发送错误状态而不是重新触发加载
                    dispatch(
                        CalendarContract.Intent.WeekDataLoadingFailed(
                            UiText.DynamicString("当前周数据加载失败: ${e.message}"),
                        ),
                    )
                }
            }
        }

        /**
         * 将DayInfo状态映射到CalendarEntry状态
         */
        private fun mapStatusFromDayInfo(
            dayInfo: com.example.gymbro.domain.workout.model.calendar.CalendarDayInfo,
        ): CalendarContract.EntryStatus {
            return when {
                dayInfo.isRestDay -> CalendarContract.EntryStatus.REST
                dayInfo.hasCompletedWorkout -> CalendarContract.EntryStatus.COMPLETED
                dayInfo.workoutCount > 0 -> CalendarContract.EntryStatus.PLANNED
                else -> CalendarContract.EntryStatus.AVAILABLE
            }
        }
    }
