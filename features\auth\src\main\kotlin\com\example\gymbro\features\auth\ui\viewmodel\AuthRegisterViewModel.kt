package com.example.gymbro.features.auth.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber

@HiltViewModel
class AuthRegisterViewModel
    @Inject
    constructor(
        private val modernErrorHandler: ModernErrorHandler,
        private val authRepository: AuthRepository,
    ) : ViewModel() {
        data class RegisterUiState(
            val isLoading: Boolean = false,
            val navigationTarget: String? = null,
            val error: UiText? = null,
            val successMessage: UiText? = null,
            val email: String = "",
            val password: String = "",
            val confirmPassword: String = "",
            val username: String = "",
            val isAnonymousUser: Boolean = false,
            val acceptTerms: Boolean = false,
        )

        private val _uiState = MutableStateFlow(RegisterUiState())
        val uiState: StateFlow<RegisterUiState> = _uiState.asStateFlow()

        init {
            checkAnonymousUserStatus()
        }

        private fun checkAnonymousUserStatus() {
            viewModelScope.launch {
                try {
                    authRepository.getCurrentUser().collect { userResult ->
                        when (userResult) {
                            is ModernResult.Success -> {
                                val user = userResult.data
                                _uiState.update {
                                    it.copy(isAnonymousUser = user?.isAnonymous == true)
                                }
                            }

                            is ModernResult.Error -> {
                                Timber.e("获取用户状态失败: ${userResult.error.message}")
                            }

                            is ModernResult.Loading -> {
                                // 保持当前状态
                            }
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "检查匿名用户状态时发生错误")
                }
            }
        }
    }
