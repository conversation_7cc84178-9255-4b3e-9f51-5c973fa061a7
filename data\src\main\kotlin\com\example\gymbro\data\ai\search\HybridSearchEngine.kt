package com.example.gymbro.data.ai.search

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.shared.models.exercise.ExerciseDto
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

/**
 * 混合搜索引擎
 *
 * 集成FTS关键词搜索和向量语义搜索的混合搜索引擎
 * 为AI Function Call提供高精度的动作检索服务
 *
 * 核心功能：
 * 1. 向量语义搜索：基于BGE模型的语义相似度匹配
 * 2. 智能排序：结合相关性分数和业务权重
 * 3. 去重过滤：避免返回重复结果
 * 4. 性能优化：支持批量搜索和缓存
 *
 * 搜索策略：
 * - 语义理解：理解用户意图，匹配相关动作
 * - 相似度计算：基于向量距离计算相关性
 * - 结果排序：综合相关性和热度排序
 *
 * @property vectorSearchService 向量搜索服务
 * @property ioDispatcher IO调度器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
@Singleton
class HybridSearchEngine
    @Inject
    constructor(
        private val vectorSearchService: VectorSearchService,
        private val logger: Logger,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) {

        /**
         * 执行语义搜索
         * 基于向量相似度匹配相关动作
         *
         * @param query 搜索查询
         * @param limit 返回结果数量限制
         * @param excludeIds 排除的动作ID集合
         * @return 匹配的动作列表
         */
        suspend fun searchSemantic(
            query: String,
            limit: Int,
            excludeIds: Set<String> = emptySet(),
        ): List<ExerciseDto> = withContext(ioDispatcher) {
            logger.d("🧠 语义搜索: query='$query', limit=$limit, exclude=${excludeIds.size}")

            try {
                // 1. 生成查询向量
                val queryEmbedding = vectorSearchService.generateEmbedding(query)

                // 2. 向量相似度搜索
                val searchResults = vectorSearchService.searchSimilar(
                    queryEmbedding = queryEmbedding,
                    limit = limit * 2, // 多取一些，后续过滤
                    threshold = 0.7f, // 相似度阈值
                )

                // 3. 过滤排除的ID
                val filteredResults = searchResults.filter { it.exercise.id !in excludeIds }

                // 4. 按相关性排序并限制数量
                val finalResults = filteredResults
                    .sortedByDescending { it.similarity }
                    .take(limit)

                logger.d("语义搜索完成: 找到${finalResults.size}条结果")

                finalResults.map { it.exercise }
            } catch (e: Exception) {
                logger.e("语义搜索失败: ${e.message}")
                emptyList()
            }
        }

        /**
         * 批量语义搜索
         * 支持多个查询的批量处理
         */
        suspend fun batchSearchSemantic(
            queries: List<String>,
            limit: Int = 5,
        ): Map<String, List<ExerciseDto>> = withContext(ioDispatcher) {
            logger.d("📦 批量语义搜索: ${queries.size}个查询")

            val results = mutableMapOf<String, List<ExerciseDto>>()

            queries.forEach { query ->
                try {
                    val searchResults = searchSemantic(query, limit)
                    results[query] = searchResults
                } catch (e: Exception) {
                    logger.w("批量搜索失败: $query - ${e.message}")
                    results[query] = emptyList()
                }
            }

            logger.d("批量搜索完成: ${results.size}个结果")
            results
        }

        /**
         * 相似动作推荐
         * 基于给定动作推荐相似的动作
         */
        suspend fun findSimilarExercises(
            exerciseId: String,
            limit: Int = 5,
        ): List<ExerciseDto> = withContext(ioDispatcher) {
            logger.d("🔗 相似动作推荐: exerciseId='$exerciseId', limit=$limit")

            try {
                // 1. 获取目标动作的向量
                val targetEmbedding = vectorSearchService.getExerciseEmbedding(exerciseId)
                if (targetEmbedding == null) {
                    logger.w("未找到动作向量: $exerciseId")
                    return@withContext emptyList()
                }

                // 2. 搜索相似动作
                val similarResults = vectorSearchService.searchSimilar(
                    queryEmbedding = targetEmbedding,
                    limit = limit + 1, // +1 因为会包含自己
                    threshold = 0.8f, // 更高的相似度阈值
                )

                // 3. 排除自己
                val filteredResults = similarResults.filter { it.exercise.id != exerciseId }

                logger.d("相似动作推荐完成: 找到${filteredResults.size}条结果")

                filteredResults.take(limit).map { it.exercise }
            } catch (e: Exception) {
                logger.e("相似动作推荐失败: ${e.message}")
                emptyList()
            }
        }

        /**
         * 智能查询扩展
         * 基于用户查询生成相关的扩展查询
         */
        suspend fun expandQuery(query: String): List<String> = withContext(ioDispatcher) {
            logger.d("🔍 查询扩展: query='$query'")

            try {
                // 基于规则的查询扩展
                val expandedQueries = mutableListOf<String>()

                // 肌群扩展
                when {
                    query.contains("胸") -> {
                        expandedQueries.addAll(listOf("胸部", "胸肌", "卧推", "飞鸟"))
                    }

                    query.contains("背") -> {
                        expandedQueries.addAll(listOf("背部", "背肌", "引体向上", "划船"))
                    }

                    query.contains("腿") -> {
                        expandedQueries.addAll(listOf("腿部", "深蹲", "硬拉", "腿举"))
                    }

                    query.contains("肩") -> {
                        expandedQueries.addAll(listOf("肩部", "肩膀", "推举", "侧平举"))
                    }

                    query.contains("手臂") -> {
                        expandedQueries.addAll(listOf("手臂", "二头肌", "三头肌", "弯举"))
                    }
                }

                // 器械扩展
                when {
                    query.contains("哑铃") -> {
                        expandedQueries.addAll(listOf("哑铃训练", "自由重量"))
                    }

                    query.contains("杠铃") -> {
                        expandedQueries.addAll(listOf("杠铃训练", "复合动作"))
                    }

                    query.contains("无器械") -> {
                        expandedQueries.addAll(listOf("自重训练", "徒手训练"))
                    }
                }

                // 难度扩展
                when {
                    query.contains("初学者") || query.contains("新手") -> {
                        expandedQueries.addAll(listOf("简单", "基础", "入门"))
                    }

                    query.contains("高级") || query.contains("进阶") -> {
                        expandedQueries.addAll(listOf("困难", "复杂", "专业"))
                    }
                }

                logger.d("查询扩展完成: ${expandedQueries.size}个扩展查询")

                expandedQueries.distinct()
            } catch (e: Exception) {
                logger.e("查询扩展失败: ${e.message}")
                emptyList()
            }
        }
    }

/**
 * 向量搜索服务接口
 * 抽象向量搜索的具体实现
 */
interface VectorSearchService {
    /**
     * 生成文本的向量表示
     */
    suspend fun generateEmbedding(text: String): FloatArray

    /**
     * 搜索相似向量
     */
    suspend fun searchSimilar(
        queryEmbedding: FloatArray,
        limit: Int,
        threshold: Float = 0.7f,
    ): List<SimilarityResult>

    /**
     * 获取动作的向量表示
     */
    suspend fun getExerciseEmbedding(exerciseId: String): FloatArray?
}

/**
 * 相似度搜索结果
 */
data class SimilarityResult(
    val exercise: ExerciseDto,
    val similarity: Float,
)
