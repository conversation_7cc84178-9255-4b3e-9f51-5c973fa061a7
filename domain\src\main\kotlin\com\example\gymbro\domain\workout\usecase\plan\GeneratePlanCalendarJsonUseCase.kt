package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.shared.models.workout.PlanCalendarData
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first

/**
 * 生成训练计划calendar.json数据用例
 *
 * 🎯 核心功能：实现Plan层的calendar.json输出要求
 * 遵循Function Call输出模式，支持日历视图展示
 *
 * 基于08_Plan层改造设计.md的要求：
 * - Plan层必须输出数据到calendar.json表格格式
 * - calendar输出功能应该遵循Function Call实现模式
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Plan层改造设计)
 */
class GeneratePlanCalendarJsonUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<GeneratePlanCalendarJsonUseCase.Params, PlanCalendarData>(dispatcher, logger) {

        /**
         * 参数类
         *
         * @property planId 训练计划ID
         * @property startDate 开始日期（格式：YYYY-MM-DD）
         */
        data class Params(
            val planId: String,
            val startDate: String,
        )

        /**
         * 执行用例，生成训练计划的calendar.json数据
         *
         * @param parameters 参数
         * @return calendar.json数据
         */
        override suspend fun execute(parameters: Params): ModernResult<PlanCalendarData> {
            val (planId, startDate) = parameters

            // 获取当前用户ID
            val userIdResult = getCurrentUserIdUseCase().first()
            if (userIdResult !is ModernResult.Success || userIdResult.data == null) {
                return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "generatePlanCalendarJson",
                        message = UiText.DynamicString("用户未登录"),
                        metadataMap =
                            mapOf(
                                "errorType" to "UNAUTHORIZED",
                                "category" to ErrorCategory.AUTH.name,
                            ),
                    ),
                )
            }

            // 验证日期格式
            if (!isValidDateFormat(startDate)) {
                return ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "generatePlanCalendarJson",
                        message = UiText.DynamicString("日期格式无效，请使用YYYY-MM-DD格式"),
                        metadataMap = mapOf(
                            "startDate" to startDate,
                            "expectedFormat" to "YYYY-MM-DD",
                        ),
                    ),
                )
            }

            // 生成calendar.json数据
            val calendarResult = planRepository.generatePlanCalendarJson(
                planId = planId,
                startDate = startDate,
            )

            return when (calendarResult) {
                is ModernResult.Success -> {
                    logger.d(
                        "成功生成Plan calendar.json: planId=$planId, entries=${calendarResult.data.calendarEntries.size}",
                    )
                    calendarResult
                }

                is ModernResult.Error -> {
                    logger.e("生成Plan calendar.json失败: planId=$planId, error=${calendarResult.error}")
                    calendarResult
                }

                is ModernResult.Loading -> {
                    logger.w("生成Plan calendar.json超时: planId=$planId")
                    ModernResult.Error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "generatePlanCalendarJson",
                            message = UiText.DynamicString("生成超时，请稍后重试"),
                            metadataMap = mapOf("planId" to planId),
                        ),
                    )
                }
            }
        }

        /**
         * 验证日期格式是否为YYYY-MM-DD
         */
        private fun isValidDateFormat(dateString: String): Boolean {
            return try {
                val regex = Regex("""^\d{4}-\d{2}-\d{2}$""")
                regex.matches(dateString)
            } catch (e: Exception) {
                false
            }
        }
    }
