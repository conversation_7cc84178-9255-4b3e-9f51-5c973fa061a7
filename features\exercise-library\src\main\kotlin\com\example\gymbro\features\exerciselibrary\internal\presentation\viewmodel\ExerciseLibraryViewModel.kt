package com.example.gymbro.features.exerciselibrary.internal.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.UserSessionManager
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.usecase.*
import com.example.gymbro.features.exerciselibrary.internal.presentation.contract.ExerciseLibraryContract.Effect
import com.example.gymbro.features.exerciselibrary.internal.presentation.contract.ExerciseLibraryContract.Intent
import com.example.gymbro.features.exerciselibrary.internal.presentation.contract.ExerciseLibraryContract.State
import com.example.gymbro.features.exerciselibrary.internal.presentation.reducer.ExerciseLibraryReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * ExerciseLibrary模块的ViewModel
 *
 * 使用MVI架构模式，通过Intent处理用户交互，通过State暴露UI状态，
 * 通过Effect处理一次性事件（如导航、Toast等）
 */
@HiltViewModel
internal class ExerciseLibraryViewModel
    @Inject
    constructor(
        private val getAllExercisesUseCase: GetAllExercisesUseCase,
        private val getExerciseDetailsUseCase: GetExerciseDetailsUseCase,
        private val searchExercisesUseCase: SearchExercisesUseCase,
        private val createCustomExerciseUseCase: CreateCustomExerciseUseCase,
        private val checkExerciseNameExistsUseCase: CheckExerciseNameExistsUseCase,
        private val userSessionManager: UserSessionManager,
    ) : ViewModel() {
        // 内部可变状态
        private val _state = MutableStateFlow(State())

        // 对外只读状态
        val state: StateFlow<State> = _state.asStateFlow()

        // Effect通道
        private val _effect = Channel<Effect>(Channel.BUFFERED)
        val effect: Flow<Effect> = _effect.receiveAsFlow()

        init {
            // 初始化时加载动作列表
            handleIntent(Intent.LoadExercises)
        }

        /**
         * 处理Intent的主要入口点
         */
        fun handleIntent(intent: Intent) {
            when (intent) {
                // === 需要副作用的Intent ===
                Intent.LoadExercises -> loadExercises()
                Intent.RetryLoad -> loadExercises()
                Intent.RefreshExercises -> refreshExercises()
                is Intent.ViewExerciseDetails -> loadExerciseDetails(intent.exerciseId)
                Intent.NavigateToAddCustomExercise -> sendEffect(Effect.NavigateToAddCustomExercise)
                is Intent.CreateCustomExercise -> createCustomExercise(intent.exercise)
                Intent.ValidateCustomExerciseForm -> validateCustomExerciseForm()

                // === 纯状态变更的Intent ===
                else -> {
                    val newState = ExerciseLibraryReducer.reduce(_state.value, intent)
                    _state.value = newState

                    // 处理可能触发的副作用
                    handleSideEffects(intent, newState)
                }
            }
        }

        /**
         * 处理Intent可能触发的副作用
         */
        private fun handleSideEffects(
            intent: Intent,
            newState: State,
        ) {
            when (intent) {
                is Intent.SelectExercise -> {
                    if (!newState.multipleSelection) {
                        // 单选模式下，立即返回结果
                        sendEffect(Effect.ReturnSelectionResult(newState.selectedExercises))
                    }
                }

                else -> {
                    /* 其他Intent暂无副作用 */
                }
            }
        }

        /**
         * 加载所有动作
         */
        private fun loadExercises() {
            viewModelScope.launch {
                getAllExercisesUseCase()
                    .collect { result ->
                        when (result) {
                            is ModernResult.Success -> {
                                handleIntent(Intent.ExercisesLoaded(result.data.toImmutableList()))
                            }

                            is ModernResult.Error -> {
                                val errorMessage = UiText.DynamicString("加载动作失败")
                                handleIntent(Intent.ExercisesLoadError(errorMessage))
                            }

                            is ModernResult.Loading -> {
                                // Loading状态已在reducer中处理，这里无需额外操作
                            }
                        }
                    }
            }
        }

        /**
         * 刷新动作列表
         */
        private fun refreshExercises() {
            viewModelScope.launch {
                getAllExercisesUseCase()
                    .collect { result ->
                        when (result) {
                            is ModernResult.Success -> {
                                handleIntent(Intent.ExercisesLoaded(result.data.toImmutableList()))
                            }

                            is ModernResult.Error -> {
                                val errorMessage = UiText.DynamicString("刷新动作失败")
                                handleIntent(Intent.ExercisesLoadError(errorMessage))
                                sendEffect(Effect.ShowSnackbar(errorMessage))
                            }

                            is ModernResult.Loading -> {
                                // Loading状态已在reducer中处理，这里无需额外操作
                            }
                        }
                    }
            }
        }

        /**
         * 加载动作详情
         */
        private fun loadExerciseDetails(exerciseId: String) {
            viewModelScope.launch {
                when (val result = getExerciseDetailsUseCase(exerciseId)) {
                    is ModernResult.Success -> {
                        handleIntent(Intent.ExerciseDetailsLoaded(result.data))
                        sendEffect(Effect.NavigateToExerciseDetails(exerciseId))
                    }

                    is ModernResult.Error -> {
                        val errorMessage = UiText.DynamicString("加载动作详情失败")
                        handleIntent(Intent.ExerciseDetailsError(errorMessage))
                        sendEffect(Effect.ShowSnackbar(errorMessage))
                    }

                    is ModernResult.Loading -> {
                        // Loading状态已在reducer中处理，这里无需额外操作
                    }
                }
            }
        }

        /**
         * 返回选择的动作
         */
        fun confirmSelection() {
            val selectedExercises = _state.value.selectedExercises
            if (selectedExercises.isNotEmpty()) {
                sendEffect(Effect.ReturnSelectionResult(selectedExercises))
            }
        }

        /**
         * 发送Effect到UI层
         */
        private fun sendEffect(effect: Effect) {
            viewModelScope.launch {
                _effect.send(effect)
            }
        }

        /**
         * 简化调用接口，直接更新搜索查询
         */
        fun updateSearchQuery(query: String) {
            handleIntent(Intent.UpdateSearchQuery(query))
        }

        /**
         * 简化调用接口，直接选择动作
         */
        fun selectExercise(exercise: Exercise) {
            handleIntent(Intent.SelectExercise(exercise))
        }

        /**
         * 简化调用接口，直接取消选择动作
         */
        fun deselectExercise(exerciseId: String) {
            handleIntent(Intent.DeselectExercise(exerciseId))
        }

        /**
         * 简化调用接口，直接查看动作详情
         */
        fun viewExerciseDetails(exerciseId: String) {
            handleIntent(Intent.ViewExerciseDetails(exerciseId))
        }

        /**
         * 简化调用接口，设置选择模式
         */
        fun setSelectionMode(
            enabled: Boolean,
            multipleSelection: Boolean = false,
            preSelectedIds: List<String> = emptyList(),
        ) {
            handleIntent(
                Intent.SetSelectionMode(enabled, multipleSelection, preSelectedIds.toImmutableList()),
            )
        }

        /**
         * 创建自定义动作
         */
        private fun createCustomExercise(exercise: Exercise) {
            viewModelScope.launch {
                // 更新UI状态为创建中
                _state.value = _state.value.copy(isCreatingCustomExercise = true)

                when (val result = createCustomExerciseUseCase(exercise)) {
                    is ModernResult.Success -> {
                        sendEffect(Effect.CustomExerciseCreated(result.data))
                        sendEffect(Effect.ShowSnackbar(UiText.DynamicString("自定义动作创建成功")))
                        sendEffect(Effect.ClearCustomExerciseForm)
                        // 重新加载动作列表以包含新创建的动作
                        loadExercises()
                    }

                    is ModernResult.Error -> {
                        val errorMessage = UiText.DynamicString("创建自定义动作失败")
                        sendEffect(Effect.CustomExerciseCreationFailed(errorMessage))
                        sendEffect(Effect.ShowSnackbar(errorMessage))
                    }

                    is ModernResult.Loading -> {
                        // Loading状态已经设置，无需额外操作
                    }
                }

                // 清除创建中状态
                _state.value = _state.value.copy(isCreatingCustomExercise = false)
            }
        }

        /**
         * 验证自定义动作表单
         */
        private fun validateCustomExerciseForm() {
            val currentState = _state.value
            val errors = mutableMapOf<String, UiText>()

            // 验证动作名称
            if (currentState.customExerciseName.isBlank()) {
                errors["name"] = UiText.DynamicString("动作名称不能为空")
            }

            // 验证描述
            if (currentState.customExerciseDescription.isBlank()) {
                errors["description"] = UiText.DynamicString("动作描述不能为空")
            }

            // 验证肌肉群
            if (currentState.customExerciseMuscleGroups.isEmpty()) {
                errors["muscleGroups"] = UiText.DynamicString("请至少选择一个目标肌肉群")
            }

            // 更新验证错误状态
            _state.value = currentState.copy(customExerciseValidationErrors = errors)

            // 如果没有错误，检查名称是否已存在
            if (errors.isEmpty()) {
                checkExerciseNameUniqueness(currentState.customExerciseName)
            }
        }

        /**
         * 检查动作名称的唯一性
         */
        private fun checkExerciseNameUniqueness(name: String) {
            viewModelScope.launch {
                val userId = userSessionManager.getCurrentUserId()
                if (userId.isNullOrBlank()) {
                    sendEffect(Effect.ShowSnackbar(UiText.DynamicString("用户未登录，无法创建自定义动作")))
                    return@launch
                }

                when (val result = checkExerciseNameExistsUseCase(name, userId)) {
                    is ModernResult.Success -> {
                        if (result.data) {
                            // 名称已存在
                            val errors = _state.value.customExerciseValidationErrors.toMutableMap()
                            errors["name"] = UiText.DynamicString("动作名称已存在，请使用其他名称")
                            _state.value = _state.value.copy(customExerciseValidationErrors = errors)
                        } else {
                            // 名称可用，可以创建动作
                            // 这里可以自动提交或等待用户确认
                        }
                    }

                    is ModernResult.Error -> {
                        sendEffect(Effect.ShowSnackbar(UiText.DynamicString("无法验证动作名称，请稍后重试")))
                    }

                    is ModernResult.Loading -> {
                        // Loading状态处理
                    }
                }
            }
        }

        /**
         * 简化调用接口，更新自定义动作名称
         */
        fun updateCustomExerciseName(name: String) {
            handleIntent(Intent.UpdateCustomExerciseName(name))
        }

        /**
         * 简化调用接口，更新自定义动作描述
         */
        fun updateCustomExerciseDescription(description: String) {
            handleIntent(Intent.UpdateCustomExerciseDescription(description))
        }

        /**
         * 简化调用接口，创建自定义动作
         */
        fun createCustomExercise() {
            val currentState = _state.value

            // 获取真实用户ID
            val userId = userSessionManager.getCurrentUserId()
            if (userId.isNullOrBlank()) {
                sendEffect(Effect.ShowSnackbar(UiText.DynamicString("用户未登录，无法创建自定义动作")))
                return
            }

            // 构建Exercise对象，使用正确的ID生成策略
            val exerciseId = com.example.gymbro.shared.models.exercise.ExerciseDto.generateUserCustomId(
                userId,
            )
            val exercise = Exercise(
                id = exerciseId,
                name = UiText.DynamicString(currentState.customExerciseName),
                description = UiText.DynamicString(currentState.customExerciseDescription),
                muscleGroup = currentState.customExerciseMuscleGroups.firstOrNull()
                    ?: com.example.gymbro.shared.models.exercise.MuscleGroup.OTHER,
                equipment = emptyList(),
                targetMuscles = currentState.customExerciseMuscleGroups,
                isCustom = true,
                userId = userId,
                createdByUserId = userId,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
            )

            handleIntent(Intent.CreateCustomExercise(exercise))
        }
    }
