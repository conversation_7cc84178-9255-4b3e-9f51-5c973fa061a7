package com.example.gymbro.domain.coach.executor.modules

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.executor.FunctionCall
import com.example.gymbro.domain.coach.executor.FunctionResult
import com.example.gymbro.domain.workout.model.WorkoutAction
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext

/**
 * 训练会话域模块
 *
 * 负责处理训练会话相关的Function Call请求
 * 包括会话开始、组数记录、会话完成等功能
 *
 * 核心功能：
 * 1. 会话开始：开始新的训练会话或从模板加载
 * 2. 组数记录：记录训练组的重量、次数等
 * 3. 会话完成：结束当前训练会话
 *
 * 设计原则：
 * - 专注于会话级别的操作管理
 * - 支持实时训练数据记录
 * - 提供训练进度跟踪和统计
 *
 * @property ioDispatcher IO调度器
 * @property logger 日志记录器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
@Singleton
class SessionFunctionModule
    @Inject
    constructor(
        private val sessionRepository: SessionRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * 处理训练会话域的Function Call
         *
         * @param functionCall Function Call请求
         * @param onActionTrigger UI动作触发回调
         * @return 函数执行结果
         */
        suspend fun handle(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)? = null,
        ): ModernResult<FunctionResult> = safeCatch {
            logger.d("🏃 处理训练会话函数: ${functionCall.name}")

            val result = when (functionCall.name) {
                "gymbro.session.start" -> handleStart(functionCall.arguments, onActionTrigger)
                "gymbro.session.log_set" -> handleLogSet(functionCall.arguments, onActionTrigger)
                "gymbro.session.complete" -> handleComplete(functionCall.arguments, onActionTrigger)
                else -> ModernResult.Success(
                    FunctionResult(
                        success = false,
                        error = "未知的训练会话函数: ${functionCall.name}",
                    ),
                )
            }

            when (result) {
                is ModernResult.Success -> result.data
                is ModernResult.Error -> FunctionResult(
                    success = false,
                    error = "执行失败: ${result.error}",
                )

                is ModernResult.Loading -> FunctionResult(
                    success = false,
                    error = "执行超时，请稍后重试",
                )
            }
        }

        /**
         * 处理会话开始
         * 🔥 修复：实际调用Repository进行数据库写入
         */
        private suspend fun handleStart(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val templateId = arguments["template_id"]
                val sessionName = arguments["session_name"] ?: "AI训练会话"
                val notes = arguments["notes"]
                val userId = getCurrentUserId() // 🔥 修复：获取真实用户ID

                logger.d("🔥 开始创建训练会话: templateId='$templateId', sessionName='$sessionName'")

                try {
                    // 🔥 实际创建训练会话
                    val createResult = if (!templateId.isNullOrBlank()) {
                        // 从模板创建会话
                        sessionRepository.createSessionFromTemplate(
                            userId = userId,
                            templateId = templateId,
                        )
                    } else {
                        // 创建空白会话
                        val session = createBlankSession(userId, sessionName, notes)
                        sessionRepository.saveSession(session)
                    }

                    when (createResult) {
                        is ModernResult.Success -> {
                            val sessionId = createResult.data
                            logger.d("✅ 训练会话创建成功: sessionId=$sessionId")

                            // 触发UI动作
                            if (!templateId.isNullOrBlank()) {
                                onActionTrigger?.invoke(WorkoutAction.LoadFromTemplate(templateId))
                            } else {
                                onActionTrigger?.invoke(WorkoutAction.StartNewSession)
                            }

                            FunctionResult(
                                success = true,
                                data = "训练会话创建成功: $sessionName (ID: $sessionId)",
                                actionTriggered = if (!templateId.isNullOrBlank()) "LoadFromTemplate" else "StartNewSession",
                                metadata = mapOf(
                                    "session_id" to sessionId,
                                    "session_name" to sessionName,
                                    "template_id" to (templateId ?: ""),
                                    "user_id" to userId,
                                    "operation" to if (!templateId.isNullOrBlank()) "start_from_template" else "start_new_session",
                                    "created_at" to System.currentTimeMillis().toString(),
                                ),
                                executionPath = "session_start_success",
                                resourceId = sessionId, // 🔥 新增：返回创建的会话ID
                                resourceType = "session", // 🔥 新增：资源类型
                            )
                        }

                        is ModernResult.Error -> {
                            logger.e("❌ 训练会话创建失败: ${createResult.error}")
                            FunctionResult(
                                success = false,
                                error = "训练会话创建失败: ${createResult.error.message}",
                            )
                        }

                        is ModernResult.Loading -> {
                            FunctionResult(
                                success = false,
                                error = "训练会话创建超时，请稍后重试",
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.e("创建训练会话异常", e)
                    FunctionResult(
                        success = false,
                        error = "训练会话创建异常: ${e.message}",
                    )
                }
            }
        }

        /**
         * 处理组数记录
         */
        private suspend fun handleLogSet(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val exerciseId = arguments["exercise_id"]
                if (exerciseId.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "动作ID不能为空",
                    )
                }

                val reps = arguments["reps"]?.toIntOrNull()
                val weight = arguments["weight"]?.toDoubleOrNull()
                val notes = arguments["notes"]

                logger.d("记录训练组: exerciseId='$exerciseId', reps=$reps, weight=$weight")

                // 触发UI动作
                onActionTrigger?.invoke(WorkoutAction.AddSet(exerciseId))

                FunctionResult(
                    success = true,
                    data = "训练组记录已提交: ${reps ?: "未知"}次 @ ${weight ?: "未知"}kg",
                    actionTriggered = "AddSet",
                    metadata = mapOf(
                        "exercise_id" to exerciseId,
                        "reps" to (reps?.toString() ?: ""),
                        "weight" to (weight?.toString() ?: ""),
                        "operation" to "log_set",
                    ),
                )
            }
        }

        /**
         * 处理会话完成
         */
        private suspend fun handleComplete(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val notes = arguments["notes"]
                val rating = arguments["rating"]?.toIntOrNull()

                logger.d("完成训练会话: notes='$notes', rating=$rating")

                // 触发UI动作
                onActionTrigger?.invoke(WorkoutAction.CompleteWorkout)

                FunctionResult(
                    success = true,
                    data = "训练会话完成请求已提交",
                    actionTriggered = "CompleteWorkout",
                    metadata = mapOf(
                        "notes" to (notes ?: ""),
                        "rating" to (rating?.toString() ?: ""),
                        "operation" to "complete_session",
                    ),
                )
            }
        }

        // ========== 🔥 新增：会话创建辅助方法 ==========

        /**
         * 获取当前用户ID
         * 🔥 修复：使用真实的用户认证系统获取用户ID
         */
        private suspend fun getCurrentUserId(): String {
            return try {
                val userIdResult = getCurrentUserIdUseCase().firstOrNull()
                when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (!userId.isNullOrBlank()) {
                            logger.d("✅ 获取到真实用户ID: $userId")
                            userId
                        } else {
                            logger.w("⚠️ 用户ID为空，使用默认值")
                            "user_default"
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("❌ 获取用户ID失败: ${userIdResult.error.message}")
                        "user_default"
                    }

                    is ModernResult.Loading -> {
                        logger.w("⏳ 获取用户ID超时，使用默认值")
                        "user_default"
                    }

                    null -> {
                        logger.w("⚠️ 用户ID结果为null，使用默认值")
                        "user_default"
                    }
                }
            } catch (e: Exception) {
                logger.e("💥 获取用户ID异常: ${e.message}")
                "user_default"
            }
        }

        /**
         * 创建空白训练会话
         */
        private fun createBlankSession(userId: String, sessionName: String, notes: String?): WorkoutSession {
            val sessionId = generateSessionId()
            val currentTime = System.currentTimeMillis()

            return WorkoutSession(
                id = sessionId,
                userId = userId,
                templateId = "", // 空白会话没有模板ID
                planId = null,
                name = sessionName,
                status = "CREATED", // 初始状态为已创建
                startTime = currentTime, // 使用创建时间作为开始时间
                endTime = null,
                totalDuration = null,
                totalVolume = null,
                caloriesBurned = null,
                notes = notes ?: "AI创建的训练会话",
                rating = null,
                lastAutosaveTime = currentTime,
                exercises = emptyList(), // 空白会话，稍后添加动作
            )
        }

        /**
         * 生成会话ID
         */
        private fun generateSessionId(): String {
            val timestamp = System.currentTimeMillis()
            val random = (1000..9999).random()
            return "session_ai_${timestamp}_$random"
        }
    }
