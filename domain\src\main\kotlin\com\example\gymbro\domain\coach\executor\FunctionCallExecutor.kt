package com.example.gymbro.domain.coach.executor

import com.example.gymbro.core.ai.prompt.function.FunctionCallValidator
import com.example.gymbro.core.ai.prompt.function.FunctionLevel
import com.example.gymbro.core.ai.prompt.function.MinimalFunctionSet
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.usecase.ExerciseQueryParams
import com.example.gymbro.domain.exercise.usecase.ExerciseQueryUseCase
import com.example.gymbro.domain.workout.model.WorkoutAction
import com.example.gymbro.domain.workout.usecase.session.EndWorkoutSessionUseCase
import com.example.gymbro.domain.workout.usecase.session.SaveWorkoutSessionUseCase
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.first
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * Function Call 执行器 - 统一架构
 *
 * 集成新的域分离架构和旧版本兼容，提供统一的Function Call执行入口
 * 实现智能路由，自动选择最佳执行路径
 *
 * 核心功能：
 * 1. 架构集成：新域分离架构 + 旧版本兼容
 * 2. 智能路由：自动选择最佳执行路径
 * 3. 验证增强：集成5条硬规则验证机制
 * 4. 性能优化：缓存和批量处理支持
 * 5. 监控统计：完整的执行统计和性能监控
 *
 * 升级策略：
 * - 新函数：优先使用域分离架构
 * - 旧函数：保持兼容，逐步迁移
 * - 混合模式：支持新旧函数同时使用
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (统一架构)
 */
@Singleton
class FunctionCallExecutor
    @Inject
    constructor(
        private val exerciseQueryUseCase: ExerciseQueryUseCase,
        private val saveWorkoutSessionUseCase: SaveWorkoutSessionUseCase,
        private val endWorkoutSessionUseCase: EndWorkoutSessionUseCase,
        private val functionCallRouter: FunctionCallRouter,
        private val validator: FunctionCallValidator,
        private val minimalFunctionSet: MinimalFunctionSet,
        private val logger: Logger,
    ) {

        companion object {
            // 新架构函数前缀
            private val NEW_ARCHITECTURE_PREFIXES = setOf(
                "gymbro.exercise.",
                "gymbro.template.",
                "gymbro.plan.",
                "gymbro.session.",
            )

            // 旧版本函数名
            private val LEGACY_FUNCTION_NAMES = setOf(
                "start_workout_session",
                "add_exercise_to_session",
                "log_exercise_set",
                "search_exercises",
                "get_workout_history",
                "complete_workout_session",
                "set_rest_timer",
                "get_exercise_recommendations",
            )
        }

        // 执行统计
        private val executionStats = ExecutionStats()

        /**
         * 函数调用请求数据类
         */
        @Serializable
        data class FunctionCall(
            val name: String,
            val arguments: Map<String, String>,
        )

        /**
         * 函数执行结果
         * 🔥 增强：添加resourceId支持精确导航
         */
        @Serializable
        data class FunctionResult(
            val success: Boolean,
            val data: String? = null,
            val error: String? = null,
            val actionTriggered: String? = null, // 触发的UI Action
            val metadata: Map<String, String> = emptyMap(),
            val executionPath: String? = null, // 执行路径标识
            val resourceId: String? = null, // 🔥 新增：创建的资源ID（如templateId, planId, sessionId）
            val resourceType: String? = null, // 🔥 新增：资源类型（如template, plan, session, exercise）
        )

        /**
         * 执行函数调用 - 统一入口
         *
         * @param functionCall Function Call请求
         * @param onActionTrigger UI动作触发回调
         * @return 函数执行结果
         */
        suspend fun executeFunctionCall(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)? = null,
        ): ModernResult<FunctionResult> = safeCatch {
            val startTime = System.currentTimeMillis()

            try {
                logger.d("🚀 执行Function Call: ${functionCall.name}")

                // 1. 验证Function Call
                val argumentsJson = Json.encodeToString(functionCall.arguments)
                val validationResult = validator.validateFunctionCall(
                    com.example.gymbro.core.ai.prompt.function.FunctionCall(
                        name = functionCall.name,
                        arguments = argumentsJson,
                    ),
                )

                if (!validationResult.isValid) {
                    executionStats.recordFailure(functionCall.name, "validation_failed")
                    return@safeCatch FunctionResult(
                        success = false,
                        error = "验证失败: ${validationResult.message}",
                        executionPath = "validation_failed",
                    )
                }

                // 2. 选择执行路径
                val result = if (isNewArchitectureFunction(functionCall.name)) {
                    executeWithNewArchitecture(functionCall, onActionTrigger)
                } else {
                    executeWithLegacyArchitecture(functionCall, onActionTrigger)
                }

                // 3. 记录统计
                val duration = System.currentTimeMillis() - startTime
                when (result) {
                    is ModernResult.Success -> {
                        if (result.data.success) {
                            executionStats.recordSuccess(functionCall.name, duration)
                        } else {
                            executionStats.recordFailure(functionCall.name, "execution_failed")
                        }
                    }

                    is ModernResult.Error -> {
                        executionStats.recordFailure(functionCall.name, "system_error")
                    }

                    is ModernResult.Loading -> {
                        executionStats.recordFailure(functionCall.name, "timeout")
                    }
                }

                when (result) {
                    is ModernResult.Success -> result.data
                    is ModernResult.Error -> FunctionResult(
                        success = false,
                        error = "系统错误: ${result.error}",
                        executionPath = "error_fallback",
                    )

                    is ModernResult.Loading -> FunctionResult(
                        success = false,
                        error = "执行超时",
                        executionPath = "timeout_fallback",
                    )
                }
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                executionStats.recordFailure(functionCall.name, "exception")

                logger.e("Function Call执行异常: ${functionCall.name}", e)
                FunctionResult(
                    success = false,
                    error = "函数调用执行失败: ${e.message}",
                    executionPath = "exception_fallback",
                )
            }
        }

        /**
         * 使用新架构执行
         */
        private suspend fun executeWithNewArchitecture(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            logger.d("🆕 使用新架构执行: ${functionCall.name}")

            // 转换为新架构的数据格式
            val routerCall = com.example.gymbro.domain.coach.executor.FunctionCall(
                name = functionCall.name,
                arguments = functionCall.arguments,
            )

            // 使用路由器执行
            val routerResult = functionCallRouter.routeFunctionCall(routerCall, onActionTrigger)

            when (routerResult) {
                is ModernResult.Success -> {
                    // 转换结果格式
                    FunctionResult(
                        success = routerResult.data.success,
                        data = routerResult.data.data,
                        error = routerResult.data.error,
                        actionTriggered = routerResult.data.actionTriggered,
                        metadata = routerResult.data.metadata.mapValues { it.value.toString() },
                        executionPath = "new_architecture",
                    )
                }

                is ModernResult.Error -> {
                    FunctionResult(
                        success = false,
                        error = "新架构执行失败: ${routerResult.error}",
                        executionPath = "new_architecture",
                    )
                }

                is ModernResult.Loading -> {
                    FunctionResult(
                        success = false,
                        error = "新架构执行超时",
                        executionPath = "new_architecture",
                    )
                }
            }
        }

        /**
         * 使用旧架构执行
         */
        private suspend fun executeWithLegacyArchitecture(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            logger.d("🔄 使用旧架构执行: ${functionCall.name}")

            val result = when (functionCall.name) {
                "start_workout_session" -> executeStartWorkoutSession(functionCall.arguments, onActionTrigger)
                "add_exercise_to_session" -> executeAddExerciseToSession(functionCall.arguments, onActionTrigger)
                "log_exercise_set" -> executeLogExerciseSet(functionCall.arguments, onActionTrigger)
                "search_exercises" -> executeSearchExercises(functionCall.arguments)
                "get_workout_history" -> executeGetWorkoutHistory(functionCall.arguments)
                "complete_workout_session" -> executeCompleteWorkoutSession(
                    functionCall.arguments,
                    onActionTrigger,
                )

                "set_rest_timer" -> executeSetRestTimer(functionCall.arguments, onActionTrigger)
                "get_exercise_recommendations" -> executeGetExerciseRecommendations(functionCall.arguments)
                else -> FunctionResult(
                    success = false,
                    error = "未知的函数名称: ${functionCall.name}",
                    executionPath = "legacy_architecture",
                )
            }

            result.copy(executionPath = "legacy_architecture")
        }

        /**
         * 判断是否为新架构函数
         */
        private fun isNewArchitectureFunction(functionName: String): Boolean {
            return NEW_ARCHITECTURE_PREFIXES.any { prefix ->
                functionName.startsWith(prefix)
            }
        }

        /**
         * 开始训练会话
         */
        private suspend fun executeStartWorkoutSession(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): FunctionResult {
            val templateId = arguments["templateId"]
            val sessionName = arguments["sessionName"]

            return if (!templateId.isNullOrBlank()) {
                // 从模板开始
                onActionTrigger?.invoke(WorkoutAction.LoadFromTemplate(templateId))
                FunctionResult(
                    success = true,
                    data = "正在从模板创建训练会话...",
                    actionTriggered = "LoadFromTemplate",
                )
            } else {
                // 开始新会话
                onActionTrigger?.invoke(WorkoutAction.StartNewSession)
                FunctionResult(
                    success = true,
                    data = "正在开始新的训练会话...",
                    actionTriggered = "StartNewSession",
                )
            }
        }

        /**
         * 添加练习到会话
         */
        private suspend fun executeAddExerciseToSession(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): FunctionResult {
            val exerciseId = arguments["exerciseId"]
            if (exerciseId.isNullOrBlank()) {
                return FunctionResult(success = false, error = "练习ID不能为空")
            }

            // TODO: 实现添加练习逻辑
            onActionTrigger?.invoke(WorkoutAction.AddSet(exerciseId))

            return FunctionResult(
                success = true,
                data = "已添加练习到训练会话",
                actionTriggered = "AddSet",
            )
        }

        /**
         * 记录练习组
         */
        private suspend fun executeLogExerciseSet(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): FunctionResult {
            val exerciseId = arguments["exerciseId"]
            val reps = arguments["reps"]?.toIntOrNull()
            val weight = arguments["weight"]?.toDoubleOrNull()

            if (exerciseId.isNullOrBlank() || reps == null || weight == null) {
                return FunctionResult(success = false, error = "练习ID、次数和重量都是必需的")
            }

            // TODO: 实现记录练习组逻辑
            // 当前触发添加组操作
            onActionTrigger?.invoke(WorkoutAction.AddSet(exerciseId))

            return FunctionResult(
                success = true,
                data = "已记录练习组: ${reps}次 @ ${weight}kg",
                actionTriggered = "AddSet",
            )
        }

        /**
         * 搜索练习
         */
        private suspend fun executeSearchExercises(arguments: Map<String, String>): FunctionResult {
            val query = arguments["query"] ?: ""
            val muscleGroup = arguments["muscle_group"]
            val equipment = arguments["equipment"]
            val difficulty = arguments["difficulty"]

            val searchParams =
                ExerciseQueryParams.Filtered(
                    searchQuery = query.takeIf { it.isNotBlank() },
                    muscleGroup = muscleGroup,
                    equipment = equipment,
                    difficulty = difficulty,
                )

            return when (val result = exerciseQueryUseCase(searchParams).first()) {
                is ModernResult.Success -> {
                    val exercises = result.data
                    val resultText =
                        if (exercises.isEmpty()) {
                            "未找到匹配的练习"
                        } else {
                            "找到 ${exercises.size} 个练习:\n${
                                exercises.take(
                                    5,
                                ).joinToString("\n") { "• ${it.name}" }
                            }"
                        }
                    FunctionResult(success = true, data = resultText)
                }

                is ModernResult.Error -> {
                    FunctionResult(success = false, error = "搜索练习失败")
                }

                is ModernResult.Loading -> {
                    FunctionResult(success = false, error = "正在搜索练习...")
                }
            }
        }

        /**
         * 获取训练历史
         */
        private suspend fun executeGetWorkoutHistory(arguments: Map<String, String>): FunctionResult {
            val days = arguments["days"]?.toIntOrNull() ?: 30
            val exerciseId = arguments["exercise_id"]

            // TODO: 实现获取训练历史逻辑
            return FunctionResult(
                success = true,
                data = "训练历史功能正在开发中，将显示最近${days}天的数据",
            )
        }

        /**
         * 完成训练会话
         */
        private suspend fun executeCompleteWorkoutSession(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): FunctionResult {
            val notes = arguments["notes"]
            val rating = arguments["rating"]?.toIntOrNull()

            onActionTrigger?.invoke(WorkoutAction.CompleteWorkout)

            return FunctionResult(
                success = true,
                data = "正在完成训练会话...",
                actionTriggered = "CompleteWorkout",
            )
        }

        /**
         * 设置休息计时器
         */
        private suspend fun executeSetRestTimer(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): FunctionResult {
            val seconds = arguments["seconds"]?.toIntOrNull()
            if (seconds == null || seconds <= 0) {
                return FunctionResult(success = false, error = "无效的计时器时间")
            }

            // 调整计时器时长并开始
            onActionTrigger?.invoke(WorkoutAction.AdjustTimerDuration(seconds))
            onActionTrigger?.invoke(WorkoutAction.StartRestTimer)

            return FunctionResult(
                success = true,
                data = "已设置${seconds}秒休息计时器",
                actionTriggered = "StartRestTimer",
            )
        }

        /**
         * 获取练习建议
         */
        private suspend fun executeGetExerciseRecommendations(
            arguments: Map<String, String>,
        ): FunctionResult {
            val muscleGroup = arguments["muscle_group"]
            val workoutType = arguments["workout_type"]
            val availableTime = arguments["available_time"]?.toIntOrNull()

            // TODO: 实现基于历史数据的练习建议算法
            return FunctionResult(
                success = true,
                data = "练习建议功能正在开发中，将基于您的训练历史提供个性化建议",
            )
        }

        /**
         * 解析函数调用JSON
         */
        fun parseFunctionCall(functionCallJson: String): ModernResult<FunctionCall> =
            try {
                val functionCall = Json.decodeFromString<FunctionCall>(functionCallJson)
                ModernResult.Success(functionCall)
            } catch (e: Exception) {
                logger.e("解析函数调用JSON失败", e)
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "FunctionCall.parse",
                        message = UiText.DynamicString("解析函数调用失败: ${e.message}"),
                        cause = e,
                    ),
                )
            }

        /**
         * 获取支持的函数列表
         */
        fun getSupportedFunctions(): Map<String, Any> {
            val newFunctions = functionCallRouter.getSupportedFunctions()
            val legacyFunctions = LEGACY_FUNCTION_NAMES.toList()

            return mapOf(
                "new_architecture" to newFunctions,
                "legacy_architecture" to legacyFunctions,
                "total_count" to (newFunctions.values.sumOf { (it as? Collection<*>)?.size ?: 0 } + legacyFunctions.size),
                "core_functions" to minimalFunctionSet.getFunctionsByLevel(FunctionLevel.CORE).map { it.name },
            )
        }

        /**
         * 获取执行统计信息
         */
        fun getExecutionStats(): ExecutionStatsReport {
            return ExecutionStatsReport(
                totalExecutions = executionStats.totalExecutions,
                successfulExecutions = executionStats.successfulExecutions,
                failedExecutions = executionStats.failedExecutions,
                averageExecutionTime = executionStats.getAverageExecutionTime(),
                successRate = executionStats.getSuccessRate(),
                newArchitectureUsage = executionStats.newArchitectureUsage,
                legacyArchitectureUsage = executionStats.legacyArchitectureUsage,
                topFunctions = executionStats.getTopFunctions(5),
                generatedAt = System.currentTimeMillis(),
            )
        }

        /**
         * 重置统计信息
         */
        fun resetStats() {
            executionStats.reset()
            logger.i("Function Call执行统计已重置")
        }
    }

/**
 * 执行统计类
 */
private class ExecutionStats {
    var totalExecutions = 0L
    var successfulExecutions = 0L
    var failedExecutions = 0L
    var newArchitectureUsage = 0L
    var legacyArchitectureUsage = 0L

    private val executionTimes = mutableListOf<Long>()
    private val functionCounts = mutableMapOf<String, Long>()
    private val failureReasons = mutableMapOf<String, Long>()

    fun recordSuccess(functionName: String, executionTime: Long) {
        totalExecutions++
        successfulExecutions++
        executionTimes.add(executionTime)
        functionCounts[functionName] = functionCounts.getOrDefault(functionName, 0) + 1

        if (functionName.startsWith("gymbro.")) {
            newArchitectureUsage++
        } else {
            legacyArchitectureUsage++
        }
    }

    fun recordFailure(functionName: String, reason: String) {
        totalExecutions++
        failedExecutions++
        failureReasons[reason] = failureReasons.getOrDefault(reason, 0) + 1

        if (functionName.startsWith("gymbro.")) {
            newArchitectureUsage++
        } else {
            legacyArchitectureUsage++
        }
    }

    fun getAverageExecutionTime(): Double {
        return if (executionTimes.isNotEmpty()) {
            executionTimes.average()
        } else {
            0.0
        }
    }

    fun getSuccessRate(): Double {
        return if (totalExecutions > 0) {
            successfulExecutions.toDouble() / totalExecutions
        } else {
            0.0
        }
    }

    fun getTopFunctions(limit: Int): List<Pair<String, Long>> {
        return functionCounts.toList()
            .sortedByDescending { it.second }
            .take(limit)
    }

    fun reset() {
        totalExecutions = 0
        successfulExecutions = 0
        failedExecutions = 0
        newArchitectureUsage = 0
        legacyArchitectureUsage = 0
        executionTimes.clear()
        functionCounts.clear()
        failureReasons.clear()
    }
}

/**
 * 执行统计报告
 */
data class ExecutionStatsReport(
    val totalExecutions: Long,
    val successfulExecutions: Long,
    val failedExecutions: Long,
    val averageExecutionTime: Double,
    val successRate: Double,
    val newArchitectureUsage: Long,
    val legacyArchitectureUsage: Long,
    val topFunctions: List<Pair<String, Long>>,
    val generatedAt: Long,
)
