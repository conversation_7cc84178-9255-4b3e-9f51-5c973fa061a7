package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.shared.models.workout.WorkoutPlan
import javax.inject.Inject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow

/**
 * 获取分类训练计划用例
 *
 * 🎯 核心功能：支持Plan层的三Tab设计
 * - 全部计划
 * - 收藏计划
 * - AI生成计划
 *
 * 基于用户反馈，去掉搜索功能，只保留分类功能
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Plan层改造设计)
 */
class GetCategorizedPlansUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernFlowUseCase<GetCategorizedPlansUseCase.Params, GetCategorizedPlansUseCase.Result>(
        dispatcher,
        logger,
    ) {

        /**
         * 计划分类枚举
         */
        enum class PlanCategory {
            ALL, // 全部计划
            FAVORITES, // 收藏计划
            AI_GENERATED, // AI生成计划
        }

        /**
         * 参数类
         *
         * @property category 计划分类
         */
        data class Params(
            val category: PlanCategory,
        )

        /**
         * 结果类
         *
         * @property plans 计划列表
         * @property category 分类类型
         * @property totalCount 总数量
         */
        data class Result(
            val plans: List<WorkoutPlan>,
            val category: PlanCategory,
            val totalCount: Int,
        )

        /**
         * 执行用例，获取分类的训练计划
         *
         * @param parameters 参数
         * @return 分类计划数据流
         */
        override fun createFlow(parameters: Params): Flow<ModernResult<Result>> = flow {
            try {
                // 获取当前用户ID
                val userIdResult = getCurrentUserIdUseCase().first()
                if (userIdResult !is ModernResult.Success || userIdResult.data.isNullOrBlank()) {
                    emit(
                        ModernResult.Error(
                            BusinessErrors.BusinessError.rule(
                                operationName = "getCategorizedPlans",
                                message = UiText.DynamicString("用户未登录"),
                                metadataMap = mapOf(
                                    "errorType" to "UNAUTHORIZED",
                                    "category" to parameters.category.name,
                                ),
                            ),
                        ),
                    )
                    return@flow
                }
                val userId = userIdResult.data!! // 确保非空

                // 根据分类获取计划
                val plansFlow = when (parameters.category) {
                    PlanCategory.ALL -> {
                        logger.d("获取全部计划: userId=$userId")
                        planRepository.getUserPlans(userId)
                    }

                    PlanCategory.FAVORITES -> {
                        logger.d("获取收藏计划: userId=$userId")
                        planRepository.getFavoritePlans(userId)
                    }

                    PlanCategory.AI_GENERATED -> {
                        logger.d("获取AI生成计划: userId=$userId")
                        planRepository.getAIGeneratedPlans(userId)
                    }
                }

                // 收集计划数据并转换为Result格式
                plansFlow.collect { plans ->
                    val result = Result(
                        plans = plans,
                        category = parameters.category,
                        totalCount = plans.size,
                    )

                    logger.d("获取分类计划成功: category=${parameters.category}, count=${plans.size}")
                    emit(ModernResult.Success(result))
                }
            } catch (e: Exception) {
                logger.e("获取分类计划失败: category=${parameters.category}, error=${e.message}")
                emit(
                    ModernResult.Error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "getCategorizedPlans",
                            message = UiText.DynamicString("获取计划失败: ${e.message}"),
                            metadataMap = mapOf(
                                "category" to parameters.category.name,
                                "exception" to e.javaClass.simpleName,
                            ),
                        ),
                    ),
                )
            }
        }
    }
