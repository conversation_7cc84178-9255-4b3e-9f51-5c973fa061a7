package com.example.gymbro.domain.auth.usecase.phone

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.model.auth.Credentials
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * 验证手机验证码的用例
 *
 * 该用例封装了验证手机验证码的业务逻辑。
 * 执行验证码验证并处理可能的错误。
 *
 * @property authRepository 认证仓库
 * @property dispatcher 协程调度器
 * @property logger 日志记录器
 */
@Singleton
class VerifyPhoneCodeUseCase
    @Inject
    constructor(
        private val authRepository: AuthRepository,
        @IoDispatcher dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<VerifyPhoneCodeUseCase.Params, AuthUser>(dispatcher, logger) {
        /**
         * 参数数据类
         */
        data class Params(
            val phoneNumber: String,
            val verificationCode: String,
            val verificationId: String,
        )

        /**
         * 执行验证手机验证码的业务逻辑
         *
         * @param parameters 包含手机号、验证码和验证ID的参数
         * @return 认证用户信息或错误结果
         */
        override suspend fun execute(parameters: Params): ModernResult<AuthUser> {
            val validationResult = validateParameters(parameters)
            if (validationResult is ModernResult.Error) {
                return validationResult
            }

            // 先验证验证码
            val verifyResult =
                authRepository.verifyPhoneCode(
                    phoneNumber = parameters.phoneNumber,
                    verificationCode = parameters.verificationCode,
                    verificationId = parameters.verificationId,
                )

            // 验证成功后进行手机号登录
            return when (verifyResult) {
                is ModernResult.Success -> {
                    val credentials =
                        Credentials.Phone(
                            phoneNumber = parameters.phoneNumber,
                            verificationCode = parameters.verificationCode,
                            verificationId = parameters.verificationId,
                        )
                    authRepository.loginWithPhone(credentials)
                }

                is ModernResult.Error -> ModernResult.Error(verifyResult.error)
                is ModernResult.Loading -> ModernResult.Loading
            }
        }

        /**
         * 验证参数
         */
        private fun validateParameters(params: Params): ModernResult<Unit> {
            if (params.phoneNumber.isBlank()) {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "phoneNumber",
                        operationName = "validateParameters",
                    ),
                )
            }

            if (params.verificationCode.isBlank()) {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "verificationCode",
                        operationName = "validateParameters",
                    ),
                )
            }

            if (params.verificationId.isBlank()) {
                return ModernResult.Error(
                    DataErrors.Validation.required(
                        field = "verificationId",
                        operationName = "validateParameters",
                    ),
                )
            }

            // 验证码格式验证（通常是6位数字）
            val codeRegex = Regex("^\\d{6}$")
            if (!params.verificationCode.matches(codeRegex)) {
                return ModernResult.Error(
                    DataErrors.Validation.formatError(
                        field = "verificationCode",
                        operationName = "validateParameters",
                        message = UiText.DynamicString("验证码格式错误，请输入6位数字验证码"),
                    ),
                )
            }

            return ModernResult.Success(Unit)
        }
    }
