package com.example.gymbro.features.workout.stats.internal.effect

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.stats.TimeRange
import com.example.gymbro.domain.workout.usecase.analysis.SendTrainingAnalysisUseCase
import com.example.gymbro.domain.workout.usecase.analysis.SimpleTimeRange
import com.example.gymbro.domain.workout.usecase.stats.CreateDailyStatsUseCase
import com.example.gymbro.domain.workout.usecase.stats.GetStatsUseCase
import com.example.gymbro.features.workout.stats.StatsContract
import javax.inject.Inject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import timber.log.Timber

/**
 * Stats EffectHandler - 副作用处理器
 *
 * 负责处理Stats模块的所有副作用操作，包括数据加载、
 * 导航、UI副作用等。与Reducer配合完成MVI 2.0数据流。
 *
 * 核心职责：
 * - 执行异步数据操作
 * - 处理导航副作用
 * - 管理UI副作用
 * - 错误处理和重试机制
 * - 缓存策略执行
 *
 * 设计原则：
 * - 所有副作用都在此处处理
 * - 通过dispatch回调与ViewModel通信
 * - 异步操作使用CoroutineScope
 * - 完备的错误处理覆盖
 *
 * @param getStatsUseCase 获取统计数据用例
 * @param createDailyStatsUseCase 创建日级统计用例
 * @param sendTrainingAnalysisUseCase AI训练分析用例
 */
internal class StatsEffectHandler
    @Inject
    constructor(
        private val getStatsUseCase: GetStatsUseCase,
        private val createDailyStatsUseCase: CreateDailyStatsUseCase,
        private val sendTrainingAnalysisUseCase: SendTrainingAnalysisUseCase,
    ) {

        private lateinit var handlerScope: CoroutineScope
        private lateinit var dispatch: (StatsContract.Intent) -> Unit

        /**
         * 初始化EffectHandler
         *
         * @param scope 协程作用域
         * @param intentSender Intent分发器
         */
        fun initialize(
            scope: CoroutineScope,
            intentSender: (StatsContract.Intent) -> Unit,
        ) {
            this.handlerScope = scope
            this.dispatch = intentSender
        }

        /**
         * 处理Effect
         *
         * @param effect 要处理的副作用
         * @param state 当前状态
         * @param scope 协程作用域
         * @param dispatch Intent分发器
         */
        fun handle(
            effect: StatsContract.Effect,
            state: StatsContract.State,
            scope: CoroutineScope,
            dispatch: (StatsContract.Intent) -> Unit,
        ) {
            when (effect) {
                // === 数据持久化副作用 ===
                is StatsContract.Effect.LoadStatsData -> handleLoadStatsData(effect, scope, dispatch)
                is StatsContract.Effect.PreloadStatsForRange -> handlePreloadStatsForRange(
                    effect,
                    scope,
                    dispatch,
                )

                is StatsContract.Effect.CacheStatsData -> handleCacheStatsData(effect)

                // === 导航副作用 ===
                is StatsContract.Effect.NavigateBack -> handleNavigateBack()
                is StatsContract.Effect.NavigateToSessionDetail -> handleNavigateToSessionDetail(effect)
                is StatsContract.Effect.NavigateToExerciseStats -> handleNavigateToExerciseStats(effect)
                is StatsContract.Effect.NavigateToStatsHistory -> handleNavigateToStatsHistory(effect)
                is StatsContract.Effect.NavigateToStartWorkout -> handleNavigateToStartWorkout()

                // === UI副作用 ===
                is StatsContract.Effect.ShowToast -> handleShowToast(effect)
                is StatsContract.Effect.ShowSnackbar -> handleShowSnackbar(effect)
                is StatsContract.Effect.ShowBottomSheet -> handleShowBottomSheet(effect)
                is StatsContract.Effect.HideBottomSheet -> handleHideBottomSheet()

                // === 图表副作用 ===
                is StatsContract.Effect.AnimateChart -> handleAnimateChart(effect)
                is StatsContract.Effect.UpdateChartData -> handleUpdateChartData(effect, state, scope, dispatch)

                // === AI分析副作用 ===
                is StatsContract.Effect.StartAiAnalysis -> handleStartAiAnalysis(effect, scope, dispatch)

                // === 分享和导出副作用 ===
                is StatsContract.Effect.ShareStats -> handleShareStats(effect)
                is StatsContract.Effect.ExportStatsData -> handleExportStatsData(effect)

                // === 系统副作用 ===
                is StatsContract.Effect.HapticFeedback -> handleHapticFeedback()
                is StatsContract.Effect.SetRefreshIndicator -> handleSetRefreshIndicator(effect)
                is StatsContract.Effect.TriggerDataSync -> handleTriggerDataSync()
            }
        }

        // ==================== 数据持久化副作用处理器 ====================

        private fun handleLoadStatsData(
            effect: StatsContract.Effect.LoadStatsData,
            scope: CoroutineScope,
            dispatch: (StatsContract.Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    // 获取主要统计数据
                    val statsFlow = getStatsUseCase(
                        timeRange = effect.timeRange,
                        startDate = effect.startDate,
                        endDate = effect.endDate,
                    )

                    // 获取对比数据（如果需要）
                    val comparisonStats = if (effect.includeComparison) {
                        // 计算对比时间范围（例如上一周、上一月等）
                        val comparisonStartDate = effect.startDate?.minus(7, DateTimeUnit.DAY)
                        val comparisonEndDate = effect.endDate?.minus(7, DateTimeUnit.DAY)

                        if (comparisonStartDate != null && comparisonEndDate != null) {
                            val comparisonResult = getStatsUseCase(
                                timeRange = effect.timeRange,
                                startDate = comparisonStartDate,
                                endDate = comparisonEndDate,
                            ).firstOrNull()

                            when (comparisonResult) {
                                is ModernResult.Success -> comparisonResult.data
                                is ModernResult.Error -> null
                                else -> null
                            }
                        } else {
                            null
                        }
                    } else {
                        null
                    }

                    // 订阅统计数据流
                    statsFlow
                        .onStart {
                            // 可以在这里显示加载指示器
                        }
                        .catch { throwable ->
                            dispatch(
                                StatsContract.Intent.StatsLoadError(
                                    UiText.DynamicString("加载统计数据失败: ${throwable.message}"),
                                ),
                            )
                        }
                        .collect { result ->
                            when (result) {
                                is ModernResult.Success -> {
                                    val stats = result.data
                                    dispatch(
                                        StatsContract.Intent.StatsLoaded(
                                            stats = stats,
                                            dailyStats = stats.dailyStats,
                                            comparisonStats = comparisonStats,
                                        ),
                                    )

                                    // 计算趋势数据
                                    comparisonStats?.let { comparison ->
                                        val trend = stats.calculateProgressTrend(comparison)
                                        dispatch(StatsContract.Intent.TrendCalculated(trend))
                                    }
                                }

                                is ModernResult.Error -> {
                                    dispatch(
                                        StatsContract.Intent.StatsLoadError(
                                            UiText.DynamicString("加载统计数据失败"),
                                        ),
                                    )
                                }

                                is ModernResult.Loading -> {
                                    // 处理加载状态，如果需要的话
                                }
                            }
                        }
                } catch (e: Exception) {
                    dispatch(
                        StatsContract.Intent.StatsLoadError(
                            UiText.DynamicString("加载统计数据异常: ${e.message}"),
                        ),
                    )
                }
            }
        }

        private fun handlePreloadStatsForRange(
            effect: StatsContract.Effect.PreloadStatsForRange,
            scope: CoroutineScope,
            dispatch: (StatsContract.Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    val result = getStatsUseCase(effect.timeRange).firstOrNull()
                    when (result) {
                        is ModernResult.Success -> {
                            // 预加载成功，可以缓存数据
                            // 这里可以添加缓存逻辑
                        }

                        is ModernResult.Error -> {
                            // 预加载失败，记录但不影响主流程
                        }

                        else -> {
                            // 处理其他情况
                        }
                    }
                } catch (e: Exception) {
                    // 预加载异常，记录但不影响主流程
                }
            }
        }

        private fun handleCacheStatsData(effect: StatsContract.Effect.CacheStatsData) {
            // 实现缓存逻辑
            // 可以使用内存缓存或本地存储
        }

        // ==================== 导航副作用处理器 ====================

        private fun handleNavigateBack() {
            // 触发导航回退
            // 具体实现依赖于导航框架
        }

        private fun handleNavigateToSessionDetail(effect: StatsContract.Effect.NavigateToSessionDetail) {
            // 导航到Session详情页
            // 传递sessionId参数
        }

        private fun handleNavigateToExerciseStats(effect: StatsContract.Effect.NavigateToExerciseStats) {
            // 导航到Exercise统计页
            // 传递exerciseId参数
        }

        private fun handleNavigateToStatsHistory(effect: StatsContract.Effect.NavigateToStatsHistory) {
            // 导航到统计历史页面
            // 传递timeRange参数
        }

        // ==================== UI副作用处理器 ====================

        private fun handleShowToast(effect: StatsContract.Effect.ShowToast) {
            // 显示Toast消息
            // 具体实现依赖于UI框架
        }

        private fun handleShowSnackbar(effect: StatsContract.Effect.ShowSnackbar) {
            // 显示Snackbar消息
            // 具体实现依赖于UI框架
        }

        private fun handleShowBottomSheet(effect: StatsContract.Effect.ShowBottomSheet) {
            // 显示底部弹窗
            // 根据content类型显示不同内容
        }

        private fun handleHideBottomSheet() {
            // 隐藏底部弹窗
        }

        // ==================== 图表副作用处理器 ====================

        private fun handleAnimateChart(effect: StatsContract.Effect.AnimateChart) {
            // 执行图表动画
            // 具体实现依赖于图表库
        }

        private fun handleUpdateChartData(
            effect: StatsContract.Effect.UpdateChartData,
            state: StatsContract.State,
            scope: CoroutineScope,
            dispatch: (StatsContract.Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    // 根据当前数据生成图表数据
                    val chartData = generateChartData(state.dailyStats, state.chartType)
                    dispatch(StatsContract.Intent.ChartDataUpdated(chartData))
                } catch (e: Exception) {
                    dispatch(
                        StatsContract.Intent.ShowError(
                            UiText.DynamicString("更新图表数据失败: ${e.message}"),
                        ),
                    )
                }
            }
        }

        private fun generateChartData(
            dailyStats: List<com.example.gymbro.domain.workout.model.stats.DailyStats>,
            chartType: StatsContract.ChartType,
        ): StatsContract.ChartData {
            // 根据图表类型和数据生成图表数据
            return when (chartType) {
                StatsContract.ChartType.LINE -> generateLineChartData(dailyStats)
                StatsContract.ChartType.BAR -> generateBarChartData(dailyStats)
                StatsContract.ChartType.PIE -> generatePieChartData(dailyStats)
                StatsContract.ChartType.AREA -> generateAreaChartData(dailyStats)
                StatsContract.ChartType.SCATTER -> generateScatterChartData(dailyStats)
            }
        }

        private fun generateLineChartData(dailyStats: List<com.example.gymbro.domain.workout.model.stats.DailyStats>): StatsContract.ChartData {
            val points = dailyStats.mapIndexed { index, stats ->
                StatsContract.ChartPoint(
                    x = index.toFloat(),
                    y = stats.totalWeight.toFloat(),
                    label = "${stats.date.month.value}/${stats.date.dayOfMonth}",
                )
            }

            return StatsContract.ChartData(
                lineData = points,
                labels = dailyStats.map { "${it.date.month.value}/${it.date.dayOfMonth}" },
                colors = listOf("#2196F3"), // Material Blue
            )
        }

        private fun generateBarChartData(dailyStats: List<com.example.gymbro.domain.workout.model.stats.DailyStats>): StatsContract.ChartData {
            val bars = dailyStats.map { stats ->
                StatsContract.BarValue(
                    value = stats.completedSets.toFloat(),
                    label = "${stats.date.month.value}/${stats.date.dayOfMonth}",
                    color = "#4CAF50", // Material Green
                )
            }

            return StatsContract.ChartData(
                barData = bars,
                labels = dailyStats.map { "${it.date.month.value}/${it.date.dayOfMonth}" },
                colors = listOf("#4CAF50"),
            )
        }

        private fun generatePieChartData(dailyStats: List<com.example.gymbro.domain.workout.model.stats.DailyStats>): StatsContract.ChartData {
            val intensityGroups = dailyStats.groupBy { it.getIntensityLevel() }
            val slices = intensityGroups.map { (intensity, stats) ->
                StatsContract.PieSlice(
                    value = stats.size.toFloat(),
                    label = intensity.displayName,
                    color = intensity.colorHex,
                )
            }

            return StatsContract.ChartData(
                pieData = slices,
                labels = slices.map { it.label },
                colors = slices.map { it.color },
            )
        }

        private fun generateAreaChartData(dailyStats: List<com.example.gymbro.domain.workout.model.stats.DailyStats>): StatsContract.ChartData {
            // 类似线图但填充区域
            return generateLineChartData(dailyStats).copy(
                colors = listOf("#E3F2FD", "#2196F3"), // Light blue fill + blue line
            )
        }

        private fun generateScatterChartData(dailyStats: List<com.example.gymbro.domain.workout.model.stats.DailyStats>): StatsContract.ChartData {
            val points = dailyStats.map { stats ->
                StatsContract.ChartPoint(
                    x = stats.sessionDurationSec.toFloat() / 60, // 转换为分钟
                    y = stats.totalWeight.toFloat(),
                    label = "${stats.date.month.value}/${stats.date.dayOfMonth}",
                )
            }

            return StatsContract.ChartData(
                lineData = points, // 散点图复用点数据
                labels = listOf("时长(分钟)", "重量(kg)"),
                colors = listOf("#FF9800"), // Material Orange
            )
        }

        // ==================== 分享和导出副作用处理器 ====================

        private fun handleShareStats(effect: StatsContract.Effect.ShareStats) {
            // 实现统计数据分享
            // 可以生成图片、PDF或文本格式
        }

        private fun handleExportStatsData(effect: StatsContract.Effect.ExportStatsData) {
            // 实现统计数据导出
            // 支持CSV、JSON、PDF等格式
        }

        // ==================== 系统副作用处理器 ====================

        private fun handleHapticFeedback() {
            // 触发触觉反馈
            // 具体实现依赖于平台
        }

        private fun handleSetRefreshIndicator(effect: StatsContract.Effect.SetRefreshIndicator) {
            // 设置下拉刷新指示器状态
        }

        private fun handleTriggerDataSync() {
            // 触发数据同步
            // 可以与远程服务器同步统计数据
        }

        private fun handleNavigateToStartWorkout() {
            // 导航到开始训练页面
            // 具体实现依赖于导航系统
            Timber.d("导航到开始训练页面")
        }

        // ==================== AI分析副作用处理器 ====================

        /**
         * 处理AI分析请求
         *
         * 🔥 【事件总线架构】调用SendTrainingAnalysisUseCase，Token会自动发布到TokenBus
         * 🔥 【硬指标】每个session完成ID只发送一次，通过completedAnalysisIds防重复
         */
        private fun handleStartAiAnalysis(
            effect: StatsContract.Effect.StartAiAnalysis,
            scope: CoroutineScope,
            dispatch: (StatsContract.Intent) -> Unit,
        ) {
            scope.launch {
                try {
                    Timber.d("开始AI分析: 数据天数=${effect.dailyStats.size}, 模型=${effect.model}")

                    // 构建UseCase参数
                    val params = SendTrainingAnalysisUseCase.Params(
                        userId = "current_user", // TODO: 从用户管理获取实际用户ID
                        timeRange = effect.timeRange.toSimpleTimeRange(),
                        dailyStats = effect.dailyStats,
                        model = effect.model,
                    )

                    // 🔥 【事件总线架构】调用UseCase，Token会自动发布到TokenBus
                    sendTrainingAnalysisUseCase(params).collect { result ->
                        when (result) {
                            is ModernResult.Success -> {
                                val analysisId = result.data
                                Timber.d("✅ AI分析启动成功: analysisId=$analysisId")

                                // 🔥 【硬指标】记录已完成的分析ID，防止重复发送
                                dispatch(StatsContract.Intent.AiAnalysisReceived(analysisId))
                            }

                            is ModernResult.Error -> {
                                Timber.e("❌ AI分析失败: ${result.error}")
                                dispatch(
                                    StatsContract.Intent.AiAnalysisError(
                                        UiText.DynamicString("AI分析失败: ${result.error}"),
                                    ),
                                )
                            }

                            is ModernResult.Loading -> {
                                // 处理加载状态，如果需要的话
                                Timber.d("🔄 AI分析加载中...")
                            }
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "❌ AI分析异常")
                    dispatch(
                        StatsContract.Intent.AiAnalysisError(
                            UiText.DynamicString("AI分析异常: ${e.message}"),
                        ),
                    )
                }
            }
        }

        /**
         * 将TimeRange转换为SimpleTimeRange
         * TODO: 这个转换逻辑可能需要根据实际的TimeRange实现调整
         */
        private fun TimeRange.toSimpleTimeRange(): SimpleTimeRange {
            val now = Clock.System.now()
            val currentDate = now.toLocalDateTime(TimeZone.currentSystemDefault()).date

            return when (this) {
                TimeRange.WEEK -> SimpleTimeRange(
                    startDate = currentDate.minus(7, DateTimeUnit.DAY),
                    endDate = currentDate,
                )

                TimeRange.MONTH -> SimpleTimeRange(
                    startDate = currentDate.minus(30, DateTimeUnit.DAY),
                    endDate = currentDate,
                )

                TimeRange.YEAR -> SimpleTimeRange(
                    startDate = currentDate.minus(365, DateTimeUnit.DAY),
                    endDate = currentDate,
                )

                TimeRange.CUSTOM -> SimpleTimeRange(
                    startDate = currentDate.minus(30, DateTimeUnit.DAY),
                    endDate = currentDate,
                )
            }
        }
    }
