package com.example.gymbro.features.coach.aicoach

import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.example.gymbro.core.ai.prompt.manager.PromptMode
import com.example.gymbro.core.ai.prompt.manager.PromptModeManager
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ml.tokenizer.OpenAiTokenizer
import com.example.gymbro.core.network.monitor.NetworkWatchdog
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.startup.StartupStatusProvider
import com.example.gymbro.domain.coach.usecase.GetSuggestionConfigUseCase
import com.example.gymbro.features.coach.aicoach.internal.components.AiCoachStrings
import com.example.gymbro.features.coach.aicoach.internal.effect.handlers.SessionEffectHandler
import com.example.gymbro.features.coach.aicoach.internal.monitoring.AiConversationMonitor
import com.example.gymbro.features.coach.aicoach.internal.paging.ChatHistoryPagingSource
import com.example.gymbro.features.coach.aicoach.internal.paging.ChatHistoryRemoteMediator
import com.example.gymbro.features.coach.aicoach.internal.reducer.AiCoachReducer
import com.example.gymbro.features.coach.aicoach.internal.viewmodel.AiCoachSessionHandler
import com.example.gymbro.features.coach.aicoach.internal.viewmodel.AiCoachViewModelDelegate
import com.example.gymbro.features.coach.shared.utils.FlowBackpressureOptimizer.withPerformanceMonitoring
import com.example.gymbro.features.coach.shared.utils.PerformanceTags
import com.example.gymbro.features.coach.shared.utils.applyNetworkOptimizations
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber

// 移除AppStartupManager导入，因为features模块不能依赖app模块

/**
 * AI Coach ViewModel - MVI 2.0 架构实现
 *
 * 核心职责：
 * 1. MVI架构状态管理和Effect处理
 * 2. ThinkingBox集成和流式AI响应处理
 * 3. 会话管理和历史记录分页
 * 4. API配置和提供商管理
 * 5. 网络监控和性能优化
 */
@HiltViewModel
internal class AiCoachViewModel
    @Inject
    constructor(
        // Core dependencies
        private val aiConfig: com.example.gymbro.domain.coach.config.AiConfig,
        private val resourceProvider: ResourceProvider,
        private val aiCoachReducer: AiCoachReducer,
        // 🔥 启动状态优化 - 智能感知应用启动完成度
        private val startupStatusProvider: StartupStatusProvider,
        // Network and monitoring
        private val networkWatchdog: NetworkWatchdog,
        private val performanceMonitor:
            com.example.gymbro.features.coach.shared.components.CoachPerformanceMonitor,
        // AI and prompt management
        private val promptModeManager: PromptModeManager,
        private val tokenizer: OpenAiTokenizer,

        // 🔥 【架构重构阶段三】移除 TokenRouter 依赖，Coach 不再负责 token 路由
        // Effect and session handlers
        private val aiCoachEffectHandler:
            com.example.gymbro.features.coach.aicoach.internal.effect.AiCoachEffectHandler,
        private val sessionHandler: AiCoachSessionHandler,
        private val sessionEffectHandler: SessionEffectHandler,

        // Use cases and repositories
        private val getSuggestionConfigUseCase: GetSuggestionConfigUseCase,
        private val chatHistoryRemoteMediator: ChatHistoryRemoteMediator,
        // 🎯 架构迁移：移除 historyPersister 依赖，使用 ChatSessionManagementUseCase 统一管理
        private val thinkingBoxHistoryRepository:
            com.example.gymbro.domain.thinkingbox.repository.HistoryRepository,

        // 🔥 BGE引擎管理 - 用于TopBar状态显示
        private val initializeBgeEngineUseCase:
            com.example.gymbro.domain.coach.usecase.InitializeBgeEngineUseCase,

        // 🔥 【架构修正】移除AppStartupManager依赖，因为features模块不能依赖app模块
        // Coach模块现在依赖应用层已初始化的服务，不需要直接访问启动管理器

        // 🔥 【Plan B架构】移除CoachThinkingBoxInitializer依赖 - ThinkingBox被动响应
    ) : BaseMviViewModel<AiCoachContract.Intent, AiCoachContract.State, AiCoachContract.Effect>(
        initialState =
            AiCoachContract.State(
                inputState =
                    AiCoachContract.InputState(
                        placeholder = AiCoachStrings.inputPlaceholderText.asString(resourceProvider),
                    ),
            ),
    ),
    AiCoachViewModelDelegate {
        override val reducer: Reducer<AiCoachContract.Intent, AiCoachContract.State, AiCoachContract.Effect> =
            aiCoachReducer

        override fun initializeEffectHandler() {
            aiCoachEffectHandler.initialize(
                scope = handlerScope,
                intentSender = this::dispatch,
                stateProvider = { getCurrentState() },
                sessionHandler = sessionHandler,
            )

            // 🔥 【Plan B架构】移除ThinkingBox集成初始化 - ThinkingBox被动响应，不需要Coach初始化

            viewModelScope.launch {
                effect.collect { effect ->
                    Timber.tag("EFFECT-FLOW").d("🎯 [AiCoachViewModel] 收到Effect: ${effect::class.simpleName}")

                    // 🔥 【消息流调试】特别记录StartAiStream Effect
                    if (effect is AiCoachContract.Effect.StartAiStream) {
                        Timber.tag("COACH-MESSAGE-FLOW")
                            .i(
                                "🚀 [EFFECT处理] StartAiStream Effect收到: messageContext=${effect.messageContext.getDebugInfo()}",
                            )
                    }

                    // 🔥 【MVI 2.0黄金标准修复】所有Effect都委托给EffectHandler处理
                    Timber
                        .tag("EFFECT-FLOW")
                        .d("📤 [AiCoachViewModel] 委托给AiCoachEffectHandler: ${effect::class.simpleName}")
                    aiCoachEffectHandler.handle(effect)
                }
            }
        }

        // Prompt模式管理
        val currentPromptMode: StateFlow<PromptMode> = promptModeManager.currentMode

        // 🔥 BGE引擎状态 - 供TopBar显示使用
        val bgeEngineStatus: StateFlow<com.example.gymbro.core.ml.embedding.EngineStatus> =
            initializeBgeEngineUseCase.getCurrentStatus().let { currentStatus ->
                // 创建一个StateFlow来暴露BGE状态
                MutableStateFlow(currentStatus).apply {
                    // 在后台监听状态变化
                    viewModelScope.launch {
                        try {
                            initializeBgeEngineUseCase.invoke().collect { status ->
                                value = status
                            }
                        } catch (e: Exception) {
                            // 只记录关键错误，移除详细异常信息
                            Timber.e(e, "BGE引擎状态监听失败")
                        }
                    }
                }.asStateFlow()
            }

        // 🔥 【704coach优化】历史消息分页流 - Week 1核心功能
        private var _historyFlow: Flow<PagingData<AiCoachContract.MessageUi>>? = null

        /**
         * 历史消息分页数据流
         *
         * 提供ChatGPT式的历史消息分页加载体验
         * 需要先调用setupHistoryFlow(sessionId)初始化
         */
        val historyFlow: Flow<PagingData<AiCoachContract.MessageUi>>?
            get() = _historyFlow

        // 🔥 实现 AiCoachViewModelDelegate 接口
        override fun getCurrentState(): AiCoachContract.State = state.value

        override fun getResourceProvider(): ResourceProvider = resourceProvider

        override fun getViewModelScope(): CoroutineScope = viewModelScope

        /**
         * 🔥 获取TokenizerService - 用于Token计算
         */
        fun getTokenizerService(): com.example.gymbro.core.ai.tokenizer.TokenizerService = tokenizer

        // ================================
        // 初始化模块
        // ================================

        init {
            initializeEffectHandler()
            sessionHandler.initialize(this)
            initializeBasicComponentsWithStartupAwareness()

            observeNetworkEvents()

            // 初始化完成
        }

        /**
         * 🔥 【启动优化】智能启动感知的渐进式初始化
         *
         * 根据应用启动完成度，采用不同的初始化策略：
         * - 如果启动已完成：立即初始化所有组件
         * - 如果启动进行中：等待关键组件就绪后初始化
         * - 如果启动刚开始：先初始化基本功能，后续渐进式加载
         */
        private fun initializeBasicComponentsWithStartupAwareness() {
            viewModelScope.launch {
                val startupReadiness = startupStatusProvider.getStartupReadiness()

                // 启动状态检查 - 移除详细调试日志

                when {
                    // 🟢 理想情况：所有服务已就绪，立即初始化
                    startupReadiness.canStartCoach -> {
                        initializeComponents(InitializationLevel.FULL)
                    }

                    // 🟡 等待情况：关键服务初始化中，监听状态变化
                    startupReadiness.isNetworkReady -> {
                        initializeComponents(InitializationLevel.BASIC)
                        waitForAiCoreAndCompleteInitialization()
                    }

                    // 🔴 启动早期：先提供基本UI，后台等待服务就绪
                    else -> {
                        initializeComponents(InitializationLevel.MINIMAL)
                        waitForStartupCompletionAndUpgrade()
                    }
                }
            }
        }

        /**
         * 统一的组件初始化方法
         */
        private suspend fun initializeComponents(level: InitializationLevel) {
            // 所有级别都需要的基本初始化
            initializePromptMode()

            when (level) {
                InitializationLevel.MINIMAL -> {
                    // 只初始化UI必需的基本状态
                }

                InitializationLevel.BASIC -> {
                    // 基本组件初始化（网络就绪但AI核心可能未完成）
                }

                InitializationLevel.FULL -> {
                    // 完整初始化所有组件
                    loadSuggestionConfig()
                }
            }
        }

        /**
         * 初始化Prompt模式 - 提取的公共方法
         */
        private suspend fun initializePromptMode() {
            val currentPromptMode = promptModeManager.getPromptRegistry().getCurrentMode()
            dispatch(AiCoachContract.Intent.SwitchPromptMode(currentPromptMode))
        }

        /**
         * 初始化级别枚举
         */
        private enum class InitializationLevel {
            MINIMAL, // 最小化初始化
            BASIC, // 基本初始化
            FULL, // 完整初始化
        }

        /**
         * 统一的错误处理方法
         */
        private fun handleError(
            exception: Exception? = null,
            message: String,
            errorCode: AiCoachContract.ErrorCode = AiCoachContract.ErrorCode.UNKNOWN_ERROR,
        ) {
            if (exception != null) {
                Timber.e(exception, message)
            } else {
                Timber.e(message)
            }

            dispatch(
                AiCoachContract.Intent.ShowError(
                    error = errorCode.toUiText().asString(resourceProvider),
                ),
            )
        }

        /**
         * 简化的错误处理方法（仅显示消息）
         */
        private fun handleSimpleError(message: String) {
            Timber.e(message)
            dispatch(AiCoachContract.Intent.ShowError(message))
        }

        /**
         * 等待AI核心就绪并完成初始化
         */
        private fun waitForAiCoreAndCompleteInitialization() {
            viewModelScope.launch {
                startupStatusProvider.startupReadiness
                    .collect { readiness ->
                        if (readiness.isAiCoreReady) {
                            Timber.tag("COACH-STARTUP").i("🎉 AI核心就绪，完成Coach模块初始化")
                            loadSuggestionConfig()
                            return@collect
                        }
                    }
            }
        }

        /**
         * 等待启动完成并升级功能
         */
        private fun waitForStartupCompletionAndUpgrade() {
            viewModelScope.launch {
                startupStatusProvider.startupReadiness
                    .collect { readiness ->
                        when {
                            readiness.canStartCoach -> {
                                Timber.tag("COACH-STARTUP").i("🎉 启动完成，升级Coach模块为完整功能")
                                initializeComponents(InitializationLevel.FULL)
                                return@collect
                            }

                            readiness.isNetworkReady -> {
                                Timber.tag("COACH-STARTUP").i("🌐 网络就绪，初始化基本功能")
                                initializeComponents(InitializationLevel.BASIC)
                            }
                        }
                    }
            }
        }

        // 🔥 【架构修正】移除AppStartupManager相关方法
        // Coach模块现在依赖应用层已初始化的服务，不需要检查启动状态
        // 所有重量级组件（网络层、AI核心）已由AppStartupManager在应用启动时初始化完成

        // ================================
        // 网络监控模块
        // ================================

        private fun observeNetworkEvents() {
            viewModelScope.launch {
                // 🔥 【架构修正】移除网络启动代码，只监听网络状态
                // NetworkWatchdog应该已经在应用启动时由AppStartupManager启动
                // Coach模块只负责消费网络状态，不管理网络生命周期

                networkWatchdog.networkEvents
                    .filterNotNull()
                    .applyNetworkOptimizations()
                    .withPerformanceMonitoring(PerformanceTags.NETWORK_EVENTS)
                    .collect { networkEvent ->
                        // 🔥 【职责分离】Coach模块只处理网络状态变化的UI响应
                        // 网络监控的启动和生命周期管理由应用层负责
                        handleNetworkStateChange(networkEvent)
                    }
            }
        }

        /**
         * 🔥 【新增】处理网络状态变化的UI响应
         */
        private fun handleNetworkStateChange(networkEvent: com.example.gymbro.core.network.monitor.NetworkEvent) {
            // 根据网络状态变化更新UI状态或显示提示
            // 例如：网络断开时显示离线提示，网络恢复时隐藏提示
            // 移除调试日志，只在关键状态变化时记录

            when (networkEvent) {
                is com.example.gymbro.core.network.monitor.NetworkEvent.Connected -> {
                    // 网络已连接，隐藏离线提示
                }

                is com.example.gymbro.core.network.monitor.NetworkEvent.Lost -> {
                    // 网络断开，显示离线提示
                }

                is com.example.gymbro.core.network.monitor.NetworkEvent.Restored -> {
                    // 网络恢复，隐藏离线提示
                }

                else -> {
                    // 其他网络事件（Losing、Bandwidth等）
                }
            }

            // 网络状态UI响应逻辑可在需要时实现
        }

        // ================================
        // 公共API - Prompt管理
        // ================================

        fun switchPromptMode(mode: String) {
            dispatch(AiCoachContract.Intent.SwitchPromptMode(mode))
        }

        fun getPromptRegistry(): com.example.gymbro.core.ai.prompt.registry.PromptRegistry =
            promptModeManager.getPromptRegistry()

        /**
         * 🔥 【704coach优化】切换到会话并设置历史消息流
         *
         * 外部调用的便捷方法，集成会话切换和历史消息分页设置
         *
         * @param sessionId 目标会话ID
         */
        fun switchToSession(sessionId: String) {
            // 切换到指定会话
            dispatch(AiCoachContract.Intent.SwitchSession(sessionId))

            // 🔥 注意：setupHistoryFlow会在Effect处理中自动调用
            // 这里不需要重复调用，避免竞态条件
        }

        /**
         * 获取Tokenizer
         */
        fun getTokenizer(): OpenAiTokenizer = tokenizer

        // 🔥 【架构简化】移除getThinkingContentFlow，由ThinkingBox直接提供

        // ================================
        // ThinkingBox集成模块
        // ================================

        // ================================
        // 公共API - ThinkingBox控制
        // ================================

        // 移除废弃方法，这些方法已不再使用且会产生警告

        /**
         * 🔥 【704coach优化】设置历史消息分页流 - Week 1核心方法
         *
         * 根据sessionId配置ChatHistoryRemoteMediator并创建PagingData流
         * 实现ChatGPT式的历史消息加载体验
         *
         * @param sessionId 会话ID，用于过滤和加载特定会话的历史消息
         */
        fun setupHistoryFlow(sessionId: String) {
            Timber.tag("HistoryFlow").d("🔥 设置历史消息分页流: sessionId=$sessionId")

            try {
                // 🔥 Step 1: 配置ChatHistoryRemoteMediator的sessionId
                chatHistoryRemoteMediator.setSessionId(sessionId)

                // 🔥 Step 2: 创建简化的Pager配置
                // 注意：RemoteMediator主要用于缓存和同步，这里先实现基础分页
                val pager =
                    Pager(
                        config =
                            PagingConfig(
                                pageSize = 20,
                                prefetchDistance = 5,
                                enablePlaceholders = false,
                                initialLoadSize = 20,
                                maxSize = 200, // 限制内存中缓存的消息数量
                            ),
                    ) {
                        // 🔥 创建基于ChatRawDao的PagingSource
                        ChatHistoryPagingSource(
                            sessionId = sessionId,
                            chatRawDao = chatHistoryRemoteMediator.chatRawDao,
                        )
                    }

                // 🔥 Step 3: 创建缓存的PagingData流
                _historyFlow = pager.flow.cachedIn(viewModelScope)

                Timber.tag("HistoryFlow").d("🔥 历史消息分页流设置成功")
            } catch (e: Exception) {
                Timber.tag("HistoryFlow").e(e, "🔥 历史消息分页流设置失败: sessionId=$sessionId")
                _historyFlow = null
            }
        }

        /**
         * 🔥 【704coach优化】清除历史消息分页流
         *
         * 在会话切换或清理时调用，释放资源
         */
        fun clearHistoryFlow() {
            Timber.tag("HistoryFlow").d("🔥 清除历史消息分页流")
            _historyFlow = null
        }

        // ================================
        // 公共API - 会话管理
        // ================================

        fun createNewSession(title: String? = null) {
            dispatch(AiCoachContract.Intent.CreateNewSession)
        }

        // ================================
        // 公共API - API配置管理
        // ================================

        fun switchApiProvider(provider: AiCoachContract.ApiProvider) {
            dispatch(AiCoachContract.Intent.SwitchApiProvider(provider))
        }

        fun getProviderModel(provider: AiCoachContract.ApiProvider): String =
            when (provider) {
                AiCoachContract.ApiProvider.DEEPSEEK -> aiConfig.defaultDeepseekModel
                AiCoachContract.ApiProvider.GOOGLE_GEMINI -> aiConfig.defaultGoogleModel
                AiCoachContract.ApiProvider.OPENAI -> aiConfig.openaiDefaultModel
            }

        fun getCurrentModelName(): String {
            val currentProvider = state.value.currentApiProvider
            return getProviderModel(currentProvider)
        }

        fun getAvailableProviders(): List<AiCoachContract.ApiProvider> =
            AiCoachContract.ApiProvider.values().filter { provider ->
                when (provider) {
                    AiCoachContract.ApiProvider.DEEPSEEK ->
                        aiConfig.deepseekApiKey.isNotBlank() && aiConfig.deepseekBaseUrl.isNotBlank()

                    AiCoachContract.ApiProvider.GOOGLE_GEMINI ->
                        aiConfig.googleApiKey.isNotBlank() && aiConfig.googleBaseUrl.isNotBlank()

                    AiCoachContract.ApiProvider.OPENAI ->
                        aiConfig.openaiApiKey.isNotBlank() && aiConfig.openaiBaseUrl.isNotBlank()
                }
            }

        // ================================
        // 公共API - 记忆管理
        // ================================

        fun recallMemory(query: String, tokenBudget: Int = 600) {
            dispatch(AiCoachContract.Intent.RecallMemoriesFromService(getUserId(), query, tokenBudget))
        }

        private fun getUserId(): String {
            // 使用默认用户ID，实际项目中应注入UserSessionManager
            return "default_user"
        }

        /**
         * 🔥 实现：模型切换逻辑
         */
        fun switchModel(model: String) {
            try {
                // 根据模型名称确定API提供商
                val provider =
                    when {
                        model.contains("deepseek", ignoreCase = true) -> AiCoachContract.ApiProvider.DEEPSEEK
                        model.contains(
                            "gemini",
                            ignoreCase = true,
                        ) -> AiCoachContract.ApiProvider.GOOGLE_GEMINI

                        model.contains("gpt", ignoreCase = true) ||
                            model.contains(
                                "openai",
                                ignoreCase = true,
                            ) -> AiCoachContract.ApiProvider.OPENAI

                        else -> {
                            // 只记录警告，移除详细调试信息
                            Timber.w("未知模型类型，使用默认DeepSeek: $model")
                            AiCoachContract.ApiProvider.DEEPSEEK
                        }
                    }

                dispatch(AiCoachContract.Intent.SwitchApiProvider(provider))
            } catch (e: Exception) {
                handleError(e, "模型切换失败: $model")
            }
        }

        /**
         * 运行消息管道诊断
         */
        private fun runMessagePipelineDiagnostic() {
            // 检查配置完整性
            val hasValidDeepSeek = aiConfig.deepseekApiKey.isNotEmpty() &&
                aiConfig.deepseekBaseUrl.isNotEmpty() &&
                aiConfig.defaultDeepseekModel.isNotEmpty()

            val hasValidGoogle = aiConfig.googleApiKey.isNotEmpty() &&
                aiConfig.googleBaseUrl.isNotEmpty() &&
                aiConfig.defaultGoogleModel.isNotEmpty()

            if (!hasValidDeepSeek && !hasValidGoogle) {
                handleSimpleError("AI配置不完整，请检查local.properties文件中的API配置")
            }
        }

        /**
         * API配置诊断 - 帮助用户快速定位400错误原因
         */
        private fun logApiConfigDiagnostic() {
            // 简化诊断，只记录关键错误信息
            Timber.w("API配置错误，请检查local.properties文件中的API配置")
            runMessagePipelineDiagnostic()
        }

        /**
         * 处理Prompt模式切换Effect
         */
        private fun handleSwitchPromptMode(mode: String) {
            try {
                // 使用PromptModeManager的统一切换入口
                promptModeManager.switchMode(mode)

                // 监控系统提示词
                val currentPrompt = promptModeManager.getPromptRegistry().getCurrentPrompt()
                AiConversationMonitor.monitorSystemPrompt(currentPrompt, "unified")
            } catch (e: Exception) {
                handleError(e, "Prompt模式切换失败: $mode")
            }
        }

        private fun loadSuggestionConfig() {
            viewModelScope.launch {
                getSuggestionConfigUseCase().collect { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            dispatch(AiCoachContract.Intent.SuggestionConfigLoadedResult(result.data))
                        }

                        is ModernResult.Error -> {
                            dispatch(AiCoachContract.Intent.SuggestionConfigFailedResult(result.error))
                        }

                        is ModernResult.Loading -> {
                            // Can be handled in reducer if needed
                        }
                    }
                }
            }
        }

        // 移除ThinkingBox API层依赖，Coach不再直接管理ThinkingBox集成

        // ================================
        // 私有方法 - Function Call处理
        // ================================

        private fun handleFunctionCallDetected(
            event:
                com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent.FunctionCallDetected,
        ) {
            viewModelScope.launch {
                try {
                    if (event.isComplete && event.arguments != null) {
                        val currentState = getCurrentState()
                        val sessionId = currentState.activeSession?.id

                        if (sessionId != null) {
                            dispatch(
                                AiCoachContract.Intent.FunctionCallDetected(
                                    messageId = generateMessageId(),
                                    functionCall =
                                        buildFunctionCallJson(
                                            event.functionName,
                                            event.arguments ?: "",
                                        ),
                                ),
                            )

                            sendEffect(
                                AiCoachContract.Effect.ExecuteFunctionCall(
                                    functionCallName = event.functionName,
                                    arguments = event.arguments ?: "{}",
                                    sessionId = sessionId,
                                ),
                            )
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "处理功能调用检测失败")
                }
            }
        }

        // ================================
        // 工具方法模块
        // ================================

        private fun generateMessageId(): String =
            com.example.gymbro.core.util.Constants.MessageId
                .generate()

        private fun buildFunctionCallJson(functionName: String, arguments: String): String =
            kotlinx.serialization.json.buildJsonObject {
                put("function_name", kotlinx.serialization.json.JsonPrimitive(functionName))
                put("arguments", kotlinx.serialization.json.Json.parseToJsonElement(arguments))
            }.toString()

        private fun handleThinkingBoxCompleted(messageId: String) {
            viewModelScope.launch {
                try {
                    // ThinkingBox现在独立管理状态，Coach不再需要重置逻辑
                    Timber.d("ThinkingBox完成: $messageId")
                } catch (e: Exception) {
                    Timber.e(e, "处理ThinkingBox完成失败")
                }
            }
        }

        // 🔥 【P0修复】移除ThinkingBox状态重置 - Coach不再管理ThinkingBox状态
        private fun resetThinkingBoxState() {
            viewModelScope.launch {
                try {
                    val currentStateValue = getCurrentState()
                    val hasActiveStream = currentStateValue.streamingState !is AiCoachContract.StreamingState.Idle

                    if (hasActiveStream) {
                        return@launch
                    }

                    // ThinkingBox现在独立管理自己的状态重置
                } catch (e: Exception) {
                    Timber.e(e, "重置ThinkingBox状态失败")
                }
            }
        }

        // ================================
        // 生命周期管理
        // ================================

        override fun onCleared() {
            super.onCleared()
            // 架构修正：移除网络生命周期管理，网络层由应用层统一管理
            // networkWatchdog.stopWatching() - 已移除，网络监控由AppStartupManager管理

            performanceMonitor.stopMonitoring()
        }
    }
