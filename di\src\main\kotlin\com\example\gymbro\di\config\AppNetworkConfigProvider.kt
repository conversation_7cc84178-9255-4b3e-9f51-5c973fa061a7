package com.example.gymbro.di.config

import com.example.gymbro.core.network.config.NetworkConfig
import com.example.gymbro.core.network.config.NetworkConfigProvider
import com.example.gymbro.domain.coach.config.AiProviderManager
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber

/**
 * App层统一配置聚合器
 * 🎯 Clean Architecture配置管理的核心实现
 *
 * 核心职责：
 * 1. 聚合所有配置源：BuildConfig + AiProviderManager + UserPreferences
 * 2. 动态组装NetworkConfig：支持AI Provider切换
 * 3. 向下依赖注入：为core-network层提供统一配置
 * 4. 配置验证与降级：确保配置有效性
 *
 * 架构优势：
 * - 依赖方向正确：App → Domain → Data ← Network
 * - 单一配置源：所有网络配置统一出口
 * - 动态配置能力：支持运行时Provider切换
 * - 测试友好：可注入Mock配置
 */
@Singleton
class AppNetworkConfigProvider
    @Inject
    constructor(
        private val aiProviderManager: AiProviderManager,
    ) : NetworkConfigProvider {

        init {
            Timber.d("🏗️ 初始化AppNetworkConfigProvider - Clean Architecture配置聚合器")
        }

        override fun getConfig(): NetworkConfig {
            return assembleConfig()
        }

        override fun observeConfig(): Flow<NetworkConfig> = flow {
            // 发射当前配置
            emit(assembleConfig())

            // 监听AI Provider变化，动态发射新配置
            aiProviderManager.currentProvider.collect { provider ->
                Timber.d("🔄 检测到AI Provider变化: ${provider.name}，重新组装NetworkConfig")
                emit(assembleConfig())
            }
        }

        /**
         * 配置聚合的核心逻辑
         *
         * 配置优先级：
         * 1. AiProviderManager当前Provider（动态业务配置）
         * 2. BuildConfig（编译时静态配置）
         * 3. 默认配置（兜底保障）
         */
        private fun assembleConfig(): NetworkConfig {
            return try {
                // 1. 获取当前AI Provider配置
                val currentProvider = aiProviderManager.currentProvider.value

                // 2. 组装NetworkConfig - 使用AiProviderManager作为主要配置源
                val config = NetworkConfig(
                    wsBase = currentProvider.baseUrl,
                    restBase = currentProvider.baseUrl,
                    apiKey = extractApiKeyFromHeaders(currentProvider.headers),
                    // 3. 使用合理的默认网络配置
                    connectTimeoutSec = 30,
                    pingSec = com.example.gymbro.core.network.BuildConfig.PING_INTERVAL_SEC,
                    pongTimeoutSec = 5,
                    maxReconnect = com.example.gymbro.core.network.BuildConfig.MAX_RECONNECT,
                    backoffStartMs = 1000,
                    backoffCapMs = 30000,
                    enableOffsetResume = true,

                    readTimeoutSec = 30,
                    writeTimeoutSec = 30,
                    enableRetry = true,
                    maxRetries = 2,
                    retryDelayMs = 2000,
                    enableDebugLogging = com.example.gymbro.core.network.BuildConfig.ENABLE_DEBUG_LOGGING,
                )

                // 4. 配置验证
                if (!config.validate()) {
                    Timber.w("⚠️ 组装的配置验证失败，使用降级配置")
                    return createFallbackConfig()
                }

                Timber.d("✅ 成功组装NetworkConfig:")
                Timber.d("  - Provider: ${currentProvider.name}")
                Timber.d("  - Base URL: ${currentProvider.baseUrl}")
                Timber.d(
                    "  - API Key: ${
                        if (config.apiKey.isNotEmpty()) {
                            "✅ 已配置 (${
                                config.apiKey.take(
                                    10,
                                )
                            }...)"
                        } else {
                            "❌ 未配置"
                        }
                    }",
                )
                Timber.d("  - 🔥 配置来源: AiProviderManager (非硬编码)")

                config
            } catch (e: Exception) {
                Timber.e(e, "❌ 配置组装失败，使用降级配置")
                createFallbackConfig()
            }
        }

        /**
         * 创建降级配置
         * 当动态配置失败时的兜底方案
         * 🔥 修复：移除硬编码配置依赖，使用合理默认值
         */
        private fun createFallbackConfig(): NetworkConfig {
            Timber.w("🔄 使用默认降级配置 - AiProviderManager配置不可用")

            return NetworkConfig(
                wsBase = "wss://api.openai.com",
                restBase = "https://api.openai.com",
                apiKey = "sk-fallback-missing-key",
                connectTimeoutSec = 30,
                pingSec = 15,
                pongTimeoutSec = 5,
                maxReconnect = 5,
                backoffStartMs = 1000,
                backoffCapMs = 30000,
                enableOffsetResume = true,

                readTimeoutSec = 30,
                writeTimeoutSec = 30,
                enableRetry = true,
                maxRetries = 2,
                retryDelayMs = 2000,

                enableDebugLogging = true,
            )
        }

        /**
         * 从Provider的headers中提取API密钥
         * 支持常见的认证头格式：Authorization、X-API-Key等
         */
        private fun extractApiKeyFromHeaders(headers: Map<String, String>): String {
            return when {
                // Bearer Token格式：Authorization: Bearer sk-xxx
                headers["Authorization"]?.startsWith("Bearer ") == true -> {
                    headers["Authorization"]?.removePrefix("Bearer ")?.trim() ?: ""
                }
                // API Key格式：X-API-Key: sk-xxx
                headers["X-API-Key"]?.isNotBlank() == true -> {
                    headers["X-API-Key"] ?: ""
                }
                // OpenAI格式：Authorization: Bearer sk-xxx
                headers["authorization"]?.startsWith("Bearer ") == true -> {
                    headers["authorization"]?.removePrefix("Bearer ")?.trim() ?: ""
                }
                // 其他可能的格式
                headers["api-key"]?.isNotBlank() == true -> {
                    headers["api-key"] ?: ""
                }

                else -> {
                    Timber.w("⚠️ 无法从Provider headers中提取API Key，headers: $headers")
                    ""
                }
            }
        }
    }
