package com.example.gymbro.features.workout.template.edit

import com.example.gymbro.core.di.qualifiers.ApplicationScope
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.template.edit.transaction.TemplateTransactionManager
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 模板保存器 - Phase 2 架构重构
 *
 * 🎯 核心优势：
 * - @Singleton 生命周期与 Application 一致，不会被 ViewModel 销毁影响
 * - 使用ApplicationScope，保存操作不受UI生命周期影响
 * - 集成事务管理器，确保原子性和一致性
 * - 完整的错误处理和回调机制
 *
 * 🔄 工作原理：
 * ViewModel → TemplateSaver → TemplateTransactionManager → Repository → templateDB
 *
 * <AUTHOR> 4.0 sonnet
 */
@Singleton
class TemplateSaver
    @Inject
    constructor(
        private val templateManagementUseCase: TemplateManagementUseCase,
        private val transactionManager: TemplateTransactionManager,
        @ApplicationScope private val applicationScope: CoroutineScope,
    ) {
        /**
         * Phase 2 重构：使用事务管理器进行原子性保存
         *
         * @param template 要保存的模板
         * @param onSuccess 保存成功回调
         * @param onError 保存失败回调
         * @param useTransaction 是否使用事务管理器（默认true）
         * @return Job 供调用方选择性等待
         */
        fun save(
            template: WorkoutTemplate,
            onSuccess: ((String) -> Unit)? = null,
            onError: ((String) -> Unit)? = null,
            useTransaction: Boolean = true,
        ): Job =
            applicationScope.launch {
                try {
                    Timber.d(
                        "💾 [Phase2] TemplateSaver: 开始保存模板 id=${template.id}, name=${template.name}, isDraft=${template.isDraft}, useTransaction=$useTransaction",
                    )

                    val result = if (useTransaction) {
                        // Phase 2: 使用事务管理器进行原子性保存
                        transactionManager.saveTemplateAtomically(template, validateBeforeSave = true)
                    } else {
                        // 向后兼容：使用原有的UseCase保存
                        templateManagementUseCase.saveTemplate(template)
                    }

                    when (result) {
                        is ModernResult.Success -> {
                            Timber.d(
                                "✅ [Phase2] TemplateSaver: 模板保存成功 ${template.name}, templateId=${result.data}",
                            )
                            onSuccess?.invoke(result.data)
                        }

                        is ModernResult.Error -> {
                            Timber.e("❌ [Phase2] TemplateSaver: 模板保存失败 ${template.name}: ${result.error}")
                            val errorMessage = when (val uiMessage = result.error.uiMessage) {
                                is UiText.DynamicString -> uiMessage.value
                                else -> "保存失败"
                            }
                            onError?.invoke(errorMessage)
                        }

                        is ModernResult.Loading -> {
                            Timber.w("⚠️ [Phase2] TemplateSaver: 返回Loading状态")
                            onError?.invoke("保存状态异常")
                        }
                    }
                } catch (e: Exception) {
                    if (e is kotlinx.coroutines.CancellationException) {
                        Timber.w("⚠️ TemplateSaver: 保存任务被手动取消")
                        println("⚠️ TemplateSaver: 保存任务被手动取消")
                        throw e
                    }
                    Timber.e(e, "❌ TemplateSaver: 保存操作发生异常 ${template.name}")
                    println("❌ TemplateSaver: 保存操作发生异常 ${template.name}: ${e.message}")
                    // 🔥 修复：异常时调用失败回调
                    onError?.invoke("保存失败: ${e.message}")
                }
            }

        /**
         * 兼容性方法：保持原有的即发即忘接口
         */
        fun save(template: WorkoutTemplate): Job = save(template, null, null, useTransaction = true)

        /**
         * Phase 2: 批量保存方法
         * 使用事务管理器确保批量操作的原子性
         */
        fun saveBatch(
            templates: List<WorkoutTemplate>,
            onSuccess: ((List<String>) -> Unit)? = null,
            onError: ((String) -> Unit)? = null,
        ): Job = applicationScope.launch {
            try {
                Timber.d("💾 [Phase2] TemplateSaver: 开始批量保存 ${templates.size} 个模板")

                val result = transactionManager.saveTemplatesBatch(templates)

                when (result) {
                    is ModernResult.Success -> {
                        Timber.d("✅ [Phase2] TemplateSaver: 批量保存成功，保存了 ${result.data.size} 个模板")
                        onSuccess?.invoke(result.data)
                    }

                    is ModernResult.Error -> {
                        Timber.e("❌ [Phase2] TemplateSaver: 批量保存失败: ${result.error}")
                        val errorMessage = when (val uiMessage = result.error.uiMessage) {
                            is UiText.DynamicString -> uiMessage.value
                            else -> "批量保存失败"
                        }
                        onError?.invoke(errorMessage)
                    }

                    is ModernResult.Loading -> {
                        Timber.w("⚠️ [Phase2] TemplateSaver: 批量保存返回Loading状态")
                        onError?.invoke("批量保存状态异常")
                    }
                }
            } catch (e: Exception) {
                if (e is kotlinx.coroutines.CancellationException) {
                    Timber.w("⚠️ TemplateSaver: 批量保存任务被取消")
                    throw e
                }
                Timber.e(e, "❌ TemplateSaver: 批量保存操作发生异常")
                onError?.invoke("批量保存失败: ${e.message}")
            }
        }

        /**
         * Phase 2 重构：同步保存方法，使用事务管理器
         */
        suspend fun saveSync(
            template: WorkoutTemplate,
            useTransaction: Boolean = true,
        ): ModernResult<WorkoutTemplate> = try {
            Timber.d("🔄 [Phase2] TemplateSaver: 同步保存模板 ${template.name}, useTransaction=$useTransaction")

            val result = if (useTransaction) {
                transactionManager.saveTemplateAtomically(template, validateBeforeSave = true)
            } else {
                templateManagementUseCase.saveTemplate(template)
            }

            when (result) {
                is ModernResult.Success -> {
                    Timber.d("✅ [Phase2] TemplateSaver: 同步保存成功 ${template.name}")
                    ModernResult.Success(template)
                }

                is ModernResult.Error -> {
                    Timber.e("❌ [Phase2] TemplateSaver: 同步保存失败 ${template.name}: ${result.error}")
                    result
                }

                is ModernResult.Loading -> {
                    Timber.w("⚠️ TemplateSaver: UseCase返回Loading状态")
                    ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "templateSaveSync",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
                            uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("保存状态异常"),
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ TemplateSaver: 同步保存异常 ${template.name}")
            ModernResult.Error(
                com.example.gymbro.core.error.types.ModernDataError(
                    operationName = "templateSaveSync",
                    errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
                    uiMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("保存失败: ${e.message}"),
                ),
            )
        }

        /**
         * 批量保存多个模板
         */
        suspend fun saveBatch(templates: List<WorkoutTemplate>): List<ModernResult<WorkoutTemplate>> {
            return templates.map { template ->
                saveSync(template)
            }
        }
    }
