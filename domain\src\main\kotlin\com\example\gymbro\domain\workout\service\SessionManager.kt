package com.example.gymbro.domain.workout.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.domain.workout.usecase.template.TemplateVersionUseCase
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.first
import kotlinx.datetime.Clock

/**
 * 训练会话管理器 - 统一JSON数据中心核心服务
 *
 * 基于Task 3.2要求：SessionManager重构
 * 功能：从Template创建Session，管理Template内嵌逻辑
 * Phase 4: Session层适配 - 引用TemplateVersion确保历史追溯性
 *
 * 核心职责：
 * - 从模板创建训练会话 (startSessionFromTemplate)
 * - 从计划创建训练会话 (startSessionFromPlan)
 * - 管理Template内嵌逻辑（为Task 3.1做准备）
 * - 支持全屏沉浸训练体验
 * - 版本控制：Session引用TemplateVersion确保不可变性
 *
 * 设计原则：
 * - Template为中心：Session内嵌Template数据
 * - 数据完整性：实时保存，防止数据丢失
 * - 状态管理：管理训练生命周期
 * - 可扩展性：支持未来的Plan调度功能
 *
 * @property getCurrentUserIdUseCase 获取当前用户ID
 * @property templateRepository 训练模板仓库
 * @property sessionRepository 训练会话仓库
 * @property getTemplateLatestVersionUseCase 获取模板最新版本
 */
@Singleton
class SessionManager
    @Inject
    constructor(
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val templateRepository: TemplateRepository,
        private val sessionRepository: SessionRepository,
        private val templateVersionUseCase: TemplateVersionUseCase,
        private val logger: Logger,
    ) {
        /**
         * 从模板创建训练会话
         *
         * 核心功能：
         * 1. 获取指定的训练模板最新版本
         * 2. 将TemplateVersion转换为可执行的训练会话
         * 3. 保存会话到活跃训练仓库
         * 4. 支持全屏沉浸训练体验
         * 5. Phase 4: 确保Session引用TemplateVersion实现历史追溯性
         *
         * @param templateId 训练模板ID
         * @return 创建的训练会话，包含完整的模板数据
         */
        suspend fun startSessionFromTemplate(templateId: String): ModernResult<WorkoutSession> {
            return try {
                logger.d("开始从模板创建训练会话: templateId=$templateId")

                // 1. 获取当前用户ID
                val userIdResult = getCurrentUserIdUseCase().first()
                if (userIdResult !is ModernResult.Success || userIdResult.data.isNullOrBlank()) {
                    return ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "start_session_from_template",
                            message = UiText.DynamicString("获取用户信息失败"),
                        ),
                    )
                }
                val userId = userIdResult.data!!

                // 2. Phase 4适配: 获取训练模板的最新TemplateVersion
                logger.d("获取Template最新版本: templateId=$templateId")
                val templateVersionResult = templateVersionUseCase.getLatestVersionWithAutoCreate.invoke(
                    TemplateVersionUseCase.GetLatestVersionWithAutoCreateParams(
                        templateId = templateId,
                        autoCreateIfNotExist = true,
                    ),
                )

                val templateVersion = when (templateVersionResult) {
                    is ModernResult.Success -> templateVersionResult.data
                    is ModernResult.Error -> {
                        logger.e("获取Template最新版本失败: ${templateVersionResult.error}")
                        return ModernResult.Error(
                            DataErrors.DataError.notFound(
                                operationName = "start_session_from_template",
                                message = UiText.DynamicString(
                                    "获取模板版本失败: ${templateVersionResult.error.message}",
                                ),
                                entityType = "TemplateVersion",
                                entityId = templateId,
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        return ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "start_session_from_template",
                                message = UiText.DynamicString("获取模板版本超时"),
                            ),
                        )
                    }
                }

                // 使用TemplateVersion的内容创建Session
                val template = templateVersion.content
                logger.d(
                    "使用TemplateVersion创建Session: versionNumber=${templateVersion.versionNumber}, versionId=${templateVersion.id}",
                )

                // 3. 将TemplateVersion转换为训练会话
                val session =
                    createSessionFromTemplateVersion(
                        userId = userId,
                        templateVersion = templateVersion,
                    )

                // 4. 保存训练会话到会话仓库
                val saveResult = sessionRepository.saveSession(session)
                if (saveResult !is ModernResult.Success) {
                    return ModernResult.Error(
                        DataErrors.DataError.create(
                            operationName = "start_session_from_template",
                            message = UiText.DynamicString("保存训练会话失败"),
                            entityType = "WorkoutSession",
                            entityId = session.id,
                        ),
                    )
                }

                // 5. 增加模板使用次数 (新的TemplateRepository可能没有这个方法，暂时注释)
                // templateRepository.incrementUsageCount(templateId)

                logger.d(
                    "成功从TemplateVersion创建训练会话: sessionId=${session.id}, templateVersion=${templateVersion.versionNumber}",
                )
                ModernResult.Success(session)
            } catch (e: Exception) {
                logger.e("从模板创建训练会话失败: templateId=$templateId", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "start_session_from_template",
                        message = UiText.DynamicString("创建训练会话异常: ${e.message}"),
                        entityType = "WorkoutSession",
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 从计划创建训练会话
         *
         * 支持计划调度功能：
         * 1. 获取指定计划和日期的模板列表
         * 2. 为每个模板创建训练会话
         * 3. 批量保存到活跃训练仓库
         *
         * TODO: 等待WorkoutPlan调度器重构完成后实现
         *
         * @param planId 训练计划ID
         * @param dayNumber 计划中的天数
         * @return 创建的训练会话列表
         */
        suspend fun startSessionFromPlan(
            planId: String,
            dayNumber: Int,
        ): ModernResult<List<WorkoutSession>> =
            try {
                logger.d("开始从计划创建训练会话: planId=$planId, dayNumber=$dayNumber")

                // TODO: 实现计划调度逻辑
                // 1. 获取计划信息
                // 2. 获取指定天数的模板ID列表
                // 3. 为每个模板创建会话
                // 4. 批量保存会话

                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "start_session_from_plan",
                        message = UiText.DynamicString("计划调度功能待实现，等待Task 4.1完成"),
                        entityType = "WorkoutPlan",
                        entityId = planId,
                    ),
                )
            } catch (e: Exception) {
                logger.e("从计划创建训练会话失败: planId=$planId, dayNumber=$dayNumber", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "start_session_from_plan",
                        message = UiText.DynamicString("创建训练会话异常: ${e.message}"),
                        entityType = "WorkoutPlan",
                        entityId = planId,
                        cause = e,
                    ),
                )
            }

        /**
         * 从TemplateVersion创建空白训练会话
         *
         * 支持全屏沉浸训练体验的核心转换逻辑：
         * 1. 生成唯一的会话ID
         * 2. 将TemplateVersion的动作转换为会话动作
         * 3. 设置初始训练状态
         * 4. 准备数据完整性保障
         * 5. Phase 4: 确保Session记录TemplateVersion信息
         *
         * @param userId 用户ID
         * @param templateVersion 训练模板版本
         * @return 可执行的训练会话
         */
        private fun createSessionFromTemplateVersion(
            userId: String,
            templateVersion: com.example.gymbro.domain.workout.model.template.TemplateVersion,
        ): WorkoutSession {
            val now = Clock.System.now()
            val sessionId = generateSessionId(userId)
            val template = templateVersion.content

            // 转换TemplateVersion动作为会话动作
            val sessionExercises =
                template.exercises.mapIndexed { index, exerciseInTemplate ->
                    com.example.gymbro.domain.workout.model.SessionExercise(
                        id = UUID.randomUUID().toString(),
                        sessionId = sessionId,
                        exerciseId = exerciseInTemplate.exerciseId,
                        order = index,
                        name = "Exercise_${exerciseInTemplate.exerciseId}", // 需要从Exercise库获取名称
                        targetSets = exerciseInTemplate.sets,
                        completedSets = 0,
                        status = "NOT_STARTED",
                        startTime = null,
                        endTime = null,
                        notes = exerciseInTemplate.notes,
                        isCompleted = false,
                        sets = (1..exerciseInTemplate.sets).map { setNumber ->
                            com.example.gymbro.domain.workout.model.SessionSet(
                                id = UUID.randomUUID().toString(),
                                sessionExerciseId = UUID.randomUUID().toString(),
                                setNumber = setNumber,
                                weight = null, // 从weightSuggestion解析
                                weightUnit = "kg",
                                reps = exerciseInTemplate.reps, // 使用TemplateExercise的reps字段
                                timeSeconds = null,
                                rpe = null,
                                isCompleted = false,
                                isWarmupSet = false,
                                notes = null,
                                timestamp = now.toEpochMilliseconds(),
                            )
                        },
                    )
                }

            return WorkoutSession(
                id = sessionId,
                userId = userId,
                templateId = templateVersion.templateId, // 保留原Template ID用于查询
                planId = null,
                name = template.name,
                status = "CREATED", // 初始状态
                startTime = now.toEpochMilliseconds(),
                endTime = null,
                totalDuration = null,
                totalVolume = null,
                caloriesBurned = null,
                notes = "Based on TemplateVersion v${templateVersion.versionNumber} (${templateVersion.id})",
                rating = null,
                lastAutosaveTime = now.toEpochMilliseconds(),
                exercises = sessionExercises,
            )
        }

        /**
         * 生成训练会话ID
         *
         * 🎯 格式: session_{userId}_{timestamp}_{compactId}
         * 确保ID的唯一性和可追溯性，使用压缩ID提高可读性
         *
         * @param userId 用户ID
         * @return 唯一的会话ID
         */
        private fun generateSessionId(userId: String): String {
            val timestamp = Clock.System.now().toEpochMilliseconds()
            val compactId = com.example.gymbro.core.util.CompactIdGenerator.generateId()
            return "session_${userId}_${timestamp}_$compactId"
        }

        companion object {
            /**
             * SessionManager版本号
             * 用于追踪架构变更和兼容性
             */
            const val VERSION = "3.2.0"

            /**
             * 支持的最大会话并发数
             * 防止资源滥用
             */
            const val MAX_CONCURRENT_SESSIONS = 5
        }
    }
