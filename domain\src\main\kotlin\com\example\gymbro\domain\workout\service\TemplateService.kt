package com.example.gymbro.domain.workout.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.model.Difficulty
import com.example.gymbro.domain.workout.model.WorkoutGoal
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.domain.workout.usecase.template.TemplateVersionUseCase
import com.example.gymbro.shared.models.workout.WorkoutPlan
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.first

/**
 * Template服务层 - 负责调度和使用模板的逻辑
 *
 * 正确的架构分工：
 * - TemplateComposer: 专注于模板的生成（动作库 + 用户输入 → Template）
 * - TemplateService: 负责调度和使用模板的逻辑（本类）
 * - UnifiedWorkoutDataCenter: 协调所有操作，提供对外接口
 *
 * 核心功能：
 * - Template调度：创建训练计划（调度多个Template）
 * - Template管理：获取、验证、使用Template
 * - Plan创建：从Template创建训练计划
 * - 版本控制：Plan引用TemplateVersion确保不可变性
 */
@Singleton
class TemplateService
    @Inject
    constructor(
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val templateRepository: TemplateRepository,
        private val planRepository: PlanRepository,
        private val templateVersionUseCase: TemplateVersionUseCase,
        private val logger: Logger,
    ) {

        /**
         * 创建训练计划（调度多个Template）
         *
         * @param planName 计划名称
         * @param templateSelections 天数 → Template ID列表的映射
         * @param totalWeeks 计划总周数
         * @return 创建的训练计划
         */
        suspend fun createWorkoutPlan(
            planName: String,
            templateSelections: Map<Int, List<String>>, // 天数 → Template ID列表
            totalWeeks: Int,
        ): ModernResult<WorkoutPlan> {
            return try {
                // 1. 验证用户权限
                val userIdResult = getCurrentUserIdUseCase()
                if (userIdResult !is ModernResult.Success<String>) {
                    return ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "createWorkoutPlan",
                            message = UiText.DynamicString("获取用户信息失败"),
                        ),
                    )
                }
                val userId = userIdResult.data!!

                // 2. 验证所有Template存在性
                val allTemplateIds = templateSelections.values.flatten().distinct()
                val validatedTemplates = validateTemplates(allTemplateIds)
                if (validatedTemplates.isEmpty()) {
                    return ModernResult.Error(
                        DataErrors.DataError.notFound(
                            operationName = "createWorkoutPlan",
                            message = UiText.DynamicString("没有找到有效的训练模板"),
                            entityType = "WorkoutTemplate",
                        ),
                    )
                }

                // 3. 过滤有效的调度安排
                val validSchedule = templateSelections.mapValues { (_, templateIds) ->
                    templateIds.filter { it in validatedTemplates }
                }.filterValues { it.isNotEmpty() }

                // TODO: 需要适配新的WorkoutPlan类型
                // 暂时创建一个简单的计划对象
                val plan = WorkoutPlan(
                    id = UUID.randomUUID().toString(),
                    name = planName,
                    description = "AI生成的训练计划",
                    userId = userId,
                    targetGoal = "AI生成的训练计划",
                    difficultyLevel = 3, // INTERMEDIATE
                    estimatedDuration = 180,
                    planType = com.example.gymbro.shared.models.workout.PlanType.CUSTOM,
                    dailySchedule = emptyMap(),
                    totalDays = totalWeeks * 7,
                    tags = emptyList(),
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

                // 5. 保存计划
                planRepository.savePlan(plan)

                logger.d("TemplateService", "成功创建训练计划: ${plan.id}")
                ModernResult.Success(plan)
            } catch (e: Exception) {
                logger.e("TemplateService", "创建训练计划失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "createWorkoutPlan",
                        message = UiText.DynamicString("创建训练计划失败: ${e.message ?: "未知错误"}"),
                    ),
                )
            }
        }

        /**
         * 获取Template详情
         */
        suspend fun getTemplate(templateId: String): ModernResult<WorkoutTemplate> {
            return try {
                val result = templateRepository.getTemplateById(templateId)
                when (result) {
                    is ModernResult.Success<WorkoutTemplate?> -> {
                        if (result.data != null) {
                            logger.d("TemplateService", "获取Template成功: $templateId")
                            ModernResult.Success(result.data!!)
                        } else {
                            logger.w("TemplateService", "Template不存在: $templateId")
                            ModernResult.Error(
                                DataErrors.DataError.notFound(
                                    operationName = "getTemplate",
                                    message = UiText.DynamicString("模板不存在"),
                                    entityType = "WorkoutTemplate",
                                    entityId = templateId,
                                ),
                            )
                        }
                    }

                    is ModernResult.Error -> {
                        logger.w("TemplateService", "Template不存在: $templateId")
                        ModernResult.Error(
                            DataErrors.DataError.notFound(
                                operationName = "getTemplate",
                                message = UiText.DynamicString("模板不存在"),
                                entityType = "WorkoutTemplate",
                                entityId = templateId,
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "getTemplate",
                                message = UiText.DynamicString("获取Template超时"),
                            ),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("TemplateService", "获取Template失败: $templateId", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "getTemplate",
                        message = UiText.DynamicString("获取Template失败: ${e.message ?: "未知错误"}"),
                    ),
                )
            }
        }

        /**
         * 获取用户的所有Template
         */
        suspend fun getUserTemplates(userId: String): ModernResult<List<WorkoutTemplate>> {
            return try {
                val result = templateRepository.getTemplatesByUser(userId).first()
                when (result) {
                    is ModernResult.Success<List<WorkoutTemplate>> -> {
                        logger.d("TemplateService", "获取用户Templates成功: ${result.data.size}个")
                        result
                    }

                    is ModernResult.Error -> {
                        logger.w("TemplateService", "获取用户Templates失败")
                        result
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "getUserTemplates",
                                message = UiText.DynamicString("获取用户Templates超时"),
                            ),
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("TemplateService", "获取用户Templates异常", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "getUserTemplates",
                        message = UiText.DynamicString("获取用户Templates失败: ${e.message ?: "未知错误"}"),
                    ),
                )
            }
        }

        /**
         * 验证Template列表的存在性
         */
        private suspend fun validateTemplates(templateIds: List<String>): List<String> {
            val validTemplates = mutableListOf<String>()

            for (templateId in templateIds) {
                try {
                    val result = templateRepository.getTemplateById(templateId)
                    if (result is ModernResult.Success<WorkoutTemplate?> && result.data != null) {
                        validTemplates.add(templateId)
                        logger.d("TemplateService", "验证Template存在: $templateId")
                    } else {
                        logger.w("TemplateService", "Template不存在: $templateId")
                    }
                } catch (e: Exception) {
                    logger.e("TemplateService", "验证Template异常: $templateId", e)
                }
            }

            return validTemplates
        }

        /**
         * 智能推荐Template组合
         */
        suspend fun recommendTemplatesCombination(
            userId: String,
            goal: WorkoutGoal,
            difficulty: Difficulty,
            daysPerWeek: Int,
        ): ModernResult<List<String>> {
            return try {
                val userTemplatesResult = getUserTemplates(userId)
                if (userTemplatesResult !is ModernResult.Success) {
                    return ModernResult.Error(
                        DataErrors.DataError.notFound(
                            operationName = "recommendTemplatesCombination",
                            message = UiText.DynamicString("无法获取用户模板"),
                            entityType = "WorkoutTemplate",
                        ),
                    )
                }

                val userTemplates = userTemplatesResult.data

                // 简单的推荐算法：根据目标和难度筛选
                val recommendedTemplates = userTemplates
                    .filter { template ->
                        // 这里可以添加更复杂的匹配逻辑
                        template.difficulty?.let { it == difficulty.ordinal } ?: true
                    }
                    .take(daysPerWeek)
                    .map { it.id }

                logger.d("TemplateService", "推荐${recommendedTemplates.size}个Template")
                ModernResult.Success(recommendedTemplates)
            } catch (e: Exception) {
                logger.e("TemplateService", "推荐Template失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "recommendTemplatesCombination",
                        message = UiText.DynamicString("推荐Template失败: ${e.message ?: "未知错误"}"),
                    ),
                )
            }
        }

        // ========== 🚀 Task 4.2：新增DayPlan调度功能 ==========

        /**
         * 创建基于DayPlan的训练计划（新架构）
         *
         * 🎯 核心功能：
         * - 支持按天调度Template
         * - 使用新的DayPlan架构
         * - 灵活的天数分配策略
         * - Phase 3: Plan层适配 - 引用TemplateVersion确保不可变性
         *
         * @param planName 计划名称
         * @param dailySchedule 天数 → Template ID列表的映射
         * @param totalDays 计划总天数
         * @return 创建的训练计划
         */
        suspend fun createDailySchedulePlan(
            planName: String,
            dailySchedule: Map<Int, List<String>>, // 天数 → Template ID列表
            totalDays: Int,
        ): ModernResult<WorkoutPlan> {
            return try {
                logger.d("TemplateService", "开始创建DayPlan调度计划: $planName")

                // 1. 验证用户权限
                val userIdResult = getCurrentUserIdUseCase()
                if (userIdResult !is ModernResult.Success<String>) {
                    return ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "createDailySchedulePlan",
                            message = UiText.DynamicString("获取用户信息失败"),
                        ),
                    )
                }
                val userId = userIdResult.data!!

                // 2. 验证所有Template存在性
                val allTemplateIds = dailySchedule.values.flatten().distinct()
                val validatedTemplates = validateTemplates(allTemplateIds)
                if (validatedTemplates.isEmpty()) {
                    return ModernResult.Error(
                        DataErrors.DataError.notFound(
                            operationName = "createDailySchedulePlan",
                            message = UiText.DynamicString("没有找到有效的训练模板"),
                            entityType = "WorkoutTemplate",
                        ),
                    )
                }

                // 3. Phase 3适配: 获取所有Template的最新TemplateVersion
                logger.d("TemplateService", "获取Template最新版本: ${validatedTemplates.size}个")
                val templateVersionResult = templateVersionUseCase.batchGetLatestVersions.invoke(
                    TemplateVersionUseCase.BatchGetLatestVersionsParams(
                        templateIds = validatedTemplates,
                        autoCreateIfNotExist = true,
                    ),
                )

                val templateVersionMap = when (templateVersionResult) {
                    is ModernResult.Success -> templateVersionResult.data
                    is ModernResult.Error -> {
                        logger.e("TemplateService", "获取Template版本失败: ${templateVersionResult.error}")
                        return ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "createDailySchedulePlan",
                                message = UiText.DynamicString(
                                    "获取模板版本失败: ${templateVersionResult.error.message}",
                                ),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        return ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "createDailySchedulePlan",
                                message = UiText.DynamicString("获取模板版本超时"),
                            ),
                        )
                    }
                }

                // 4. 构建DayPlan调度，记录使用的TemplateVersion信息
                val dayPlanSchedule = dailySchedule.mapValues { (day, templateIds) ->
                    val validTemplateIds = templateIds.filter { it in validatedTemplates }

                    // 记录此DayPlan引用的TemplateVersion信息（用于审计和追溯）
                    val versionInfo = validTemplateIds.mapNotNull { templateId ->
                        templateVersionMap[templateId]?.let { version ->
                            "Template[$templateId] → Version[${version.versionNumber}]"
                        }
                    }.joinToString(", ")

                    if (versionInfo.isNotEmpty()) {
                        logger.d("TemplateService", "Day $day 版本信息: $versionInfo")
                    }

                    com.example.gymbro.shared.models.workout.DayPlan(
                        dayNumber = day,
                        templateIds = validTemplateIds, // 暂时仍使用templateIds保持兼容性
                        isRestDay = validTemplateIds.isEmpty(),
                        orderIndex = day,
                        // 在notes中记录版本快照信息（临时方案，后续可改为专门的版本字段）
                        dayNotes = if (versionInfo.isNotEmpty()) "Versions: $versionInfo" else null,
                    )
                }

                // 5. 创建计划，记录总体版本元数据
                val totalVersionedTemplates = templateVersionMap.size
                val versionSummary = templateVersionMap.values.joinToString(", ") {
                    "v${it.versionNumber}"
                }

                val plan = WorkoutPlan(
                    id = UUID.randomUUID().toString(),
                    name = planName,
                    description = "基于DayPlan的训练计划 (引用版本: $versionSummary)",
                    userId = userId,
                    targetGoal = "基于DayPlan的训练计划",
                    difficultyLevel = 3, // INTERMEDIATE
                    estimatedDuration = 180,
                    planType = com.example.gymbro.shared.models.workout.PlanType.CUSTOM,
                    dailySchedule = dayPlanSchedule,
                    totalDays = totalDays,
                    tags = listOf("版本控制", "模板数量:$totalVersionedTemplates"),
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

                // 6. 保存计划
                planRepository.savePlan(plan)

                logger.d(
                    "TemplateService",
                    "成功创建DayPlan调度计划: ${plan.id}, 引用${totalVersionedTemplates}个版本化模板",
                )
                ModernResult.Success(plan)
            } catch (e: Exception) {
                logger.e("TemplateService", "创建DayPlan调度计划失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "createDailySchedulePlan",
                        message = UiText.DynamicString("创建训练计划失败: ${e.message ?: "未知错误"}"),
                    ),
                )
            }
        }

        /**
         * 创建循环训练计划
         *
         * 🎯 核心功能：
         * - Template循环分配到指定天数
         * - 支持休息日设置
         * - 自动循环Template避免重复
         * - Phase 3: Plan层适配 - 引用TemplateVersion确保不可变性
         *
         * @param planName 计划名称
         * @param templateIds Template ID列表
         * @param cycleDays 循环天数
         * @param restDays 休息日设置（可选）
         * @return 创建的循环训练计划
         */
        suspend fun createCyclePlan(
            planName: String,
            templateIds: List<String>,
            cycleDays: Int,
            restDays: Set<Int> = emptySet(),
        ): ModernResult<WorkoutPlan> {
            return try {
                logger.d("TemplateService", "开始创建循环训练计划: $planName")

                // 1. 验证用户权限
                val userIdResult = getCurrentUserIdUseCase()
                if (userIdResult !is ModernResult.Success<String>) {
                    return ModernResult.Error(
                        DataErrors.DataError.access(
                            operationName = "createCyclePlan",
                            message = UiText.DynamicString("获取用户信息失败"),
                        ),
                    )
                }
                val userId = userIdResult.data!!

                // 2. 验证Template存在性
                val validatedTemplates = validateTemplates(templateIds)
                if (validatedTemplates.isEmpty()) {
                    return ModernResult.Error(
                        DataErrors.DataError.notFound(
                            operationName = "createCyclePlan",
                            message = UiText.DynamicString("没有找到有效的训练模板"),
                            entityType = "WorkoutTemplate",
                        ),
                    )
                }

                // 3. Phase 3适配: 获取所有Template的最新TemplateVersion
                logger.d("TemplateService", "获取循环计划Template最新版本: ${validatedTemplates.size}个")
                val templateVersionResult = templateVersionUseCase.batchGetLatestVersions.invoke(
                    TemplateVersionUseCase.BatchGetLatestVersionsParams(
                        templateIds = validatedTemplates,
                        autoCreateIfNotExist = true,
                    ),
                )

                val templateVersionMap = when (templateVersionResult) {
                    is ModernResult.Success -> templateVersionResult.data
                    is ModernResult.Error -> {
                        logger.e("TemplateService", "获取循环计划Template版本失败: ${templateVersionResult.error}")
                        return ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "createCyclePlan",
                                message = UiText.DynamicString(
                                    "获取模板版本失败: ${templateVersionResult.error.message}",
                                ),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        return ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "createCyclePlan",
                                message = UiText.DynamicString("获取模板版本超时"),
                            ),
                        )
                    }
                }

                // 4. 构建循环调度，记录使用的TemplateVersion信息
                val dailySchedule = mutableMapOf<Int, com.example.gymbro.shared.models.workout.DayPlan>()
                var templateIndex = 0

                for (day in 1..cycleDays) {
                    if (day in restDays) {
                        // 休息日
                        dailySchedule[day] = com.example.gymbro.shared.models.workout.DayPlan.createRestDay(
                            dayNumber = day,
                            notes = "休息日",
                        )
                    } else {
                        // 训练日 - 循环分配Template
                        val templateId = validatedTemplates[templateIndex % validatedTemplates.size]
                        val templateVersion = templateVersionMap[templateId]

                        // 记录版本信息
                        val versionInfo = templateVersion?.let { version ->
                            "Template[$templateId] → Version[${version.versionNumber}]"
                        } ?: "Template[$templateId] → Version[未知]"

                        logger.d("TemplateService", "循环计划 Day $day: $versionInfo")

                        dailySchedule[day] = com.example.gymbro.shared.models.workout.DayPlan.createWorkoutDay(
                            dayNumber = day,
                            templateIds = listOf(templateId),
                            notes = "Cycle Day $day - $versionInfo",
                        )
                        templateIndex++
                    }
                }

                // 5. 创建循环计划，记录版本元数据
                val totalVersionedTemplates = templateVersionMap.size
                val versionSummary = templateVersionMap.values.joinToString(", ") {
                    "v${it.versionNumber}"
                }

                val plan = WorkoutPlan(
                    id = UUID.randomUUID().toString(),
                    name = planName,
                    description = "循环训练计划 (${cycleDays}天循环，引用版本: $versionSummary)",
                    userId = userId,
                    targetGoal = "循环训练计划",
                    difficultyLevel = 3, // INTERMEDIATE
                    estimatedDuration = 180,
                    planType = com.example.gymbro.shared.models.workout.PlanType.CYCLIC,
                    dailySchedule = dailySchedule,
                    totalDays = cycleDays,
                    tags = listOf("循环计划", "版本控制", "模板数量:$totalVersionedTemplates"),
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

                // 6. 保存计划
                planRepository.savePlan(plan)

                logger.d(
                    "TemplateService",
                    "成功创建循环训练计划: ${plan.id}, ${cycleDays}天循环，引用${totalVersionedTemplates}个版本化模板",
                )
                ModernResult.Success(plan)
            } catch (e: Exception) {
                logger.e("TemplateService", "创建循环训练计划失败", e)
                ModernResult.Error(
                    DataErrors.DataError.create(
                        operationName = "createCyclePlan",
                        message = UiText.DynamicString("创建循环训练计划失败: ${e.message ?: "未知错误"}"),
                    ),
                )
            }
        }
    }
