package com.example.gymbro.domain.coach.executor.modules

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.executor.FunctionCall
import com.example.gymbro.domain.coach.executor.FunctionResult
import com.example.gymbro.domain.workout.model.WorkoutAction
import com.example.gymbro.domain.workout.model.calendar.CalendarItem
import com.example.gymbro.domain.workout.model.calendar.CalendarItemType
import com.example.gymbro.domain.workout.repository.CalendarRepository
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus
import kotlinx.serialization.json.Json

/**
 * 日历域模块
 *
 * 负责处理日历相关的Function Call请求
 * 支持template和plan的日历排程操作
 *
 * 核心功能：
 * 1. 添加template到指定日期
 * 2. 添加plan到日历排程
 * 3. 移动日历项目
 * 4. 删除日历项目
 * 5. 获取日历排程
 *
 * 设计原则：
 * - 保持简单，避免过度设计
 * - 直接处理参数，严格验证
 * - 返回简单JSON格式，便于AI解析
 * - 完整的错误处理和日志记录
 *
 * @property calendarRepository 日历数据仓库
 * @property templateRepository 模板数据仓库
 * @property planRepository 计划数据仓库
 * @property getCurrentUserIdUseCase 获取当前用户ID用例
 * @property ioDispatcher IO调度器
 * @property logger 日志记录器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Calendar Function Call集成)
 */
@Singleton
class CalendarFunctionModule
    @Inject
    constructor(
        private val calendarRepository: CalendarRepository,
        private val templateRepository: TemplateRepository,
        private val planRepository: PlanRepository,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
        private val logger: Logger,
    ) {

        /**
         * 处理日历域的Function Call
         *
         * @param functionCall Function Call请求
         * @param onActionTrigger UI动作触发回调
         * @return 函数执行结果
         */
        suspend fun handle(
            functionCall: FunctionCall,
            onActionTrigger: ((WorkoutAction) -> Unit)? = null,
        ): ModernResult<FunctionResult> = safeCatch {
            logger.d("📅 处理日历函数: ${functionCall.name}")

            val result = when (functionCall.name) {
                "gymbro.calendar.add_template" -> handleAddTemplate(functionCall.arguments, onActionTrigger)
                "gymbro.calendar.add_plan" -> handleAddPlan(functionCall.arguments, onActionTrigger)
                "gymbro.calendar.move_item" -> handleMoveItem(functionCall.arguments, onActionTrigger)
                "gymbro.calendar.remove_item" -> handleRemoveItem(functionCall.arguments, onActionTrigger)
                "gymbro.calendar.get_schedule" -> handleGetSchedule(functionCall.arguments)
                "gymbro.calendar.add_custom_workout" -> handleAddCustomWorkout(
                    functionCall.arguments,
                    onActionTrigger,
                )

                else -> return@safeCatch FunctionResult(
                    success = false,
                    error = "未知的日历函数: ${functionCall.name}",
                )
            }

            when (result) {
                is ModernResult.Success -> result.data
                is ModernResult.Error -> FunctionResult(
                    success = false,
                    error = "执行失败: ${result.error.uiMessage}",
                )

                is ModernResult.Loading -> FunctionResult(
                    success = false,
                    error = "执行超时，请稍后重试",
                )
            }
        }

        /**
         * 处理添加template到日历
         *
         * 参数：
         * - template_id: 模板ID
         * - date: 日期 (YYYY-MM-DD)
         * - time: 时间 (HH:mm) [可选]
         * - notes: 备注 [可选]
         */
        private suspend fun handleAddTemplate(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val templateId = arguments["template_id"]
                val dateStr = arguments["date"]

                if (templateId.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "模板ID不能为空",
                    )
                }

                if (dateStr.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "日期不能为空",
                    )
                }

                // 解析日期
                val date = try {
                    LocalDate.parse(dateStr)
                } catch (e: Exception) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "日期格式错误，请使用YYYY-MM-DD格式",
                    )
                }

                val time = arguments["time"] // 可选时间
                val notes = arguments["notes"] // 可选备注
                val userId = getCurrentUserIdUseCase().first().getOrNull()

                if (userId == null) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "用户未登录",
                    )
                }

                logger.d("添加模板到日历: templateId=$templateId, date=$date, time=$time")

                // 构建metadata
                val metadata = buildMap {
                    time?.let { put("time", it) }
                    notes?.let { put("notes", it) }
                    put("user_id", userId)
                }

                // 添加模板到日历
                val addResult = calendarRepository.addTemplateToCalendar(templateId, date, userId, metadata)
                when (addResult) {
                    is ModernResult.Success -> {
                        val calendarItem = addResult.data

                        // 触发UI动作
                        onActionTrigger?.invoke(WorkoutAction.TemplateAddedToCalendar)

                        val responseData = mapOf(
                            "calendar_item_id" to calendarItem.id,
                            "template_id" to templateId,
                            "template_name" to calendarItem.name,
                            "date" to dateStr,
                            "operation" to "template_added_to_calendar",
                            "message" to "训练模板「${calendarItem.name}」已添加到$dateStr",
                        )

                        val responseJson = Json.encodeToString(responseData)

                        return@withContext FunctionResult(
                            success = true,
                            data = responseJson,
                            actionTriggered = "TemplateAddedToCalendar",
                            metadata = mapOf(
                                "calendar_item_id" to calendarItem.id,
                                "template_id" to templateId,
                                "date" to dateStr,
                            ),
                        )
                    }

                    is ModernResult.Error -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "保存到日历失败: ${addResult.error.uiMessage}",
                        )
                    }

                    is ModernResult.Loading -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "保存超时，请稍后重试",
                        )
                    }
                }
            }
        }

        /**
         * 处理添加plan到日历
         *
         * 参数：
         * - plan_id: 计划ID
         * - start_date: 开始日期 (YYYY-MM-DD)
         * - auto_schedule: 是否自动排程 [可选，默认true]
         */
        private suspend fun handleAddPlan(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val planId = arguments["plan_id"]
                val startDateStr = arguments["start_date"]
                val autoSchedule = arguments["auto_schedule"]?.toBoolean() ?: true

                if (planId.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "计划ID不能为空",
                    )
                }

                if (startDateStr.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "开始日期不能为空",
                    )
                }

                // 解析日期
                val startDate = try {
                    LocalDate.parse(startDateStr)
                } catch (e: Exception) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "日期格式错误，请使用YYYY-MM-DD格式",
                    )
                }

                val userId = getCurrentUserIdUseCase().first().getOrNull()
                if (userId == null) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "用户未登录",
                    )
                }

                logger.d("添加计划到日历: planId=$planId, startDate=$startDate, autoSchedule=$autoSchedule")

                // 添加计划到日历
                val addResult = calendarRepository.addPlanToCalendar(
                    planId = planId,
                    startDate = startDate,
                    autoSchedule = autoSchedule,
                    userId = userId,
                )

                when (addResult) {
                    is ModernResult.Success -> {
                        val addedItemsCount = addResult.data

                        // 触发UI动作
                        onActionTrigger?.invoke(WorkoutAction.PlanAddedToCalendar)

                        val responseData = mapOf(
                            "plan_id" to planId,
                            "start_date" to startDateStr,
                            "items_added" to addedItemsCount.toString(),
                            "auto_schedule" to autoSchedule.toString(),
                            "operation" to "plan_added_to_calendar",
                            "message" to "训练计划已添加到日历，从${startDateStr}开始，共${addedItemsCount}个训练日",
                        )

                        val responseJson = Json.encodeToString(responseData)

                        return@withContext FunctionResult(
                            success = true,
                            data = responseJson,
                            actionTriggered = "PlanAddedToCalendar",
                            metadata = mapOf(
                                "plan_id" to planId,
                                "start_date" to startDateStr,
                                "items_added" to addedItemsCount,
                            ),
                        )
                    }

                    is ModernResult.Error -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "添加计划到日历失败: ${addResult.error.uiMessage}",
                        )
                    }

                    is ModernResult.Loading -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "添加超时，请稍后重试",
                        )
                    }
                }
            }
        }

        /**
         * 处理移动日历项目
         *
         * 参数：
         * - item_id: 项目ID
         * - from_date: 原日期 (YYYY-MM-DD)
         * - to_date: 目标日期 (YYYY-MM-DD)
         */
        private suspend fun handleMoveItem(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val itemId = arguments["item_id"]
                val fromDateStr = arguments["from_date"]
                val toDateStr = arguments["to_date"]

                if (itemId.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "项目ID不能为空",
                    )
                }

                if (fromDateStr.isNullOrBlank() || toDateStr.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "日期不能为空",
                    )
                }

                val fromDate = try {
                    LocalDate.parse(fromDateStr)
                } catch (e: Exception) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "原日期格式错误",
                    )
                }

                val toDate = try {
                    LocalDate.parse(toDateStr)
                } catch (e: Exception) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "目标日期格式错误",
                    )
                }

                logger.d("移动日历项目: itemId=$itemId, from=$fromDate, to=$toDate")

                val moveResult = calendarRepository.moveCalendarItem(itemId, fromDate, toDate)
                when (moveResult) {
                    is ModernResult.Success -> {
                        val movedItem = moveResult.data

                        onActionTrigger?.invoke(WorkoutAction.CalendarItemMoved)

                        val responseData = mapOf(
                            "item_id" to itemId,
                            "item_name" to movedItem.name,
                            "from_date" to fromDateStr,
                            "to_date" to toDateStr,
                            "operation" to "calendar_item_moved",
                            "message" to "「${movedItem.name}」已从${fromDateStr}移动到$toDateStr",
                        )

                        return@withContext FunctionResult(
                            success = true,
                            data = Json.encodeToString(responseData),
                            actionTriggered = "CalendarItemMoved",
                        )
                    }

                    is ModernResult.Error -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "移动项目失败: ${moveResult.error.uiMessage}",
                        )
                    }

                    is ModernResult.Loading -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "移动超时，请稍后重试",
                        )
                    }
                }
            }
        }

        /**
         * 处理删除日历项目
         *
         * 参数：
         * - item_id: 项目ID
         */
        private suspend fun handleRemoveItem(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val itemId = arguments["item_id"]

                if (itemId.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "项目ID不能为空",
                    )
                }

                val removeResult = calendarRepository.removeCalendarItem(itemId)
                when (removeResult) {
                    is ModernResult.Success -> {
                        val removedItem = removeResult.data

                        onActionTrigger?.invoke(WorkoutAction.CalendarItemRemoved)

                        val responseData = mapOf(
                            "item_id" to itemId,
                            "item_name" to removedItem.name,
                            "operation" to "calendar_item_removed",
                            "message" to "「${removedItem.name}」已从日历中删除",
                        )

                        return@withContext FunctionResult(
                            success = true,
                            data = Json.encodeToString(responseData),
                            actionTriggered = "CalendarItemRemoved",
                        )
                    }

                    is ModernResult.Error -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "删除项目失败: ${removeResult.error.uiMessage}",
                        )
                    }

                    is ModernResult.Loading -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "删除超时，请稍后重试",
                        )
                    }
                }
            }
        }

        /**
         * 处理获取日历排程
         *
         * 参数：
         * - start_date: 开始日期 (YYYY-MM-DD)
         * - end_date: 结束日期 (YYYY-MM-DD) [可选，默认为开始日期后7天]
         */
        private suspend fun handleGetSchedule(
            arguments: Map<String, String>,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val startDateStr = arguments["start_date"]
                val endDateStr = arguments["end_date"]

                if (startDateStr.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "开始日期不能为空",
                    )
                }

                val startDate = try {
                    LocalDate.parse(startDateStr)
                } catch (e: Exception) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "开始日期格式错误",
                    )
                }

                val endDate = if (endDateStr.isNullOrBlank()) {
                    startDate.plus(7, DateTimeUnit.DAY) // 默认查询7天
                } else {
                    try {
                        LocalDate.parse(endDateStr)
                    } catch (e: Exception) {
                        return@withContext FunctionResult(
                            success = false,
                            error = "结束日期格式错误",
                        )
                    }
                }

                val userId = getCurrentUserIdUseCase().first().getOrNull()
                if (userId == null) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "用户未登录",
                    )
                }

                val scheduleResult = calendarRepository.getCalendarSchedule(startDate, endDate, userId)
                when (scheduleResult) {
                    is ModernResult.Success -> {
                        val schedule = scheduleResult.data

                        val responseData = mapOf(
                            "start_date" to startDateStr,
                            "end_date" to endDate.toString(),
                            "total_items" to schedule.size.toString(),
                            "schedule" to schedule.map { (date, items) ->
                                mapOf(
                                    "date" to date.toString(),
                                    "items" to items.map { item ->
                                        mapOf(
                                            "id" to item.id,
                                            "name" to item.name,
                                            "type" to item.type.name,
                                            "duration" to (item.estimatedDuration?.toString() ?: ""),
                                            "completed" to item.isCompleted.toString(),
                                        )
                                    },
                                )
                            },
                            "operation" to "calendar_schedule_retrieved",
                        )

                        return@withContext FunctionResult(
                            success = true,
                            data = Json.encodeToString(responseData),
                        )
                    }

                    is ModernResult.Error -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "获取排程失败: ${scheduleResult.error.uiMessage}",
                        )
                    }

                    is ModernResult.Loading -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "获取超时，请稍后重试",
                        )
                    }
                }
            }
        }

        /**
         * 处理添加自定义训练
         *
         * 参数：
         * - name: 训练名称
         * - date: 日期 (YYYY-MM-DD)
         * - estimated_duration: 预计时长（分钟）[可选]
         */
        private suspend fun handleAddCustomWorkout(
            arguments: Map<String, String>,
            onActionTrigger: ((WorkoutAction) -> Unit)?,
        ): ModernResult<FunctionResult> = safeCatch {
            withContext(ioDispatcher) {
                val name = arguments["name"]
                val dateStr = arguments["date"]
                val estimatedDuration = arguments["estimated_duration"]?.toIntOrNull()

                if (name.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "训练名称不能为空",
                    )
                }

                if (dateStr.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "日期不能为空",
                    )
                }

                // 解析日期
                val date = try {
                    LocalDate.parse(dateStr)
                } catch (e: Exception) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "日期格式错误，请使用YYYY-MM-DD格式",
                    )
                }

                val userId = getCurrentUserIdUseCase().first().getOrNull()
                if (userId == null) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "用户未登录",
                    )
                }

                logger.d("添加自定义训练: name=$name, date=$date, duration=$estimatedDuration")

                // 创建自定义训练项目
                val calendarItem = CalendarItem(
                    id = "custom_${System.currentTimeMillis()}",
                    type = CalendarItemType.CUSTOM_WORKOUT,
                    sourceId = "", // 自定义训练没有源ID
                    date = date,
                    name = name,
                    estimatedDuration = estimatedDuration,
                    canDrag = true,
                    metadata = mapOf("user_id" to userId),
                )

                // 保存到日历
                val saveResult = calendarRepository.addCalendarItem(calendarItem)
                when (saveResult) {
                    is ModernResult.Success -> {
                        // 触发UI动作
                        onActionTrigger?.invoke(WorkoutAction.CustomWorkoutAdded)

                        val responseData = mapOf(
                            "calendar_item_id" to calendarItem.id,
                            "name" to name,
                            "date" to dateStr,
                            "estimated_duration" to (estimatedDuration?.toString() ?: ""),
                            "operation" to "custom_workout_added",
                            "message" to "自定义训练「$name」已添加到$dateStr",
                        )

                        val responseJson = Json.encodeToString(responseData)

                        return@withContext FunctionResult(
                            success = true,
                            data = responseJson,
                            actionTriggered = "CustomWorkoutAdded",
                            metadata = mapOf(
                                "calendar_item_id" to calendarItem.id,
                                "name" to name,
                                "date" to dateStr,
                            ),
                        )
                    }

                    is ModernResult.Error -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "保存自定义训练失败: ${saveResult.error.uiMessage}",
                        )
                    }

                    is ModernResult.Loading -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "保存超时，请稍后重试",
                        )
                    }
                }
            }
        }
    }
