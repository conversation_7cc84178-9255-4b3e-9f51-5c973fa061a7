package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.InputDraft
import com.example.gymbro.domain.coach.repository.DraftRepository
import javax.inject.Inject
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter

/**
 * ManageDraftUseCase - 草稿管理用例
 *
 * 负责：
 * 1. 自动保存用户输入的草稿
 * 2. 恢复会话的草稿内容
 * 3. 清理过期草稿
 *
 * 提供高级的草稿管理功能，包括防抖保存和自动清理
 */
class ManageDraftUseCase
    @Inject
    constructor(
        private val draftRepository: DraftRepository,
    ) {

        companion object {
            private const val AUTO_SAVE_DEBOUNCE_MS = 500L
        }

        /**
         * 启动会话级别的草稿管理
         *
         * @param sessionId 会话ID
         * @param textFlow 用户输入的文本流
         * @return 恢复的草稿内容，如果没有则返回空字符串
         */
        suspend fun startSession(sessionId: String, textFlow: Flow<String>): String {
            // 恢复现有草稿
            val restoredContent = when (val result = draftRepository.getDraft(sessionId)) {
                is ModernResult.Success -> result.data?.content ?: ""
                is ModernResult.Error -> ""
                is ModernResult.Loading -> ""
            }

            // 设置自动保存
            setupAutoSave(sessionId, textFlow)

            return restoredContent
        }

        /**
         * 设置自动保存
         */
        @OptIn(kotlinx.coroutines.FlowPreview::class)
        private suspend fun setupAutoSave(sessionId: String, textFlow: Flow<String>) {
            textFlow
                .debounce(AUTO_SAVE_DEBOUNCE_MS)
                .distinctUntilChanged()
                .filter { it.isNotBlank() }
                .collect { content ->
                    saveDraft(sessionId, content)
                }
        }

        /**
         * 保存草稿
         *
         * @param sessionId 会话ID
         * @param content 草稿内容
         */
        suspend fun saveDraft(sessionId: String, content: String): ModernResult<Unit> {
            if (content.isBlank()) {
                return ModernResult.Success(Unit)
            }

            val draft = InputDraft.create(sessionId, content)
            return draftRepository.saveDraft(draft)
        }

        /**
         * 获取草稿
         *
         * @param sessionId 会话ID
         * @return 草稿内容
         */
        suspend fun getDraft(sessionId: String): ModernResult<InputDraft?> {
            return draftRepository.getDraft(sessionId)
        }

        /**
         * 清除草稿
         *
         * @param sessionId 会话ID
         */
        suspend fun clearDraft(sessionId: String): ModernResult<Unit> {
            return draftRepository.clearDraft(sessionId)
        }

        /**
         * 清理过期草稿
         *
         * @return 清理的草稿数量
         */
        suspend fun clearExpiredDrafts(): ModernResult<Int> {
            return draftRepository.clearExpiredDrafts()
        }
    }
