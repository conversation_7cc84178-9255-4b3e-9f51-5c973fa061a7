package com.example.gymbro.app.loading

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.gymbro.app.loading.components.RainbowLogo
import com.example.gymbro.app.version.AppLockedScreen
import com.example.gymbro.app.version.ForceUpdateScreen
import kotlinx.coroutines.delay

@Composable
fun LoadingScreen(
    loadingViewModel: LoadingViewModel = hiltViewModel(),
    onLoadingFinished: () -> Unit,
) {
    // 观察统一的loading状态
    val loadingState by loadingViewModel.loadingState.collectAsState()

    // 使用Material主题系统的背景色
    val backgroundColor = MaterialTheme.colorScheme.background

    // 状态变化处理：静默后台处理，只关注最终结果
    LaunchedEffect(loadingState) {
        when (loadingState) {
            is LoadingState.Ready -> {
                // 短暂延迟确保用户看到Loading完成
                delay(StartupConstants.Animation.LOADING_FINISH_DELAY_MS)
                onLoadingFinished()
            }

            is LoadingState.AppLocked,
            is LoadingState.ForceUpdate,
            -> {
                // 这些状态会在UI中直接显示，不跳转
            }

            is LoadingState.Initializing,
            is LoadingState.CheckingVersion,
            -> {
                // 继续显示Loading，无需特殊处理
            }
        }
    }

    // 根据状态显示不同的UI
    when (val currentState = loadingState) {
        is LoadingState.AppLocked -> {
            AppLockedScreen(lockInfo = currentState.lockInfo)
        }

        is LoadingState.ForceUpdate -> {
            ForceUpdateScreen(updateInfo = currentState.updateInfo)
        }

        is LoadingState.Initializing,
        is LoadingState.CheckingVersion,
        is LoadingState.Ready,
        -> {
            // 统一的Loading界面：简洁优雅，无多余信息
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(backgroundColor),
                contentAlignment = Alignment.Center,
            ) {
                RainbowLogo()
            }
        }
    }
}
