package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.ui.text.UiText
import javax.inject.Inject

/**
 * BuildFunctionCallMessageUseCase - 构建Function Call用户友好消息
 *
 * 负责：
 * 1. 将Function Call名称转换为用户友好的描述文本
 * 2. 支持国际化的字符串资源管理
 * 3. 提供默认的Function Call消息格式
 *
 * 这个UseCase将硬编码字符串逻辑从Reducer中移出，
 * 符合Clean Architecture原则和国际化要求
 */
class BuildFunctionCallMessageUseCase
    @Inject
    constructor() {

        /**
         * 构建Function Call用户友好消息内容
         *
         * @param functionCallName Function Call的名称
         * @return 用户友好的描述文本，使用UiText.StringResource支持国际化
         */
        operator fun invoke(functionCallName: String): UiText {
            return when (functionCallName) {
                // === 工具面板核心4个功能 ===

                // 制作训练模板
                "gymbro.template.generate" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_template_generate,
                    emptyList(),
                )

                // 制作训练计划
                "gymbro.plan.generate_blank" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_plan_generate,
                    emptyList(),
                )

                // 制作饮食计划
                "gymbro.nutrition.generate_plan" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_nutrition_plan,
                    emptyList(),
                )

                // 分析训练
                "gymbro.training.analyze" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_training_analyze,
                    emptyList(),
                )

                // === 其他Extended功能 ===

                // Exercise Domain
                "gymbro.exercise.search" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_exercise_search,
                    emptyList(),
                )

                "gymbro.exercise.get_detail" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_exercise_detail,
                    emptyList(),
                )

                "gymbro.exercise.upsert" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_exercise_create,
                    emptyList(),
                )

                // Template Domain
                "gymbro.template.search" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_template_search,
                    emptyList(),
                )

                "gymbro.template.get_detail" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_template_detail,
                    emptyList(),
                )

                "gymbro.template.upsert" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_template_create,
                    emptyList(),
                )

                // Plan Domain
                "gymbro.plan.search" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_plan_search,
                    emptyList(),
                )

                "gymbro.plan.get_detail" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_plan_detail,
                    emptyList(),
                )

                "gymbro.plan.upsert" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_plan_create,
                    emptyList(),
                )

                // Session Domain
                "gymbro.session.start" -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_session_start,
                    emptyList(),
                )

                // 默认情况
                else -> UiText.StringResource(
                    com.example.gymbro.designSystem.R.string.ai_coach_function_call_default,
                    listOf(functionCallName),
                )
            }
        }
    }
