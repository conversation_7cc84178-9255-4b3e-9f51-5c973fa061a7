package com.example.gymbro.features.coach.history.internal.effect

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.history.ConversationPagingSourceFactory
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.usecase.ChatSessionManagementUseCase
import com.example.gymbro.domain.coach.usecase.GenerateChatTitleUseCase
import com.example.gymbro.domain.coach.usecase.GetMessageByIdUseCase
import com.example.gymbro.features.coach.history.HistoryContract
import com.example.gymbro.features.coach.shared.services.HybridSearchUseCase
import com.example.gymbro.features.coach.shared.utils.GlobalPerformanceConfig
import javax.inject.Inject
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

/**
 * History EffectHandler - Paging3支持版（基于task1.md债务偿还方案 + history.md设计）
 *
 * 新增特性：
 * 1. Paging3集成 - 使用ConversationPagingSource实现分页加载
 * 2. 搜索防抖优化 - 300ms防抖避免频繁请求
 * 3. 取消机制 - 新搜索自动取消旧搜索
 * 4. 结构化错误 - 使用ErrorState统一错误处理
 * 5. 重试机制 - 失败操作支持重试
 *
 * 🔥 修复：协程生命周期管理和取消处理
 *
 * @since Paging3集成重构 - 解决大量会话数据的性能问题
 */
internal class HistoryEffectHandler
    @Inject
    constructor(
        private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val hybridSearchUseCase: HybridSearchUseCase,
        private val pagingSourceFactory: ConversationPagingSourceFactory,
        private val generateChatTitleUseCase: GenerateChatTitleUseCase,
        // 移除RAG相关依赖 - 不属于History模块职责
        private val getMessageByIdUseCase: GetMessageByIdUseCase, // 🔥 新增: 单个消息获取用例
        @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    ) {

        companion object {
            private const val TAG = "HistoryEffectHandler"
        }

        // 🔥 修复：改进协程作用域管理
        private lateinit var handlerScope: CoroutineScope
        private lateinit var intentSender: (HistoryContract.Intent) -> Unit

        // Paging 配置
        private val pagingConfig = PagingConfig(
            pageSize = 30,
            initialLoadSize = 30,
            enablePlaceholders = false,
            prefetchDistance = 10,
            maxSize = 300, // 🔥 修复：限制内存使用，避免过度缓存
        )

        // 搜索任务管理
        private var currentSearchJob: Job? = null

        @Volatile
        private var isLoading = false

        /**
         * 🔥 修复：改进初始化方法，确保协程作用域正确管理
         */
        fun initialize(scope: CoroutineScope, intentSender: (HistoryContract.Intent) -> Unit) {
            this.handlerScope = scope
            this.intentSender = intentSender

            // 🔥 修复：确保协程作用域有效性
            if (!scope.isActive) {
                Timber.w(TAG, "⚠️ 接收到已取消的协程作用域，可能存在生命周期问题")
            } else {
                Timber.d(TAG, "✅ EffectHandler初始化完成，协程作用域有效")
            }
        }

        /**
         * Effect处理主入口
         */
        fun handle(effect: HistoryContract.Effect) {
            // 🔥 修复：在处理Effect前检查协程作用域状态
            if (!::handlerScope.isInitialized || !handlerScope.isActive) {
                Timber.w(TAG, "⚠️ 协程作用域无效，跳过Effect处理: $effect")
                return
            }

            when (effect) {
                is HistoryContract.Effect.LoadInitialHistory -> handleLoadHistory()
                is HistoryContract.Effect.RefreshHistoryData -> handleRefreshHistory()
                is HistoryContract.Effect.PerformSearch -> handlePerformSearch(effect)
                is HistoryContract.Effect.StartHybridSearch -> handlePerformSearch(
                    HistoryContract.Effect.PerformSearch(effect.query, HistoryContract.SearchMode.HYBRID),
                )

                is HistoryContract.Effect.NavigateToSession -> handleNavigateToSession(effect)
                is HistoryContract.Effect.ShowMessage -> handleShowMessage(effect)
                is HistoryContract.Effect.GenerateSessionTitle -> handleGenerateTitle(effect)
                is HistoryContract.Effect.GenerateMessageEmbedding -> handleGenerateEmbedding(effect)
                is HistoryContract.Effect.ProcessPendingEmbeddings -> handleProcessPendingEmbeddings(effect)
                is HistoryContract.Effect.RetrieveRagContext -> handleRetrieveContext(effect)
                is HistoryContract.Effect.InjectSelectedContext -> handleInjectContext(effect)
                is HistoryContract.Effect.RefreshEmbeddingStats -> handleRefreshEmbeddingStats()

                // === 会话管理副作用处理 ===
                is HistoryContract.Effect.DeleteConversation -> handleDeleteConversation(effect)
                is HistoryContract.Effect.RenameConversation -> handleRenameConversation(effect)
                is HistoryContract.Effect.ExportConversation -> handleExportConversation(effect)
                is HistoryContract.Effect.BatchDeleteConversations -> handleBatchDeleteConversations(effect)
            }
        }

        /**
         * Intent处理（某些Intent直接由EffectHandler处理）
         */
        fun handleIntent(intent: HistoryContract.Intent) {
            // 🔥 修复：在处理Intent前检查协程作用域状态
            if (!::handlerScope.isInitialized || !handlerScope.isActive) {
                Timber.w(TAG, "⚠️ 协程作用域无效，跳过Intent处理: $intent")
                return
            }

            when (intent) {
                is HistoryContract.Intent.LoadHistory -> handleLoadHistory()
                is HistoryContract.Intent.LoadInitialHistory -> handleLoadHistory()
                is HistoryContract.Intent.RefreshHistoryData -> handleRefreshHistory()
                is HistoryContract.Intent.PerformSearch -> handlePerformSearch(
                    HistoryContract.Effect.PerformSearch(intent.query, intent.mode),
                )

                is HistoryContract.Intent.StartHybridSearch -> handlePerformSearch(
                    HistoryContract.Effect.PerformSearch(intent.query, HistoryContract.SearchMode.HYBRID),
                )

                else -> {
                    // 其他Intent发送回ViewModel处理
                    sendIntent(intent)
                }
            }
        }

        /**
         * 🔥 修复：安全的Intent发送方法
         */
        private fun sendIntent(intent: HistoryContract.Intent) {
            try {
                if (::intentSender.isInitialized) {
                    Timber.d(TAG, "🚀 发送Intent: ${intent::class.simpleName}")
                    intentSender(intent)
                    Timber.d(TAG, "✅ Intent发送成功: ${intent::class.simpleName}")
                } else {
                    Timber.w(TAG, "⚠️ intentSender未初始化，无法发送Intent: $intent")
                }
            } catch (e: Exception) {
                Timber.e(TAG, "发送Intent失败: $intent", e)
            }
        }

        /**
         * 处理历史记录加载 - 🔥 修复：协程生命周期管理
         */
        private fun handleLoadHistory() {
            // 🔥 修复：防止重复加载
            if (isLoading) {
                Timber.d(TAG, "历史记录正在加载中，跳过重复请求")
                return
            }

            isLoading = true
            handlerScope.launch {
                try {
                    // 🔥 修复：在协程内部定期检查取消状态
                    currentCoroutineContext().ensureActive()

                    // 创建 Paging3 数据流
                    val pagingDataFlow: Flow<PagingData<ChatSession>> = Pager(
                        config = pagingConfig,
                        pagingSourceFactory = {
                            pagingSourceFactory.create(searchQuery = "")
                        },
                    ).flow

                    // 🔥 修复：确保协程仍然活跃才发送Intent
                    currentCoroutineContext().ensureActive()

                    // 发送 PagingData 流给 Contract
                    sendIntent(HistoryContract.Intent.HistoryLoadedResult(pagingDataFlow))

                    Timber.d(TAG, "🔥 Paging3 数据流创建成功，已发送HistoryLoadedResult Intent")
                } catch (e: CancellationException) {
                    // 🔥 修复：正确处理协程取消
                    Timber.d(TAG, "历史记录加载被取消")
                    throw e // 重新抛出取消异常
                } catch (e: Exception) {
                    Timber.e(e, "创建 Paging3 数据流失败")
                    sendIntent(
                        HistoryContract.Intent.OperationFailedResult(
                            HistoryContract.ErrorState(
                                message = UiText.DynamicString("加载会话失败"),
                                type = HistoryContract.ErrorType.STORAGE,
                                retryAction = { handleLoadHistory() },
                            ),
                        ),
                    )
                } finally {
                    isLoading = false
                }
            }
        }

        /**
         * 处理搜索请求 - 防抖和取消机制（关键债务偿还功能）
         * 🔥 修复：协程取消处理
         */
        private fun handlePerformSearch(effect: HistoryContract.Effect.PerformSearch) {
            // 🔥 修复：取消之前的搜索任务，避免资源泄露
            currentSearchJob?.cancel()

            currentSearchJob = handlerScope.launch {
                try {
                    // 🔥 修复：在延迟前检查协程状态
                    currentCoroutineContext().ensureActive()
                    delay(GlobalPerformanceConfig.SEARCH.DEBOUNCE_MS)

                    // 🔥 修复：延迟后再次检查协程状态
                    currentCoroutineContext().ensureActive()

                    if (effect.query.isBlank()) {
                        handleLoadHistory()
                    } else {
                        val searchPagingDataFlow: Flow<PagingData<ChatSession>> = Pager(
                            config = pagingConfig,
                            pagingSourceFactory = {
                                pagingSourceFactory.create(searchQuery = effect.query)
                            },
                        ).flow

                        // 🔥 修复：确保协程仍然活跃才发送Intent
                        currentCoroutineContext().ensureActive()

                        // 🔥 分发新的、专用的搜索结果意图
                        sendIntent(HistoryContract.Intent.SearchResultsLoadedResult(searchPagingDataFlow))

                        Timber.d(TAG, "搜索 Paging3 数据流创建成功: query=${effect.query}")
                    }
                } catch (e: CancellationException) {
                    // 🔥 修复：正确处理协程取消
                    Timber.d(TAG, "搜索任务被取消: query=${effect.query}")
                    throw e // 重新抛出取消异常
                } catch (e: Exception) {
                    Timber.e(e, "搜索处理失败: query=${effect.query}")
                    sendIntent(
                        HistoryContract.Intent.OperationFailedResult(
                            HistoryContract.ErrorState(
                                message = UiText.DynamicString("搜索失败"),
                                type = HistoryContract.ErrorType.SEARCH,
                                retryAction = { handlePerformSearch(effect) },
                            ),
                        ),
                    )
                }
            }
        }

        /**
         * 处理刷新历史记录 - 🔥 修复：真正刷新Paging3数据源
         */
        private fun handleRefreshHistory() {
            Timber.d(TAG, "🔄 [handleRefreshHistory] 开始处理历史记录刷新请求")
            handlerScope.launch {
                try {
                    // 🔥 修复：在操作前检查协程状态
                    currentCoroutineContext().ensureActive()

                    Timber.d(TAG, "🔄 [handleRefreshHistory] 协程状态检查通过，开始创建Paging数据流")

                    // 创建新的 Paging3 数据流来触发刷新
                    val refreshedPagingDataFlow: Flow<PagingData<ChatSession>> = Pager(
                        config = pagingConfig,
                        pagingSourceFactory = {
                            Timber.d(TAG, "🔄 [handleRefreshHistory] 创建新的PagingSource实例")
                            pagingSourceFactory.create(searchQuery = "")
                        },
                    ).flow

                    // 🔥 修复：确保协程仍然活跃才发送Intent
                    currentCoroutineContext().ensureActive()

                    Timber.d(
                        TAG,
                        "🔄 [handleRefreshHistory] Paging数据流创建成功，准备发送HistoryLoadedResult Intent",
                    )
                    sendIntent(HistoryContract.Intent.HistoryLoadedResult(refreshedPagingDataFlow))

                    Timber.d(TAG, "✅ [handleRefreshHistory] 历史记录刷新完成")
                } catch (e: CancellationException) {
                    // 🔥 修复：正确处理协程取消
                    Timber.d(TAG, "❌ [handleRefreshHistory] 历史记录刷新被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "❌ [handleRefreshHistory] 刷新历史记录失败")
                    sendIntent(
                        HistoryContract.Intent.OperationFailedResult(
                            HistoryContract.ErrorState(
                                message = UiText.DynamicString("刷新失败"),
                                type = HistoryContract.ErrorType.STORAGE,
                                retryAction = { handleRefreshHistory() },
                            ),
                        ),
                    )
                }
            }
        }

        /**
         * 处理混合搜索
         */
        private fun handleHybridSearch(effect: HistoryContract.Effect.StartHybridSearch) {
            // 使用 PerformSearch 的搜索逻辑
            handlePerformSearch(
                HistoryContract.Effect.PerformSearch(
                    query = effect.query,
                    mode = HistoryContract.SearchMode.HYBRID,
                ),
            )
        }

        /**
         * 处理导航到会话 - 简单实现
         */
        private fun handleNavigateToSession(effect: HistoryContract.Effect.NavigateToSession) {
            sendIntent(HistoryContract.Intent.NavigateToSession(effect.sessionId))
        }

        /**
         * 处理显示消息 - 简单实现
         */
        private fun handleShowMessage(effect: HistoryContract.Effect.ShowMessage) {
            sendIntent(HistoryContract.Intent.ShowMessage(effect.message))
        }

        /**
         * 处理生成标题 - 简单实现
         */
        private fun handleGenerateTitle(effect: HistoryContract.Effect.GenerateSessionTitle) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 实现实际的标题生成逻辑
                    sendIntent(
                        HistoryContract.Intent.TitleGeneratedResult(
                            sessionId = effect.sessionId,
                            title = "新的对话",
                            source = "default",
                        ),
                    )
                } catch (e: CancellationException) {
                    Timber.d(TAG, "标题生成被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "标题生成失败")
                }
            }
        }

        /**
         * 处理生成嵌入 - 简单实现
         */
        private fun handleGenerateEmbedding(effect: HistoryContract.Effect.GenerateMessageEmbedding) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 实现实际的嵌入生成逻辑
                    sendIntent(
                        HistoryContract.Intent.EmbeddingGeneratedResult(
                            messageId = effect.messageId,
                            success = true,
                        ),
                    )
                } catch (e: CancellationException) {
                    Timber.d(TAG, "嵌入生成被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "嵌入生成失败")
                }
            }
        }

        /**
         * 处理检索上下文 - 简单实现
         */
        private fun handleRetrieveContext(effect: HistoryContract.Effect.RetrieveRagContext) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 实现实际的上下文检索逻辑
                    sendIntent(HistoryContract.Intent.ContextRetrievedResult(emptyList()))
                } catch (e: CancellationException) {
                    Timber.d(TAG, "上下文检索被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "上下文检索失败")
                }
            }
        }

        /**
         * 处理注入上下文 - 简单实现
         */
        private fun handleInjectContext(effect: HistoryContract.Effect.InjectSelectedContext) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 实现实际的上下文注入逻辑
                    sendIntent(HistoryContract.Intent.ContextSelectionFinalizedResult(""))
                } catch (e: CancellationException) {
                    Timber.d(TAG, "上下文注入被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "上下文注入失败")
                }
            }
        }

        /**
         * 处理刷新嵌入统计 - 简单实现
         */
        private fun handleRefreshEmbeddingStats() {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 实现实际的统计刷新逻辑
                } catch (e: CancellationException) {
                    Timber.d(TAG, "统计刷新被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "统计刷新失败")
                }
            }
        }

        /**
         * 处理处理待处理嵌入 - 简单实现
         */
        private fun handleProcessPendingEmbeddings(effect: HistoryContract.Effect.ProcessPendingEmbeddings) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 实现实际的批量嵌入处理逻辑
                } catch (e: CancellationException) {
                    Timber.d(TAG, "批量嵌入处理被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "批量嵌入处理失败")
                }
            }
        }

        // === 会话管理处理方法 ===

        /**
         * 处理删除对话
         */
        private fun handleDeleteConversation(effect: HistoryContract.Effect.DeleteConversation) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 调用 Repository 删除对话
                    Timber.d(TAG, "删除对话: ${effect.conversationId}")
                    // 删除成功后刷新历史记录
                    intentSender(HistoryContract.Intent.RefreshHistoryData)
                } catch (e: CancellationException) {
                    Timber.d(TAG, "删除对话被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "删除对话失败")
                    intentSender(HistoryContract.Intent.ShowMessage(UiText.DynamicString("删除对话失败")))
                }
            }
        }

        /**
         * 处理重命名对话
         */
        private fun handleRenameConversation(effect: HistoryContract.Effect.RenameConversation) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 调用 Repository 重命名对话
                    Timber.d(TAG, "重命名对话: ${effect.conversationId} -> ${effect.newTitle}")
                    // 重命名成功后刷新历史记录
                    intentSender(HistoryContract.Intent.RefreshHistoryData)
                } catch (e: CancellationException) {
                    Timber.d(TAG, "重命名对话被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "重命名对话失败")
                    intentSender(HistoryContract.Intent.ShowMessage(UiText.DynamicString("重命名对话失败")))
                }
            }
        }

        /**
         * 处理导出对话
         */
        private fun handleExportConversation(effect: HistoryContract.Effect.ExportConversation) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 实现对话导出逻辑
                    Timber.d(TAG, "导出对话: ${effect.conversationId}, 格式: ${effect.format}")
                    intentSender(HistoryContract.Intent.ShowMessage(UiText.DynamicString("导出功能开发中")))
                } catch (e: CancellationException) {
                    Timber.d(TAG, "导出对话被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "导出对话失败")
                    intentSender(HistoryContract.Intent.ShowMessage(UiText.DynamicString("导出对话失败")))
                }
            }
        }

        /**
         * 处理批量删除对话
         */
        private fun handleBatchDeleteConversations(effect: HistoryContract.Effect.BatchDeleteConversations) {
            handlerScope.launch {
                try {
                    currentCoroutineContext().ensureActive()
                    // TODO: 调用 Repository 批量删除对话
                    Timber.d(TAG, "批量删除对话: ${effect.conversationIds}")
                    // 删除成功后刷新历史记录
                    intentSender(HistoryContract.Intent.RefreshHistoryData)
                } catch (e: CancellationException) {
                    Timber.d(TAG, "批量删除对话被取消")
                    throw e
                } catch (e: Exception) {
                    Timber.e(e, "批量删除对话失败")
                    intentSender(HistoryContract.Intent.ShowMessage(UiText.DynamicString("批量删除对话失败")))
                }
            }
        }
    }
