package com.example.gymbro.core.logging

import android.annotation.SuppressLint
import android.util.Log
import com.example.gymbro.core.util.LogTagOptimizer
import com.example.gymbro.core.util.OptimizedLogger
import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * 🏷️ GymBro 统一日志标签管理系统
 *
 * 集中管理所有模块的日志标签，确保标签格式统一和易于维护
 */
object GymBroLogTags {

    // ==================== Core-Network 模块标签 ====================

    /**
     * Core-Network 模块日志标签
     * 格式：CNET-{子包名}-{功能}
     */
    object CoreNetwork {
        // 核心服务层
        const val SERVICE_UNIFIED_AI = "CNET-SERVICE-UnifiedAi"

        // 处理器层
        const val PROCESSOR_STREAM = "CNET-PROCESSOR-Stream"
        const val PROCESSOR_CONTENT = "CNET-PROCESSOR-Content"
        const val PROCESSOR_SANITIZER = "CNET-PROCESSOR-Sanitizer"

        // 输出层
        const val OUTPUT_DIRECT = "CNET-OUTPUT-Direct"

        // 网络层
        const val REST_CLIENT = "CNET-REST-Client"
        const val INTERCEPTOR_AUTH = "CNET-INTERCEPTOR-Auth"
        const val INTERCEPTOR_NETWORK = "CNET-INTERCEPTOR-Network"
        const val INTERCEPTOR_RETRY = "CNET-INTERCEPTOR-Retry"
        const val INTERCEPTOR_LOGGING = "CNET-INTERCEPTOR-Logging"

        // 配置层
        const val CONFIG_MANAGER = "CNET-CONFIG-Manager"

        // 监控层
        const val MONITOR_NETWORK = "CNET-MONITOR-Network"
        const val MONITOR_PERFORMANCE = "CNET-MONITOR-Performance"

        // 安全层
        const val SECURITY_PII = "CNET-SECURITY-Pii"

        // 状态层
        const val STATE_NETWORK = "CNET-STATE-Network"

        // 🔥 【IPv4优先策略】DNS解析器
        const val DNS_RESOLVER = "CNET-DNS-Resolver"

        // 依赖注入层
        const val DI_MODULE = "CNET-DI-Module"

        // 特殊调试标签（仅在需要时使用）
        const val TOKEN_RECV = "CNET-TOKEN-RECV"
        const val TOKEN_OUT = "CNET-TOKEN-OUT"
        const val TOKEN_STATS = "CNET-TOKEN-STATS"
        const val TOKEN_BATCH = "CNET-TOKEN-BATCH"
        const val TOKEN_ERROR = "CNET-TOKEN-ERROR"
    }

    // ==================== ThinkingBox 模块标签 ====================

    /**
     * ThinkingBox 模块日志标签
     * 格式：TB-{子包名}-{功能}
     */
    object ThinkingBox {
        // 核心组件
        const val PARSER = "TB-PARSER-Stream"
        const val MAPPER = "TB-MAPPER-Domain"
        const val REDUCER = "TB-REDUCER-Segment"
        const val VIEWMODEL = "TB-VM-Main"
        const val EFFECT = "TB-EFFECT-Handler"

        // 数据流
        const val STREAM = "TB-STREAM-Token"
        const val STATE = "TB-STATE-Manager"
        const val TOKEN = "TB-TOKEN-Flow"
        const val DATAFLOW = "TB-DATAFLOW-Trace" // 🔥 【新增】数据流追踪专用标签

        // UI组件
        const val RENDERER = "TB-RENDERER-UI"
        const val ADAPTER = "TB-ADAPTER-UI"

        // 调试追踪
        const val E2E_TRACE = "TB-E2E-TRACE"
        const val ADAPTER_TRACE = "TB-ADAPTER-TRACE"
        const val INIT = "TB-INIT-Setup"
        const val ERROR = "TB-ERROR-Handler"
        const val WARN = "TB-WARN-Handler"
        const val DEBUG = "TB-DEBUG-Info"

        // 业务逻辑
        const val SEGMENT = "TB-SEGMENT-Queue"
        const val FINAL = "TB-FINAL-Content"
        const val BREAKPOINT = "TB-BREAKPOINT-Phase"
    }

    // ==================== 其他模块标签 ====================

    /**
     * Core 模块日志标签
     */
    object Core {
        const val LOG_MANAGER = "LOG-MANAGER"
        const val ERROR_HANDLER = "CORE-ERROR"
        const val SERVICE_MANAGER = "CORE-SERVICE"
    }

    /**
     * Data 模块日志标签
     * 格式：DATA-{子包名}-{功能}
     */
    object Data {
        // Repository 层
        const val REPOSITORY = "DATA-REPO-Main"
        const val REPOSITORY_AI = "DATA-REPO-AI"
        const val REPOSITORY_CHAT = "DATA-REPO-Chat"
        const val REPOSITORY_USER = "DATA-REPO-User"
        const val REPOSITORY_WORKOUT = "DATA-REPO-Workout"

        // Service 层
        const val SERVICE = "DATA-SERVICE-Main"
        const val SERVICE_AI = "DATA-SERVICE-AI"
        const val SERVICE_NETWORK = "DATA-SERVICE-Network"

        // Mapper 层
        const val MAPPER = "DATA-MAPPER-Main"
        const val MAPPER_AI = "DATA-MAPPER-AI"
        const val MAPPER_ENTITY = "DATA-MAPPER-Entity"

        // Database 层
        const val DATABASE = "DATA-DB-Main"
        const val DATABASE_DAO = "DATA-DB-DAO"
        const val DATABASE_ENTITY = "DATA-DB-Entity"

        // Cache 层
        const val CACHE = "DATA-CACHE-Main"
        const val CACHE_MEMORY = "DATA-CACHE-Memory"
        const val CACHE_DISK = "DATA-CACHE-Disk"

        // 错误处理
        const val ERROR = "DATA-ERROR-Handler"
        const val VALIDATION = "DATA-VALIDATION-Check"
    }

    /**
     * Coach 模块日志标签
     * 格式：COACH-{子包名}-{功能}
     */
    object Coach {
        // ViewModel 层
        const val VIEWMODEL = "COACH-VM-Main"
        const val VIEWMODEL_AI = "COACH-VM-AI"

        // Reducer 层
        const val REDUCER = "COACH-REDUCER-Main"
        const val REDUCER_MESSAGING = "COACH-REDUCER-Messaging"
        const val REDUCER_SESSION = "COACH-REDUCER-Session"

        // Effect Handler 层
        const val EFFECT = "COACH-EFFECT-Handler"
        const val EFFECT_AI = "COACH-EFFECT-AI"
        const val EFFECT_THINKING = "COACH-EFFECT-Thinking"

        // Service 层
        const val SERVICE = "COACH-SERVICE-Main"
        const val SERVICE_COMPLETION = "COACH-SERVICE-Completion"

        // UI 层
        const val UI = "COACH-UI-Main"
        const val UI_CHAT = "COACH-UI-Chat"
        const val UI_INPUT = "COACH-UI-Input"

        // 业务逻辑
        const val MESSAGING = "COACH-MESSAGING-Handler"
        const val SESSION = "COACH-SESSION-Manager"
        const val HISTORY = "COACH-HISTORY-Manager"

        // 集成
        const val INTEGRATION = "COACH-INTEGRATION-TB"
        const val CALLBACK = "COACH-CALLBACK-Listener"

        // 错误处理
        const val ERROR = "COACH-ERROR-Handler"
        const val VALIDATION = "COACH-VALIDATION-Check"
    }

    /**
     * 获取所有Core-Network标签列表
     */
    fun getAllCoreNetworkTags(): List<String> {
        return listOf(
            CoreNetwork.SERVICE_UNIFIED_AI,
            CoreNetwork.PROCESSOR_STREAM,
            CoreNetwork.PROCESSOR_CONTENT,
            CoreNetwork.PROCESSOR_SANITIZER,
            CoreNetwork.OUTPUT_DIRECT,
            CoreNetwork.REST_CLIENT,
            CoreNetwork.INTERCEPTOR_AUTH,
            CoreNetwork.INTERCEPTOR_NETWORK,
            CoreNetwork.INTERCEPTOR_RETRY,
            CoreNetwork.INTERCEPTOR_LOGGING,
            CoreNetwork.CONFIG_MANAGER,
            CoreNetwork.MONITOR_NETWORK,
            CoreNetwork.MONITOR_PERFORMANCE,
            CoreNetwork.SECURITY_PII,
            CoreNetwork.STATE_NETWORK,
            CoreNetwork.DNS_RESOLVER,
            CoreNetwork.DI_MODULE,
        )
    }
}

/**
 * 🔥 【重构】增强的 Timber 日志管理器
 *
 * 统一管理Timber日志系统的初始化、配置和控制。
 * 集成模块级别的日志控制和性能优化。
 */
@Singleton
class TimberManager
    @Inject
    constructor(
        private val loggingConfig: LoggingConfig,
    ) {
        /**
         * 🔥 【修复】初始化日志系统 - 使用专用ThinkingBoxLogTree
         *
         * 移除ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree作为主要日志处理器
         * 🎯 新增：集成UUID压缩和日志优化功能
         *
         * @param isDebug 是否为调试模式
         * @param environment 环境类型
         * @param enableThinkingBoxMode 是否启用ThinkingBox专用模式
         */
        fun initialize(
            isDebug: Boolean,
            environment: LoggingConfig.Environment = LoggingConfig.Environment.DEVELOPMENT,
            enableThinkingBoxMode: Boolean = true, // 🔥 【临时调试】启用ThinkingBox专用日志
        ) {
            // 设置环境
            loggingConfig.setEnvironment(environment)

            // 🔄 重构：启用日志优化功能
            OptimizedLogger.setOptimizationEnabled(true)
            if (isDebug) {
                OptimizedLogger.setDetailedStatsEnabled(true)
            }

            // 🔄 重构：设置全局标签过滤器，应用标签优化
            setGlobalTagFilter { tag ->
                LogTagOptimizer.optimizeTag(tag ?: "GymBro")
            }

            // 清除所有已有的Tree
            Timber.uprootAll()

            // 根据环境和模式安装适当的Tree
            when {
                isDebug && enableThinkingBoxMode -> {
                    // 🔥 【修复】ThinkingBox专用模式：使用专用的ThinkingBoxLogTree作为主要日志处理器
                    // 移除ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree
                    loadThinkingBoxLogTree()

                    // 🔥 【重构清理】移除WorkoutLogTree动态加载，统一由ModuleAwareTree处理
                    // WorkoutLogTree保留为工具类，避免重复Tree处理同一日志

                    // 🔥 【新增】动态加载NetworkLogTree
                    loadNetworkLogTree()

                    OptimizedLogger.i(
                        "LOG-MANAGER",
                        "🔥 ThinkingBox专用日志系统已启动 - 使用专用ThinkingBoxLogTree + 统一模块路由 + 网络支持 + CompactId统一生成",
                    )
                }

                isDebug -> {
                    // 开发环境：使用模块感知的Tree + 专用模块LogTree
                    Timber.plant(ModuleAwareTree(loggingConfig))

                    // 🔥 【新增】动态加载CoachLogTree
                    loadCoachLogTree()

                    // 🔥 【重构清理】移除WorkoutLogTree动态加载，统一由ModuleAwareTree处理
                    // 避免同一日志被多个Tree重复处理

                    // 🔥 【新增】动态加载NetworkLogTree
                    loadNetworkLogTree()

                    OptimizedLogger.i(
                        "LOG-MANAGER",
                        "🔥 开发环境日志系统已启动 - 模块感知模式 + Coach专用Tree + 网络支持 + CompactId统一生成",
                    )
                }

                environment == LoggingConfig.Environment.PRODUCTION -> {
                    // 生产环境：只记录错误，但仍启用UUID压缩以减少日志大小
                    Timber.plant(ReleaseTree())

                    // 🔥 【关键修复】生产环境也需要加载NetworkLogTree以处理CNET-ERROR
                    loadNetworkLogTree()

                    OptimizedLogger.i(
                        "LOG-MANAGER",
                        "🔥 生产环境日志系统已启动 - 仅错误模式 + 网络错误支持 + CompactId统一生成",
                    )
                }

                else -> {
                    // 测试环境：适度日志
                    Timber.plant(ModuleAwareTree(loggingConfig))

                    // 🔥 【关键修复】测试环境也需要加载NetworkLogTree以处理CNET标签
                    loadNetworkLogTree()

                    OptimizedLogger.i(
                        "LOG-MANAGER",
                        "🔥 测试环境日志系统已启动 - 适度日志模式 + 网络支持 + CompactId统一生成",
                    )
                }
            }

            // 启用协程调试支持（仅在调试模式下）
            if (isDebug) {
                System.setProperty("kotlinx.coroutines.debug", "on")
            }

            // 🎯 输出优化器统计信息
            if (isDebug) {
                OptimizedLogger.d("LOG-MANAGER", OptimizedLogger.getOptimizerStats())
            }
        }

        /**
         * 🔥 【新增】专用于应用层的ThinkingBox模式初始化
         *
         * 符合Clean Architecture原则，应用层通过此方法启用ThinkingBox功能
         * 而无需直接依赖features模块
         */
        fun initializeWithThinkingBoxSupport(isDebug: Boolean) {
            initialize(
                isDebug = isDebug,
                environment = if (isDebug) LoggingConfig.Environment.DEVELOPMENT else LoggingConfig.Environment.PRODUCTION,
                enableThinkingBoxMode = isDebug,
            )

            if (isDebug) {
                // 配置ThinkingBox专用的模块设置
                enableThinkingBoxDebugLogs()
                // 🔥 移除噪音日志：ThinkingBox专用模式启动信息
            }
        }

        /**
         * 启用ThinkingBox调试日志
         */
        fun enableThinkingBoxDebugLogs() {
            loggingConfig.updateModuleConfig(
                LoggingConfig.MODULE_THINKING_BOX,
                LoggingConfig.ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf(
                        // 🔥 【核心流程标签】
                        "TB-CORE", "TB-STREAM", "TB-STATE",

                        // 🔥 【架构组件标签】
                        "TB-MAPPER", "TB-REDUCER", "TB-VM", "TB-EFFECT",

                        // 🔥 【数据处理标签】
                        "TB-TOKEN", "TB-XML", "TB-BUFFER",

                        // 🔥 【业务逻辑标签】
                        "TB-SEGMENT", "TB-BREAKPOINT", "TB-FINAL",

                        // 🔥 【UI渲染标签】
                        "TB-UI", "TB-RENDER",

                        // 🔥 【诊断调试标签】
                        "TB-INIT", "TB-ERROR", "TB-WARN", "TB-DEBUG",

                        // 🔥 【兼容旧标签 - 逐步迁移】
                        "TB-VIEWMODEL", "TB-PARSER", "TB-DOMAIN-MAPPER",
                        "TB-XML-SCANNER", "TB-GUARDRAIL",
                        "TB-TOKEN-FLOW", "TB-RAW-COLLECTOR", "TB-RAW-PROCESSOR",
                        "TB-LIFECYCLE", "TB-AUTO-SCROLL", "TB-FINAL-RENDERER",
                        "TB-XML-INPUT", "TB-XML-BUFFER", "TB-XML-PARSE", "TB-XML-OUTPUT",
                        "TB-FIX", "TB-TAG-PHASE", "TB-TAG-FINAL", "TB-TAG-CLOSE", "TB-PROCESSED",
                        "TB-XML-PARTIAL",
                    ),
                    sampleRate = 1,
                ),
            )

            // 🔥 【新增】启用 core-network 模块的日志
            enableCoreNetworkDebugLogs()

            Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 调试日志已启用 - 包含Token流修复验证标签")
        }

        /**
         * 🔥 【新增】启用 core-network 模块调试日志
         */
        fun enableCoreNetworkDebugLogs() {
            loggingConfig.updateModuleConfig(
                LoggingConfig.MODULE_CORE_NETWORK,
                LoggingConfig.ModuleLogConfig(
                    enabled = true,
                    minLevel = Log.DEBUG,
                    tags = setOf(
                        // 🔥 【核心网络标签】
                        "CNET-ERROR", "CNET-STREAM", "CNET-CHECK", "CNET-SSE",
                        "CNET-MONITOR", "CNET-RETRY", "CNET-LIFECYCLE",
                        "CNET-SECURITY", "CNET-PERF", "CNET-GENERAL",
                        // 🔥 【Token日志采集标签】
                        "CNET-TOKEN-RECV", "CNET-TOKEN-OUT", "CNET-TOKEN-STATS",
                        "CNET-TOKEN-BATCH", "CNET-TOKEN-ERROR",
                        // 🔥 【Token路由标签】
                        // 旧架构组件已删除：CNT-TOKEN-ROUTER, ConversationScope
                        // 🔥 【消息流调试标签】
                        "CNT-MESSAGE-FLOW",
                        // 🔥 【AI响应管道调试标签】
                        "HTTP-RAW-RESPONSE", "HTTP-RAW-XML", "JSON-PARSE-INPUT",
                        "JSON-PARSE-CLEANED", "JSON-PARSE-CRITICAL", "JSON-PARSE-TEST",
                        "JSON-FIELD-EXTRACT", "JSON-FIELD-ALL", "JSON-DEBUG",
                        "XML-REASSEMBLY-RESULT", "FINAL-CONTENT",
                        "ASC-RAW-TOKENS", "CNT-RAW-TOKENS",
                    ),
                    sampleRate = 1,
                ),
            )
            Timber.tag("LOG-MANAGER").i("🔥 core-network 调试日志已启用 - 包含CNET标签和Token日志采集")
        }

        /**
         * 🏷️ 【统一日志标签】测试Core-Network日志系统
         *
         * 使用统一的日志标签管理系统测试core-network日志功能
         */
        fun testCoreNetworkLogging() {
            // 测试新的统一标签系统
            Timber.tag(GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI).i("🧪 [测试] 统一AI响应服务日志")
            Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).d("🧪 [测试] 流式处理器日志")
            Timber.tag(GymBroLogTags.CoreNetwork.OUTPUT_DIRECT).i("🧪 [测试] 直接输出通道日志")
            Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i("🧪 [测试] REST客户端日志")
            Timber.tag(GymBroLogTags.CoreNetwork.INTERCEPTOR_AUTH).d("🧪 [测试] 认证拦截器日志")
            Timber.tag(GymBroLogTags.CoreNetwork.INTERCEPTOR_RETRY).w("🧪 [测试] 重试拦截器日志")
            Timber.tag(GymBroLogTags.CoreNetwork.CONFIG_MANAGER).d("🧪 [测试] 配置管理器日志")
            Timber.tag(GymBroLogTags.CoreNetwork.MONITOR_PERFORMANCE).i("🧪 [测试] 性能监控日志")
            Timber.tag(GymBroLogTags.CoreNetwork.SECURITY_PII).w("🧪 [测试] PII安全处理日志")
            Timber.tag(GymBroLogTags.CoreNetwork.DI_MODULE).d("🧪 [测试] 依赖注入模块日志")

            // 测试敏感信息过滤
            Timber.tag(GymBroLogTags.CoreNetwork.SECURITY_PII).w("🧪 [测试] 敏感信息过滤: api_key=*****, token=*****")

            Timber.tag(GymBroLogTags.Core.LOG_MANAGER).i("🧪 [测试完成] Core-Network统一日志标签系统测试完成")
        }

        /**
         * 运行时切换环境
         */

        /**
         * 获取当前环境
         */
        fun getCurrentEnvironment(): LoggingConfig.Environment = loggingConfig.getCurrentEnvironment()

        /**
         * 🔥 【修复】动态加载ThinkingBoxLogTree作为主要日志处理器
         *
         * 替换ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree
         * 🔥 【Token流修复】确保ThinkingBox相关日志能够正确输出，包含所有修复验证功能
         */
        private fun loadThinkingBoxLogTree() {
            try {
                // 动态加载ThinkingBoxLogTree类
                val thinkingBoxLogTreeClass = Class.forName(
                    "com.example.gymbro.features.thinkingbox.logging.ThinkingBoxLogTree",
                )

                // 🔥 【修复】ThinkingBoxLogTree使用无参构造函数（继承自Timber.DebugTree）
                val constructor = thinkingBoxLogTreeClass.getConstructor()
                val thinkingBoxLogTree = constructor.newInstance() as Timber.Tree

                // 植入ThinkingBoxLogTree作为主要日志处理器
                Timber.plant(thinkingBoxLogTree)

                Timber.tag("LOG-MANAGER").i("🔥 ThinkingBoxLogTree 已加载作为主要日志处理器 - 包含Token流修复调试功能")
            } catch (e: Exception) {
                Timber.tag("LOG-MANAGER").w("ThinkingBoxLogTree 加载失败，回退到基础日志: ${e.message}")
                // 回退到基础日志树
                Timber.plant(Timber.DebugTree())
            }
        }

        /**
         * 🔥 【新增】动态加载CoachLogTree
         *
         * 参考ThinkingBox模块标准，动态加载Coach模块的专用日志树
         * 🔥 【架构集成】使用COA前缀标签，统一Coach模块日志管理
         */
        private fun loadCoachLogTree() {
            try {
                // 动态加载CoachLogTree类
                val coachLogTreeClass = Class.forName(
                    "com.example.gymbro.features.coach.logging.CoachLogTree",
                )

                // 🔥 【架构集成】使用无参构造函数（继承自Timber.DebugTree）
                val constructor = coachLogTreeClass.getConstructor()
                val coachLogTree = constructor.newInstance() as Timber.Tree

                // 植入CoachLogTree
                Timber.plant(coachLogTree)

                Timber.tag("LOG-MANAGER").i("🔥 CoachLogTree 已加载 - 支持COA-*标签和AI流聚合")
            } catch (e: Exception) {
                Timber.tag("LOG-MANAGER").w("CoachLogTree 加载失败: ${e.message}")
                // Coach日志树加载失败不影响其他功能，继续运行
            }
        }

        /**
         * 🔥 【新增】动态加载NetworkLogTree
         *
         * 遵循现有的模块化日志架构，动态加载网络模块的专用日志树
         * 🔥 【架构集成】使用LoggingConfig统一管理，与其他模块保持一致
         */
        private fun loadNetworkLogTree() {
            try {
                // 动态加载NetworkLogTree类
                val networkLogTreeClass = Class.forName(
                    "com.example.gymbro.core.network.logging.NetworkLogTree",
                )

                // 🔥 【架构集成】使用LoggingConfig作为构造参数，与WorkoutLogTree保持一致
                val constructor = networkLogTreeClass.getConstructor(LoggingConfig::class.java)
                val networkLogTree = constructor.newInstance(loggingConfig) as Timber.Tree

                // 植入NetworkLogTree
                Timber.plant(networkLogTree)

                Timber.tag(
                    "LOG-MANAGER",
                ).i("🔥 NetworkLogTree 已加载 - 环境: ${loggingConfig.getCurrentEnvironment()}")
            } catch (e: Exception) {
                Timber.tag("LOG-MANAGER").w("NetworkLogTree 加载失败: ${e.message}")
                // 网络日志树加载失败不影响其他功能，继续运行
            }
        }

        /**
         * 设置全局日志标签过滤器
         *
         * @param tagFilter 标签过滤函数
         */
        fun setGlobalTagFilter(tagFilter: (String?) -> String) {
            globalTagFilter = tagFilter
        }

        companion object {
            private const val LOG_STACK_INDICATOR = "LogStack"

            // 全局标签过滤器
            @Volatile
            private var globalTagFilter: ((String?) -> String)? = null

            /**
             * 应用全局标签过滤器
             */
            internal fun applyTagFilter(
                tag: String?,
            ): String = globalTagFilter?.invoke(tag) ?: tag ?: "GymBro"

            /**
             * 基于当前类名生成的标签
             */
            @SuppressLint("DefaultLocale")
            fun createTag(): String {
                val stackTrace = Thread.currentThread().stackTrace
                for (element in stackTrace) {
                    val className = element.className
                    if (!className.contains("TimberManager") &&
                        !className.contains("Thread") &&
                        !className.contains("VMStack") &&
                        !className.contains("Method") &&
                        !className.startsWith("java.") &&
                        !className.startsWith("android.") &&
                        !className.startsWith("dalvik.")
                    ) {
                        val fullClassName = className.substringAfterLast('.')
                        return fullClassName.substring(fullClassName.lastIndexOf('.') + 1)
                    }
                }
                return "Unknown"
            }
        }
    }
