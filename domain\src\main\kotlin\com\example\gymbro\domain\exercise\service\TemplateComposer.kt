package com.example.gymbro.domain.exercise.service

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.model.ExerciseSelection
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import com.example.gymbro.shared.models.exercise.DifficultyLevel
import com.example.gymbro.shared.models.exercise.ExerciseDto
import com.example.gymbro.shared.models.exercise.ExerciseSource
import com.example.gymbro.shared.models.workout.TemplateExercise
import com.example.gymbro.shared.models.workout.TemplateMetadata
import com.example.gymbro.shared.models.workout.WorkoutTemplate
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Template组合器服务
 * 负责从动作库组合训练模板的核心业务逻辑
 *
 * 统一JSON数据中心架构的核心组件：
 * 动作库 + 用户输入 → Template（核心训练单元）
 */
@Singleton
class TemplateComposer
    @Inject
    constructor(
        private val exerciseRepository: ExerciseRepository,
        private val resourceProvider: ResourceProvider,
    ) {

        /**
         * 从动作库和用户输入创建训练模板
         *
         * @param userId 用户ID
         * @param templateName 模板名称
         * @param exerciseSelections 用户选择的动作配置
         * @return 组合后的训练模板
         */
        suspend fun composeTemplate(
            userId: String,
            templateName: String,
            exerciseSelections: List<ExerciseSelection>,
        ): ModernResult<WorkoutTemplate> {
            return try {
                // 验证输入参数
                if (templateName.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError.fromGlobalErrorType(
                            operationName = "composeTemplate",
                            errorType = GlobalErrorType.Validation.Required,
                            uiMessage = UiText.DynamicString("模板名称不能为空"),
                        ),
                    )
                }

                if (exerciseSelections.isEmpty()) {
                    return ModernResult.Error(
                        ModernDataError.fromGlobalErrorType(
                            operationName = "composeTemplate",
                            errorType = GlobalErrorType.Validation.Required,
                            uiMessage = UiText.DynamicString("至少需要选择一个动作"),
                        ),
                    )
                }

                val templateExercises = mutableListOf<TemplateExercise>()

                // 处理每个用户选择的动作
                for (selection in exerciseSelections) {
                    val libraryExercise = getExerciseFromLibrary(selection.exerciseId)
                        ?: return ModernResult.Error(
                            ModernDataError.fromGlobalErrorType(
                                operationName = "composeTemplate",
                                errorType = GlobalErrorType.Data.NotFound,
                                uiMessage = UiText.DynamicString("动作不存在: ${selection.exerciseId}"),
                            ),
                        )

                    // 验证用户是否有权限使用该动作
                    if (!canUserAccessExercise(userId, libraryExercise)) {
                        return ModernResult.Error(
                            ModernDataError.fromGlobalErrorType(
                                operationName = "composeTemplate",
                                errorType = GlobalErrorType.Auth.Forbidden,
                                uiMessage = UiText.DynamicString("无权限使用动作"),
                            ),
                        )
                    }

                    // 将ExerciseDto转换为ExerciseSelection
                    val exerciseSelection = com.example.gymbro.shared.models.workout.ExerciseSelection(
                        exerciseId = selection.exerciseId,
                        exerciseName = libraryExercise.name,
                        imageUrl = libraryExercise.imageUrl,
                        videoUrl = libraryExercise.videoUrl,
                        targetSets = selection.customSets.map { set ->
                            com.example.gymbro.shared.models.workout.TargetSet(
                                setNumber = set.setNumber,
                                targetWeight = set.targetWeight,
                                targetReps = set.targetReps,
                                targetDuration = set.targetDuration,
                                rpe = set.rpe,
                            )
                        },
                        restTimeSeconds = selection.restTimeSeconds,
                        notes = selection.notes,
                    )

                    // 使用TemplateExercise.fromExerciseSelection工厂方法
                    val templateExercise = TemplateExercise.fromExerciseSelection(exerciseSelection)

                    templateExercises.add(templateExercise)
                }

                // 计算模板元数据
                val metadata = calculateTemplateMetadata(templateExercises)

                val workoutTemplate = WorkoutTemplate(
                    id = generateTemplateId(userId),
                    name = templateName,
                    description = generateTemplateDescription(templateExercises),
                    exercises = templateExercises,
                    metadata = metadata,
                    createdAt = System.currentTimeMillis(),
                )

                ModernResult.Success(workoutTemplate)
            } catch (e: Exception) {
                ModernResult.Error(
                    ModernDataError.fromGlobalErrorType(
                        operationName = "composeTemplate",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("创建训练模板失败: ${e.message}"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 验证用户是否可以使用特定动作
         */
        private fun canUserAccessExercise(userId: String, exercise: ExerciseDto): Boolean {
            return when (exercise.source) {
                ExerciseSource.OFFICIAL -> true // 官方动作所有人可用
                ExerciseSource.USER_CUSTOM -> exercise.userId == userId || exercise.isPublic // 自定义动作需要是创建者或公开
                ExerciseSource.COMMUNITY_SHARED -> true // 社区分享动作所有人可用
                ExerciseSource.AI_GENERATED -> true // AI生成动作所有人可用
            }
        }

        /**
         * 从动作库获取动作（支持官方+自定义）
         */
        private suspend fun getExerciseFromLibrary(exerciseId: String): ExerciseDto? {
            return try {
                // 使用Repository获取Exercise，然后转换为ExerciseDto
                val result = exerciseRepository.getExerciseById(exerciseId)
                when (result) {
                    is ModernResult.Success -> {
                        val exercise = result.data
                        // 将Exercise转换为ExerciseDto
                        ExerciseDto(
                            id = exercise.id,
                            name = exercise.name.asString(resourceProvider),
                            description = exercise.description.asString(resourceProvider),
                            imageUrl = exercise.imageUrl,
                            videoUrl = exercise.videoUrl,
                            muscleGroup = exercise.muscleGroup,
                            targetMuscles = exercise.targetMuscles,
                            equipment = exercise.equipment,
                            instructions = exercise.instructions.map { it.asString(resourceProvider) },
                            difficultyLevel = exercise.difficultyLevel,
                            source = ExerciseSource.USER_CUSTOM,
                            userId = exercise.userId,
                            isPublic = exercise.isCustom,
                        )
                    }

                    is ModernResult.Error -> null
                    else -> null
                }
            } catch (e: Exception) {
                null
            }
        }

        /**
         * 计算模板元数据
         */
        private suspend fun calculateTemplateMetadata(exercises: List<TemplateExercise>): TemplateMetadata {
            var totalEstimatedTime = 0
            val targetMuscleGroups = mutableSetOf<String>()
            val equipment = mutableSetOf<String>()
            var totalDifficulty = 0
            var exerciseCount = 0

            for (exercise in exercises) {
                // 计算预估时间：每组30秒 + 休息时间
                val setsTime = exercise.customSets.size * 30 // 每组30秒
                val restTime = exercise.customSets.size * exercise.restTimeSeconds
                totalEstimatedTime += setsTime + restTime

                // 获取动作详情计算元数据
                val libraryExercise = getExerciseFromLibrary(exercise.exerciseId)
                if (libraryExercise != null) {
                    // 收集目标肌群
                    targetMuscleGroups.add(libraryExercise.muscleGroup.name)
                    libraryExercise.targetMuscles.forEach {
                        targetMuscleGroups.add(it.name)
                    }

                    // 收集器材
                    libraryExercise.equipment.forEach {
                        equipment.add(it.name)
                    }

                    // 累计难度
                    totalDifficulty += libraryExercise.difficultyLevel
                    exerciseCount++
                }
            }

            // 计算平均难度
            val averageDifficulty = if (exerciseCount > 0) {
                totalDifficulty.toDouble() / exerciseCount
            } else {
                2.5 // 默认中等难度
            }

            // 转换难度等级
            val difficulty = when {
                averageDifficulty <= 1.5 -> DifficultyLevel.BEGINNER
                averageDifficulty <= 2.5 -> DifficultyLevel.NOVICE
                averageDifficulty <= 3.5 -> DifficultyLevel.INTERMEDIATE
                averageDifficulty <= 4.5 -> DifficultyLevel.ADVANCED
                else -> DifficultyLevel.EXPERT
            }

            return TemplateMetadata(
                estimatedDuration = (totalEstimatedTime / 60).coerceAtLeast(1), // 转换为分钟，至少1分钟
                targetMuscleGroups = targetMuscleGroups.toList(),
                difficultyLevel = difficulty,
                equipment = equipment.toList(),
                tags = generateTemplateTags(exercises, targetMuscleGroups.toList()),
            )
        }

        /**
         * 生成模板标签
         */
        private fun generateTemplateTags(
            exercises: List<TemplateExercise>,
            muscleGroups: List<String>,
        ): List<String> {
            val tags = mutableSetOf<String>()

            // 基于肌群生成标签
            if (muscleGroups.contains("胸部") || muscleGroups.contains("背部") || muscleGroups.contains("肩部")) {
                tags.add("上肢训练")
            }
            if (muscleGroups.contains("腿部") || muscleGroups.contains("臀部")) {
                tags.add("下肢训练")
            }
            if (muscleGroups.contains("核心") || muscleGroups.contains("腹肌")) {
                tags.add("核心训练")
            }

            // 基于动作数量生成标签
            when (exercises.size) {
                in 1..3 -> tags.add("简短训练")
                in 4..7 -> tags.add("标准训练")
                else -> tags.add("全面训练")
            }

            return tags.toList()
        }

        /**
         * 生成模板描述
         */
        private fun generateTemplateDescription(exercises: List<TemplateExercise>): String? {
            val exerciseNames = exercises.take(3).map { it.exerciseName }
            return if (exerciseNames.isNotEmpty()) {
                "包含${exercises.size}个动作：${exerciseNames.joinToString("、")}${if (exercises.size > 3) "等" else ""}"
            } else {
                null
            }
        }

        /**
         * 生成模板ID
         * 🎯 使用压缩ID提高可读性
         */
        private fun generateTemplateId(userId: String): String {
            return "tmpl_${userId}_${com.example.gymbro.core.util.CompactIdGenerator.generateId()}"
        }

        /**
         * 生成模板动作ID
         * 🎯 使用压缩ID提高可读性
         */
        private fun generateTemplateExerciseId(): String {
            return "tmpl_ex_${com.example.gymbro.core.util.CompactIdGenerator.generateId()}"
        }

        /**
         * 智能推荐相似动作
         * 当用户选择的动作不可用时，推荐替代动作
         */
        suspend fun recommendAlternativeExercises(
            unavailableExerciseId: String,
            userId: String,
            limit: Int = 3,
        ): ModernResult<List<ExerciseDto>> {
            return try {
                val unavailableExercise = getExerciseFromLibrary(unavailableExerciseId)
                    ?: return ModernResult.Error(
                        ModernDataError.fromGlobalErrorType(
                            operationName = "recommendAlternativeExercises",
                            errorType = GlobalErrorType.Data.NotFound,
                            uiMessage = UiText.DynamicString("原动作不存在"),
                        ),
                    )

                // 使用搜索方法获取相似动作（基于肌群）
                val searchResult = exerciseRepository.searchExercises(
                    query = unavailableExercise.muscleGroup.name,
                    limit = limit + 5, // 多获取一些，用于过滤
                )

                when (searchResult) {
                    is ModernResult.Success -> {
                        // 过滤出相似动作
                        val similarExercises = searchResult.data
                            .filter { exercise ->
                                // 不包含原动作
                                exercise.id != unavailableExerciseId &&
                                    // 相同主要肌群
                                    exercise.muscleGroup == unavailableExercise.muscleGroup &&
                                    // 用户有权限使用
                                    canUserAccessExercise(userId, exercise.toDto())
                            }
                            .take(limit)
                            .map { it.toDto() }

                        ModernResult.Success(similarExercises)
                    }

                    is ModernResult.Error -> {
                        ModernResult.Success(emptyList())
                    }

                    is ModernResult.Loading -> {
                        ModernResult.Success(emptyList())
                    }
                }
            } catch (e: Exception) {
                ModernResult.Error(
                    ModernDataError.fromGlobalErrorType(
                        operationName = "recommendAlternativeExercises",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("推荐替代动作失败: ${e.message}"),
                        cause = e,
                    ),
                )
            }
        }

        /**
         * 将Exercise转换为ExerciseDto的扩展函数
         */
        private fun Exercise.toDto(): ExerciseDto {
            return ExerciseDto(
                id = this.id,
                name = this.name.asString(resourceProvider),
                description = this.description.asString(resourceProvider),
                imageUrl = this.imageUrl,
                videoUrl = this.videoUrl,
                muscleGroup = this.muscleGroup,
                targetMuscles = this.targetMuscles,
                equipment = this.equipment,
                instructions = this.instructions.map { it.asString(resourceProvider) },
                difficultyLevel = this.difficultyLevel,
                source = ExerciseSource.USER_CUSTOM,
                userId = this.userId,
                isPublic = this.isCustom,
            )
        }
    }
