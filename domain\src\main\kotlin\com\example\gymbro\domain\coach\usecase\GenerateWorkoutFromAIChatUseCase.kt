package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.service.workout.AiInteractionService
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.usecase.workout.feedback.GetWorkoutContextUseCase
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first

/**
 * 基于AI对话生成训练模板用例
 *
 * 业务场景：基于AI对话生成训练模板
 *
 * 功能说明：
 * - 获取当前用户ID
 * - 获取用户训练上下文
 * - 调用AI服务生成训练模板
 *
 * @param aiInteractionService AI交互服务
 * @param getWorkoutContextUseCase 获取训练上下文用例
 * @param authRepository 认证仓库
 * @param dispatcher IO调度器
 * @param logger 日志记录器
 */
@Singleton
class GenerateWorkoutFromAIChatUseCase
    @Inject
    constructor(
        private val aiInteractionService: AiInteractionService,
        private val getWorkoutContextUseCase: GetWorkoutContextUseCase,
        private val authRepository: AuthRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<GenerateWorkoutFromAIChatUseCase.Params, WorkoutTemplate>(dispatcher, logger) {
        /**
         * 参数数据类
         *
         * @property sessionId 会话ID
         * @property userIntentSummary 用户意图摘要
         */
        data class Params(
            val sessionId: String,
            val userIntentSummary: String,
        )

        /**
         * 执行基于AI对话生成训练模板逻辑
         *
         * @param parameters 包含会话ID和用户意图摘要的参数
         * @return 生成的训练模板或错误信息
         */
        override suspend fun execute(parameters: Params): ModernResult<WorkoutTemplate> {
            logger.d("基于AI对话生成训练模板: sessionId=${parameters.sessionId}")

            return try {
                // 验证参数
                if (parameters.sessionId.isBlank()) {
                    logger.w("会话ID为空")
                    return ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "generateWorkoutFromAIChat",
                            message = UiText.DynamicString("会话ID不能为空"),
                            metadataMap = mapOf("sessionId" to parameters.sessionId),
                        ),
                    )
                }

                if (parameters.userIntentSummary.isBlank()) {
                    logger.w("用户意图摘要为空")
                    return ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "generateWorkoutFromAIChat",
                            message = UiText.DynamicString("用户意图摘要不能为空"),
                            metadataMap = mapOf("sessionId" to parameters.sessionId),
                        ),
                    )
                }

                // 1. 获取当前用户ID
                val userIdResult = authRepository.getCurrentUserId().first()
                if (userIdResult !is ModernResult.Success || userIdResult.data.isNullOrBlank()) {
                    logger.w("用户未登录或获取用户ID失败")
                    return ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "generateWorkoutFromAIChat",
                            message = UiText.DynamicString("用户未登录"),
                            metadataMap = mapOf<String, Any>("sessionId" to parameters.sessionId),
                            recoverable = true,
                        ),
                    )
                }

                val userId = userIdResult.data!!
                logger.d("获取到用户ID: $userId")

                // 2. 获取用户训练上下文
                val contextResult = getWorkoutContextUseCase(userId)
                if (contextResult !is ModernResult.Success) {
                    logger.e("获取训练上下文失败: $contextResult")
                    return ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "generateWorkoutFromAIChat",
                            message = UiText.DynamicString("无法获取训练上下文"),
                            metadataMap =
                                mapOf<String, Any>(
                                    "sessionId" to parameters.sessionId,
                                    "userId" to userId,
                                ),
                            recoverable = true,
                        ),
                    )
                }

                val workoutContext = contextResult.data
                logger.d("获取训练上下文成功")

                // 3. 调用AI服务生成训练模板
                val templateResult =
                    aiInteractionService.generateWorkoutTemplateFromPrompt(
                        userId = userId,
                        prompt = parameters.userIntentSummary,
                        context = workoutContext,
                    )

                when (templateResult) {
                    is ModernResult.Success -> {
                        logger.d("AI训练模板生成成功: templateId=${templateResult.data.id}")
                        templateResult
                    }

                    is ModernResult.Error -> {
                        logger.e("AI训练模板生成失败: ${templateResult.error}")
                        templateResult
                    }

                    is ModernResult.Loading -> {
                        logger.d("AI训练模板生成中...")
                        ModernResult.Loading
                    }
                }
            } catch (e: Exception) {
                logger.e(e, "生成训练模板失败")
                ModernResult.error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "generateWorkoutFromAIChat",
                        message = UiText.DynamicString("生成训练模板失败: ${e.message}"),
                        metadataMap = mapOf<String, Any>("sessionId" to parameters.sessionId),
                        recoverable = true,
                    ),
                )
            }
        }
    }
