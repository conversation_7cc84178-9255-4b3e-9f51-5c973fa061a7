package com.example.gymbro.domain.workout.usecase.plan

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.template.TemplateVersion
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.shared.models.workout.WorkoutPlan
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.CoroutineDispatcher

/**
 * 获取计划及其TemplateVersion详情用例
 *
 * ✅ Phase 3: UseCase层适配 - Plan详情查询增强
 * - 获取Plan基本信息
 * - 解析并获取引用的TemplateVersion完整内容
 * - 为UI展示提供丰富的版本信息
 */
@Singleton
class GetPlanWithTemplateVersionsUseCase
    @Inject
    constructor(
        private val planRepository: PlanRepository,
        private val templateRepository: TemplateRepository,
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        logger: Logger,
    ) : ModernUseCase<String, GetPlanWithTemplateVersionsUseCase.Result>(dispatcher, logger) {

        /**
         * Plan + TemplateVersion详情结果
         */
        data class Result(
            val plan: WorkoutPlan,
            val templateVersions: Map<String, TemplateVersion>,
            val hasVersionedReferences: Boolean,
        ) {
            fun getTemplateVersionsForDay(dayNumber: Int): List<TemplateVersion> {
                val dayPlan = plan.dailySchedule[dayNumber] ?: return emptyList()
                return if (dayPlan.isAdaptedToTemplateVersion()) {
                    dayPlan.templateVersionIds.mapNotNull { versionId ->
                        templateVersions[versionId]
                    }
                } else {
                    emptyList()
                }
            }

            fun getTotalTemplateVersionCount(): Int = templateVersions.size

            fun hasLegacyTemplateReferences(): Boolean {
                return plan.dailySchedule.values.any { dayPlan ->
                    !dayPlan.isAdaptedToTemplateVersion() && dayPlan.templateIds.isNotEmpty()
                }
            }
        }

        override suspend fun execute(planId: String): ModernResult<Result> {
            logger.d("获取Plan详情及TemplateVersion: $planId")

            if (planId.isBlank()) {
                return ModernResult.Error(
                    com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                        operationName = "getPlanWithTemplateVersions",
                        message = UiText.DynamicString("计划ID不能为空"),
                    ),
                )
            }

            // 1. 获取Plan基本信息
            val planResult = planRepository.getPlan(planId)
            if (planResult !is ModernResult.Success) {
                return when (planResult) {
                    is ModernResult.Error -> ModernResult.Error(planResult.error)
                    is ModernResult.Loading -> ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "getPlanWithTemplateVersions",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Operation.Timeout,
                            uiMessage = UiText.DynamicString("获取计划信息超时"),
                        ),
                    )

                    else -> ModernResult.Error(
                        com.example.gymbro.core.error.types.ModernDataError(
                            operationName = "getPlanWithTemplateVersions",
                            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.General,
                            uiMessage = UiText.DynamicString("获取计划信息失败"),
                        ),
                    )
                }
            }

            val plan = planResult.data

            // 2. 收集所有TemplateVersion ID
            val allVersionIds = plan.dailySchedule.values
                .filter { it.isAdaptedToTemplateVersion() && it.templateVersionIds.isNotEmpty() }
                .flatMap { it.templateVersionIds }
                .distinct()

            if (allVersionIds.isEmpty()) {
                logger.d("Plan未包含TemplateVersion引用，返回基础信息")
                return ModernResult.Success(
                    Result(
                        plan = plan,
                        templateVersions = emptyMap(),
                        hasVersionedReferences = false,
                    ),
                )
            }

            // 3. 批量获取TemplateVersion详情
            logger.d("获取${allVersionIds.size}个TemplateVersion详情")
            val versionResults = mutableMapOf<String, TemplateVersion>()

            for (versionId in allVersionIds) {
                val versionResult = templateRepository.getVersion(versionId)
                when (versionResult) {
                    is ModernResult.Success -> {
                        versionResults[versionId] = versionResult.data
                    }

                    is ModernResult.Error -> {
                        logger.w("获取TemplateVersion失败: $versionId, ${versionResult.error}")
                    }

                    is ModernResult.Loading -> {
                        logger.w("获取TemplateVersion超时: $versionId")
                    }
                }
            }

            // 4. 构建完整结果
            val result = Result(
                plan = plan,
                templateVersions = versionResults,
                hasVersionedReferences = true,
            )

            logger.d("成功获取Plan详情，包含${versionResults.size}个TemplateVersion")
            return ModernResult.Success(result)
        }
    }
