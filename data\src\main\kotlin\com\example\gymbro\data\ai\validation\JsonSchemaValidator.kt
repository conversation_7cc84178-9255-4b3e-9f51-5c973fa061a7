package com.example.gymbro.data.ai.validation

import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.serialization.json.*
import timber.log.Timber

/**
 * JSON Schema校验器
 * Sprint 1A: 双层校验机制实现
 *
 * 校验策略：
 * 1. 第一层：本地Schema快速校验
 * 2. 第二层：业务逻辑校验
 * 3. 错误时：重试校正机制
 */
@Singleton
class JsonSchemaValidator
    @Inject
    constructor(
        private val json: Json,
    ) {
        /**
         * 校验训练模板JSON
         * @param jsonString JSON字符串
         * @return 校验结果
         */
        fun validateWorkoutTemplate(jsonString: String): ValidationResult {
            return try {
                // 安全检查：防止超大payload
                if (jsonString.length > MAX_PAYLOAD_SIZE) {
                    return ValidationResult.Invalid(
                        errors = listOf("Payload超过${MAX_PAYLOAD_SIZE}字符限制"),
                        errorCode = "payload_too_large",
                    )
                }

                // 第一层：JSON格式校验
                val jsonElement = json.parseToJsonElement(jsonString)
                if (jsonElement !is JsonObject) {
                    return ValidationResult.Invalid(
                        errors = listOf("根对象必须是JSON Object"),
                        errorCode = "invalid_root_type",
                    )
                }

                // 第二层：Schema结构校验
                val structureValidation = validateTemplateStructure(jsonElement)
                if (!structureValidation.isValid) {
                    return structureValidation
                }

                // 第三层：业务逻辑校验
                val businessValidation = validateBusinessLogic(jsonElement)
                if (!businessValidation.isValid) {
                    return businessValidation
                }

                Timber.d("JSON Schema校验通过: ${jsonString.take(100)}...")
                ValidationResult.Valid
            } catch (e: Exception) {
                Timber.w(e, "JSON Schema校验异常")
                ValidationResult.Invalid(
                    errors = listOf("JSON解析失败: ${e.message}"),
                    errorCode = "json_parse_error",
                )
            }
        }

        /**
         * 校验模板结构
         */
        private fun validateTemplateStructure(jsonObject: JsonObject): ValidationResult {
            val errors = mutableListOf<String>()

            // 检查template根字段
            val template = jsonObject["template"]
            if (template == null || template !is JsonObject) {
                errors.add("缺少template字段或类型错误")
                return ValidationResult.Invalid(errors, "missing_template_field")
            }

            // 检查name字段
            val name = template["name"]
            if (name == null || name !is JsonPrimitive || name.content.isBlank()) {
                errors.add("模板名称不能为空")
            }

            // 检查exercises字段
            val exercises = template["exercises"]
            if (exercises == null || !exercises.jsonArray.isNotEmpty()) {
                errors.add("训练动作列表不能为空")
            } else {
                val exerciseValidation = validateExercisesArray(exercises.jsonArray)
                if (!exerciseValidation.isValid) {
                    errors.addAll(exerciseValidation.errors)
                }
            }

            return if (errors.isEmpty()) {
                ValidationResult.Valid
            } else {
                ValidationResult.Invalid(errors, "structure_validation_failed")
            }
        }

        /**
         * 校验动作数组
         */
        private fun validateExercisesArray(
            exercises: kotlinx.serialization.json.JsonArray,
        ): ValidationResult {
            val errors = mutableListOf<String>()

            if (exercises.size > MAX_EXERCISES_COUNT) {
                errors.add("动作数量不能超过${MAX_EXERCISES_COUNT}个")
            }

            exercises.forEachIndexed { index, exerciseElement ->
                if (exerciseElement !is JsonObject) {
                    errors.add("动作${index + 1}必须是对象")
                    return@forEachIndexed
                }

                val exercise = exerciseElement.jsonObject

                // 检查必需字段
                val requiredFields = listOf("code", "sets", "reps", "weight")
                requiredFields.forEach { field ->
                    if (!exercise.containsKey(field)) {
                        errors.add("动作${index + 1}缺少${field}字段")
                    }
                }

                // 检查sets字段
                exercise["sets"]?.let { sets ->
                    if (sets !is JsonPrimitive) {
                        errors.add("动作${index + 1}的sets必须是数字")
                    } else {
                        val setsValue = sets.content.toIntOrNull()
                        if (setsValue == null || setsValue < 1 || setsValue > 10) {
                            errors.add("动作${index + 1}的sets必须在1-10之间")
                        }
                    }
                }

                // 检查reps字段
                exercise["reps"]?.let { reps ->
                    if (reps !is JsonPrimitive) {
                        errors.add("动作${index + 1}的reps必须是字符串")
                    } else {
                        if (!isValidRepsFormat(reps.content)) {
                            errors.add("动作${index + 1}的reps格式错误，应为'8'或'8-12'")
                        }
                    }
                }

                // 检查weight字段
                exercise["weight"]?.let { weight ->
                    if (weight !is JsonPrimitive) {
                        errors.add("动作${index + 1}的weight必须是字符串")
                    } else {
                        if (!isValidWeightFormat(weight.content)) {
                            errors.add("动作${index + 1}的weight必须是bodyweight/percentage/fixed之一")
                        }
                    }
                }
            }

            return if (errors.isEmpty()) {
                ValidationResult.Valid
            } else {
                ValidationResult.Invalid(errors, "exercises_validation_failed")
            }
        }

        /**
         * 业务逻辑校验
         */
        private fun validateBusinessLogic(jsonObject: JsonObject): ValidationResult {
            val errors = mutableListOf<String>()
            val template = jsonObject["template"]?.jsonObject ?: return ValidationResult.Valid

            // 检查难度合理性
            template["difficulty"]?.let { difficulty ->
                if (difficulty is JsonPrimitive) {
                    val difficultyValue = difficulty.content.toIntOrNull()
                    if (difficultyValue != null && (difficultyValue < 1 || difficultyValue > 5)) {
                        errors.add("难度等级必须在1-5之间")
                    }
                }
            }

            // 检查预估时间合理性
            template["estimatedDuration"]?.let { duration ->
                if (duration is JsonPrimitive) {
                    val durationValue = duration.content.toIntOrNull()
                    if (durationValue != null && (durationValue < 5 || durationValue > 300)) {
                        errors.add("预估时间必须在5-300分钟之间")
                    }
                }
            }

            return if (errors.isEmpty()) {
                ValidationResult.Valid
            } else {
                ValidationResult.Invalid(errors, "business_logic_validation_failed")
            }
        }

        /**
         * 尝试修复常见错误
         * @param jsonString 原始JSON
         * @param errors 错误列表
         * @return 修复后的JSON或原始JSON
         */
        fun attemptAutoFix(
            jsonString: String,
            errors: List<String>,
        ): String {
            var fixedJson = jsonString

            // 修复常见格式问题
            errors.forEach { error ->
                when {
                    error.contains("缺少template字段") -> {
                        // 尝试包装整个JSON为template字段
                        fixedJson = """{"template": $fixedJson}"""
                    }

                    error.contains("reps格式错误") -> {
                        // 修复reps格式：将数字转换为字符串
                        fixedJson =
                            fixedJson.replace(
                                Regex(""""reps":\s*(\d+)"""),
                                """"reps":"$1"""",
                            )
                    }

                    error.contains("weight必须是字符串") -> {
                        // 修复weight格式
                        fixedJson =
                            fixedJson.replace(
                                Regex(""""weight":\s*(\d+(?:\.\d+)?)"""),
                                """"weight":"fixed"""",
                            )
                    }
                }
            }

            return fixedJson
        }

        /**
         * 生成重试校正提示
         * @param errors 错误列表
         * @return 校正提示文本
         */
        fun generateCorrectionPrompt(errors: List<String>): String {
            val errorSummary = errors.take(3).joinToString("; ")
            return "请修复以下JSON格式错误后重新生成: $errorSummary"
        }

        /**
         * 校验reps格式
         */
        private fun isValidRepsFormat(reps: String): Boolean = reps.matches(Regex("^\\d+(-\\d+)?$"))

        /**
         * 校验weight格式
         */
        private fun isValidWeightFormat(
            weight: String,
        ): Boolean = weight in listOf("bodyweight", "percentage", "fixed")

        companion object {
            private const val MAX_PAYLOAD_SIZE = 16 * 1024 // 16KB限制，防止prompt injection
            private const val MAX_EXERCISES_COUNT = 20 // 最多20个动作
        }
    }

/**
 * 校验结果
 */
sealed class ValidationResult {
    abstract val isValid: Boolean
    abstract val errors: List<String>
    abstract val errorCode: String?

    /**
     * 校验通过
     */
    object Valid : ValidationResult() {
        override val isValid = true
        override val errors = emptyList<String>()
        override val errorCode: String? = null
    }

    /**
     * 校验失败
     */
    data class Invalid(
        override val errors: List<String>,
        override val errorCode: String? = null,
    ) : ValidationResult() {
        override val isValid = false
    }
}

/**
 * Schema校验配置
 */
data class ValidationConfig(
    val enableStrictMode: Boolean = true, // 严格模式
    val maxPayloadSize: Int = 16 * 1024, // 最大载荷大小
    val maxExercises: Int = 20, // 最大动作数量
    val enableAutoFix: Boolean = true, // 启用自动修复
)
