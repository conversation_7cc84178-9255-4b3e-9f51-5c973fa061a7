package com.example.gymbro.features.subscription.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.content.ContentDisplayProvider
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.designSystem.components.ToastState
import com.example.gymbro.domain.usecase.subscription.QuerySubscriptionUseCase
import com.example.gymbro.features.subscription.presentation.contract.SubscriptionContract
import com.example.gymbro.features.subscription.presentation.state.SubscriptionUiState
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 计费周期枚举 - 类型安全的计划选择
 */
enum class BillingPeriod {
    MONTHLY, // 月度计费
    YEARLY, // 年度计费
}

/**
 * 订阅屏幕本地状态 - 简化的UI状态管理
 */
data class SubscriptionLocalState(
    val selectedBillingPeriod: BillingPeriod = BillingPeriod.MONTHLY,
    val toastState: ToastState? = null,
    val isProcessingPlanChange: Boolean = false,
)

/**
 * 订阅ViewModel - 使用BaseMviViewModel和MVI 2.0架构
 *
 * 🔥 MVI 2.0特性：
 * - 继承BaseMviViewModel实现标准MVI架构
 * - Intent→Reducer→State→UI单向数据流
 * - 完整的Effect副作用管理
 * - Profile模块标准对齐
 */
@HiltViewModel
class SubscriptionViewModel
    @Inject
    constructor(
        private val querySubscriptionUseCase: QuerySubscriptionUseCase,
        private val contentDisplayProvider: ContentDisplayProvider,
        private val errorHandler: ModernErrorHandler,
    ) : BaseMviViewModel<SubscriptionContract.Intent, SubscriptionContract.State, SubscriptionContract.Effect>(
        initialState = SubscriptionContract.State(),
    ) {

        // 🔥 MVI 2.0: 实现抽象的reducer属性
        override val reducer:
            Reducer<SubscriptionContract.Intent, SubscriptionContract.State, SubscriptionContract.Effect> =
            object : Reducer<SubscriptionContract.Intent, SubscriptionContract.State, SubscriptionContract.Effect> {
                override fun reduce(
                    intent: SubscriptionContract.Intent,
                    state: SubscriptionContract.State,
                ): ReduceResult<SubscriptionContract.State, SubscriptionContract.Effect> {
                    return when (intent) {
                        is SubscriptionContract.Intent.StartSubscription -> {
                            ReduceResult(
                                newState = state.copy(isLoading = true),
                                effects = emptyList(),
                            )
                        }

                        is SubscriptionContract.Intent.ChangeBillingPeriod -> {
                            ReduceResult(
                                newState = state.copy(selectedBillingPeriod = intent.period),
                                effects = emptyList(),
                            )
                        }

                        is SubscriptionContract.Intent.ClearError -> {
                            ReduceResult(
                                newState = state.copy(error = null),
                                effects = emptyList(),
                            )
                        }

                        is SubscriptionContract.Intent.NavigateBack -> {
                            ReduceResult(
                                newState = state,
                                effects = listOf(SubscriptionContract.Effect.NavigateBack),
                            )
                        }

                        is SubscriptionContract.Intent.NavigateToManageSubscription -> {
                            ReduceResult(
                                newState = state,
                                effects = listOf(SubscriptionContract.Effect.NavigateToManageSubscription),
                            )
                        }

                        is SubscriptionContract.Intent.NavigateToPaymentHistory -> {
                            ReduceResult(
                                newState = state,
                                effects = listOf(SubscriptionContract.Effect.NavigateToPaymentHistory),
                            )
                        }

                        else -> {
                            ReduceResult(
                                newState = state,
                                effects = emptyList(),
                            )
                        }
                    }
                }
            }

        // 🔥 MVI 2.0: 兼容性属性，映射到新的State
        private val _localState = MutableStateFlow(SubscriptionLocalState())
        val localState: StateFlow<SubscriptionLocalState> = _localState.asStateFlow()

        val uiState: StateFlow<SubscriptionUiState> by lazy {
            state.map { contractState ->
                SubscriptionUiState(
                    isLoading = contractState.isLoading,
                    availablePlans = contractState.availablePlans,
                    selectedPlan = contractState.selectedPlan,
                    errorMessage = contractState.error,
                    successMessage = if (contractState.isSubscribed) {
                        com.example.gymbro.core.ui.text.UiText.DynamicString("订阅成功")
                    } else {
                        null
                    },
                    isRetryable = contractState.error != null,
                )
            }.stateIn(
                scope = viewModelScope,
                started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
                initialValue = SubscriptionUiState(),
            )
        }

        init {
            loadSubscriptionPlans()
        }

        // === MVI 2.0: Intent处理方法 ===

        /**
         * 🔥 MVI 2.0: 分发Intent的主要方法
         */
        override fun dispatch(intent: SubscriptionContract.Intent) {
            viewModelScope.launch {
                when (intent) {
                    is SubscriptionContract.Intent.StartSubscription -> handleStartSubscription()
                    is SubscriptionContract.Intent.ChangeBillingPeriod -> handleChangeBillingPeriod(intent.period)
                    is SubscriptionContract.Intent.RetrySubscription -> handleRetrySubscription()
                    is SubscriptionContract.Intent.CancelSubscription -> handleCancelSubscription()
                    is SubscriptionContract.Intent.NavigateBack -> super.dispatch(intent)
                    is SubscriptionContract.Intent.NavigateToManageSubscription -> super.dispatch(intent)
                    is SubscriptionContract.Intent.NavigateToPaymentHistory -> super.dispatch(intent)
                    is SubscriptionContract.Intent.ClearError -> handleClearError()
                    is SubscriptionContract.Intent.ClearSuccess -> handleClearSuccess()
                    is SubscriptionContract.Intent.LoadSubscriptionPlansResult -> handleLoadSubscriptionPlansResult(
                        intent.plans,
                        intent.error,
                    )

                    is SubscriptionContract.Intent.StartSubscriptionResult -> handleStartSubscriptionResult(
                        intent.success,
                        intent.error,
                    )
                }
            }
        }

        // === 便捷方法 (为UI层提供简化的API) ===

        fun onBillingPeriodChanged(period: BillingPeriod) {
            dispatch(SubscriptionContract.Intent.ChangeBillingPeriod(period))
        }

        fun onSubscribeClicked() {
            dispatch(SubscriptionContract.Intent.StartSubscription)
        }

        fun retryPayment() {
            dispatch(SubscriptionContract.Intent.RetrySubscription)
        }

        fun clearErrorMessage() {
            dispatch(SubscriptionContract.Intent.ClearError)
        }

        fun clearSuccessMessage() {
            dispatch(SubscriptionContract.Intent.ClearSuccess)
        }

        fun dismissToast() {
            _localState.value = _localState.value.copy(toastState = null)
        }

        fun getPriceConfig(): Any {
            return contentDisplayProvider
        }

        // === 私有处理方法 ===

        private fun loadSubscriptionPlans() {
            viewModelScope.launch {
                updateState { it.copy(isLoading = true) }

                querySubscriptionUseCase.getAvailablePlans().collectLatest { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            updateState {
                                it.copy(
                                    availablePlans = result.data.toImmutableList(),
                                    isLoading = false,
                                    error = null,
                                )
                            }
                        }

                        is ModernResult.Error -> {
                            updateState {
                                it.copy(
                                    isLoading = false,
                                    error = errorHandler.getUiMessage(result.error),
                                )
                            }
                        }

                        is ModernResult.Loading -> {
                            updateState { it.copy(isLoading = true) }
                        }
                    }
                }
            }
        }

        private fun handleStartSubscription() {
            updateState { it.copy(isLoading = true) }
            // TODO: 实现订阅逻辑
        }

        private fun handleChangeBillingPeriod(period: BillingPeriod) {
            updateState { it.copy(selectedBillingPeriod = period) }
        }

        private fun handleRetrySubscription() {
            loadSubscriptionPlans()
        }

        private fun handleCancelSubscription() {
            // TODO: 实现取消订阅逻辑
        }

        private fun handleClearError() {
            updateState { it.copy(error = null) }
        }

        private fun handleClearSuccess() {
            // TODO: 清除成功状态
        }

        private fun handleLoadSubscriptionPlansResult(plans: List<Any>?, error: String?) {
            if (error != null) {
                updateState {
                    it.copy(
                        isLoading = false,
                        error = com.example.gymbro.core.ui.text.UiText.DynamicString(error),
                    )
                }
            } else {
                // TODO: 处理计划结果
            }
        }

        private fun handleStartSubscriptionResult(success: Boolean, error: String?) {
            if (success) {
                // 使用dispatch发送Effect
                super.dispatch(SubscriptionContract.Intent.ClearSuccess)
            } else {
                updateState {
                    it.copy(
                        isLoading = false,
                        error = com.example.gymbro.core.ui.text.UiText.DynamicString(error ?: "订阅失败"),
                    )
                }
            }
        }
    }
