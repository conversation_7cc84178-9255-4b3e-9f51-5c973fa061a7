package com.example.gymbro.features.subscription.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.error.ModernErrorHandler
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.payment.Payment
import com.example.gymbro.domain.subscription.model.Subscription
import com.example.gymbro.domain.usecase.subscription.ManageSubscriptionUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

/**
 * 管理订阅页面的状态
 */
data class ManageSubscriptionState(
    val isLoading: Boolean = false,
    val errorMessage: UiText? = null,
    val subscription: Subscription? = null,
    val payments: List<Payment> = emptyList(),
    val isAutoRenewalEnabled: Boolean = false,
)

/**
 * 管理订阅ViewModel
 */
@HiltViewModel
class ManageSubscriptionViewModel
    @Inject
    constructor(
        private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
        private val manageSubscriptionUseCase: ManageSubscriptionUseCase,
        private val errorHandler: ModernErrorHandler,
    ) : ViewModel() {
        private val _state = MutableStateFlow(ManageSubscriptionState())
        val state: StateFlow<ManageSubscriptionState> = _state.asStateFlow()

        init {
            loadSubscriptionData()
        }

        /**
         * 加载订阅数据
         */
        private fun loadSubscriptionData() {
            viewModelScope.launch {
                _state.value = _state.value.copy(isLoading = true, errorMessage = null)

                val userIdResult = getCurrentUserIdUseCase().first()
                when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (userId.isNullOrBlank()) {
                            _state.value =
                                _state.value.copy(
                                    isLoading = false,
                                    errorMessage = UiText.DynamicString("未登录，无法获取订阅信息"),
                                )
                            return@launch
                        }

                        _state.value =
                            _state.value.copy(
                                isLoading = false,
                                errorMessage = null,
                            )
                    }

                    is ModernResult.Error -> {
                        _state.value =
                            _state.value.copy(
                                isLoading = false,
                                errorMessage = errorHandler.getUiMessage(userIdResult.error),
                            )
                    }

                    is ModernResult.Loading -> {
                        _state.value = _state.value.copy(isLoading = true)
                    }
                }
            }
        }

        /**
         * 取消订阅
         */
        fun cancelSubscription() {
            viewModelScope.launch {
                _state.value = _state.value.copy(isLoading = true, errorMessage = null)

                val result = manageSubscriptionUseCase.cancelCurrentSubscription()
                when (result) {
                    is ModernResult.Success<*> -> {
                        _state.value =
                            _state.value.copy(
                                isLoading = false,
                                errorMessage = null,
                            )
                        loadSubscriptionData()
                    }

                    is ModernResult.Error -> {
                        _state.value =
                            _state.value.copy(
                                isLoading = false,
                                errorMessage = errorHandler.getUiMessage(result.error),
                            )
                    }

                    is ModernResult.Loading -> {
                        _state.value = _state.value.copy(isLoading = true)
                    }
                }
            }
        }

        /**
         * 切换自动续费状态
         */
        fun toggleAutoRenewal() {
            _state.value =
                _state.value.copy(
                    errorMessage = UiText.DynamicString("自动续费设置功能暂时不可用"),
                )
        }

        /**
         * 重试加载数据
         */
        fun retryLoadData() {
            loadSubscriptionData()
        }
    }
