package com.example.gymbro.features.coach.integration

import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.util.CompactIdGenerator
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.internal.reducer.handlers.MessagingReducerHandler
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Plan B架构端到端集成测试
 *
 * 验证从Coach模块到ThinkingBox模块的完整ID传递流程：
 * - Coach模块使用ConversationIdManager创建消息上下文
 * - Core-Network使用messageId进行路由
 * - ThinkingBox通过messageId订阅正确的数据流
 * - 整个流程的ID一致性和正确性
 */
class PlanBEndToEndTest {

    private lateinit var conversationIdManager: ConversationIdManager
    private lateinit var messagingReducerHandler: MessagingReducerHandler
    private lateinit var directOutputChannel: DirectOutputChannel
    private lateinit var mockCompactIdGenerator: CompactIdGenerator

    @Before
    fun setup() {
        // Mock CompactIdGenerator
        mockCompactIdGenerator = mockk<CompactIdGenerator>()
        every { mockCompactIdGenerator.generateCompactId(any<String>()) } answers {
            val uuid = firstArg<String>()
            "E2E${uuid.take(3).uppercase()}" // 生成类似 "E2EABC" 的压缩ID
        }
        every { mockCompactIdGenerator.getOriginalUuid(any<String>()) } returns null

        // 创建真实的ConversationIdManager
        conversationIdManager = ConversationIdManager(mockCompactIdGenerator)

        // 创建MessagingReducerHandler
        messagingReducerHandler = MessagingReducerHandler(conversationIdManager)

        // Mock DirectOutputChannel
        directOutputChannel = mockk<DirectOutputChannel>(relaxed = true)
    }

    @Test
    fun `Plan B端到端 - Coach发送消息到ThinkingBox的完整流程`() = runTest {
        // === 阶段1: 模拟Coach模块创建会话 ===
        val userId = "e2e-test-user"
        val sessionContext = conversationIdManager.createSessionContext(userId)

        // === 阶段2: 模拟用户发送消息 ===
        val userInput = "请帮我制定一个健身计划"

        // 创建初始状态（模拟Coach的当前状态）
        val initialState = AiCoachContract.State(
            activeSession = AiCoachContract.SessionUi(
                id = sessionContext.sessionId,
                userId = userId,
                title = "健身咨询",
                messages = emptyList(),
                createdAt = kotlinx.datetime.Clock.System.now(),
                lastMessageAt = kotlinx.datetime.Clock.System.now(),
            ),
            isLoading = false,
            streamingState = AiCoachContract.StreamingState.Idle,
        )

        // 创建SendMessage Intent
        val sendMessageIntent = AiCoachContract.Intent.SendMessage(userInput)

        // === 阶段3: 执行MessagingReducerHandler处理 ===
        val result = messagingReducerHandler.handleSendMessage(sendMessageIntent, initialState)

        // === 阶段4: 验证结果状态 ===
        assertNotNull(result)
        assertTrue("应该有Effect生成", result.effects.isNotEmpty())

        // 验证生成的Effects
        val effects = result.effects

        // 应该包含SaveUserMessage Effect
        val saveUserMessageEffect = effects.find { it is AiCoachContract.Effect.SaveUserMessage }
        assertNotNull("应该生成SaveUserMessage Effect", saveUserMessageEffect)

        // 应该包含StartAiStream Effect
        val startAiStreamEffect = effects.find { it is AiCoachContract.Effect.StartAiStream }
        assertNotNull("应该生成StartAiStream Effect", startAiStreamEffect)

        // 应该包含LaunchThinkingBoxDisplay Effect
        val launchThinkingBoxEffect = effects.find { it is AiCoachContract.Effect.LaunchThinkingBoxDisplay }
        assertNotNull("应该生成LaunchThinkingBoxDisplay Effect", launchThinkingBoxEffect)

        // === 阶段5: 验证ID一致性 ===
        val startAiStream = startAiStreamEffect as AiCoachContract.Effect.StartAiStream
        val launchThinkingBox = launchThinkingBoxEffect as AiCoachContract.Effect.LaunchThinkingBoxDisplay

        // 验证AI响应ID和ThinkingBox显示ID一致
        assertEquals(
            "AI响应ID和ThinkingBox显示ID应该一致",
            startAiStream.aiResponseId,
            launchThinkingBox.messageId,
        )

        // 验证ID是有效的UUID格式
        assertTrue(
            "AI响应ID应该是有效的UUID格式",
            startAiStream.aiResponseId.matches(Regex("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")),
        )

        // === 阶段6: 验证ConversationIdManager中的消息上下文 ===
        val userMessageContext = conversationIdManager.getMessageContext(startAiStream.userMessageId)
        val aiMessageContext = conversationIdManager.getMessageContext(startAiStream.aiResponseId)

        assertNotNull("用户消息上下文应该存在", userMessageContext)
        assertNotNull("AI消息上下文应该存在", aiMessageContext)

        assertEquals("用户消息应该属于正确的会话", sessionContext.sessionId, userMessageContext!!.sessionId)
        assertEquals("AI消息应该属于正确的会话", sessionContext.sessionId, aiMessageContext!!.sessionId)

        // === 阶段7: 验证压缩ID生成 ===
        assertTrue("用户消息应该有压缩ID", userMessageContext.compactId.startsWith("E2E"))
        assertTrue("AI消息应该有压缩ID", aiMessageContext.compactId.startsWith("E2E"))

        // === 阶段8: 验证会话消息链更新 ===
        val sessionMessages = conversationIdManager.getSessionMessages(sessionContext.sessionId)
        assertEquals("会话应该包含2条消息", 2, sessionMessages.size)

        val userMessage = sessionMessages.find { it.messageId == startAiStream.userMessageId }
        val aiMessage = sessionMessages.find { it.messageId == startAiStream.aiResponseId }

        assertNotNull("会话中应该包含用户消息", userMessage)
        assertNotNull("会话中应该包含AI消息", aiMessage)
    }

    @Test
    fun `Plan B端到端 - 智能ID匹配在实际场景中的应用`() = runTest {
        // === 创建测试数据 ===
        val userId = "smart-match-user"
        val sessionContext = conversationIdManager.createSessionContext(userId)
        val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)

        // === 测试各种ID格式的匹配 ===

        // 1. 完整messageId匹配
        val fullIdMatch = conversationIdManager.findBestMatchingMessage(messageContext.messageId)
        assertNotNull("完整ID应该能匹配", fullIdMatch)
        assertEquals(messageContext.messageId, fullIdMatch!!.messageId)

        // 2. 压缩ID匹配
        val compactIdMatch = conversationIdManager.findBestMatchingMessage(messageContext.compactId)
        assertNotNull("压缩ID应该能匹配", compactIdMatch)
        assertEquals(messageContext.messageId, compactIdMatch!!.messageId)

        // 3. 前缀匹配（模拟用户输入部分ID）
        val prefix = messageContext.messageId.take(12) // 取前12位
        val prefixMatch = conversationIdManager.findBestMatchingMessage(prefix)
        assertNotNull("前缀应该能匹配", prefixMatch)
        assertEquals(messageContext.messageId, prefixMatch!!.messageId)

        // 4. 无效输入的容错
        val invalidMatch1 = conversationIdManager.findBestMatchingMessage("")
        assertNull("空字符串应该返回null", invalidMatch1)

        val invalidMatch2 = conversationIdManager.findBestMatchingMessage("invalid")
        assertNull("无效ID应该返回null", invalidMatch2)

        val invalidMatch3 = conversationIdManager.findBestMatchingMessage("short")
        assertNull("太短的ID应该返回null", invalidMatch3)
    }

    @Test
    fun `Plan B端到端 - 多轮对话的ID管理`() = runTest {
        // === 创建多轮对话场景 ===
        val userId = "multi-turn-user"
        val sessionContext = conversationIdManager.createSessionContext(userId)

        val conversationRounds =
            mutableListOf<Pair<ConversationIdManager.MessageContext, ConversationIdManager.MessageContext>>()

        // 模拟3轮对话
        repeat(3) { round ->
            val userMessage = conversationIdManager.createMessageContext(sessionContext.sessionId)
            val aiMessage = conversationIdManager.createMessageContext(sessionContext.sessionId)
            conversationRounds.add(userMessage to aiMessage)
        }

        // === 验证会话完整性 ===
        val sessionMessages = conversationIdManager.getSessionMessages(sessionContext.sessionId)
        assertEquals("应该有6条消息（3轮 * 2条/轮）", 6, sessionMessages.size)

        // 验证所有消息都属于同一会话
        sessionMessages.forEach { message ->
            assertEquals("所有消息都应该属于同一会话", sessionContext.sessionId, message.sessionId)
        }

        // === 验证消息时间顺序 ===
        for (i in 1 until sessionMessages.size) {
            assertTrue(
                "消息应该按时间顺序排列",
                sessionMessages[i].timestamp >= sessionMessages[i - 1].timestamp,
            )
        }

        // === 验证会话统计 ===
        val stats = conversationIdManager.getStatistics()
        assertEquals("应该有6条消息", 6, stats.totalMessages)
        assertEquals("应该有1个会话", 1, stats.totalSessions)
        assertEquals("应该有6个压缩ID映射", 6, stats.compactIdMappings)

        // === 验证会话上下文更新 ===
        val updatedSession = conversationIdManager.getSessionContext(sessionContext.sessionId)
        assertNotNull(updatedSession)
        assertEquals("消息链应该包含6条消息", 6, updatedSession!!.messageChain.size)
        assertTrue("最后活跃时间应该更新", updatedSession.lastActiveAt >= updatedSession.createdAt)
    }

    @Test
    fun `Plan B端到端 - 错误处理和恢复机制`() = runTest {
        // === 测试无效会话ID处理 ===
        val invalidSessionId = "invalid-session-id"

        // 尝试在无效会话中创建消息（应该仍然成功，但不会更新会话链）
        val messageInInvalidSession = conversationIdManager.createMessageContext(invalidSessionId)
        assertNotNull("即使会话无效，消息创建也应该成功", messageInInvalidSession)
        assertEquals("消息应该记录指定的会话ID", invalidSessionId, messageInInvalidSession.sessionId)

        // === 测试ID验证的错误处理 ===
        val emptyIdValidation = conversationIdManager.validateMessageId("")
        assertTrue("空ID应该返回Invalid", emptyIdValidation is ConversationIdManager.ValidationResult.Invalid)

        val invalidIdValidation = conversationIdManager.validateMessageId("not-a-uuid")
        assertTrue("无效格式应该返回Invalid", invalidIdValidation is ConversationIdManager.ValidationResult.Invalid)

        val nonExistentIdValidation = conversationIdManager.validateMessageId("550e8400-e29b-41d4-a716-************")
        assertTrue(
            "不存在的ID应该返回NotFound",
            nonExistentIdValidation is ConversationIdManager.ValidationResult.NotFound,
        )

        // === 测试智能匹配的边界情况 ===
        val nullMatch = conversationIdManager.findBestMatchingMessage("")
        assertNull("空字符串匹配应该返回null", nullMatch)

        val shortMatch = conversationIdManager.findBestMatchingMessage("abc")
        assertNull("太短的字符串应该返回null", shortMatch)

        val randomMatch = conversationIdManager.findBestMatchingMessage("random-non-existing-id")
        assertNull("随机字符串应该返回null", randomMatch)
    }

    @Test
    fun `Plan B端到端 - 性能基准测试`() = runTest {
        val startTime = System.currentTimeMillis()

        // === 创建大量会话和消息 ===
        val sessions = mutableListOf<ConversationIdManager.SessionContext>()
        val messages = mutableListOf<ConversationIdManager.MessageContext>()

        repeat(10) { sessionIndex ->
            val session = conversationIdManager.createSessionContext("user-$sessionIndex")
            sessions.add(session)

            repeat(10) { messageIndex ->
                val message = conversationIdManager.createMessageContext(session.sessionId)
                messages.add(message)
            }
        }

        val creationTime = System.currentTimeMillis() - startTime

        // === 性能验证 ===
        assertTrue("创建100条消息应该在合理时间内完成", creationTime < 2000) // 2秒内

        // === 查询性能测试 ===
        val queryStartTime = System.currentTimeMillis()

        messages.forEach { message ->
            val retrieved = conversationIdManager.getMessageContext(message.messageId)
            assertNotNull("所有消息都应该能被查询到", retrieved)
        }

        val queryTime = System.currentTimeMillis() - queryStartTime
        assertTrue("查询100条消息应该在合理时间内完成", queryTime < 1000) // 1秒内

        // === 统计验证 ===
        val stats = conversationIdManager.getStatistics()
        assertEquals("应该有100条消息", 100, stats.totalMessages)
        assertEquals("应该有10个会话", 10, stats.totalSessions)
        assertEquals("应该有100个压缩ID映射", 100, stats.compactIdMappings)

        println("性能测试结果:")
        println("- 创建时间: ${creationTime}ms")
        println("- 查询时间: ${queryTime}ms")
        println("- 内存使用估算: ${stats.memoryUsageEstimate}B")
        println("- 统计信息: ${stats.toLogString()}")
    }
}
