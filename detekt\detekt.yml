# =========================================================================================
# GymBro Detekt Configuration - v4.0 [ULTIMATE]
#
# 本文件为 GymBro 项目定义了世界级的、自动化的代码质量守护规则。
# 它是项目架构原则 (Clean Architecture + MVI 2.0) 的代码实现。
#
# 核心原则:
# 1. 零容忍策略: 任何 detekt 问题都将导致构建失败。
# 2. 架构纯洁性: 规则强制执行层分离 (e.g., domain层无android依赖)。
# 3. 设计系统强制: 严禁硬编码，100% 使用 designSystem Tokens。
# 4. 日志规范: 强制使用 Timber，每文件最多5个调用，禁止token级日志。
# 5. 代码简洁性: 函数<80行，文件<500行，促进单一职责。
# 6. MVI纯洁性: Reducer必须是纯函数，强制执行MVI架构约束。
# 7. Compose最佳实践: 强制执行Jetpack Compose编码规范。
# =========================================================================================

build:
  maxIssues: 0
  weights:
    complexity: 2
    performance: 2
    style: 1
    potential-bugs: 3

config:
  validation: true
  warningsAsErrors: true

# =========================================================================================
# 🔥 GymBro 自定义规则集 - 强制执行项目特定架构约束
# =========================================================================================
gymbro-rules:
  active: true
  # MVI Architecture Rules
  MviStateImmutability:
    active: true
  ImmutableStateClass:
    active: true
  MviIntentNaming:
    active: true
  # Design System Rules
  NoHardcodedColor:
    active: true
  NoHardcodedDimension:
    active: true
  NoHardcodedDesignValues:
    active: true
  UseWorkoutColors:
    active: true
  # Logging Rules
  MaxTimberLogsPerFile:
    active: true
    threshold: 5
  MaxTimberLogsPerFunction:
    active: true
  LoggingModuleRestriction:
    active: true
  # Quality Rules
  NoTodoOrFixme:
    active: true

# =========================================================================================
# Style 规则集
# =========================================================================================
style:
  active: true
  MaxLineLength:
    active: true
    maxLineLength: 120
    excludeCommentStatements: true
  MagicNumber:
    active: true
    ignoreNumbers: ['-1', '0', '1', '2', '100'] # 允许一些常用数字
    ignorePropertyDeclaration: true
    ignoreCompanionObjectPropertyDeclaration: true
    ignoreAnnotation: true
    ignoreNamedArgument: true
    ignoreEnums: true
    ignoreRanges: true
  ForbiddenMethodCall:
    active: true
    methods:
      - 'kotlin.io.print'
      - 'kotlin.io.println'
      - 'android.util.Log.d'
      - 'android.util.Log.e'
      - 'android.util.Log.i'
      - 'android.util.Log.v'
      - 'android.util.Log.w'
      - 'java.lang.System.out.println'
      - 'java.lang.System.err.println'
  ForbiddenImport:
    active: true
    imports:
      - 'android.**'
      - 'androidx.**'
    # 优化：仅在 domain 模块中强制执行此规则
    includes: [ '**/domain/src/main/kotlin/**' ]
  WildcardImport:
    active: true
    autoCorrect: true
  UnusedPrivateMember:
    active: true
    autoCorrect: false # 自动修复可能不安全，建议手动删除
    allowedNames: "(_|ignored|expected|serialVersionUID)"
  NoTabs:
    active: true
    autoCorrect: true
  TrailingWhitespace:
    active: true
    autoCorrect: true
  EndOfSentenceFormat:
    active: true
    autoCorrect: true
  InvalidPackageDeclaration:
    active: true
    autoCorrect: true
  UnusedParameter:
    active: true
    autoCorrect: true
  DeprecatedBlockTag:
    active: true
    autoCorrect: true

# =========================================================================================
# Complexity 规则集
# =========================================================================================
complexity:
  active: true
  TooManyFunctions:
    active: true
    thresholdInClasses: 15
    thresholdInFiles: 20
    thresholdInObjects: 15
    thresholdInInterfaces: 10
  LargeClass:
    active: true
    threshold: 500
  LongMethod:
    active: true
    threshold: 80
    ignoreAnnotated: ['Test', 'Composable']
  NestedBlockDepth:
    active: true
    threshold: 4
  CyclomaticComplexMethod:
    active: true
    threshold: 12
    ignoreSingleWhenExpression: true
  LongParameterList:
    active: true
    functionThreshold: 6
    constructorThreshold: 7
    ignoreDefaultParameters: true
  ReturnCount:
    active: true
    max: 3
    excludeLabeled: false
    excludeReturnFromLambda: true
    excludeGuardClauses: true

# =========================================================================================
# Performance 规则集
# =========================================================================================
performance:
  active: true
  ForEachOnRange:
    active: true
  SpreadOperator:
    active: true

# =========================================================================================
# Exceptions 规则集
# =========================================================================================
exceptions:
  active: true
  SwallowedException:
    active: true
    allowedExceptionNameRegex: "^(_|(ignore|expected).*)"
  TooGenericExceptionCaught:
    active: true
    exceptionNames:
      - 'Error'
      - 'Exception'
      - 'Throwable'
      - 'RuntimeException'
    allowedExceptionNameRegex: "^(_|(ignore|expected).*)"
  TooGenericExceptionThrown:
    active: true
    exceptionNames:
      - 'Error'
      - 'Exception'
      - 'Throwable'
      - 'RuntimeException'
  ReturnFromFinally:
    active: true
  ThrowingExceptionFromFinally:
    active: true

# =========================================================================================
# Coroutines 规则集
# =========================================================================================
coroutines:
  active: true
  GlobalCoroutineUsage:
    active: true
  InjectDispatcher:
    active: true
  RedundantSuspendModifier:
    active: true
    autoCorrect: true
  SleepInsteadOfDelay:
    active: true

# =========================================================================================
# Comments 规则集
# =========================================================================================
comments:
  active: true
  OutdatedDocumentation:
    active: true
    matchTypeParameters: true
    matchDeclarationsOrder: true
    allowParamOnConstructorProperties: false
  UndocumentedPublicClass:
    active: true
  UndocumentedPublicFunction:
    active: true
  UndocumentedPublicProperty:
    active: true
  KDocReferencesNonPublicProperty:
    active: true
  CommentOverPrivateFunction:
    active: true
  CommentOverPrivateProperty:
    active: true

# =========================================================================================
# Potential Bugs 规则集
# =========================================================================================
potential-bugs:
  active: true
  LateinitUsage:
    active: true
    ignoreAnnotated: ["Inject", "Mock", "MockK"]
  EqualsAlwaysReturnsTrueOrFalse:
    active: true
  EqualsWithHashCodeExist:
    active: true
  UnsafeCast:
    active: true
  FunctionOnlyReturningConstant:
    active: true
    autoCorrect: true

# =========================================================================================
# Naming 规则集
# =========================================================================================
naming:
  active: true
  ClassNaming:
    active: true
  ConstructorParameterNaming:
    active: true
  EnumNaming:
    active: true
  FunctionNaming:
    active: true
    ignoreAnnotated: ['Composable', 'Test']
  FunctionParameterNaming:
    active: true
  VariableNaming:
    active: true
    privateVariablePattern: '(_)?[a-z][A-Za-z0-9]*'
  PackageNaming:
    active: true
  ObjectPropertyNaming:
    active: false # 禁用以允许 object 常量使用驼峰命名

# =========================================================================================
# Jetpack Compose 最佳实践规则集 (需要 detekt-compose 插件)
# =========================================================================================
compose:
  active: true
  ComposableNaming:
    active: true
  ModifierMissing:
    active: true
  ModifierReused:
    active: true
  ModifierNotUsedAtRoot:
    active: true
  ViewModelForwarding:
    active: true
  MutableStateInComposable:
    active: true
